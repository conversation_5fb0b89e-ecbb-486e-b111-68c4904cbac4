{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "ESNext", "lib": ["dom", "dom.iterable", "esnext", "webworker"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "types": ["vitest/globals", "@serwist/next/typings"], "paths": {"@/*": ["./src/*"], "~test-utils": ["./tests/utils.tsx"]}, "plugins": [{"name": "next"}]}, "exclude": ["node_modules", "public/sw.js", "node_modules/.pnpm/**/*"], "include": ["next-env.d.ts", "vitest.config.ts", "src", "tests", "**/*.ts", "**/*.d.ts", "**/*.tsx", ".next/types/**/*.ts", "trigger.config.ts"], "ts-node": {"compilerOptions": {"module": "commonjs"}}}