# Gitignore for LobeHub
################################################################

# general
.DS_Store
.idea
.vscode
.history
.temp
.env.local
venv
temp
tmp

# dependencies
node_modules
*.log
*.lock
package-lock.json

# ci
coverage
.coverage
.eslintcache
.stylelintcache

# production
dist
lib
logs
test-output

# umi
.umi
.umi-production
.umi-test
.dumi/tmp*

# husky
.husky/prepare-commit-msg

# misc
# add other ignore file below

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
.next
.env
public/*.js
bun.lockb
temp.mjs
*.patch

# Sentry Config File
.sentryclirc

pnpm-lock.yaml

# clerk configuration (can include secrets)
/.clerk/

litellm_config.yaml
setup.sh