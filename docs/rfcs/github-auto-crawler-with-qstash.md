# RFC: 基于 Upstash QStash 的 GitHub 仓库自动爬取系统

## 摘要

本 RFC 提议在现有的 cloud-admin 系统中集成基于 Upstash QStash 的 GitHub 仓库自动爬取功能，利用现有的 mcp-crawler 库，实现对 MCP Server 仓库的批量自动发现、解析和入库。

## 背景

当前系统已有手动管理 plugins 的能力，但缺乏自动发现和解析新的 MCP Server 仓库的能力。通过集成自动爬取系统，可以：

1. **自动发现**：从多个数据源自动发现新的 MCP Server 仓库
2. **批量处理**：使用队列系统处理大量仓库 URL
3. **异步处理**：避免长时间运行的任务阻塞主线程
4. **可扩展性**：支持水平扩展的消息队列处理

## 动机

### 现有痛点

1. **手动维护成本高**：需要人工发现和添加新的 MCP Server
2. **更新滞后**：无法及时发现仓库的更新和变化
3. **数据源分散**：多个数据源需要手动整合
4. **处理能力限制**：批量处理时容易超时或阻塞

### 解决方案优势

1. **自动化程度高**：减少人工干预，提高效率
2. **实时性强**：通过队列系统实现准实时处理
3. **可靠性高**：支持重试机制和错误处理
4. **成本可控**：使用 Upstash 的 serverless 架构，按需付费

## 详细设计

### 架构概览（最终版）

```mermaid
graph TB
    A[数据源] --> B[mcp-crawler<br/>URL 解析器]
    B --> C[Admin 系统<br/>任务调度器]
    C --> D[去重检查<br/>查询 plugins 表]
    D --> E[QStash 队列]
    E --> F[mcp-crawler<br/>单仓库爬取]
    F --> G[Admin Webhook<br/>结果处理器]
    G --> H[Market SDK<br/>批量导入]
    H --> I[数据库存储]
    I --> J[Admin 面板]

    K[Admin 定时任务] --> C
    L[Admin 手动触发] --> C
```

### 核心组件重新设计

#### 1. mcp-crawler 库职责

专注于数据解析，不涉及任务调度：

```typescript
// packages/mcp-crawler/src/url-parser.ts
export class URLParser {
  // 从各个数据源解析出 GitHub URL 列表
  async parseFromRegistry(): Promise<string[]>;
  async parseFromGlama(): Promise<string[]>;
  async parseFromSmitery(): Promise<string[]>;
  async parseFromGitHubSearch(keywords: string[]): Promise<string[]>;
  async parseFromAllSources(): Promise<string[]>;
  async deduplicateURLs(urls: string[]): Promise<string[]>;
}

// packages/mcp-crawler/src/repo-crawler.ts
export class RepoCrawler {
  // 单个仓库爬取（由 QStash 调用）
  async crawlRepository(repoUrl: string): Promise<MCPManifest | null>;
}
```

#### 2. Admin 系统完整职责

负责任务调度、去重检查、队列管理：

```typescript
// src/server/services/crawler-scheduler.ts
export class CrawlerScheduler {
  constructor(
    private urlParser: URLParser,
    private qstash: QStashManager,
    private marketAdmin: MarketAdmin,
  ) {}

  async scheduleBatchCrawl(sources: string[]): Promise<{
    totalUrls: number;
    newUrls: number;
    skippedUrls: number;
  }> {
    // 1. 使用 mcp-crawler 解析 URL
    const urls = await this.urlParser.parseFromAllSources();

    // 2. 查询现有 plugins 表进行去重
    const existingPlugins = await this.marketAdmin.plugins.getPluginsByRepositoryUrls(urls);
    const existingUrls = new Set(existingPlugins.map((p) => p.repositoryUrl));
    const newUrls = urls.filter((url) => !existingUrls.has(url));

    // 3. 提交新的 URL 到 QStash
    for (const url of newUrls) {
      await this.qstash.publishCrawlJob(url);
    }

    return {
      totalUrls: urls.length,
      newUrls: newUrls.length,
      skippedUrls: urls.length - newUrls.length,
    };
  }

  async handleCrawlResult(result: CrawlResult): Promise<void> {
    if (result.manifest && result.success) {
      // 再次检查是否已存在（防止并发问题）
      const existing = await this.marketAdmin.plugins.getPluginByRepositoryUrl(result.repoUrl);
      if (!existing) {
        await this.marketAdmin.plugins.importFromManifest(result.manifest);
      }
    }
  }
}
```

#### 3. QStash 队列管理器

```typescript
// src/server/services/qstash-manager.ts
export class QStashManager {
  async publishCrawlJob(repoUrl: string): Promise<string> {
    return await this.client.publishJSON({
      url: `${process.env.NEXT_PUBLIC_BASE_URL}/api/webhooks/qstash`,
      body: {
        action: 'crawl',
        repoUrl,
        timestamp: Date.now(),
      },
    });
  }

  verifySignature(signature: string, body: string): boolean {
    // QStash 签名验证逻辑
  }
}
```

### API 设计优化

#### 1. 简化的爬取任务 API

```typescript
// src/server/routers/market/crawler.ts
export const crawlerRouter = router({
  // 启动批量爬取任务
  startBatchCrawl: marketProcedure
    .input(
      z.object({
        sources: z.array(z.enum(['registry', 'glama', 'smithery', 'search'])).optional(),
        keywords: z.array(z.string()).optional(), // 用于 GitHub 搜索
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const scheduler = new CrawlerScheduler(new URLParser(), new QStashManager(), ctx.marketAdmin);
      return await scheduler.scheduleBatchCrawl(input.sources);
    }),

  // 手动添加单个仓库爬取
  crawlRepository: marketProcedure
    .input(
      z.object({
        repoUrl: z.string().url(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // 检查是否已存在
      const existing = await ctx.marketAdmin.plugins.getPluginByRepositoryUrl(input.repoUrl);
      if (existing) {
        throw new Error('该仓库已存在对应的插件');
      }

      const qstash = new QStashManager();
      return await qstash.publishCrawlJob(input.repoUrl);
    }),

  // 获取爬取统计
  getCrawlStats: marketProcedure.query(async ({ ctx }) => {
    // 直接从 plugins 表统计
    const stats = await ctx.db.plugin.groupBy({
      by: ['source'],
      _count: true,
      where: {
        createdAt: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 最近30天
        },
      },
    });
    return stats;
  }),
});
```

#### 2. Webhook 处理器

```typescript
// src/app/api/webhooks/qstash/route.ts
export async function POST(request: Request) {
  const signature = request.headers.get('upstash-signature');
  const body = await request.text();

  const qstash = new QStashManager();
  if (!qstash.verifySignature(signature, body)) {
    return new Response('Unauthorized', { status: 401 });
  }

  const payload = JSON.parse(body);

  if (payload.action === 'crawl') {
    // 使用 mcp-crawler 处理单个仓库
    const crawler = new RepoCrawler();

    try {
      const manifest = await crawler.crawlRepository(payload.repoUrl);

      // 处理结果
      const scheduler = new CrawlerScheduler();
      await scheduler.handleCrawlResult({
        repoUrl: payload.repoUrl,
        manifest,
        success: !!manifest,
      });

      return Response.json({ success: true, hasManifest: !!manifest });
    } catch (error) {
      console.error('Crawl job failed:', error);
      return Response.json({ success: false, error: error.message }, { status: 500 });
    }
  }

  return Response.json({ success: false, error: 'Unknown action' }, { status: 400 });
}
```

### 数据库设计简化

完全移除 crawl_history 表，直接利用现有的 plugins 表：

```typescript
// 扩展 Market SDK 以支持按仓库 URL 查询
// src/server/services/market-admin-extensions.ts
export class MarketAdminExtensions {
  async getPluginsByRepositoryUrls(urls: string[]): Promise<Plugin[]> {
    return await this.db.plugin.findMany({
      where: {
        repositoryUrl: {
          in: urls,
        },
      },
      select: {
        id: true,
        repositoryUrl: true,
        name: true,
      },
    });
  }

  async getPluginByRepositoryUrl(url: string): Promise<Plugin | null> {
    return await this.db.plugin.findFirst({
      where: {
        repositoryUrl: url,
      },
    });
  }
}
```

### mcp-crawler 数据源实现

```typescript
// packages/mcp-crawler/src/data-sources/registry.ts
export class RegistryDataSource {
  async fetchURLs(): Promise<string[]> {
    const response = await fetch(
      'https://raw.githubusercontent.com/modelcontextprotocol/registry/main/data/seed.json',
    );
    const data = await response.json();

    // 提取 GitHub URLs
    return data
      .filter((item) => item.repository?.startsWith('https://github.com/'))
      .map((item) => item.repository);
  }
}

// packages/mcp-crawler/src/data-sources/glama.ts
export class GlamaDataSource {
  async fetchURLs(): Promise<string[]> {
    const response = await fetch('https://glama.ai/mcp/servers.json');
    const data = await response.json();

    // 解析并提取 GitHub URLs
    return data
      .filter((item) => item.sourceUrl?.includes('github.com'))
      .map((item) => this.normalizeGitHubUrl(item.sourceUrl));
  }

  private normalizeGitHubUrl(url: string): string {
    // 标准化 GitHub URL 格式
    return url.replace(/\.git$/, '');
  }
}
```

### 配置管理

#### 环境变量

```env
# Upstash QStash
QSTASH_URL=https://qstash.upstash.io
QSTASH_TOKEN=your_qstash_token
QSTASH_CURRENT_SIGNING_KEY=your_signing_key
QSTASH_NEXT_SIGNING_KEY=your_next_signing_key

# GitHub API (mcp-crawler 使用)
GITHUB_TOKEN=your_github_token

# OpenAI API (mcp-crawler 增强解析使用)
OPENAI_API_KEY=your_openai_api_key
```

#### 配置文件

```typescript
// src/config/crawler.ts
export const crawlerConfig = {
  qstash: {
    url: process.env.QSTASH_URL!,
    token: process.env.QSTASH_TOKEN!,
    signingKey: process.env.QSTASH_CURRENT_SIGNING_KEY!,
  },
  webhook: {
    endpoint: `${process.env.NEXT_PUBLIC_BASE_URL}/api/webhooks/qstash`,
  },
};
```

## 实现计划（最终版）

### Phase 1: mcp-crawler 库扩展 (1 周)

1. **数据源解析器**

   - 实现 RegistryDataSource、GlamaDataSource、SmitheryDataSource
   - 实现 URLParser 统一接口
   - 添加 URL 去重和标准化功能

2. **单仓库爬取器**
   - 重构现有 MCPCrawler 为 RepoCrawler
   - 确保可以独立运行（用于 QStash 回调）

### Phase 2: Admin 系统集成 (1 周)

1. **QStash 集成**

   - 实现 QStashManager 服务
   - 配置环境变量和签名验证
   - 实现 Webhook 处理器

2. **任务调度器**

   - 实现 CrawlerScheduler 服务
   - 集成 URLParser 和去重逻辑
   - 扩展 Market SDK 支持按 URL 查询

3. **API 开发**
   - 实现 crawler router
   - 集成批量爬取和单个爬取功能

### Phase 3: 前端集成 (3-5 天)

1. **管理界面**

   - 爬取任务触发页面
   - 统计和监控面板

2. **手动操作功能**
   - 手动添加仓库爬取
   - 查看爬取统计

### Phase 4: 测试和优化 (2-3 天)

1. **测试完善**

   - 端到端流程测试
   - 错误处理测试

2. **性能优化**
   - 批量处理优化
   - 并发控制

## 风险和缓解（更新版）

### 技术风险

1. **并发重复处理**

   - 风险：多个任务同时处理同一个仓库
   - 缓解：在 handleCrawlResult 中再次检查是否已存在

2. **API 限制**

   - 风险：GitHub API 请求限制
   - 缓解：在 mcp-crawler 中实现请求频率控制

3. **QStash 可靠性**
   - 风险：消息丢失或重复处理
   - 缓解：QStash 提供 at-least-once 保证，通过数据库去重处理

### 数据风险

1. **数据源变更**

   - 风险：第三方数据源格式变更
   - 缓解：实现容错解析和监控告警

2. **重复插件**
   - 风险：同一仓库创建多个插件
   - 缓解：基于 repositoryUrl 字段进行严格去重

## 测试策略

### 单元测试

- URL 收集器组件测试
- 爬取处理器核心逻辑测试
- QStash 管理器功能测试

### 集成测试

- 端到端爬取流程测试
- Webhook 处理测试
- 数据库操作测试

### 性能测试

- 批量处理性能测试
- 并发处理能力测试
- 内存使用监控测试

## 部署和运维

### 部署流程

1. **环境配置**

   - 设置所需环境变量
   - 配置 Upstash QStash

2. **数据库迁移**

   - 执行数据库表创建脚本
   - 设置索引和约束

3. **应用部署**
   - 部署包含新功能的应用版本
   - 验证 Webhook 端点可访问性

### 监控指标

- 爬取任务成功率
- 平均处理时间
- 队列积压情况
- API 错误率
- 数据库性能指标

## 总结

本 RFC 提出了一个基于 Upstash QStash 的 GitHub 仓库自动爬取系统设计，该系统将：

1. **提高效率**：自动化 MCP Server 发现和入库流程
2. **增强可靠性**：通过队列系统和重试机制确保处理可靠性
3. **降低成本**：使用 serverless 架构，按需付费
4. **易于维护**：模块化设计，便于后续扩展和维护

通过分阶段实施，可以逐步构建完整的自动爬取系统，为 cloud-admin 平台提供强大的自动化能力。
