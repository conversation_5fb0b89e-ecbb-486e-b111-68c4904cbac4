# MCP Badge 检查 Workflow

本文档描述了新创建的插件 MCP Badge 检查功能。

## 功能概述

这个 workflow 用于检查已发布插件的 GitHub README 中是否包含正确的 MCP Badge。

## 实现的组件

### 1. 后端 Workflow (`src/app/(backend)/api/workflows/check-plugin-claims/route.ts`)

- **功能**: 自动检查所有已发布插件的 GitHub README 中是否包含 MCP Badge
- **逻辑流程**:
  1. 获取所有已发布的插件列表
  2. 并行检查每个插件的 GitHub README 内容
  3. 使用正则表达式匹配预期的 Badge 格式
  4. 汇总结果并生成报告

### 2. 前端触发按钮 (`src/app/[variants]/(main)/market/plugins/features/PluginList/CheckBadgeButton.tsx`)

- **功能**: 管理界面中的触发按钮
- **位置**: 插件管理页面的工具栏
- **交互**: 点击按钮后触发 workflow 执行

### 3. 后端 API 路由 (`src/server/routers/market/plugins.ts`)

- **新增方法**: `triggerCheckPluginClaims`
- **功能**: 提供 TRPC 接口来触发 workflow
- **备用方法**: `getUnclaimedPlugins` (为未来扩展准备)

## Badge 检查逻辑

预期的 MCP Badge 格式:

```markdown
[![MCP Badge](https://lobehub.com/badge/mcp-full/{plugin-identifier})](https://lobehub.com/mcp/{plugin-identifier})
```

检查策略:

1. **完全匹配**: 检查是否包含完整的预期 Badge
2. **部分匹配**: 使用正则表达式检查关键 URL 模式
3. **容错处理**: 对于无法访问或无 GitHub URL 的插件进行适当处理

## 输出结果

workflow 返回的结果包含:

```typescript
interface CheckClaimsResult {
  total: number; // 总检查插件数
  checked: number; // 成功检查的插件数
  withGitHub: number; // 有 GitHub URL 的插件数
  hasBadge: number; // 包含 MCP Badge 的插件数
}
```
