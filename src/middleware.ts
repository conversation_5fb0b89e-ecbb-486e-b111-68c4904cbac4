import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';
import { NextRequest, NextResponse } from 'next/server';
import { UAParser } from 'ua-parser-js';
import urlJoin from 'url-join';

import { LOBE_LOCALE_COOKIE } from '@/const/locale';
import { LOBE_THEME_APPEARANCE } from '@/const/theme';
import { checkLicenseValidity } from '@/libs/license';
import { parseBrowserLanguage } from '@/utils/locale';
import { RouteVariants } from '@/utils/server/routeVariants';

export const config = {
  matcher: [
    // exclude static files
    '/((?!api|_next/static|_next/image|favicon.ico|favicon-|manifest.webmanifest|sitemap.xml|robots.txt|icons).*)',
    '/',
    '/(api|trpc)(.*)',
  ],
};

const defaultMiddleware = (request: NextRequest) => {
  // 1. 从 cookie 中读取用户偏好
  const theme = request.cookies.get(LOBE_THEME_APPEARANCE)?.value || 'light';

  // if it's a new user, there's no cookie
  // So we need to use the fallback language parsed by accept-language
  const browserLanguage = parseBrowserLanguage(request.headers);
  const locale = request.cookies.get(LOBE_LOCALE_COOKIE)?.value || browserLanguage;

  const ua = request.headers.get('user-agent');

  const device = new UAParser(ua || '').getDevice();

  // 2. 创建规范化的偏好值
  const route = RouteVariants.serializeVariants({
    isMobile: device.type === 'mobile',
    locale,
    theme,
  });

  const url = new URL(request.url);
  if (['/api', '/trpc'].some((path) => url.pathname.startsWith(path))) return NextResponse.next();

  // 优先处理 OPTIONS 请求
  if (request.method === 'OPTIONS') {
    return new NextResponse(null, {
      headers: {
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Origin': '*',
      },
    });
  }

  // 处理 URL 重写
  // 检查路径是否已经包含路由变体（避免重复重写）
  if (url.pathname.startsWith(`/${route}/`)) {
    // 路径已经包含路由变体，直接返回，不进行重写
    return NextResponse.next();
  }

  // 构建新路径: /${route}${originalPathname}
  // 只对 GET 请求进行 URL 重写，确保其他类型的请求（包括 OPTIONS）不受影响
  const nextPathname = `/${urlJoin(route, url.pathname)}`;
  console.log(`[rewrite] ${url.pathname} -> ${nextPathname}`);
  url.pathname = nextPathname;

  return NextResponse.rewrite(url);
};

const isPublicRoute = createRouteMatcher([
  '/login(.*)',
  '/api/webhooks(.*)',
  '/api/workflows/(.*)',
  '/license-invalid(.*)',
]);

export default clerkMiddleware(
  async (auth, req) => {
    const url = new URL(req.url);

    // 如果已经在 license-invalid 页面，跳过所有验证，避免无限重定向
    const isLicenseInvalidPage = url.pathname.includes('/license-invalid');

    if (isLicenseInvalidPage) {
      return defaultMiddleware(req);
    }

    // 如果是 API 或 TRPC 路由，跳过 license 验证，只进行用户认证
    if (['/api', '/trpc'].some((path) => url.pathname.startsWith(path))) {
      if (!isPublicRoute(req)) {
        await auth.protect();
      }
      return defaultMiddleware(req);
    }

    // 如果是公开路由，跳过 license 验证
    if (isPublicRoute(req)) {
      return defaultMiddleware(req);
    }

    // 只对页面路由进行 license 验证
    const licenseResponse = await checkLicenseValidity(req);
    if (licenseResponse) {
      return licenseResponse;
    }

    // License 验证通过后，进行用户认证（此时已确认不是公开路由）
    await auth.protect();

    return defaultMiddleware(req);
  },
  {
    // https://github.com/lobehub/lobe-chat/pull/3084
    clockSkewInMs: 120 * 1000,
    signInUrl: '/login',
    signUpUrl: '/login',
  },
);
