import { MenuDataItem, Route } from '@ant-design/pro-layout/es/typing';
import { Icon, IconProps } from '@lobehub/ui';
import {
  ArrowRightLeft,
  BadgeCentIcon,
  BrainIcon,
  Codesandbox,
  HomeIcon,
  IdCard,
  LockIcon,
  PackageIcon,
  RadarIcon,
  ReceiptIcon,
  StoreIcon,
  UserIcon,
  WebhookIcon,
} from 'lucide-react';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { serverConfigSelectors, useServerConfigStore } from '@/store/serverConfig';

export enum Routes {
  Channel = '/channel',
  ChannelManagement = '/channel/list',
  // ChannelModel = '/channel/model',
  // ChannelMonitor = '/channel/monitor',
  // ChannelSpend = '/channel/spend',
  Console = '/console',
  ConsoleFeatureFlag = '/console/feature-flag',
  ConsoleMigration = '/console/migration',
  ConsoleStatus = '/console/status',
  ConsoleWebhook = '/console/webhook',
  Market = '/market',
  MarketDashboard = '/market/dashboard',
  MarketOrganizations = '/market/organizations',
  MarketPluginConfig = '/market/plugins/:id/config',
  MarketPluginDetail = '/market/plugins/:id',
  MarketPluginVersion = '/market/plugins/:id/versions',
  MarketPlugins = '/market/plugins',
  MarketReview = '/market/review',
  MarketSettings = '/market/settings',
  MarketUsers = '/market/users',
  Models = '/models',
  Operation = '/operation',
  OperationFeedback = '/operation/feedback',
  OperationSegmentation = '/operation/segmentation',
  Others = '/others',
  OthersRbac = '/others/rbac/list',
  Overview = '/',
  Reporting = '/reporting',
  ReportingProduct = '/reporting/product',
  ReportingRevenue = '/reporting/revenue',
  Subscription = '/subscription',
  SubscriptionBudgets = '/subscription/budgets',
  SubscriptionMeters = '/subscription/meters',
  SubscriptionOrders = '/subscription/orders',
  SubscriptionPlans = '/subscription/plans',
  Users = '/users',
  UsersManagement = '/users/list',
  UsersProfile = '/users/:slug',
  UsersRestriction = '/users/restriction',
}

export const useRoutes = (): Route => {
  const { enableClerk } = useServerConfigStore(serverConfigSelectors.config);
  const { t } = useTranslation('routes');
  const size: IconProps['size'] = { size: 16, strokeWidth: 2.5 };

  const routes: MenuDataItem[] = [
    {
      icon: <Icon icon={HomeIcon} size={size} />,
      key: Routes.Overview,
      name: t('overview'),
      path: Routes.Overview,
    },
    {
      children: [
        {
          icon: <Icon icon={UserIcon} size={size} />,
          key: Routes.UsersManagement,
          name: t('user.management'),
          path: Routes.UsersManagement,
        },
        {
          hideInMenu: !enableClerk,
          icon: <Icon icon={LockIcon} size={size} />,
          key: Routes.UsersRestriction,
          name: t('user.restriction'),
          path: Routes.UsersRestriction,
        },
        {
          hideInMenu: true,
          key: Routes.UsersProfile,
          name: t('user.profile'),
          path: Routes.UsersProfile,
        },
      ],
      key: Routes.Users,
      name: t('user.title'),
      path: Routes.Users,
    },

    {
      children: [
        {
          icon: <Icon icon={StoreIcon} size={size} />,
          key: Routes.MarketDashboard,
          name: t('market.dashboard'),
          path: Routes.MarketDashboard,
        },
        {
          icon: <Icon icon={PackageIcon} size={size} />,
          key: Routes.MarketPlugins,
          name: t('market.plugins'),
          path: Routes.MarketPlugins,
        },
        {
          hideInMenu: true,
          key: Routes.MarketPluginDetail,
          name: t('market.pluginDetail'),
          path: Routes.MarketPluginDetail,
        },
        {
          hideInMenu: true,
          key: Routes.MarketPluginVersion,
          name: t('market.pluginVersion'),
          path: Routes.MarketPluginVersion,
        },
        {
          hideInMenu: true,
          key: Routes.MarketPluginConfig,
          name: t('market.pluginConfig'),
          path: Routes.MarketPluginConfig,
        },
        // {
        //   icon: <Icon icon={UsersIcon} size={size} />,
        //   key: Routes.MarketUsers,
        //   name: t('market.users'),
        //   path: Routes.MarketUsers,
        // },
        // {
        //   icon: <Icon icon={UsersIcon} size={size} />,
        //   key: Routes.MarketOrganizations,
        //   name: t('market.organizations'),
        //   path: Routes.MarketOrganizations,
        // },
        // {
        //   icon: <Icon icon={ShieldIcon} size={size} />,
        //   key: Routes.MarketReview,
        //   name: t('market.review'),
        //   path: Routes.MarketReview,
        // },
        // {
        //   icon: <Icon icon={SettingsIcon} size={size} />,
        //   key: Routes.MarketSettings,
        //   name: t('market.settings'),
        //   path: Routes.MarketSettings,
        // },
      ],
      key: Routes.Market,
      name: t('market.title'),
      path: Routes.Market,
    },
    {
      children: [
        {
          icon: <Icon icon={ReceiptIcon} size={size} />,
          key: Routes.SubscriptionOrders,
          name: t('subscription.orders'),
          path: Routes.SubscriptionOrders,
        },
        {
          icon: <Icon icon={BadgeCentIcon} size={size} />,
          key: Routes.SubscriptionBudgets,
          name: t('subscription.budgets'),
          path: Routes.SubscriptionBudgets,
        },
      ],
      key: Routes.Subscription,
      name: t('subscription.title'),
      path: Routes.Subscription,
    },
    {
      children: [
        {
          exact: true,
          icon: <Icon icon={RadarIcon} size={size} />,
          key: Routes.ChannelManagement,
          name: t('channel.management'),
          path: Routes.ChannelManagement,
        },
        {
          exact: true,
          icon: <Icon icon={Codesandbox} size={size} />,
          key: Routes.Models,
          name: '模型管理',
          path: Routes.Models,
        },
        // {
        //   icon: <Icon icon={BrainIcon} size={size} />,
        //   key: Routes.ChannelModel,
        //   name: t('channel.model'),
        //   path: Routes.ChannelModel,
        // },
      ],
      key: Routes.Channel,
      name: t('channel.title'),
      path: Routes.Channel,
    },
    {
      children: [
        {
          icon: <Icon icon={RadarIcon} size={size} />,
          key: 'yinghe-test',
          name: '渠道测试',
          path: '/channel/test',
        },
        {
          exact: true,
          icon: <Icon icon={BrainIcon} size={size} />,
          key: 'model-management-list',
          name: '模型管理测试',
          path: '/model-management/list',
        },
        {
          hideInMenu: true,
          key: 'model-management-detail',
          name: '模型详情测试',
          path: '/model-management/:id',
        },
        {
          icon: <Icon icon={UserIcon} size={size} />,
          key: 'roles',
          name: '角色管理测试',
          path: '/roles',
        },
      ],
      key: 'YINGHE-TEST',
      name: '影和测试',
      path: 'yinghe-test',
    },
    {
      children: [
        {
          icon: <Icon icon={ArrowRightLeft} size={size} />,
          key: Routes.ConsoleMigration,
          name: t('console.migration'),
          path: Routes.ConsoleMigration,
        },
        {
          icon: <Icon icon={WebhookIcon} size={size} />,
          key: Routes.ConsoleWebhook,
          name: t('console.webhook'),
          path: Routes.ConsoleWebhook,
        },
      ],
      key: Routes.Console,
      name: t('console.title'),
      path: Routes.Console,
    },
    {
      children: [
        {
          icon: <Icon icon={IdCard} size={size} />,
          key: Routes.OthersRbac,
          name: t('others.rbac.title'),
          path: Routes.OthersRbac,
        },
      ],
      key: Routes.Others,
      name: t('others.title'),
      path: Routes.Others,
    },
  ];

  return useMemo(
    () => ({
      path: '/',
      routes,
    }),
    [routes],
  );
};
