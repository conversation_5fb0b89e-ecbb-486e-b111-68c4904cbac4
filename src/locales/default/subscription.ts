export default {
  billing: {
    amount: '金额',
    closed: '已关闭',
    created: '付款日期',
    draft: '草稿',
    draftTooltip: '该草稿账单由新的订阅生成，很快将会自动扣款',
    empty: '暂无历史账单',
    endDate: '到期日期',
    history: '历史账单',
    orderNumber: '订单号',
    paid: '已付',
    pay: '去支付',
    paymentMethod: '支付渠道',
    price: '订阅价格',
    startDate: '开始日期',
    status: '交易状态',
    subscriptionId: '订阅号',
    unpaid: '待支付',
    view: '查看',
  },
  cancelAt: '取消时间',
  cancelPlan: {
    alert: '在当前方案到期前 ({{date}}) 仍享受对应权益。您可以在到期前随时重新订阅。',
    desc: '取消订阅后，在当前方案到期时将自动降级为免费版。',
    title: '取消订阅',
  },
  cancelSubscription: {
    confirm: '确定要取消该订阅吗？取消后将立即终止该用户的订阅状态。',
    error: '取消订阅失败，请稍后重试',
    success: '订阅已成功取消',
    title: '取消订阅',
  },
  compare: {
    hobbyCreditTooltip: '不含每月计算积分额度，需要自行配置模型 API',
    monthlyCredit: '每月计算积分',
    title: '方案对比',
  },
  compareAllPlans: '查看所有方案',
  comparePlans: '查看方案',
  createdAt: '订阅时间',
  currentPlan: {
    cancelAlert: `将在 {{canceledAt}} 后取消订阅。在此之前，你仍然可以在「管理订阅」中恢复`,
    downgradeAlert: '将在 {{downgradedAt}} 后降级到 {{plan}}。',
    management: '管理订阅',
    notIncluded: '不包含在当前方案中',
    paymentExpired: `本次订阅将在 {{expiredAt}} 到期，请合理安排使用`,
    seeAllFeaturesAndComparePlans: '查看所有特性并对比方案',
    title: '当前方案',
  },
  discount: {
    add: '增加',
    maxOff: '最高优惠 {{percent}}%',
    off: '优惠 {{percent}}%',
    save: '优惠',
  },
  downgradePlans: {
    alert: '在当前方案到期前 ({{date}}) 仍享受对应权益。新方案将在当前方案到期后生效。',
    desc: '切换方案将在当前方案到期后生效。',
    success: '订阅取消成功',
    title: '切换为{{plan}}',
  },
  header: {
    desc: '用量与订阅管理',
    title: '计费',
  },
  limitation: {
    chat: {
      expired: {
        desc: '您的 {{plan}} 计算积分已于 {{expiredAt}} 过期，立即升级计划以获取计算积分。',
        title: '计算积分已过期',
      },
      hobby: {
        action: '已配置，继续对话',
        configAPI: '前往配置 API',
        desc: '您的免费计算积分已用尽，请配置自定义模型 API 后继续使用。',
        docs: '查看配置文档',
        tip: '记得切换到自定义 API Key 的模型哦',
        title: '请配置模型服务 API',
      },
      limited: {
        action: '立即升级',
        advanceFeature: '升级享受高级功能:',
        desc: '您的 {{plan}} 计算积分已用尽，立即升级以获取计算积分。',
        title: '计算积分已用尽',
      },
      success: {
        action: '继续对话',
        desc: '您所订阅的 {{plan}} 已升级成功，尽情享受 AI 聊天乐趣。当前方案带给您的高级功能:',
        title: '恭喜升级成功',
      },
    },
    providers: {
      mask: {
        action: '立即升级',
        subTitle: '当前方案暂不支持，自定义 API 服务仅对付费方案可用，立即升级享受全球主流模型服务',
        title: '升级到付费方案，使用自定义 API 服务',
      },
      tooltip: '自定义 API 服务仅对付费计划可用',
    },
  },
  modelPricing: {
    button: '调用计费详情文档',
    desc: '{{name}} 采用 Credits 计算积分的方式来衡量 AI 模型的用量，对应大模型使用的 Tokens。下表以 1M Tokens 为计价单位，列出对应的计算积分。',
    title: '文本模型计价',
  },
  models: {
    input: '输入',
    intro: '介绍',
    link: '查看',
    output: '输出',
    title: '模型',
  },
  payDiffPrice: '支付差价',
  payment: {
    error: {
      actions: {
        billing: '账单管理',
        home: '返回首页',
      },
      desc: '系统并未查询到订阅号: {{id}} ，如有疑问请邮件联系',
      title: '查询失败',
    },
    success: {
      actions: {
        startUsing: '开始使用',
        viewBill: '查看账单记录',
      },
      desc: '您所订阅的方案已激活成功',
      title: '订阅成功',
    },
    switchSuccess: {
      desc: '您所订阅的方案将于 {{switchAt}} 自动切换',
      title: '切换成功',
    },
    title: '支付方式',
    upgradeFailed: {
      alert: {
        reason: {
          bank3DS: '发卡行存在 3DS 校验，需要您进行二次确认',
          inefficient: '卡片余额不足',
          security: 'Stripe 系统风控',
        },
        title: '自动扣款常见失败原因',
      },
      desc: '您所订阅的方案升级失败，请检查后重试',
      title: '升级失败',
    },
    upgradeSuccess: {
      desc: '您所订阅的方案已升级成功',
      title: '升级成功',
    },
  },
  plans: {
    btn: {
      contact: '联系我们',
      noAction: '方案锁定中',
      payment: '购买',
      paymentDesc: '支持银行卡 / 支付宝 / 微信支付',
      soon: '即将开放',
    },
    changePlan: '选择计划',
    cloud: {
      history: '无限对话历史记录',
      sync: '全局云同步',
      title: '云服务',
    },
    credit: {
      api: '自定义 API',
      apiDesc: '需配置自有模型 API',
      apiProvider: '支持 OpenAI / Anthropic / OpenRouter 等 20+ 主流模型服务商',
      buy: '购买计算积分',
      buyDesc: '同时支持购买计算积分按需付费',
      none: '不内置计算积分',
      tip: '{{duration}} 天内每天免费提供 {{credit}} 积分',
      title: '计算积分',
      tooltip: '每月模型消息计算积分',
    },
    current: '当前方案',
    downgradePlan: '目标降级方案',
    downgradeTip: '你已经切换订阅，在订阅切换完成前将不能进行其他操作',
    embeddingStorage: {
      embeddings: '条',
      title: '向量存储',
      tooltip:
        '一页文档 (1000~1500 字符) 约生成 1 条向量。(使用 OpenAI Embeddings 进行估计，不同模型可能有所不同)',
    },
    features: {
      agents: '精选助手市场',
      ceAgents: '社区助手市场',
      cePlugins: '社区插件市场',
      internet: '智能联网查询',
      plugins: '专享高级插件',
      showAll: '查看所有特性',
      title: '高级功能',
    },
    fileStorage: {
      title: '文件存储',
      tooltip: '文件存储用于存储文件、图片等数据',
    },
    free: '免费',
    freeTrail: '注册即可获得调用 {{name}} 免费试用，无需信用卡',
    includes: '权益包含:',
    includesExtra: '{{name}}中所有权益，加上:',
    knowledgeBase: {
      desc: '在对话中使用文件和知识库功能',
      filetype: '支持 PDF / MD / DOC / XLS / PPT 等多格式文件',
      title: '文件与知识库',
      tooltip:
        '支持文件上传与知识库功能，你可以上传文件、图片、音频、视频等多种类型的文件，以及创建知识库，方便用户管理和查找文件。同时在对话中使用文件和知识库功能，实现更加丰富的对话体验。',
    },
    llm: {
      customAPI: '全球主流模型自定义 API 服务',
      messageRequest: '无限消息请求',
      title: '模型服务',
      tooltip: '可以添加私有模型 Provider API 同时享受云同步功能',
    },
    message: {
      count: '约 {{number}} 条',
      more: '更多模型见方案对比',
      normalLLM: '通用模型',
      proLLM: '高级模型',
      tooltip: '按平均每条消息消耗 {{number}} Token 估算',
    },
    mostPicked: '最多选择',
    navs: {
      monthly: '月付费',
      payonce: '一次性付费',
      yearly: '年付费',
    },
    payonce: {
      cancel: '取消',
      ok: '确认选择',
      popconfirm:
        '一次性付费购买成功后，需要等订阅过期后才可切换订阅方案与修改周期，请确认选择无误。',
      tooltip: '一次性付费需要等订阅过期后才可切换订阅方案与修改周期',
    },
    plan: {
      enterprise: {
        contactSales: '联系销售',
        title: '企业版',
      },
      free: {
        desc: '初次体验功能用户',
        title: '免费版',
      },
      hobby: {
        desc: '适合自有 API 按需付费的用户',
        title: '自助版',
      },
      premium: {
        desc: '为频繁使用 AI 功能的专业用户设计',
        title: '进阶版',
      },
      starter: {
        desc: '适合偶尔使用 AI 功能的用户',
        title: '基础版',
      },
      ultimate: {
        desc: '针对需要大量 AI 复杂对话的重度用户',
        title: '专业版',
      },
    },
    storage: {
      title: '数据存储',
    },
    subscribe: '订阅方案',
    support: {
      hobby: '社区论坛',
      premium: '优先邮件支持',
      starter: '邮件和社区论坛',
      title: '服务支持',
      ultimate: '优先聊天和邮件支持',
    },
    target: '目标方案',
    unlimited: '无限制',
  },
  qa: {
    desc: '若没有回答到您想了解的问题, 可以查阅 <1>产品文档</1> 获取更多常见问题，同时欢迎与我们联系。',
    detail: '查看详细信息',
    list: {
      credit: {
        a: '计算积分是 {{cloud}} 在调用 AI 模型时，用于衡量模型使用量的指标。不同 AI 模型的计算积分消耗有所差异。',
        q: '什么是计算积分？',
      },
      embeddings: {
        a: '向量存储不等于您上传或导入的数据集的原始大小，而是根据您文件中的纯文本内容向量化计算得出的。例如，一个 1 页的 PDF 文件在提取并矢量化为纯文本时 (1000~1500 字符)，可能只占用大约 1 条矢量存储空间。您可以在「{{usage}}」下查看您的使用情况。',
        q: '向量存储是如何计算的？',
      },
      free: {
        a: '{{name}} 始终秉持开源理念，对于专业的开发人员，您可以通过自部署社区版使用所有开源能力。在 {{cloud}} 版中，我们为所有注册用户提供了 {{credit}} 免费的计算积分，无需复杂配置开箱即用，如果你需要更多用量，可付费订阅 {{starter}}、{{premium}} 或 {{ultimate}}。',
        q: '{{name}} 可以免费使用吗？',
      },
      limit: {
        a: '{{cloud}} 订阅方案分为 {{starter}}、{{premium}} 和 {{ultimate}}，每种方案提供不同的计算积分用量。如当前方案的计算积分不足，建议您考虑升级方案。此外，也可设置自定义模型 API 密钥，使用从其他渠道购买的 API 用量。',
        q: '计算积分不足怎么办？',
      },
      management: {
        a: '在 {{subscribe}} 页面，可以「升级 / 降级」当前的订阅方案，也可以切换年度或月度计费。通过「{{usage}}-{{management}}」可前往 Strip 进行订阅管理，也可随时取消当前订阅。取消订阅后，在当前方案到期时将自动降级为免费版。',
        q: '如何更改或取消订阅方案？',
      },
    },
    support: {
      community: '社区支持',
      email: '邮件支持',
    },
    title: '常见问题',
  },
  recurring: {
    day: '每天',
    fullYear: '全年',
    month: '{{quantity}} 个月',
    monthly: '月度计费',
    oneMonth: '一个月',
    oneYear: '一年',
    payonce: '一次性付费',
    perMonth: '每月',
    perYear: '每年',
    sixMonth: '六个月',
    threeMonth: '三个月',
    title: '计费方式',
    yearly: '年度计费',
  },
  sessionCard: {
    title: '准备好告别免费计划了吗？升级以享受高级功能。',
  },
  status: {
    active: '生效订阅',
    activeFutureCancellation: '生效待取消',
    activeNotCancelling: '持续生效',
    cancelled: '取消订阅',
    inactive: '未生效',
  },
  summary: {
    desc: '此金额仅包含订阅服务的支出。',
    dueBy: '将于 {{date}} 支付',
    nextPayment: '您的下一笔付款',
    paymentInformation: '计费信息',
    title: '计费摘要',
    usageThisMonth: '查看您本月的使用情况。',
    viewBillingHistory: '查看付款历史',
  },
  switchPlan: '切换方案',
  switchToMonthly: {
    desc: '切换后，月度计费方案将在当前年度方案到期后生效。',
    title: '切换为月度计费',
  },
  switchToYearly: {
    desc: '切换后，年度计费方案将在支付差价后立即生效，起始日期继承升级前方案。',
    title: '切换为年度计费',
  },
  tab: {
    billing: '账单管理',
    funds: '积分管理',
    plans: '订阅方案',
    spend: '积分明细',
    usage: '用量统计',
  },

  upgrade: '升级',
  upgradeNow: '立即升级',
  upgradePlan: '升级方案',
  upgradePlans: {
    desc: '升级方案将在支付差价后立即生效，起始日期继承升级前方案。',
    title: '升级为{{plan}}',
  },
  usage: {
    credit: {
      addon: {
        desc: '订阅方案持续时间内生效',
        used: '充值积分',
      },
      desc: '用于 AI 对话、文生图、语音合成的积分用量',
      detail: '近 {{day}} 天用量统计',
      free: {
        desc: '在 {{time}} 后重置',
        expired: '已于 {{date}} 过期',
        used: '免费积分',
      },
      subscription: {
        desc: '配额将于 {{day}} 天后重置',
        used: '订阅积分',
      },
      title: '计算积分用量',
    },
    overview: {
      charge: '费用结算',
      included: '方案用量',
      onDemand: '按需使用',
      product: '产品条目',
      title: '用量总览',
    },
    storage: {
      desc: '可通过手动清理释放数据存储',
      embeddings: {
        used: '向量存储',
      },
      file: {
        used: '文件用量',
      },
      title: '数据存储',
    },
    title: '本月用量',
    used: '已使用',
  },
};
