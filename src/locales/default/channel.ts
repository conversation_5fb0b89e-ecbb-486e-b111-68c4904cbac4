export default {
  abilityList: {
    cache: '支持上下文缓存',
    functionCall: '支持工具使用',
    functionCallDesc: '此配置仅启用模型使用工具的能力，允许添加工具类插件。但模型是否真正能够使用这些工具完全取决于模型本身，请自行测试可用性。',
    imageGeneration: '支持图像生成',
    imageGenerationDesc: '此配置将启用模型的图像生成能力，具体效果完全取决于模型本身。请测试该模型是否具备可用的图像生成能力。',
    reasoning: '支持深度思考',
    reasoningDesc: '此配置将启用模型的深度思考能力，具体效果完全取决于模型本身。请测试该模型是否具备可用的深度思考能力。',
    search: '支持联网搜索',
    searchDesc: '此配置将启用模型的联网搜索能力，具体效果完全取决于模型本身。请测试该模型是否具备可用的联网搜索能力。',
    vision: '支持视觉工具',
    visionDesc: '此配置仅会在应用程序中启用图像上传功能。是否支持识别完全取决于模型本身。请自行测试模型的视觉识别能力。',
  },
  action: {
    add: '添加渠道',
    addFailed: '渠道添加失败',
    addSuccess: '渠道添加成功',
    deleteFailed: '渠道删除失败',
    deleteSuccess: '渠道删除成功',
    disableFailed: '渠道禁用失败',
    disableSuccess: '渠道禁用成功',
    editFailed: '渠道编辑失败',
    editSuccess: '渠道编辑成功',
    enableFailed: '渠道启用失败',
    enableSuccess: '渠道启用成功',
    noSelect: '未选中要删除的渠道',
  },
  addChannel: {
    add: '添加渠道',
    addFailed: '渠道添加失败',
    addSuccess: '渠道添加成功',
    apiConfig: 'API 配置',
    basicInfo: '基本信息',
    cancel: '取消',
    channelDescription: '渠道说明',
    channelDescriptionPlaceholder: '请输入渠道说明',
    channelDescriptionRequired: '请输入渠道说明',
    channelId: '渠道编码',
    channelIdPlaceholder: '请输入渠道编码',
    channelIdRequired: '请输入渠道编码',
    channelName: '渠道名称',
    channelNameMax: '渠道名称不能超过 200 个字符',
    channelNamePlaceholder: '请输入渠道名称',
    channelNameRequired: '请输入渠道名称',
    channelType: '渠道类型',
    channelTypeMax: '渠道类型不能超过 200 个字符',
    channelTypePlaceholder: '请选择渠道类型',
    channelTypeRequired: '请选择渠道类型',
    confirm: '确定',
    edit: '编辑渠道',
    editFailed: '渠道编辑失败',
    editSuccess: '渠道编辑成功',
  },
  addModel: {
    abilityList: '能力列表',
    add: '添加模型',
    addFailed: '模型添加失败',
    addSuccess: '模型添加成功',
    cancel: '取消',
    confirm: '确定',
    contextWindowTokens: '最大上下文窗口',
    contextWindowTokensDesc: '设置模型支持的最大语境数值',
    contextWindowTokensMax: '最大上下文窗口不能超过 256',
    contextWindowTokensPlaceholder: '请输入最大上下文窗口',
    contextWindowTokensRequired: '请输入最大上下文窗口',
    contextWindowTokensUnit: '个',
    edit: '编辑模型',
    editFailed: '模型编辑失败',
    editSuccess: '模型编辑成功',
    enableFallbackModel: '启用 Fallback 模型',
    fallbackModel: 'Fallback 模型',
    fallbackModelPlaceholder: '请选择 Fallback 模型',
    fallbackModelRequired: '请选择 Fallback 模型',
    inputPrice: '输入价格',
    inputPricePlaceholder: '请输入输入价格',
    inputPriceUnit: '元/M',
    modelDescription: '模型描述',
    modelDescriptionDesc: '请输入模型描述（可选）',
    modelDescriptionMax: '模型描述不能超过 500 个字符',
    modelDescriptionPlaceholder: '请输入模型描述（可选）',
    modelDescriptionRequired: '请输入模型描述',
    modelDisplayName: '模型显示名称',
    modelDisplayNameDesc: '请输入模型的显示名称，例如 ChatGPT、GPT-4o..',
    modelDisplayNameMax: '模型显示名称不能超过 200 个字符',
    modelDisplayNamePlaceholder: '请输入模型的显示名称，例如 ChatGPT、GPT-4o..',
    modelDisplayNameRequired: '请输入模型显示名称',
    modelId: '模型ID',
    modelIdDesc: '创建后不可修改，将在调用 AI 时用作模型 ID',
    modelIdMax: '模型 ID 不能超过 150 个字符',
    modelIdPlaceholder: '请输入模型 ID，例如 gpt-4o、claude-3.5-sonnet...',
    modelIdRequired: '请输入模型ID',
    modelLogo: '模型LOGO',
    modelLogoMax: '模型LOGO不能超过 200 个字符',
    modelLogoPlaceholder: '请输入模型LOGO',
    modelLogoRequired: '请输入模型LOGO',
    modelName: '模型名称',
    modelType: '模型类型',
    modelTypeDesc: '模型类型描述',
    modelTypePlaceholder: '请选择模型类型',
    modelTypeRequired: '请选择模型类型',
    outputPrice: '输出价格',
    outputPriceDesc: '请输入输出价格（可选）',
    outputPriceMax: '输出价格不能超过 500 个字符',
    outputPricePlaceholder: '请输入输出价格',
    outputPriceRequired: '请输入输出价格',
    outputPriceUnit: '元/M',
  },
  detail: {
    abledConfirmContent: '你确定要解除禁用吗？',
    addModel: '添加模型',
    addModelFailed: '模型添加失败',
    addModelSuccess: '模型添加成功',
    apiConfig: 'API 配置',
    clouums: {
      channelDescription: '渠道说明',
      channelType: '渠道类型',
    },
    confirm: '确定',
    delete: '删除',
    deleteConfirmContent: '你确定要删除吗？',
    deleteFailed: '删除失败',
    deleteSuccess: '删除成功',
    disable: '禁用',
    disableFailed: '禁用失败',
    disableSuccess: '禁用成功',
    disabledConfirmContent: '你确定要禁用吗？',
    edit: '编辑',
    editSuccess: '编辑成功',
    empty: '请选择一个渠道',
    enable: '启用',
    enableFailed: '解除禁用失败',
    enableSuccess: '解除禁用成功',
    statistic: '详细数据',
  },
  menu: {
    configured: '已配置模型',
    disabled: '已禁用',
    normal: '正常',
    notConfigured: '未配置模型',
    searchPlaceholder: '搜索渠道名称',
  },
  statisic: {
    apiCallCount: 'API 调用量',
    averageResponseTime: '平均响应时长（24h）',
    certificateValidity: '证书有效期',
    checkEvery60s: '每 60s 检查一次',
    cost: '成本支出',
    costUnit: '元',
    onlineRate: '在线率（24h）',
    onlineRate30day: '在线率（30day）',
    performanceIndicator: '性能指标',
    responseTimeDistribution: '响应时间分布（ms）',
    tokenConsumptionInput: 'Token 消耗量（输入）',
    tokenConsumptionOutput: 'Token 消耗量（输出）',
    usageData: '使用统计',
    usageData2: '用量统计',
    usageDataDesc: '使用统计数据',
    usageDataFailed: '获取使用统计失败:',
    usageDataMax: '使用统计数据不能超过 200 个字符',
    usageDataPlaceholder: '请选择使用统计数据',
    usageDataRequired: '请选择使用统计数据',
    usageDataUnit: '个',
  },
  table: {
    disabled: '未启用',
    enabled: '已启用',
    getModel: '获取模型',
    modelList: '模型列表',
    modelListCount: '个模型',
    resetModel: '重置模型',
    resetModelConfirm: '你确定要重置吗？',
    resetModelFailed: '重置模型失败',
    resetModelFailedMessage: '重置模型失败:',
    resetModelSuccess: '重置模型成功',
    searchModelPlaceholder: '搜索模型名称',
    syncModelFailedMessage: '同步模型失败:',
    syncModelSuccess: '同步模型成功',
  },
  title: '渠道管理',
  types: {
    chat: '大语言模型',
    embedding: 'embedding模型',
    image: '图片生成模型',
    realtime: 'Realtime模型',
    rerank: 'rerank模型',
    stt: 'ASR模型',
    text2video: '视频生成',
    tts: 'TTS模型',
  },
};
