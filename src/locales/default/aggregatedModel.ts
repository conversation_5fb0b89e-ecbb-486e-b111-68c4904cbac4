export default {
  form: {
    contextWindowTokens: '上下文窗口大小',
    createdBy: '创建者',
    description: '模型描述',
    displayName: '显示名称',
    enabled: '启用状态',
    id: '模型ID',
    inputPrice: '输入价格',
    outputPrice: '输出价格',
    type: '模型类型',
  },
  message: {
    createSuccess: '模型创建成功',
    deleteConfirm: '确定要删除模型 "{name}" 吗？此操作不可撤销。',
    deleteSuccess: '模型删除成功',
    loadDetailFailed: '加载模型详情失败',
    modelNotFound: '模型不存在',
    relationAddSuccess: '渠道模型关联成功',
    relationDeleteSuccess: '关联模型删除成功',
    statusToggleSuccess: '模型状态切换成功',
    updateSuccess: '模型更新成功',
  },
  modal: {
    addRelation: '添加渠道模型关联',
    create: '新建聚合模型',
    detail: '模型详情',
    edit: '编辑模型',
  },
  table: {
    actions: '操作',
    addRelation: '添加关联',
    contextWindow: '上下文窗口',
    createdAt: '创建时间',
    createdBy: '创建者',
    delete: '删除',
    deleteRelation: '删除关联',
    detail: '查看详情',
    displayName: '显示名称',
    edit: '编辑',
    enabled: '启用',
    id: '模型ID',
    noRelatedModels: '暂无关联的渠道模型',
    pricing: '定价信息',
    relatedModels: '关联的渠道模型',
    status: '状态',
    type: '类型',
    unset: '未设置',
  },
  title: '聚合模型管理',
  type: {
    audio: '音频',
    chat: '对话',
    embedding: '嵌入',
    image: '图像',
  },
};
