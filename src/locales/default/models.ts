export default {
  addModel: {
    addModelFailed: '关联模型添加失败',
    addModelSuccess: '关联模型添加成功',
    addModelTitle: '添加渠道模型关联',
    editModelTitle: '编辑渠道模型关联',
    loadChannelListFailedMessage: '加载渠道列表失败:',
    loadModelListFailedMessage: '加载模型列表失败:',
    selectChannel: '选择渠道',
    selectChannelPlaceholder: '请先选择渠道',
    selectChannelRequired: '请选择渠道',
    selectModel: '选择模型',
    selectModelFailed: '模型列表加载失败',
    selectModelFailedMessage: '加载模型列表失败:',
    selectModelRequired: '请选择模型',
    selectModelSuccess: '模型列表加载成功',
    selectModelSuccessMessage: '模型列表加载成功',
    updateModelFailed: '关联模型更新失败',
    updateModelSuccess: '关联模型更新成功',
    weight: '权重',
    weightPlaceholder: '请输入权重值（可选）',
    weightRequired: '请输入权重值',
    weightTooltip: '用于负载均衡的权重值，数值越大权重越高',
  },
  detail:{
    confirm: '确定',
    createdAt: '创建时间',
    delete: '删除',
    deleteConfirm: '你确定要删除吗？',
    deleteFailed: '删除失败',
    deleteSuccess: '删除成功',
    disable: '禁用',
    disableConfirm: '你确定要禁用吗？',
    disableFailed: '禁用失败',
    disableSuccess: '禁用成功',
    edit: '编辑',
    empty:'请选择一个模型',
    fallbackModel: 'Fallback 模型',
    fallbackModelId: 'Fallback 模型ID',
    inputPrice: '输入价格',
    modelDescription: '模型描述',
    modelId: '模型ID',
    open: '解除禁用',
    openConfirm: '你确定要解除禁用吗？',
    openFailed: '解除禁用失败',
    openSuccess: '解除禁用成功',
    outputPrice: '输出价格'
  },
  table:{
    abilityList: '能力列表',
    addChannel: '添加渠道',
    channel: '渠道',
    channelList: '渠道列表',
    contextWindowTokens: '最大上下文窗口',
    delete: '删除',
    deleteAssociationFailed: '关联模型删除失败',
    deleteAssociationSuccess: '关联模型删除成功',
    edit: '编辑',
    getAssociationInfoFailed: '获取关联信息失败',
    inputPrice: '输入价格',
    loadBalance: '负载均衡',
    model: '模型',
    noAssociationChannelModel: '暂无关联的渠道模型',
    outputPrice: '输出价格',
    price: '价格',
    weight: '权重',
  }
};
