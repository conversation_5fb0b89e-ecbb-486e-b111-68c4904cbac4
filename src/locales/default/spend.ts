export default {
  duration: {
    TPS: 'TPS(Token per Second): 每秒 Token 输出量',
    TTFT: 'TTFT(Time To First Token): 首 Token 延迟',
    completion: '完成输出用时',
    latency: '持续时间',
    stage: {
      end: '结束',
      fistToken: '首 Token',
      start: '开始',
    },
  },
  table: {
    columns: {
      TTFT: {
        tooltip: '首 Token 延迟 (Time To First Token)，单位: 秒',
      },
      duration: '用时',
      emailOrUsernameOrUserId: '邮箱/用户名/UserId',
      model: '模型',
      spend: '积分',
      startTime: '创建时间',
      totalTokens: 'Token 用量',
      type: {
        enums: {
          acompletion: '文本生成',
          aembedding: '向量化',
          aimage_generation: '文生图',
        },
        title: '类型',
      },
      user: '用户',
    },
    desc: '文本生成、向量化、文生图等计算积分使用明细',
    more: '查看详情',
    title: '计算积分使用明细',
    totalToken: {
      completion: '输出补全',
      prompt: '输入提示词',
    },
  },
};
