import { ProviderIcon } from '@lobehub/icons';
import { Avatar, AvatarProps } from '@lobehub/ui';
import { useTheme } from 'antd-style';
import { memo } from 'react';

import { channelAvatar } from '@/const/channel';
import { formatApiBase } from '@/utils/format';

const ChannelIcon = memo<AvatarProps & { apiBase: string; size?: number }>(
  ({ apiBase, size = 20, ...rest }) => {
    const theme = useTheme();

    const key = apiBase.startsWith('http') ? formatApiBase(apiBase) : apiBase;

    const avatar = channelAvatar[key];

    if (!avatar || avatar.startsWith('http'))
      return (
        <Avatar
          alt={avatar || key}
          avatar={avatar || key}
          background={theme.colorBgLayout}
          size={size}
          title={avatar || key}
          {...rest}
        />
      );

    return <ProviderIcon provider={avatar} size={size} {...rest} />;
  },
);

export default ChannelIcon;
