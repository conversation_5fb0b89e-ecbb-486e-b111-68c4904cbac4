'use client';

import { useTheme } from 'antd-style';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

const PageTitle = memo(() => {
  const pathname = usePathname();
  const { t } = useTranslation('common');
  const theme = useTheme();

  const isHome = pathname === '/';
  const isProfile = pathname.startsWith('/users/user_');

  if (isHome) {
    return false;
  }

  if (isProfile) {
    return (
      <Flexbox gap={'0.5em'} horizontal>
        <Link href={'/users'} style={{ color: theme.colorTextSecondary }}>
          {t('title.users')}
        </Link>
        <span style={{ color: theme.colorTextSecondary }}>/</span>
        <span>{t('title.profile')}</span>
      </Flexbox>
    );
  }

  return undefined;
});

export default PageTitle;
