import { Icon } from '@lobehub/ui';
import { CircleUserRound, LogOut } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import Menu from '@/components/Menu';
import { useUserStore } from '@/store/user';

import UserInfo from '../UserInfo';

const PanelContent = memo<{ closePopover: () => void }>(({ closePopover }) => {
  const { t } = useTranslation(['common', 'auth']);
  const router = useRouter();
  const [openUserProfile, signOut] = useUserStore((s) => [s.openUserProfile, s.logout]);

  const handleOpenProfile = () => {
    openUserProfile();
    closePopover();
  };

  return (
    <Flexbox gap={2} style={{ minWidth: 300 }}>
      <UserInfo onClick={handleOpenProfile} />
      <Menu
        items={[
          {
            type: 'divider',
          },
          {
            icon: <Icon icon={CircleUserRound} />,
            key: 'profile',
            label: t('userPanel.profile'),
            onClick: () => openUserProfile(),
          },
          {
            type: 'divider',
          },
          {
            icon: <Icon icon={LogOut} />,
            key: 'logout',
            label: <span>{t('signout', { ns: 'auth' })}</span>,
            onClick: () => {
              signOut();
              router.push('/login');
            },
          },
        ]}
        onClick={closePopover}
      />
    </Flexbox>
  );
});

export default PanelContent;
