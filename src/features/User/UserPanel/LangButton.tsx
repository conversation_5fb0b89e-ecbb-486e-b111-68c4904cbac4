import { ActionIcon, Icon } from '@lobehub/ui';
import { Popover, type PopoverProps } from 'antd';
import { Languages } from 'lucide-react';
import { memo, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import Menu, { type MenuProps } from '@/components/Menu';
import { localeOptions } from '@/locales/resources';
import { useUserStore } from '@/store/user';
import { userGeneralSettingsSelectors } from '@/store/user/selectors';

const LangButton = memo<{ placement?: PopoverProps['placement']; type?: 'icon' | 'button' }>(
  ({ placement = 'right', type = 'button' }) => {
    const [language, switchLocale] = useUserStore((s) => [
      userGeneralSettingsSelectors.language(s),
      s.switchLocale,
    ]);

    const { t } = useTranslation('setting');
    const isIcon = type === 'icon';

    const items: MenuProps['items'] = useMemo(
      () => [
        {
          key: 'auto',
          label: t('settingTheme.lang.autoMode'),
          onClick: () => switchLocale('auto'),
        },
        ...localeOptions.map((item) => ({
          key: item.value,
          label: item.label,
          onClick: () => switchLocale(item.value),
        })),
      ],
      [t],
    );

    return (
      <Popover
        arrow={false}
        content={<Menu items={items} selectable selectedKeys={[language]} />}
        placement={placement}
        styles={{
          body: {
            padding: 0,
          },
        }}
        trigger={['click']}
      >
        {isIcon ? <Icon icon={Languages} /> : <ActionIcon icon={Languages} />}
      </Popover>
    );
  },
);

export default LangButton;
