'use client';

import { ReactNode, memo } from 'react';

import { IServerConfig } from '@/config/serverConfig';

import { Provider, createServerConfigStore } from './store';

interface GlobalStoreProviderProps {
  children: ReactNode;
  isMobile?: boolean;
  serverConfig?: IServerConfig;
}

export const ServerConfigStoreProvider = memo<GlobalStoreProviderProps>(
  ({ children, serverConfig, isMobile }) => (
    <Provider createStore={() => createServerConfigStore({ isMobile, serverConfig })}>
      {children}
    </Provider>
  ),
);
