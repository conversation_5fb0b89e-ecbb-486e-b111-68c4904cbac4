import { ThemeMode } from 'antd-style';
import isEqual from 'fast-deep-equal';
import { DeepPartial } from 'utility-types';
import type { StateCreator } from 'zustand/vanilla';

import type { UserStore } from '@/store/user';
import { LocaleMode } from '@/types/locale';
import { UserGeneralConfig, UserSettings } from '@/types/user/settings';
import { switchLang } from '@/utils/client/switchLang';
import { difference } from '@/utils/difference';
import { merge } from '@/utils/merge';

export interface UserSettingsAction {
  setSettings: (settings: DeepPartial<UserSettings>) => Promise<void>;
  switchLocale: (locale: LocaleMode) => Promise<void>;
  switchThemeMode: (themeMode: ThemeMode) => Promise<void>;
  updateGeneralConfig: (settings: Partial<UserGeneralConfig>) => Promise<void>;
}

export const createSettingsSlice: StateCreator<
  UserStore,
  [['zustand/devtools', never]],
  [],
  UserSettingsAction
> = (set, get) => ({
  setSettings: async (settings) => {
    const { settings: prevSetting, defaultSettings } = get();

    const nextSettings = merge(prevSetting, settings);

    if (isEqual(prevSetting, nextSettings)) return;

    const diffs = difference(nextSettings, defaultSettings);
    set({ settings: diffs }, false, 'optimistic_updateSettings');
  },
  switchLocale: async (locale) => {
    await get().updateGeneralConfig({ language: locale });

    switchLang(locale);
  },
  switchThemeMode: async (themeMode) => {
    await get().updateGeneralConfig({ themeMode });
  },
  updateGeneralConfig: async (general) => {
    await get().setSettings({ general });
  },
});
