import { act, renderHook, waitFor } from '@testing-library/react';
import { afterEach, describe, expect, it, vi } from 'vitest';

import { useUserStore } from '@/store/user';

vi.mock('zustand/traditional');

vi.mock('swr', async (importOriginal) => {
  const modules = await importOriginal();
  return {
    ...(modules as any),
    mutate: vi.fn(),
  };
});

// 定义一个变量来存储 enableAuth 的值
let enableClerk = false;

let enableNextAuth = false;

// 模拟 @/const/auth 模块
vi.mock('@/const/auth', () => ({
  get enableClerk() {
    return enableClerk;
  },
  get enableNextAuth() {
    return enableNextAuth;
  },
}));

afterEach(() => {
  vi.restoreAllMocks();

  enableNextAuth = false;
  enableClerk = false;
});

describe('createAuthSlice', () => {
  describe('logout', () => {
    it('should call clerkSignOut when Clerk is enabled', async () => {
      enableClerk = true;

      const clerkSignOutMock = vi.fn();
      useUserStore.setState({ clerkSignOut: clerkSignOutMock });

      const { result } = renderHook(() => useUserStore());

      await act(async () => {
        await result.current.logout();
      });

      expect(clerkSignOutMock).toHaveBeenCalled();
    });

    it('should not call clerkSignOut when Clerk is disabled', async () => {
      const clerkSignOutMock = vi.fn();
      useUserStore.setState({ clerkSignOut: clerkSignOutMock });

      const { result } = renderHook(() => useUserStore());

      await act(async () => {
        await result.current.logout();
      });

      expect(clerkSignOutMock).not.toHaveBeenCalled();
    });
  });

  describe('openLogin', () => {
    it('should call clerkSignIn when Clerk is enabled', async () => {
      enableClerk = true;
      const clerkSignInMock = vi.fn();
      useUserStore.setState({ clerkSignIn: clerkSignInMock });

      const { result } = renderHook(() => useUserStore());

      await act(async () => {
        await result.current.openLogin();
      });

      expect(clerkSignInMock).toHaveBeenCalled();
    });
    it('should not call clerkSignIn when Clerk is disabled', async () => {
      const clerkSignInMock = vi.fn();
      useUserStore.setState({ clerkSignIn: clerkSignInMock });

      const { result } = renderHook(() => useUserStore());

      await act(async () => {
        await result.current.openLogin();
      });

      expect(clerkSignInMock).not.toHaveBeenCalled();
    });
  });

  describe('openUserProfile', () => {
    it('should call clerkOpenUserProfile when Clerk is enabled', async () => {
      enableClerk = true;

      const clerkOpenUserProfileMock = vi.fn();
      useUserStore.setState({ clerkOpenUserProfile: clerkOpenUserProfileMock });

      const { result } = renderHook(() => useUserStore());

      await act(async () => {
        await result.current.openUserProfile();
      });

      expect(clerkOpenUserProfileMock).toHaveBeenCalled();
    });
    it('should not call clerkOpenUserProfile when Clerk is disabled', async () => {
      const clerkOpenUserProfileMock = vi.fn();
      useUserStore.setState({ clerkOpenUserProfile: clerkOpenUserProfileMock });

      const { result } = renderHook(() => useUserStore());

      await act(async () => {
        await result.current.openUserProfile();
      });

      expect(clerkOpenUserProfileMock).not.toHaveBeenCalled();
    });
  });
});
