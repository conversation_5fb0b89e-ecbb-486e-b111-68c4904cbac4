import { UserStore } from '@/store/user';
import { LobeUser } from '@/types/user';

export const userProfileSelectors = {
  nickName: (s: UserStore) => {
    return s.user?.fullName || s.user?.username;
  },
  userAvatar: (s: UserStore): string => s.user?.avatar || '',
  userId: (s: UserStore) => s.user?.id,
  userProfile: (s: UserStore): LobeUser | null | undefined => s.user,
  username: (s: UserStore) => s.user?.username,
};

export const authSelectors = {
  isLoaded: (s: UserStore) => s.isLoaded,
  isLogin: (s: UserStore) => s.isSignedIn,
};
