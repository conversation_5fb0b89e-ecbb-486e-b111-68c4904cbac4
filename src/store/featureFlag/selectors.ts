import { FeatureFlag } from '@/const/featureFlag';

import { FeatureFlagStore } from './store';

// 通用的基础 Selectors
const CommonFeatureFlagSelectors = {
  /**
   * 判断功能是否被启用，没有被禁用且被授权
   * 优先级：禁用 > 授权
   */
  isEnabled: (featureFlag: string) => (store: FeatureFlagStore) => {
    return !store.revokeFeatures.includes(featureFlag) && store.grantFeatures.includes(featureFlag);
  },

  /**
   * 判断功能是否被授权
   */
  isGranted: (featureFlag: string) => (store: FeatureFlagStore) => {
    return store.grantFeatures.includes(featureFlag);
  },

  /**
   * 判断功能是否被禁用
   */
  isRevoked: (featureFlag: string) => (store: FeatureFlagStore) => {
    return store.revokeFeatures.includes(featureFlag);
  },
};

// 具体功能的选择器
const subscriptionEnabled = (s: FeatureFlagStore) =>
  CommonFeatureFlagSelectors.isEnabled(FeatureFlag.Subscription)(s);

const overviewEnabled = (s: FeatureFlagStore) =>
  CommonFeatureFlagSelectors.isEnabled(FeatureFlag.Overview)(s);

const usersEnabled = (s: FeatureFlagStore) =>
  CommonFeatureFlagSelectors.isEnabled(FeatureFlag.Users)(s);

const usersManagementEnabled = (s: FeatureFlagStore) =>
  CommonFeatureFlagSelectors.isEnabled(FeatureFlag.UsersManagement)(s);

const usersRestrictionEnabled = (s: FeatureFlagStore) =>
  CommonFeatureFlagSelectors.isEnabled(FeatureFlag.UsersRestriction)(s);

const marketEnabled = (s: FeatureFlagStore) =>
  CommonFeatureFlagSelectors.isEnabled(FeatureFlag.Market)(s);

const marketDashboardEnabled = (s: FeatureFlagStore) =>
  CommonFeatureFlagSelectors.isEnabled(FeatureFlag.MarketDashboard)(s);

const marketPluginsEnabled = (s: FeatureFlagStore) =>
  CommonFeatureFlagSelectors.isEnabled(FeatureFlag.MarketPlugins)(s);

const subscriptionOrdersEnabled = (s: FeatureFlagStore) =>
  CommonFeatureFlagSelectors.isEnabled(FeatureFlag.SubscriptionOrders)(s);

const subscriptionBudgetsEnabled = (s: FeatureFlagStore) =>
  CommonFeatureFlagSelectors.isEnabled(FeatureFlag.SubscriptionBudgets)(s);

const channelEnabled = (s: FeatureFlagStore) =>
  CommonFeatureFlagSelectors.isEnabled(FeatureFlag.Channel)(s);

const channelManagementEnabled = (s: FeatureFlagStore) =>
  CommonFeatureFlagSelectors.isEnabled(FeatureFlag.ChannelManagement)(s);

const modelsEnabled = (s: FeatureFlagStore) =>
  CommonFeatureFlagSelectors.isEnabled(FeatureFlag.Models)(s);

const yingheTestEnabled = (s: FeatureFlagStore) =>
  CommonFeatureFlagSelectors.isEnabled(FeatureFlag.YingheTest)(s);

const channelTestEnabled = (s: FeatureFlagStore) =>
  CommonFeatureFlagSelectors.isEnabled(FeatureFlag.ChannelTest)(s);

const modelManagementListEnabled = (s: FeatureFlagStore) =>
  CommonFeatureFlagSelectors.isEnabled(FeatureFlag.ModelManagementList)(s);

const rolesEnabled = (s: FeatureFlagStore) =>
  CommonFeatureFlagSelectors.isEnabled(FeatureFlag.Roles)(s);

const consoleEnabled = (s: FeatureFlagStore) =>
  CommonFeatureFlagSelectors.isEnabled(FeatureFlag.Console)(s);

const consoleMigrationEnabled = (s: FeatureFlagStore) =>
  CommonFeatureFlagSelectors.isEnabled(FeatureFlag.ConsoleMigration)(s);

const consoleWebhookEnabled = (s: FeatureFlagStore) =>
  CommonFeatureFlagSelectors.isEnabled(FeatureFlag.ConsoleWebhook)(s);

const othersEnabled = (s: FeatureFlagStore) =>
  CommonFeatureFlagSelectors.isEnabled(FeatureFlag.Others)(s);

const othersRbacEnabled = (s: FeatureFlagStore) =>
  CommonFeatureFlagSelectors.isEnabled(FeatureFlag.OthersRbac)(s);

export const featureFlagSelectors = {
  // 通用选择器
  ...CommonFeatureFlagSelectors,

  // 具体功能选择器
  subscriptionEnabled,
  overviewEnabled,
  usersEnabled,
  usersManagementEnabled,
  usersRestrictionEnabled,
  marketEnabled,
  marketDashboardEnabled,
  marketPluginsEnabled,
  subscriptionOrdersEnabled,
  subscriptionBudgetsEnabled,
  channelEnabled,
  channelManagementEnabled,
  modelsEnabled,
  yingheTestEnabled,
  channelTestEnabled,
  modelManagementListEnabled,
  rolesEnabled,
  consoleEnabled,
  consoleMigrationEnabled,
  consoleWebhookEnabled,
  othersEnabled,
  othersRbacEnabled,
};
