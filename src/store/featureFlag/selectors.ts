import { FeatureFlagStore } from './store';

// 通用的基础选择器（用于获取功能的状态）
const CommonFeatureFlagSelectors = {
  /**
   * 获取功能是否被禁用
   * 优先级：禁用 > 授权
   * @param featureFlag
   * @returns boolean
   */
  isDisabled: (featureFlag: string) => (store: FeatureFlagStore) => {
    return store.revokeFeatures.includes(featureFlag) || !store.grantFeatures.includes(featureFlag);
  },

  /**
   * 判断功能是否被启用，没有被禁用且被授权
   * 优先级：禁用 > 授权
   * @param featureFlag
   * @returns boolean
   */
  isEnabled: (featureFlag: string) => (store: FeatureFlagStore) => {
    return !store.revokeFeatures.includes(featureFlag) && store.grantFeatures.includes(featureFlag);
  },

  /**
   * 判断功能是否被授权
   * @param featureFlag
   * @returns boolean
   */
  isGranted: (featureFlag: string) => (store: FeatureFlagStore) => {
    return store.grantFeatures.includes(featureFlag);
  },

  /**
   * 判断功能是否被禁用
   * @param featureFlag
   * @returns boolean
   */
  isRevoked: (featureFlag: string) => (store: FeatureFlagStore) => {
    return store.revokeFeatures.includes(featureFlag);
  },
};

export const featureFlagSelectors = {
  // 通用选择器
  ...CommonFeatureFlagSelectors,
};
