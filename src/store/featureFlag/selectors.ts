import { FeatureFlag } from '@/const/featureFlag';

import { FeatureFlagStore } from './store';

// 通用的基础 Selectors
const CommonFeatureFlagSelectors = {
  /**
   * 判断功能是否被启用，没有被禁用且被授权
   * 优先级：禁用 > 授权
   */
  isEnabled: (featureFlag: string) => (store: FeatureFlagStore) => {
    return !store.revokeFeatures.includes(featureFlag) && store.grantFeatures.includes(featureFlag);
  },

  /**
   * 判断功能是否被授权
   */
  isGranted: (featureFlag: string) => (store: FeatureFlagStore) => {
    return store.grantFeatures.includes(featureFlag);
  },

  /**
   * 判断功能是否被禁用
   */
  isRevoked: (featureFlag: string) => (store: FeatureFlagStore) => {
    return store.revokeFeatures.includes(featureFlag);
  },
};

const subscriptionEnabled = (s: FeatureFlagStore) =>
  CommonFeatureFlagSelectors.isEnabled(FeatureFlag.Subscription)(s);

export const featureFlagSelectors = {
  subscriptionEnabled,
};
