import { subscribeWithSelector } from 'zustand/middleware';
import { shallow } from 'zustand/shallow';
import { createWithEqualityFn } from 'zustand/traditional';
import { StateCreator } from 'zustand/vanilla';

import { createDevtools } from '../middleware/createDevtools';
import { type FeatureFlagState, initialState } from './initialState';

//  ===============  聚合 createStoreFn ============ //

export type FeatureFlagStore = FeatureFlagState;

const createStore: StateCreator<FeatureFlagStore, [['zustand/devtools', never]]> = () => ({
  ...initialState,
});

//  ===============  实装 useStore ============ //

const devtools = createDevtools('featureFlag');

export const useFeatureFlagStore = createWithEqualityFn<FeatureFlagStore>()(
  subscribeWithSelector(devtools(createStore)),
  shallow,
);
