'use client';

import { ReactNode, memo, useEffect } from 'react';

import { useFeatureFlagStore } from './store';

interface FeatureFlagProviderProps {
  children: ReactNode;
}

export const FeatureFlagProvider = memo<FeatureFlagProviderProps>(({ children }) => {
  const initFeatureFlags = useFeatureFlagStore((s) => s.initFeatureFlags);

  useEffect(() => {
    // 在组件挂载时初始化功能开关
    initFeatureFlags();
  }, [initFeatureFlags]);

  return <>{children}</>;
});

FeatureFlagProvider.displayName = 'FeatureFlagProvider';
