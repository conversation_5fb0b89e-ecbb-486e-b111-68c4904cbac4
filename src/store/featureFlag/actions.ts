import { StateCreator } from 'zustand/vanilla';

import { getFeatureFlags } from '@/server/services/console';

import { FeatureFlagStore } from './store';

export interface FeatureFlagAction {
  /**
   * init feature flag
   */
  initFeatureFlags: () => void;
}

export const createFeatureFlagSlice: StateCreator<
  FeatureFlagStore,
  [['zustand/devtools', never]],
  [],
  FeatureFlagAction
> = (set) => ({
  initFeatureFlags: async () => {
    try {
      const data = await getFeatureFlags();

      console.log('data', data)

      if (data) {
        set({
          grantFeatures: data.grantFeatures,
          revokeFeatures: data.revokeFeatures,
        });

        console.log('Feature flags initialized:', data);
      } else {
        console.warn('Failed to fetch feature flags');
      }
    } catch (error) {
      console.error('Error initializing feature flags:', error);
      // 在错误情况下，保持默认状态
    }
  },
});
