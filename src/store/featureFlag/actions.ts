import { StateCreator } from 'zustand/vanilla';

import { lobeConsoleEnv } from '@/envs/lobeconsole';

import { FeatureFlagStore } from './store';

export interface FeatureFlagAction {
  /**
   * init feature flag
   */
  initFeatureFlags: () => void;
}

export const createFeatureFlagSlice: StateCreator<
  FeatureFlagStore,
  [['zustand/devtools', never]],
  [],
  FeatureFlagAction
> = () => ({
  initFeatureFlags: async () => {
    const response = await fetch(
      `${lobeConsoleEnv.LOBE_CONSOLE_URL}/api/v1/${lobeConsoleEnv.LOBE_LICENSE}/features`,
      {
        headers: {
          'Content-Type': 'application/json',
        },
        method: 'GET',
      },
    ).then((res) => res.json());

    const data = await response.json();

    console.log(data);
  },
});
