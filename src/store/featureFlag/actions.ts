import { StateCreator } from 'zustand/vanilla';

import { lobeConsoleEnv } from '@/envs/lobeconsole';
import { FeatureFlagStore } from './store';

export interface FeatureFlagAction {
  /**
   * init feature flag
   */
  initFeatureFlags: () => void;
}

export const createFeatureFlagSlice: StateCreator<
  FeatureFlagStore,
  [['zustand/devtools', never]],
  [],
  FeatureFlagAction
> = (set) => ({
  initFeatureFlags: async () => {
    try {
      const response = await fetch(
        `${lobeConsoleEnv.LOBE_CONSOLE_URL}/api/v1/${lobeConsoleEnv.LOBE_LICENSE}/features`,
        {
          headers: {
            'Content-Type': 'application/json',
          },
          method: 'GET',
        },
      );

      if (!response.ok) {
        console.error('Failed to fetch feature flags:', response.statusText);
        return;
      }

      const data = await response.json();

      // 假设 API 返回的数据格式为 { grantFeatures: string[], revokeFeatures: string[] }
      if (data && typeof data === 'object') {
        set({
          grantFeatures: Array.isArray(data.grantFeatures) ? data.grantFeatures : [],
          revokeFeatures: Array.isArray(data.revokeFeatures) ? data.revokeFeatures : [],
        });

        console.log('Feature flags initialized:', data);
      } else {
        console.warn('Invalid feature flags data format:', data);
      }
    } catch (error) {
      console.error('Error initializing feature flags:', error);
      // 在错误情况下，保持默认状态
    }
  },
});
