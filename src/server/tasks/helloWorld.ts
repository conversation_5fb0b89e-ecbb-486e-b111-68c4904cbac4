import { AsyncTask } from '@/types/task';

/**
 * 一个简单的 "Hello World" 任务
 * @param payload - 任务载荷
 */
export const helloWorldTask = async (payload: { message: string }) => {
  console.log('Task "helloWorld" started with payload:', payload);

  const result = `Hello, ${payload.message}! The time is ${new Date().toLocaleTimeString()}.`;
  console.log(result);

  // 模拟一个耗时操作
  await new Promise((resolve) => {
    setTimeout(() => {
      resolve(undefined);
    }, 2000);
  });

  console.log('Task "helloWorld" finished.');

  return { result };
};

const task: AsyncTask = {
  id: 'helloWorld',
  runner: helloWorldTask,
};

export default task;
