import { camelCase } from 'lodash-es';

interface Item {
  [key: string]: any;
  count: number;
  date: string;
}

export const formatRangeCompare = (data: Item[], prevData: Item[]) => {
  const maxLength = Math.max(data.length, prevData.length);
  return Array.from({ length: maxLength }, (_, i) => {
    const prev: any = {};
    if (prevData[i]) {
      Object.entries(prevData[i]).forEach(([key, value]) => {
        prev[camelCase(`prev-${key}`)] = value;
      });
    }
    return {
      ...data[i],
      date: data[i]?.date || prevData[i]?.date,
      ...prev,
    };
  });
};

export default formatRangeCompare;
