import { z } from 'zod';

import { SubscriptionStatus, subscriptionPlan } from '@/const/subscription';
import { AgentModel } from '@/database/models/agent';
import { BudgetModel } from '@/database/models/budget';
import { EmbeddingModel } from '@/database/models/embedding';
import { FileModel } from '@/database/models/file';
import { MessageModel } from '@/database/models/message';
import { SpendModel } from '@/database/models/spend';
import { SubscriptionModel } from '@/database/models/subscription';
import { TopicModel } from '@/database/models/topic';
import { UserModel } from '@/database/models/user';
import { VirtualKeyModel } from '@/database/models/virtualKey';
import { clerkEnv } from '@/envs/clerk';
import { stripeEnv } from '@/envs/stripe';
import { authedProcedure, router } from '@/libs/trpc';
import { clerkServices } from '@/server/services/clerk';
import { UserQuerySchema } from '@/types/endUsers';
import { MessageQuerySchema } from '@/types/message';
import { SortQuerySchema } from '@/types/query';
import { lastMonth } from '@/utils/time';

const userProcedure = authedProcedure.use(async (opts) => {
  return opts.next({
    ctx: {
      agentModel: new AgentModel(),
      budgetModel: new BudgetModel(),
      clerkServices: new clerkServices(),
      embeddingModel: new EmbeddingModel(),
      fileModel: new FileModel(),
      messageModel: new MessageModel(),
      spendModel: new SpendModel(),
      subscriptionModel: new SubscriptionModel(),
      topicModel: new TopicModel(),
      userModel: new UserModel(),
      virtualKeyModel: new VirtualKeyModel(),
    },
  });
});

export const userRouter = router({
  banUser: userProcedure
    .input(
      z.object({
        current: z.boolean().optional(),
        userId: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      if (input.current) {
        return ctx.clerkServices.unbanUser(input.userId);
      } else {
        return ctx.clerkServices.banUser(input.userId);
      }
    }),

  cancelUserSubscription: userProcedure
    .input(
      z.object({
        subscriptionId: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.subscriptionModel.cancelUserSubscriptionById(input.subscriptionId);
    }),

  deleteUser: userProcedure
    .input(
      z.object({
        userId: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      try {
        // 1. 调用 clerkService 删除用户
        await ctx.clerkServices.deleteUser(input.userId);

        // 2. 调用 UserModel 删除用户在 lobe 中的数据
        await ctx.userModel.deleteUserData(input.userId);

        // 3. 调用 VirtualKeyModel 删除用户产生的虚拟key数据
        await ctx.virtualKeyModel.deleteAllUserVirtualKeys(input.userId);

        return { success: true };
      } catch (error) {
        console.error('Failed to delete user:', error);
        throw new Error('删除用户失败');
      }
    }),

  getActiveVsInactiveUsers: userProcedure
    .input(
      z.object({
        range: z.tuple([z.string(), z.string()]),
      }),
    )
    .query(async ({ ctx, input }) => {
      return ctx.userModel.getActiveVsInactiveUsers(input);
    }),

  getFreeVsSubscriptionUsers: userProcedure
    .input(
      z.object({
        range: z.tuple([z.string(), z.string()]),
        type: z.enum(['active', 'effective', 'total']),
      }),
    )
    .query(async ({ ctx, input }) => {
      return ctx.userModel.getFreeVsSubscriptionUsers(input);
    }),

  getRefundableById: userProcedure.input(z.string()).query(async ({ ctx, input }) => {
    const budget = await ctx.budgetModel.getUserBudgetById(input);
    const subscription = await ctx.budgetModel.getBudgeUsageById(budget?.subscriptionBudgetId);
    const subscriptionUsage = subscription?.spend;

    const fileUsage = await ctx.fileModel.countTotalFileSize({ userId: input });
    const embeddingUsage = await ctx.embeddingModel.countTotalEmbeddings({ userId: input });
    const messages = await ctx.messageModel.countTotalMessages({
      role: 'assistant',
      userId: input,
    });
    const messagesWithCustomProvider = await ctx.messageModel.countTotalMessages({
      customProvider: true,
      role: 'assistant',
      userId: input,
    });

    const spendUsage = await ctx.spendModel.getTotalSpend({
      userId: input,
    });

    const subscriptionPlan = await ctx.subscriptionModel.getUserSubscriptionById(input);
    const currentPlan = subscriptionPlan.find((plan) => plan.status === SubscriptionStatus.Active);

    return {
      currentPlan,
      embeddingUsage,
      enable: !messagesWithCustomProvider && !subscriptionUsage && !fileUsage && !embeddingUsage,
      fileUsage,
      messages,
      messagesWithCustomProvider,
      plans: subscriptionPlan,
      spendUsage,
      subscriptionUsage,
    };
  }),

  getUserAgents: userProcedure
    .input(
      z.object({
        params: z
          .object({
            current: z.number().optional(),
            pageSize: z.number().optional(),
            userId: z.string(),
          })
          .optional(),
        sorts: SortQuerySchema.optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      if (!input.params?.userId) {
        throw new Error('userId is required');
      }
      const result = await ctx.agentModel.getUserAgentsList(input.params, input.sorts);
      return { success: true, ...result };
    }),

  getUserBudgetById: userProcedure.input(z.string()).query(async ({ ctx, input }) => {
    const budget = await ctx.budgetModel.getUserBudgetById(input);
    const freeUsage = await ctx.budgetModel.getBudgeUsageById(budget?.freeBudgetId);
    const subscriptionUsage = await ctx.budgetModel.getBudgeUsageById(budget?.subscriptionBudgetId);

    const plan = await ctx.subscriptionModel.getUserCurrentPlanById(input);
    const fileUsage = await ctx.fileModel.countTotalFileSize({ userId: input });
    const embeddingUsage = await ctx.embeddingModel.countTotalEmbeddings({ userId: input });
    const limit = subscriptionPlan[plan].limit;

    return {
      ...budget,
      plan,
      usage: {
        embeddingStorage: { limit: limit.embeddingStorage, spend: embeddingUsage },
        fileStorage: { limit: limit.fileStorage, spend: fileUsage },
        free: freeUsage!,
        subscription: subscriptionUsage,
      },
    };
  }),

  getUserBudgetDetailById: userProcedure.input(z.string()).query(async ({ ctx, input }) => {
    return ctx.budgetModel.getUserBudgetDetailById(input);
  }),

  getUserClerkInfoById: userProcedure.input(z.string()).query(async ({ ctx, input }) => {
    if (!clerkEnv.CHAT_CLERK_SECRET_KEY) return;
    return ctx.clerkServices.getUser(input);
  }),

  getUserInfoById: userProcedure.input(z.string()).query(async ({ ctx, input }) => {
    const info = await ctx.userModel.getUserInfoById(input);

    return {
      ...info,
      stripId: stripeEnv.CHAT_ENABLE_STRIPE
        ? await ctx.subscriptionModel.getUserStripIdById(input)
        : undefined,
    };
  }),

  getUserMessages: userProcedure
    .input(
      z.object({
        params: MessageQuerySchema.optional(),
        sorts: SortQuerySchema.optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      return ctx.messageModel.getMessagesList(input.params, input.sorts);
    }),

  getUserStatisticsById: userProcedure.input(z.string()).query(async ({ ctx, input }) => {
    const userId = input;
    const endDate = lastMonth().format('YYYY-MM-DD');
    return {
      agents: {
        count: await ctx.agentModel.countTotalAgents({ userId }),
        prevCount: await ctx.agentModel.countTotalAgents({ endDate, userId }),
      },
      files: {
        count: await ctx.fileModel.countTotalFiles({ userId }),
        prevCount: await ctx.fileModel.countTotalFiles({ endDate, userId }),
      },
      messages: {
        count: await ctx.messageModel.countTotalMessages({ userId }),
        prevCount: await ctx.messageModel.countTotalMessages({ endDate, userId }),
      },
      topics: {
        count: await ctx.topicModel.getTotalTopics({ userId }),
        prevCount: await ctx.topicModel.getTotalTopics({ endDate, userId }),
      },
    };
  }),

  getUserSubscriptionById: userProcedure.input(z.string()).query(async ({ ctx, input }) => {
    return ctx.subscriptionModel.getUserSubscriptionById(input);
  }),

  getUserTopics: userProcedure
    .input(
      z.object({
        params: z
          .object({
            current: z.number().optional(),
            pageSize: z.number().optional(),
            userId: z.string(),
          })
          .optional(),
        sorts: SortQuerySchema.optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      if (!input.params?.userId) {
        throw new Error('userId is required');
      }
      const result = await ctx.topicModel.getUserTopicsList(input.params, input.sorts);
      return { success: true, ...result };
    }),

  getUserTypes: userProcedure.query(async ({ ctx }) => {
    return ctx.userModel.getUserTypes();
  }),

  getUsers: userProcedure
    .input(
      z.object({
        params: UserQuerySchema.optional(),
        sorts: SortQuerySchema.optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const result = await ctx.userModel.getUserList(input.params, input.sorts);

      if (clerkEnv.CHAT_CLERK_SECRET_KEY) {
        const userIds = result.data.map((item) => item.id);
        const clerkInfo = await ctx.clerkServices.getUserList({
          current: input?.params?.current,
          pageSize: input?.params?.pageSize,
          userIds,
        });
        result.data.forEach((item) => {
          item.clerk = clerkInfo.data.find((info) => info.id === item.id);
        });
      }

      return { success: true, ...result };
    }),

  lockUser: userProcedure
    .input(
      z.object({
        current: z.boolean().optional(),
        userId: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      if (input.current) {
        return ctx.clerkServices.unlockUser(input.userId);
      } else {
        return ctx.clerkServices.lockUser(input.userId);
      }
    }),
});

export type UserRouter = typeof userRouter;
