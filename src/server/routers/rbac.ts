/* eslint-disable sort-keys-fix/sort-keys-fix  */
import { TRPCError } from '@trpc/server';

import {
  ALL_PROXY_MODELS,
  ALL_TEAM_MODELS,
  API_KEY_CONFIG,
  DEFAULT_TEAM_MEMBER_ROLE,
  ERROR_MESSAGES,
  LOG_MESSAGES,
  RBAC_SYSTEM_CREATOR,
  generateApi<PERSON>eyAlias,
  generateTeamId,
} from '@/const/rbac';
import { QuotaModel } from '@/database/models/quota';
import { RbacModel } from '@/database/models/rbac';
import { VirtualKeyModel } from '@/database/models/virtualKey';
import { authedProcedure, router } from '@/libs/trpc';
import { KeyVaultsGateKeeper } from '@/server/modules/KeyVaultsEncrypt';
import type { components } from '@/server/modules/litellm/openapi';
import { liteLLMServices } from '@/server/services/litellm';
import {
  AddRoleUsersSchema,
  CreateRoleSchema,
  DeleteRoleSchema,
  RemoveRoleUsersSchema,
  RoleIdSchema,
  RoleModelsSchema,
  ToggleRoleActiveSchema,
  UpdateRoleSchema,
  VirtualKeyInfo,
} from '@/types/rbac';

// 使用openapi生成的类型
type NewTeamRequest = components['schemas']['NewTeamRequest'];
type GenerateKeyRequest = components['schemas']['GenerateKeyRequest'];
type TeamMemberAddRequest = components['schemas']['TeamMemberAddRequest'];
type TeamMemberDeleteRequest = components['schemas']['TeamMemberDeleteRequest'];

const rbacProcedure = authedProcedure.use(async (opts) => {
  return opts.next({
    ctx: {
      gateKeeper: await KeyVaultsGateKeeper.initWithEnvKey(),
      virtualKeyModel: new VirtualKeyModel(),
      liteLLMService: new liteLLMServices(),
      quotaModel: new QuotaModel(),
      rbacModel: new RbacModel(),
    },
  });
});

export const rbacRouter = router({
  // ================== 角色管理相关接口 ==================
  // 获取系统中所有角色列表
  getAllRoles: rbacProcedure.query(async ({ ctx }) => {
    return ctx.rbacModel.getAllRoles();
  }),

  // 创建角色，同时在 litellm 中新增一个同名的 team 并创建配额
  createRole: rbacProcedure.input(CreateRoleSchema).mutation(async ({ ctx, input }) => {
    try {
      // 1. 创建角色
      const newRole = await ctx.rbacModel.createRole({
        name: input.name,
        displayName: input.displayName,
        description: input.description,
        isActive: input.isActive,
      });

      const teamId = generateTeamId(newRole.id);

      // 2. 在LiteLLM中创建对应的team
      try {
        const teamParams: NewTeamRequest = {
          blocked: !input.isActive,
          metadata: {
            created_by: RBAC_SYSTEM_CREATOR,
            role_id: newRole.id.toString(),
            role_name: input.name,
          } as any,
          team_alias: input.displayName,
          team_id: teamId,
        };

        await ctx.liteLLMService.createTeam(teamParams);
      } catch (litellmError) {
        // 如果LiteLLM team创建失败，记录错误但不回滚角色创建
        console.error(LOG_MESSAGES.LITELLM_TEAM_CREATE_FAILED, litellmError);
      }

      // 3. 创建角色配额记录
      try {
        const quotaData = {
          fileMb: input.quota?.fileMb ?? null,
          roleId: newRole.id.toString(),
          tokenBudget: input.quota?.tokenBudget ?? null,
          vectorCount: input.quota?.vectorCount ?? null,
        };

        await ctx.quotaModel.createRoleQuotas(quotaData);
      } catch (quotaError) {
        // 如果配额创建失败，记录错误但不回滚角色创建
        console.error(LOG_MESSAGES.ROLE_QUOTA_CREATE_FAILED, quotaError);
      }

      return { success: true, data: newRole };
    } catch (error) {
      throw new TRPCError({
        cause: error,
        code: 'INTERNAL_SERVER_ERROR',
        message: ERROR_MESSAGES.ROLE_CREATE_FAILED,
      });
    }
  }),

  // 删除角色，同时删除 litellm 中对应的team
  deleteRole: rbacProcedure.input(DeleteRoleSchema).mutation(async ({ ctx, input }) => {
    try {
      // 检查角色是否可以删除
      const canDelete = await ctx.rbacModel.canDeleteRole(input.roleId);
      if (!canDelete) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: ERROR_MESSAGES.ROLE_DELETE_RESTRICTED,
        });
      }

      // 1. 删除LiteLLM中对应的team
      try {
        const teamId = generateTeamId(input.roleId);
        await ctx.liteLLMService.deleteTeam([teamId]);
      } catch (litellmError) {
        // 记录错误但继续删除角色
        console.error(LOG_MESSAGES.LITELLM_TEAM_DELETE_FAILED, litellmError);
      }

      // 2. 删除角色配额记录
      try {
        await ctx.quotaModel.deleteRoleQuotas(input.roleId.toString());
      } catch (quotaError) {
        // 如果配额删除失败，记录错误但不影响角色删除
        console.error(LOG_MESSAGES.ROLE_QUOTA_DELETE_FAILED, quotaError);
      }

      // 3. 删除角色
      await ctx.rbacModel.deleteRole(input.roleId);

      return { success: true };
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }
      throw new TRPCError({
        cause: error,
        code: 'INTERNAL_SERVER_ERROR',
        message: ERROR_MESSAGES.ROLE_DELETE_FAILED,
      });
    }
  }),

  // 更新角色信息，同时更新 litellm 中的team 和配额
  updateRole: rbacProcedure.input(UpdateRoleSchema).mutation(async ({ ctx, input }) => {
    try {
      const { roleId, quota, ...updateData } = input;

      // 更新 rbac 表中的角色信息
      const updatedRole = await ctx.rbacModel.updateRole(roleId, updateData);

      // 更新角色配额信息
      const quotaData = {
        fileMb: quota?.fileMb ?? null,
        tokenBudget: quota?.tokenBudget ?? null,
        vectorCount: quota?.vectorCount ?? null,
      };
      await ctx.quotaModel.updateRoleQuotas(roleId.toString(), quotaData);

      // 更新 liteLLM 中的 team 状态
      await ctx.liteLLMService.updateTeam({
        team_id: generateTeamId(roleId),
        metadata: {
          created_by: RBAC_SYSTEM_CREATOR,
          role_id: roleId.toString(),
          role_name: updatedRole.name,
        } as any,
        team_alias: updatedRole.displayName,
      });

      return { success: true, data: { ...updatedRole, quota: quotaData } };
    } catch (error) {
      throw new TRPCError({
        cause: error,
        code: 'INTERNAL_SERVER_ERROR',
        message: ERROR_MESSAGES.ROLE_UPDATE_FAILED,
      });
    }
  }),

  // 切换角色的启用状态，同时更新 litellm 中team的budget
  toggleRoleActive: rbacProcedure.input(ToggleRoleActiveSchema).mutation(async ({ ctx, input }) => {
    try {
      const roles = await ctx.rbacModel.getAllRoles();
      const role = roles.find((r) => r.id === input.roleId);

      if (!role) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: ERROR_MESSAGES.ROLE_NOT_FOUND,
        });
      }

      const updatedRole = role.isActive
        ? await ctx.rbacModel.disableRole(input.roleId)
        : await ctx.rbacModel.enableRole(input.roleId);

      // 更新 liteLLM 中的 team 状态
      await ctx.liteLLMService.updateTeam({
        team_id: generateTeamId(input.roleId),
        blocked: !updatedRole.isActive,
      });

      return { success: true, data: updatedRole };
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }
      throw new TRPCError({
        cause: error,
        code: 'INTERNAL_SERVER_ERROR',
        message: ERROR_MESSAGES.ROLE_TOGGLE_FAILED,
      });
    }
  }),

  // ================== 角色用户管理相关接口 ==================
  // 获取角色对应的用户列表
  getRoleUsers: rbacProcedure.input(RoleIdSchema).query(async ({ ctx, input }) => {
    return ctx.rbacModel.getRoleUsers(input.roleId);
  }),

  // 获取角色配额信息
  getRoleQuotas: rbacProcedure.input(RoleIdSchema).query(async ({ ctx, input }) => {
    try {
      const quota = await ctx.quotaModel.getRoleQuotas(input.roleId.toString());
      return quota;
    } catch (error) {
      throw new TRPCError({
        cause: error,
        code: 'INTERNAL_SERVER_ERROR',
        message: ERROR_MESSAGES.QUOTA_GET_FAILED,
      });
    }
  }),

  // 在角色中新增用户
  addRoleUsers: rbacProcedure.input(AddRoleUsersSchema).mutation(async ({ ctx, input }) => {
    try {
      const role = await ctx.rbacModel.getRoleById(input.roleId);

      if (!role) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: ERROR_MESSAGES.ROLE_NOT_FOUND,
        });
      }

      // 获取角色配额信息
      const quota = await ctx.quotaModel.getRoleQuotas(input.roleId.toString());

      // 1. 在角色中新增用户
      await ctx.rbacModel.addRoleUsers(input.roleId, input.userIds, input.expiresAt);

      const teamId = generateTeamId(input.roleId);
      const apiKeys: { userId: string; virtualKeyInfo: VirtualKeyInfo }[] = [];

      // 2. 为每个用户在LiteLLM中添加到team并创建Virtual key
      for (const userId of input.userIds) {
        try {
          // 2.1 添加用户到team
          const teamMemberParams: TeamMemberAddRequest = {
            member: {
              role: DEFAULT_TEAM_MEMBER_ROLE,
              user_id: userId,
            },
            team_id: teamId,
          };
          await ctx.liteLLMService.addTeamUser(teamMemberParams);

          // 2.2 判断角色&用户之前是否已经存在对应的 virtual key
          const apiKey = await ctx.virtualKeyModel.getVirtualKeyByUserAndRole(
            userId,
            input.roleId.toString(),
            ctx.gateKeeper,
          );

          // 2.2.1 如果存在，则更新 virtual key，打开 virtual key 的启用状态
          if (apiKey) {
            await ctx.liteLLMService.updateApiKey({
              key: apiKey.virtualKeyInfo.virtualKeyId,
              team_id: teamId,
              user_id: userId,
              blocked: false,
            });
          } else {
            // 2.2.2 为用户创建Virtual key
            const apiKeyParams: GenerateKeyRequest = {
              key_alias: generateApiKeyAlias(userId, teamId),
              max_budget: quota?.tokenBudget ?? null,
              budget_duration: 'monthly',
              models: [ALL_TEAM_MODELS],
              metadata: {
                created_by: RBAC_SYSTEM_CREATOR,
                role_id: input.roleId.toString(),
              } as any,
              send_invite_email: API_KEY_CONFIG.SEND_INVITE_EMAIL,
              team_id: teamId,
              user_id: userId,
            };
            const apiKeyInfo = await ctx.liteLLMService.createApiKey(apiKeyParams);

            apiKeys.push({
              userId,
              virtualKeyInfo: {
                virtualKeyId: apiKeyInfo.token_id!,
                virtualKey: apiKeyInfo.key,
              },
            });
          }
        } catch (litellmError) {
          console.error(`${LOG_MESSAGES.LITELLM_OPERATIONS_FAILED} ${userId}:`, litellmError);
        }
      }

      // 3. 在 adminDB 中添加 user_id & role_id 与 virtual key 的映射关系
      if (apiKeys.length > 0) {
        const apiKeyDataList = apiKeys.map((apiKey) => ({
          userId: apiKey.userId,
          roleId: input.roleId.toString(),
          virtualKeyInfo: apiKey.virtualKeyInfo,
        }));
        await ctx.virtualKeyModel.batchCreateVirtualKeys(apiKeyDataList, ctx.gateKeeper);
      }

      return {
        success: true,
        data: apiKeys,
      };
    } catch (error) {
      throw new TRPCError({
        cause: error,
        code: 'INTERNAL_SERVER_ERROR',
        message: ERROR_MESSAGES.USER_ADD_TO_ROLE_FAILED,
      });
    }
  }),

  // 在角色中删除用户
  removeRoleUsers: rbacProcedure.input(RemoveRoleUsersSchema).mutation(async ({ ctx, input }) => {
    try {
      // 1. 从角色中删除用户
      await ctx.rbacModel.removeRoleUsers(input.roleId, input.userIds);

      const teamId = generateTeamId(input.roleId);

      // 2. 操作 liteLLM 中的 team 状态和 virtual key 状态
      for (const userId of input.userIds) {
        try {
          // 2.1 从 team 中删除用户
          const teamMemberParams: TeamMemberDeleteRequest = {
            team_id: teamId,
            user_id: userId,
          };
          await ctx.liteLLMService.deleteTeamUser(teamMemberParams);

          // 2.2 查询取消关联的角色的 virtual key，如果存在，则更新 virtual key，关闭 virtual key 的启用状态
          const apiKeyDetails = await ctx.virtualKeyModel.getVirtualKeyByUserAndRole(
            userId,
            input.roleId.toString(),
            ctx.gateKeeper,
          );

          if (apiKeyDetails) {
            await ctx.liteLLMService.updateApiKey({
              key: apiKeyDetails.virtualKeyInfo.virtualKeyId,
              team_id: teamId,
              user_id: userId,
              blocked: true,
            });
          }
        } catch (litellmError) {
          console.error(`${LOG_MESSAGES.LITELLM_OPERATIONS_FAILED} ${userId}:`, litellmError);
        }
      }

      return { success: true };
    } catch (error) {
      throw new TRPCError({
        cause: error,
        code: 'INTERNAL_SERVER_ERROR',
        message: ERROR_MESSAGES.USER_REMOVE_FROM_ROLE_FAILED,
      });
    }
  }),

  // ================== 角色模型权限相关操作接口 ==================
  // 获取角色的模型权限列表
  getRoleModels: rbacProcedure.input(RoleIdSchema).query(async ({ ctx, input }) => {
    try {
      const teamId = generateTeamId(input.roleId);

      const teamInfo = await ctx.liteLLMService.getTeamInfo(teamId);

      return { success: true, data: teamInfo.team_info.models };
    } catch (error) {
      throw new TRPCError({
        cause: error,
        code: 'INTERNAL_SERVER_ERROR',
        message: ERROR_MESSAGES.MODEL_GET_FAILED,
      });
    }
  }),

  // 为角色添加模型权限
  addRoleModels: rbacProcedure.input(RoleModelsSchema).mutation(async ({ ctx, input }) => {
    try {
      const teamId = generateTeamId(input.roleId);

      // 获取角色原有的模型列表
      const originalTeamInfo = await ctx.liteLLMService.getTeamInfo(teamId);

      /**
       * 这里不使用 litellm 的 addTeamModel 接口
       * 是因为 add 接口是增量添加的接口，当我们创建一个团队但没有给它设置模型权限列表时（空或者空数组）
       * 它默认会添加一个 'all-proxy-models' 的模型权限，这个模型权限会覆盖掉我们设置的模型权限列表
       * 如果我们使用 add 接口，那么会出现两个模型权限，一个是 'all-proxy-models'，一个是我们设置的模型权限列表
       * 所以在这边做了个手动合并并过滤的逻辑，过滤掉 'all-proxy-models' 这个模型权限
       * 然后使用 updateTeam 接口，直接设置模型权限列表，这样就不会出现多余的模型权限
       */
      const newModels = [
        ...((originalTeamInfo.team_info.models as string[]) || []),
        ...input.models,
      ].filter((model) => model !== ALL_PROXY_MODELS);

      const updatedTeam = await ctx.liteLLMService.updateTeam({
        team_id: teamId,
        models: newModels,
      });

      // 返回更新后的模型列表
      return { success: true, data: updatedTeam.models };
    } catch (error) {
      throw new TRPCError({
        cause: error,
        code: 'INTERNAL_SERVER_ERROR',
        message: ERROR_MESSAGES.MODEL_ADD_FAILED,
      });
    }
  }),

  // 移除角色的模型权限
  removeRoleModels: rbacProcedure.input(RoleModelsSchema).mutation(async ({ ctx, input }) => {
    try {
      const teamId = generateTeamId(input.roleId);

      await ctx.liteLLMService.deleteTeamModel({
        team_id: teamId,
        models: input.models,
      });

      // 返回更新后的模型列表
      const teamInfo = await ctx.liteLLMService.getTeamInfo(teamId);
      return { success: true, data: teamInfo.team_info.models };
    } catch (error) {
      throw new TRPCError({
        cause: error,
        code: 'INTERNAL_SERVER_ERROR',
        message: ERROR_MESSAGES.MODEL_REMOVE_FAILED,
      });
    }
  }),
});

export type RbacRouter = typeof rbacRouter;
