import { TRPCError } from '@trpc/server';
import { z } from 'zod';

import { BudgetModel } from '@/database/models/budget';
import { UserModel } from '@/database/models/user';
import { authedProcedure, router } from '@/libs/trpc';
import { liteLLMServices } from '@/server/services/litellm';
import { LiteLLMKeyUpdateParamsSchema } from '@/types/litellm';

const budgetProcedure = authedProcedure.use(async (opts) => {
  return opts.next({
    ctx: {
      budgetModel: new BudgetModel(),
      liteLLMService: new liteLLMServices(),
      userModel: new UserModel(),
    },
  });
});

export const budgetRouter = router({
  updateBudgetKey: budgetProcedure
    .input(
      z.object({
        token: z.string(),
        userId: z.string(),
        value: LiteLLMKeyUpdateParamsSchema,
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const userApiKey = await ctx.userModel.getUserApiKey(input.userId, input.token);

      if (!userApiKey) return;

      try {
        await ctx.liteLLMService.updateBudgetKey(userApiKey, {
          budget_duration: input.value.budgetDuration,
          max_budget: input.value.maxBudget,
          spend: input.value.spend,
        });
      } catch (e) {
        if ('code' in (e as any)) {
          throw new TRPCError({
            code: 'SERVICE_UNAVAILABLE',
            message: JSON.stringify(e),
          });
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: JSON.stringify(e),
        });
      }

      // TODO: 等 litellm 自身的 Update key 完善了，再去掉这部分直接改 db 的实现
      await ctx.budgetModel.updateBudgetKey(input.token, {
        budgetResetAt: input.value.budgetResetAt,
        expires: input.value.expires,
      });
    }),
});

export type ChannelRouter = typeof budgetRouter;
