import { z } from 'zod';

import { DisplayType } from '@/components/DateRangeCompare/type';
import { BudgetModel } from '@/database/models/budget';
import { OrderModel } from '@/database/models/order';
import { SubscriptionModel } from '@/database/models/subscription';
import { authedProcedure, router } from '@/libs/trpc';
import formatRangeCompare from '@/server/utils/formatRangeCompare';
import { SortQuerySchema } from '@/types/query';
import { BudgetQuerySchema, OrderQuerySchema, Plans } from '@/types/subscription';
import { lastMonth } from '@/utils/time';

const subscriptProcedure = authedProcedure.use(async (opts) => {
  return opts.next({
    ctx: {
      budgetModel: new BudgetModel(),
      orderModel: new OrderModel(),
      subscriptionModel: new SubscriptionModel(),
    },
  });
});

export const subscriptRouter = router({
  getBudgets: subscriptProcedure
    .input(
      z.object({
        params: BudgetQuerySchema.optional(),
        sorts: SortQuerySchema.optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const result = await ctx.budgetModel.getBudgets(input.params, input.sorts);
      return { success: true, ...result };
    }),

  getFutureCancelUsers: subscriptProcedure
    .input(
      z
        .object({
          current: z.number().optional(),
          pageSize: z.number().optional(),
        })
        .optional(),
    )
    .query(async ({ ctx, input }) => {
      return await ctx.subscriptionModel.getFutureCancelUsers(input);
    }),

  getOrders: subscriptProcedure
    .input(
      z.object({
        params: OrderQuerySchema.optional(),
        sorts: SortQuerySchema.optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const result = await ctx.orderModel.getOrders(input.params, input.sorts);
      return { success: true, ...result };
    }),

  getRangeCancel: subscriptProcedure
    .input(
      z.object({
        display: z.nativeEnum(DisplayType),
        prevRange: z.tuple([z.string(), z.string()]).optional(),
        range: z.tuple([z.string(), z.string()]),
      }),
    )
    .query(async ({ ctx, input }) => {
      const data = await ctx.subscriptionModel.getRangeCancel(input);

      let prevData;
      if (input.prevRange)
        prevData = await ctx.subscriptionModel.getRangeCancel({
          display: input.display,
          range: input.prevRange,
        });
      const formatedData = prevData ? formatRangeCompare(data, prevData) : data;
      const currentData = formatedData[data.length - 1];

      return {
        current: currentData?.count || 0,
        data: formatedData,
        prevCurrent: currentData?.prevCount || 0,
        prevSum: prevData?.reduce((acc, cur) => acc + (cur?.count || 0), 0),
        sum: data.reduce((acc, cur) => acc + (cur?.count || 0), 0),
      };
    }),

  getRecentSubscriptUsers: subscriptProcedure
    .input(
      z
        .object({
          current: z.number().optional(),
          pageSize: z.number().optional(),
        })
        .optional(),
    )
    .query(async ({ ctx, input }) => {
      return await ctx.subscriptionModel.getRecentSubscriptUsers(input);
    }),

  getTotalFutureCancelUsers: subscriptProcedure.query(async ({ ctx }) => {
    return await ctx.subscriptionModel.getTotalFutureCancelUsers();
  }),

  getTotalModeCount: subscriptProcedure.query(async ({ ctx }) => {
    return await ctx.subscriptionModel.getTotalModeCount();
  }),

  getTotalPlanCount: subscriptProcedure.query(async ({ ctx }) => {
    return await ctx.subscriptionModel.getTotalPlanCount();
  }),

  getTotalRecentSubscriptUsers: subscriptProcedure.query(async ({ ctx }) => {
    return await ctx.subscriptionModel.getTotalRecentSubscriptUsers();
  }),

  getTotalRecurringCount: subscriptProcedure.query(async ({ ctx }) => {
    return await ctx.subscriptionModel.getTotalRecurringCount();
  }),

  getTotalStatusCount: subscriptProcedure.query(async ({ ctx }) => {
    return await ctx.subscriptionModel.getTotalStatusCount();
  }),

  getTotalSubscriptions: subscriptProcedure
    .input(z.nativeEnum(Plans).optional())
    .query(async ({ ctx, input }) => {
      const plan = input;
      return {
        count: await ctx.subscriptionModel.getTotalSubscriptions({
          plan,
        }),
        prevCount: await ctx.subscriptionModel.getTotalSubscriptions({
          endDate: lastMonth().format('YYYY-MM-DD'),
          plan,
        }),
      };
    }),
});

export type SubscriptRouter = typeof subscriptRouter;
