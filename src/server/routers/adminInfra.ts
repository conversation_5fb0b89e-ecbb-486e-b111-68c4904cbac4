/* eslint-disable sort-keys-fix/sort-keys-fix */
import { z } from 'zod';

import { LOBE_DEFAULT_MODEL_LIST } from '@/config/aiModels';
import { NewAiModelItem } from '@/database/instance/lobechatDB/schemas/aiInfra';
import { AdminInfraModel } from '@/database/models/adminInfra';
import { AggregatedModelModel } from '@/database/models/aggregatedModel';
import { SpendModel } from '@/database/models/spend';
import { authedProcedure, router } from '@/libs/trpc';
import { liteLLMServices } from '@/server/services/litellm';
import {
  AdminModelSelectItem,
  AdminProviderSelectItem,
  AiProviderQuerySchema,
  AiProviderWithModels,
  ApiResponse,
  ClearProviderModelsResult,
  CreateAiModelSchema,
  CreateAiProviderSchema,
  DeleteInfraModelInputSchema,
  GetProviderUsageStatsInputSchema,
  ProviderUsageStatsResult,
  ToggleInfraModelStatusInputSchema,
  ToggleInfraProviderStatusInputSchema,
  UpdateInfraModelInputSchema,
  UpdateInfraProviderInputSchema,
} from '@/types/adminInfra';

import { KeyVaultsGateKeeper } from '../modules/KeyVaultsEncrypt';

const aiInfraProcedure = authedProcedure.use(async (opts) => {
  return opts.next({
    ctx: {
      aggregatedModelModel: new AggregatedModelModel(),
      adminInfraModel: new AdminInfraModel(),
      gateKeeper: await KeyVaultsGateKeeper.initWithEnvKey(),
      liteLLMServices: new liteLLMServices(),
      spendModel: new SpendModel(),
    },
  });
});

export const adminInfraRouter = router({
  // ==================== Provider 相关接口 ====================
  /**
   * 从 Admin 数据库中获取所有的 provider 列表
   */
  getAllInfraProviders: aiInfraProcedure
    .input(AiProviderQuerySchema.optional())
    .query(async ({ ctx }): Promise<ApiResponse<AdminProviderSelectItem[]>> => {
      const data = await ctx.adminInfraModel.getAllInfraProviders(ctx.gateKeeper);

      return { success: true, data };
    }),

  /**
   * 根据 Provider ID 查询 provider 详情
   */
  getInfraProviderById: aiInfraProcedure
    .input(z.string())
    .query(async ({ ctx, input }): Promise<ApiResponse<AiProviderWithModels | null>> => {
      // 1. 获取渠道基本信息
      const provider = await ctx.adminInfraModel.getInfraProviderById(input, ctx.gateKeeper);

      if (!provider) {
        return { success: true, data: null };
      }

      // 2. 获取渠道对应的模型列表
      const models = await ctx.adminInfraModel.getInfraModelsByProviderId(input);

      const data = {
        ...provider,
        models,
      };

      return { success: true, data };
    }),

  /**
   * 创建渠道信息
   */
  createInfraProvider: aiInfraProcedure
    .input(CreateAiProviderSchema)
    .mutation(async ({ ctx, input }): Promise<ApiResponse<AdminProviderSelectItem>> => {
      try {
        // 1. 创建 LiteLLM 凭证
        const credentialName = `${input.id}_credential`;
        const credentialValues = {
          api_key: input.keyVaults.apiKey, // 从 keyVaults 获取 API Key
          api_base: input.keyVaults.baseURL || null, // 从 keyVaults 获取 API Base
        };
        const description = `${input.name || input.id} - Channel ID: ${input.id}`;

        const credential = await ctx.liteLLMServices.createCredential({
          credentialName,
          credentialValues,
          description,
        });

        // 2. 创建渠道信息，包含凭证名称
        const providerData = {
          ...input,
          litellmCredential: credential.credential_name,
        };

        const data = await ctx.adminInfraModel.createInfraProvider(providerData, ctx.gateKeeper);

        return { success: true, data };
      } catch (error) {
        console.error('创建渠道失败:', error);
        return {
          success: false,
          error: `创建渠道失败: ${error instanceof Error ? error.message : '未知错误'}`,
        };
      }
    }),

  /**
   * 更新渠道信息
   */
  updateInfraProvider: aiInfraProcedure
    .input(UpdateInfraProviderInputSchema)
    .mutation(async ({ ctx, input }): Promise<ApiResponse<AdminProviderSelectItem | null>> => {
      const { providerId, data } = input;

      try {
        const { litellmCredential, keyVaults } = data;

        // 1. 更新 LiteLLM 凭证
        await ctx.liteLLMServices.updateCredential(litellmCredential, {
          credentialName: litellmCredential,
          credentialValues: {
            api_base: keyVaults.baseURL || null,
            api_key: keyVaults.apiKey,
          },
        });

        // 2. 更新渠道信息
        const result = await ctx.adminInfraModel.updateInfraProvider(
          providerId,
          data,
          ctx.gateKeeper,
        );

        return { success: true, data: result };
      } catch (error) {
        console.error('更新渠道失败:', error);
        return {
          success: false,
          error: `更新渠道失败: ${error instanceof Error ? error.message : '未知错误'}`,
        };
      }
    }),

  /**
   * 删除渠道信息
   */
  deleteInfraProvider: aiInfraProcedure
    .input(z.string())
    .mutation(async ({ ctx, input }): Promise<ApiResponse<boolean>> => {
      const providerId = input;

      try {
        // 1. 获取渠道信息以获取凭证 ID
        const provider = await ctx.adminInfraModel.getInfraProviderById(providerId, ctx.gateKeeper);

        if (!provider) {
          return { success: false, error: '渠道不存在' };
        }

        // 2. 根据渠道 ID 从聚合模型关联表中查询对应的 LiteLLM 模型列表，并删除
        const litellmModelIds =
          await ctx.adminInfraModel.getLitellmModelIdsByInfraProviderId(providerId);

        // 删除 LiteLLM 中的模型
        for (const litellmModelId of litellmModelIds) {
          await ctx.liteLLMServices.deleteModel(litellmModelId);
        }

        // 3. 删除 LiteLLM 凭证
        await ctx.liteLLMServices.deleteCredential(provider.litellmCredential);

        // 4. 删除渠道（会级联删除关联的模型和关联关系）
        const result = await ctx.adminInfraModel.deleteInfraProvider(providerId);

        return { success: true, data: result };
      } catch (error) {
        console.error('删除渠道失败:', error);
        return {
          success: false,
          error: `删除渠道失败: ${error instanceof Error ? error.message : '未知错误'}`,
        };
      }
    }),

  /**
   * 切换渠道的启用状态
   */
  toggleInfraProviderStatus: aiInfraProcedure
    .input(ToggleInfraProviderStatusInputSchema)
    .mutation(async ({ ctx, input }): Promise<ApiResponse<AdminProviderSelectItem | null>> => {
      const { providerId, enabled } = input;

      const result = await ctx.adminInfraModel.toggleInfraProviderStatus(providerId, enabled);

      return { success: true, data: result };
    }),

  // ==================== Model 相关接口 ====================
  /**
   * 根据 Provider ID 从 models 中查询所有的模型
   */
  getInfraModelsByProviderId: aiInfraProcedure
    .input(z.string())
    .query(async ({ ctx, input }): Promise<ApiResponse<AdminModelSelectItem[]>> => {
      const data = await ctx.adminInfraModel.getInfraModelsByProviderId(input);

      return { success: true, data };
    }),

  /**
   * 创建模型信息
   */
  createInfraModel: aiInfraProcedure
    .input(CreateAiModelSchema)
    .mutation(async ({ ctx, input }): Promise<ApiResponse<AdminModelSelectItem>> => {
      try {
        // 1. 前置校验：检查 providerId + id 的唯一性
        const existingModel = await ctx.adminInfraModel.getInfraModelById(
          input.providerId,
          input.id,
        );

        if (existingModel) {
          return {
            success: false,
            error: `模型 "${input.id}" 在渠道 "${input.providerId}" 下已存在，请使用不同的模型 ID`,
          };
        }

        // 2. 创建模型
        const data = await ctx.adminInfraModel.createInfraModel(input);

        return { success: true, data };
      } catch (error) {
        console.error('创建模型失败:', error);
        return {
          success: false,
          error: `创建模型失败: ${error instanceof Error ? error.message : '未知错误'}`,
        };
      }
    }),

  /**
   * 更新模型信息
   */
  updateInfraModel: aiInfraProcedure
    .input(UpdateInfraModelInputSchema)
    .mutation(async ({ ctx, input }): Promise<ApiResponse<AdminModelSelectItem | null>> => {
      const { providerId, modelId, data } = input;

      const result = await ctx.adminInfraModel.updateInfraModel(providerId, modelId, data);

      return { success: true, data: result };
    }),

  /**
   * 删除模型
   */
  deleteInfraModel: aiInfraProcedure
    .input(DeleteInfraModelInputSchema)
    .mutation(async ({ ctx, input }): Promise<ApiResponse<boolean>> => {
      const { providerId, modelId } = input;

      try {
        // 1. 根据模型ID从聚合模型关联表中查询对应的 LiteLLM 模型信息，并删除
        // 需要先获取模型信息以获取 providerId
        const model = await ctx.adminInfraModel.getInfraModelById(providerId, modelId);

        if (!model) {
          return { success: false, error: `模型 ${modelId} 不存在` };
        }

        // 获取该模型关联的所有 LiteLLM 模型ID
        const litellmModelIds = await ctx.adminInfraModel.getLitellmModelIdsByInfraProviderAndModel(
          providerId,
          modelId,
        );

        // 删除 LiteLLM 中的模型
        for (const litellmModelId of litellmModelIds) {
          try {
            await ctx.liteLLMServices.deleteModel(litellmModelId);
          } catch (error) {
            console.warn(`删除 LiteLLM 模型 ${litellmModelId} 失败:`, error);
          }
        }

        // 2. 删除模型（会级联删除关联关系）
        const result = await ctx.adminInfraModel.deleteInfraModel(providerId, modelId);

        return { success: true, data: result };
      } catch (error) {
        console.error('删除模型失败:', error);
        return {
          success: false,
          error: `删除模型失败: ${error instanceof Error ? error.message : '未知错误'}`,
        };
      }
    }),

  /**
   * 清空/重置渠道下模型列表
   */
  clearProviderModels: aiInfraProcedure
    .input(z.string())
    .mutation(async ({ ctx, input }): Promise<ApiResponse<ClearProviderModelsResult>> => {
      const providerId = input;

      try {
        // 1. 读取系统中已经加入的模型列表，并删除
        const existingModels = await ctx.adminInfraModel.getInfraModelsByProviderId(providerId);

        // 2. 读取渠道ID和模型ID从聚合模型关联表中读取对应的 LiteLLM 模型列表，删掉对应的模型
        for (const model of existingModels) {
          const litellmModelIds =
            await ctx.adminInfraModel.getLitellmModelIdsByInfraProviderAndModel(
              providerId,
              model.id,
            );

          // 删除 LiteLLM 中的模型
          for (const litellmModelId of litellmModelIds) {
            await ctx.liteLLMServices.deleteModel(litellmModelId);
          }
        }

        // 3. 删除渠道下的所有模型（会级联删除关联关系）
        const deletedCount = await ctx.adminInfraModel.deleteAllModelsByProviderId(providerId);

        const data = {
          deletedCount,
          success: true,
        };

        return { success: true, data };
      } catch (error) {
        throw new Error(`清空模型失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    }),

  /**
   * 获取渠道下的模型列表（同步模型）
   */
  syncProviderModels: aiInfraProcedure
    .input(z.string())
    .mutation(async ({ ctx, input: providerId }): Promise<ApiResponse<void>> => {
      try {
        // 1. 获取渠道信息
        const provider = await ctx.adminInfraModel.getInfraProviderById(providerId, ctx.gateKeeper);

        if (!provider) {
          return { success: true };
        }

        const sdkType = provider.settings?.sdkType;

        // 2. 从 LOBE_DEFAULT_MODEL_LIST 获取对应渠道的模型信息
        const availableModels = LOBE_DEFAULT_MODEL_LIST.filter(
          (model) => model.providerId === sdkType,
        );

        if (availableModels.length === 0) {
          return { success: true };
        }

        // 3. 获取已存在的模型
        const existingModels = await ctx.adminInfraModel.getInfraModelsByProviderId(providerId);
        const existingModelIds = new Set(existingModels.map((model) => model.id));

        // 4. 过滤掉已添加的模型
        const modelsToAdd = availableModels.filter((model) => !existingModelIds.has(model.id));

        if (modelsToAdd.length === 0) {
          return { success: true };
        }

        // 5. 将模型信息转换为数据库格式
        const newModels: NewAiModelItem[] = modelsToAdd.map((model) => ({
          abilities: model.abilities || {},
          config: model.config || null,
          contextWindowTokens: model.contextWindowTokens,
          description: model.description,
          displayName: model.displayName || model.id,
          enabled: model.enabled || true,
          id: model.id,
          organization: model.organization,
          parameters: {},
          pricing: model.pricing || null,
          providerId: providerId,
          releasedAt: model.releasedAt,
          sort: 0,
          source: 'builtin',
          type: model.type || 'chat',
        }));

        // 6. 批量创建模型
        await ctx.adminInfraModel.batchCreateInfraModels(newModels);

        return { success: true };
      } catch {
        return { success: false };
      }
    }),

  /**
   * 切换模型的激活状态
   */
  toggleInfraModelStatus: aiInfraProcedure
    .input(ToggleInfraModelStatusInputSchema)
    .mutation(async ({ ctx, input }): Promise<ApiResponse<AdminModelSelectItem | null>> => {
      const { providerId, modelId, enabled } = input;
      const result = await ctx.adminInfraModel.toggleInfraModelStatus(providerId, modelId, enabled);
      return { success: true, data: result };
    }),

  /**
   * 根据渠道ID和时间范围汇总模型使用记录
   */
  getProviderUsageStats: aiInfraProcedure
    .input(GetProviderUsageStatsInputSchema)
    .query(async ({ ctx, input }): Promise<ApiResponse<ProviderUsageStatsResult>> => {
      const { providerId, startDate, endDate, range } = input;

      try {
        // 1. 获取该渠道下所有关联的 LiteLLM 模型ID
        const litellmModelIds =
          await ctx.adminInfraModel.getLitellmModelIdsByInfraProviderId(providerId);

        if (litellmModelIds.length === 0) {
          return {
            success: true,
            data: {
              modelStats: [],
              totalCalls: 0,
              totalInputTokens: 0,
              totalOutputTokens: 0,
              totalSpend: 0,
              totalTokens: 0,
            },
          };
        }

        // 2. 使用 SpendModel 获取使用统计
        const usageStats = await ctx.spendModel.getSpendByModelIds({
          modelIds: litellmModelIds,
          startDate,
          endDate,
          range,
        });

        return { success: true, data: usageStats };
      } catch (error) {
        console.error('获取渠道使用统计失败:', error);
        return {
          success: false,
          error: `获取渠道使用统计失败: ${error instanceof Error ? error.message : '未知错误'}`,
        };
      }
    }),
});

export type AdminInfraRouter = typeof adminInfraRouter;
