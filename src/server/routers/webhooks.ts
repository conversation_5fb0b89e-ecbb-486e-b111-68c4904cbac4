import { z } from 'zod';

import { authedProcedure, router } from '@/libs/trpc';
import { VercelServices } from '@/server/services/vercel';

const webhooksProcedure = authedProcedure.use(async (opts) => {
  return opts.next({
    ctx: {
      vercelServices: new VercelServices(),
    },
  });
});

export const webhooksRouter = router({
  getConfig: webhooksProcedure.query(async ({ ctx }) => {
    return ctx.vercelServices.getEdgeConfig('webhooks');
  }),
  updateConfig: webhooksProcedure
    .input(
      z.object({
        enabled: z.boolean(),
        lang: z.string(),
        time: z.string(),
        webhookUrl: z.string(),
      }),
    )
    .query(async ({ ctx, input }) => {
      return ctx.vercelServices.updateEdgeConfig('webhooks', input);
    }),
});

export type WebhooksRouter = typeof webhooksRouter;
