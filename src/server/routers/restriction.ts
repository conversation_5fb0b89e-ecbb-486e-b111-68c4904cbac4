import type { User } from '@clerk/nextjs/server';
import pMap from 'p-map';
import { z } from 'zod';

import { genUsername } from '@/database/utils/genUsername';
import { authedProcedure, router } from '@/libs/trpc';
import { clerkServices } from '@/server/services/clerk';
import { SortQuerySchema } from '@/types/query';
import { AllowlistQuerySchema, BlocklistQuerySchema } from '@/types/restriction';

const restrictionProcedure = authedProcedure.use(async (opts) => {
  return opts.next({
    ctx: {
      clerkServices: new clerkServices(),
    },
  });
});

export const restrictionRouter = router({
  batchCreateAllowlistIdentifier: restrictionProcedure
    .input(z.array(z.string()))
    .query(async ({ ctx, input }) => {
      return pMap(input, (email) => ctx.clerkServices.createAllowlistIdentifier(email), {
        concurrency: 5,
      });
    }),

  batchCreateBlocklistIdentifier: restrictionProcedure
    .input(z.array(z.string()))
    .query(async ({ ctx, input }) => {
      return pMap(input, (email) => ctx.clerkServices.createBlocklistIdentifier(email), {
        concurrency: 5,
      });
    }),

  createAllowlistIdentifier: restrictionProcedure
    .input(z.string())
    .query(async ({ ctx, input }) => {
      return ctx.clerkServices.createAllowlistIdentifier(input);
    }),

  createBlocklistIdentifier: restrictionProcedure
    .input(z.string())
    .query(async ({ ctx, input }) => {
      return ctx.clerkServices.createBlocklistIdentifier(input);
    }),

  deleteAllowlistIdentifier: restrictionProcedure
    .input(z.string())
    .query(async ({ ctx, input }) => {
      return ctx.clerkServices.deleteAllowlistIdentifier(input);
    }),

  deleteBlocklistIdentifier: restrictionProcedure
    .input(z.string())
    .query(async ({ ctx, input }) => {
      return ctx.clerkServices.deleteBlocklistIdentifier(input);
    }),

  getAllowlist: restrictionProcedure
    .input(
      z.object({
        params: AllowlistQuerySchema.optional(),
        sorts: SortQuerySchema.optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const result = await ctx.clerkServices.getAllowlist(input);
      const emailAddress = result.data.map((item) => item.identifier);

      let users: {
        data: User[];
      } = {
        data: [],
      };

      if (emailAddress.length > 0)
        users = await ctx.clerkServices.getUserList({
          emailAddress,
          ...input,
        });

      if (users.data.length === 0) return result;

      return {
        data: result.data.map((item) => {
          const user = users.data.find((user) => {
            const emails = user.emailAddresses.map((email) => email.emailAddress.toLowerCase());
            return emails.includes(item.identifier.toLowerCase());
          });
          return {
            ...item,
            user: user
              ? {
                  avatar: user.imageUrl,
                  id: user.id,
                  username: genUsername(user),
                }
              : undefined,
          };
        }),
        total: result.total,
      };
    }),

  getBlocklist: restrictionProcedure
    .input(
      z.object({
        params: BlocklistQuerySchema.optional(),
        sorts: SortQuerySchema.optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const result = await ctx.clerkServices.getBlocklist(input);
      const emailAddress = result.data.map((item) => item.identifier);

      let users: {
        data: User[];
      } = {
        data: [],
      };

      if (emailAddress.length > 0)
        users = await ctx.clerkServices.getUserList({
          emailAddress,
          ...input,
        });

      if (users.data.length === 0) return result;

      return {
        data: result.data.map((item) => {
          const user = users?.data.find((user) => {
            const emails = user.emailAddresses.map((email) => email.emailAddress.toLowerCase());
            return emails.includes(item.identifier.toLowerCase());
          });
          return {
            ...item,
            user: user
              ? {
                  avatar: user.imageUrl,
                  id: user.id,
                  username: genUsername(user),
                }
              : undefined,
          };
        }),
        total: result.total,
      };
    }),

  getRestrictionSettings: restrictionProcedure.query(async ({ ctx }) => {
    return ctx.clerkServices.getRestrictionSettings();
  }),

  getRestrictionTypes: restrictionProcedure.query(async ({ ctx }) => {
    const allowlist = await ctx.clerkServices.getAllowlist();
    const blocklist = await ctx.clerkServices.getBlocklist();

    return {
      allowlist: allowlist.total,
      blocklist: blocklist.total,
    };
  }),

  updateRestrictionSettings: restrictionProcedure
    .input(z.object({ allowlist: z.boolean().optional(), blocklist: z.boolean().optional() }))
    .query(async ({ ctx, input }) => {
      return ctx.clerkServices.updateRestrictionSettings(input);
    }),
});

export type RestrictionRouter = typeof restrictionRouter;
