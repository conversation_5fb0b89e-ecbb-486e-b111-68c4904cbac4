import { z } from 'zod';

import { FileModel } from '@/database/models/file';
import { authedProcedure, router } from '@/libs/trpc';
import { FileQuerySchema } from '@/types/file';
import { SortQuerySchema } from '@/types/query';

const fileProcedure = authedProcedure.use(async (opts) => {
  return opts.next({
    ctx: {
      fileModel: new FileModel(),
    },
  });
});

export const fileRouter = router({
  getFilesList: fileProcedure
    .input(
      z.object({
        params: FileQuerySchema.optional(),
        sorts: SortQuerySchema.optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const result = await ctx.fileModel.getFilesList(input.params, input.sorts);

      return {
        success: true,
        ...result,
      };
    }),
});
