import { router } from '@/libs/trpc';

import { adminInfraRouter } from './adminInfra';
import { aggregatedModelRouter } from './aggregatedModel';
import { budgetRouter } from './budget';
import { channelRouter } from './channel';
import { fileRouter } from './file';
import { marketRouter } from './market';
import { migrationRouter } from './migration';
import { overviewRouter } from './overview';
import { rbacRouter } from './rbac';
import { restrictionRouter } from './restriction';
import { subscriptRouter } from './subscript';
import { userRouter } from './user';
import { webhooksRouter } from './webhooks';

export const appRouter = router({
  adminInfra: adminInfraRouter,
  aggregatedModel: aggregatedModelRouter,
  budget: budgetRouter,
  channel: channelRouter,
  file: fileRouter,
  market: marketRouter,
  migration: migrationRouter,
  overview: overviewRouter,
  rbac: rbacRouter,
  restriction: restrictionRouter,
  subscript: subscriptRouter,
  user: userRouter,
  webhooks: webhooksRouter,
});

export type AppRouter = typeof appRouter;
