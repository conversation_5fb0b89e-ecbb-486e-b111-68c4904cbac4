import { z } from 'zod';

import { DisplayType } from '@/components/DateRangeCompare/type';
import { AgentModel } from '@/database/models/agent';
import { DistributionModel } from '@/database/models/distribution';
import { MessageModel } from '@/database/models/message';
import { RankModel } from '@/database/models/rank';
import { SpendModel } from '@/database/models/spend';
import { SubscriptionModel } from '@/database/models/subscription';
import { TopicModel } from '@/database/models/topic';
import { UserModel } from '@/database/models/user';
import { authedProcedure, router } from '@/libs/trpc';
import formatRangeCompare from '@/server/utils/formatRangeCompare';
import { lastMonth } from '@/utils/time';

const overviewProcedure = authedProcedure.use(async (opts) => {
  return opts.next({
    ctx: {
      agentModel: new AgentModel(),
      distributionModel: new DistributionModel(),
      messageModel: new MessageModel(),
      rankModel: new RankModel(),
      spendModel: new SpendModel(),
      subscriptionModel: new SubscriptionModel(),
      topicModel: new TopicModel(),
      userModel: new UserModel(),
    },
  });
});

export const overviewRouter = router({
  countTotalAgents: overviewProcedure.query(async ({ ctx }) => {
    return {
      count: await ctx.agentModel.countTotalAgents(),
      prevCount: await ctx.agentModel.countTotalAgents({
        endDate: lastMonth().format('YYYY-MM-DD'),
      }),
    };
  }),

  countTotalMessages: overviewProcedure.query(async ({ ctx }) => {
    return {
      count: await ctx.messageModel.countTotalMessages(),
      prevCount: await ctx.messageModel.countTotalMessages({
        endDate: lastMonth().format('YYYY-MM-DD'),
      }),
    };
  }),

  getAgentDistribution: overviewProcedure
    .input(
      z.object({
        range: z.tuple([z.string(), z.string()]),
      }),
    )
    .query(async ({ ctx, input }) => {
      const freeUsers = await ctx.userModel.getFreeUsers();
      const subscriptionUsers = await ctx.userModel.getSubscriptionUsers();
      const activeUsers = await ctx.userModel.getActiveUsers();
      return {
        active: await ctx.distributionModel.getAgentDistribution({
          range: input.range,
          userIds: activeUsers,
        }),
        all: await ctx.distributionModel.getAgentDistribution({
          range: input.range,
        }),
        free: await ctx.distributionModel.getAgentDistribution({
          range: input.range,
          userIds: freeUsers,
        }),
        subscription: await ctx.distributionModel.getAgentDistribution({
          range: input.range,
          userIds: subscriptionUsers,
        }),
      };
    }),

  getAgentRank: overviewProcedure
    .input(
      z.object({
        limit: z.number().optional(),
        range: z.tuple([z.string(), z.string()]),
      }),
    )
    .query(async ({ ctx, input }) => {
      return ctx.rankModel.getAgentRank(input);
    }),

  getMessageDistribution: overviewProcedure
    .input(
      z.object({
        range: z.tuple([z.string(), z.string()]),
      }),
    )
    .query(async ({ ctx, input }) => {
      const freeUsers = await ctx.userModel.getFreeUsers();
      const subscriptionUsers = await ctx.userModel.getSubscriptionUsers();
      const activeUsers = await ctx.userModel.getActiveUsers();
      return {
        active: await ctx.distributionModel.getMessageDistribution({
          range: input.range,
          userIds: activeUsers,
        }),
        all: await ctx.distributionModel.getMessageDistribution({
          range: input.range,
        }),
        free: await ctx.distributionModel.getMessageDistribution({
          range: input.range,
          userIds: freeUsers,
        }),
        subscription: await ctx.distributionModel.getMessageDistribution({
          range: input.range,
          userIds: subscriptionUsers,
        }),
      };
    }),

  getMessageRank: overviewProcedure
    .input(
      z.object({
        limit: z.number().optional(),
        range: z.tuple([z.string(), z.string()]),
      }),
    )
    .query(async ({ ctx, input }) => {
      return ctx.rankModel.getMessageRank(input);
    }),

  getRangeModelCalls: overviewProcedure
    .input(
      z.object({
        display: z.nativeEnum(DisplayType),
        prevRange: z.tuple([z.string(), z.string()]).optional(),
        range: z.tuple([z.string(), z.string()]),
      }),
    )
    .query(async ({ ctx, input }) => {
      const data = await ctx.spendModel.getRangeModelCalls(input);

      let prevData;
      if (input.prevRange)
        prevData = await ctx.spendModel.getRangeModelCalls({
          display: input.display,
          range: input.prevRange,
        });
      const formatedData = prevData ? formatRangeCompare(data, prevData) : data;
      const currentData = formatedData[data.length - 1];

      return {
        current: currentData?.count || 0,
        data: formatedData,
        prevCurrent: currentData?.prevCount || 0,
        prevSum: prevData?.reduce((acc, cur) => acc + (cur?.count || 0), 0),
        sum: data.reduce((acc, cur) => acc + (cur?.count || 0), 0),
      };
    }),

  getRangeModelCallsDetails: overviewProcedure
    .input(
      z.object({
        range: z.tuple([z.string(), z.string()]),
      }),
    )
    .query(async ({ ctx, input }) => {
      return ctx.spendModel.getRangeModelCallsDetails(input);
    }),

  getRangePlans: overviewProcedure
    .input(
      z.object({
        display: z.nativeEnum(DisplayType),
        range: z.tuple([z.string(), z.string()]),
      }),
    )
    .query(async ({ ctx, input }) => {
      return ctx.subscriptionModel.getRangePlans(input);
    }),

  getRangeSpend: overviewProcedure
    .input(
      z.object({
        display: z.nativeEnum(DisplayType),
        prevRange: z.tuple([z.string(), z.string()]).optional(),
        range: z.tuple([z.string(), z.string()]),
      }),
    )
    .query(async ({ ctx, input }) => {
      const data = await ctx.spendModel.getRangeSpend(input);

      let prevData;
      if (input.prevRange)
        prevData = await ctx.spendModel.getRangeSpend({
          display: input.display,
          range: input.prevRange,
        });
      const formatedData = prevData ? formatRangeCompare(data, prevData) : data;
      const currentData = formatedData[data.length - 1];

      return {
        current: currentData?.count || 0,
        data: formatedData,
        prevCurrent: currentData?.prevCount || 0,
        prevSum: prevData?.reduce((acc, cur) => acc + (cur?.count || 0), 0),
        sum: data.reduce((acc, cur) => acc + (cur?.count || 0), 0),
      };
    }),
  getRangeSpendDetails: overviewProcedure
    .input(
      z.object({
        range: z.tuple([z.string(), z.string()]),
      }),
    )
    .query(async ({ ctx, input }) => {
      return ctx.spendModel.getRangeSpendDetails(input);
    }),

  getRangeSubscriptions: overviewProcedure
    .input(
      z.object({
        display: z.nativeEnum(DisplayType),
        prevRange: z.tuple([z.string(), z.string()]).optional(),
        range: z.tuple([z.string(), z.string()]),
      }),
    )
    .query(async ({ ctx, input }) => {
      const data = await ctx.subscriptionModel.getRangeSubscriptions(input);

      let prevData;
      if (input.prevRange)
        prevData = await ctx.subscriptionModel.getRangeSubscriptions({
          display: input.display,
          range: input.prevRange,
        });
      const formatedData = prevData ? formatRangeCompare(data, prevData) : data;
      const currentData = formatedData[data.length - 1];

      return {
        current: currentData?.count || 0,
        data: formatedData,
        prevCurrent: currentData?.prevCount || 0,
        prevSum: prevData?.reduce((acc, cur) => acc + (cur?.count || 0), 0),
        sum: data.reduce((acc, cur) => acc + (cur?.count || 0), 0),
      };
    }),

  getRangeToken: overviewProcedure
    .input(
      z.object({
        display: z.nativeEnum(DisplayType),
        prevRange: z.tuple([z.string(), z.string()]).optional(),
        range: z.tuple([z.string(), z.string()]),
      }),
    )
    .query(async ({ ctx, input }) => {
      const data = await ctx.spendModel.getRangeToken(input);

      const models: { [key: string]: number } = {};
      data.forEach((d) => {
        Object.keys(d).forEach((key) => {
          if (key === 'date' || key === 'count') return;
          if (!models[key]) models[key] = 0;
          models[key] += d[key] as number;
        });
      });

      const keys = Object.entries(models).sort((a, b) => b[1] - a[1]);

      return {
        data,
        models: keys.map(([key]) => key),
        sum: data.reduce((acc, cur) => acc + (cur?.count || 0), 0),
      };
    }),

  getRangeUsers: overviewProcedure
    .input(
      z.object({
        display: z.nativeEnum(DisplayType),
        prevRange: z.tuple([z.string(), z.string()]).optional(),
        range: z.tuple([z.string(), z.string()]),
      }),
    )
    .query(async ({ ctx, input }) => {
      const data = await ctx.userModel.getRangeUsers(input);

      let prevData;
      if (input.prevRange)
        prevData = await ctx.userModel.getRangeUsers({
          display: input.display,
          range: input.prevRange,
        });
      const formatedData = prevData ? formatRangeCompare(data, prevData) : data;
      const currentData = formatedData[data.length - 1];

      return {
        current: currentData?.count || 0,
        data: formatedData,
        prevCurrent: currentData?.prevCount || 0,
        prevSum: prevData?.reduce((acc, cur) => acc + (cur?.count || 0), 0),
        sum: data.reduce((acc, cur) => acc + (cur?.count || 0), 0),
      };
    }),

  getTopicDistribution: overviewProcedure
    .input(
      z.object({
        range: z.tuple([z.string(), z.string()]),
      }),
    )
    .query(async ({ ctx, input }) => {
      const freeUsers = await ctx.userModel.getFreeUsers();
      const subscriptionUsers = await ctx.userModel.getSubscriptionUsers();
      const activeUsers = await ctx.userModel.getActiveUsers();
      return {
        active: await ctx.distributionModel.getTopicDistribution({
          range: input.range,
          userIds: activeUsers,
        }),
        all: await ctx.distributionModel.getTopicDistribution({
          range: input.range,
        }),
        free: await ctx.distributionModel.getTopicDistribution({
          range: input.range,
          userIds: freeUsers,
        }),
        subscription: await ctx.distributionModel.getTopicDistribution({
          range: input.range,
          userIds: subscriptionUsers,
        }),
      };
    }),

  getTopicRank: overviewProcedure
    .input(
      z.object({
        limit: z.number().optional(),
        range: z.tuple([z.string(), z.string()]),
      }),
    )
    .query(async ({ ctx, input }) => {
      return ctx.rankModel.getTopicRank(input);
    }),

  getTotalSubscriptions: overviewProcedure.query(async ({ ctx }) => {
    return {
      count: await ctx.subscriptionModel.getTotalSubscriptions(),
      prevCount: await ctx.subscriptionModel.getTotalSubscriptions({
        endDate: lastMonth().format('YYYY-MM-DD'),
      }),
    };
  }),

  getTotalTopics: overviewProcedure.query(async ({ ctx }) => {
    return {
      count: await ctx.topicModel.getTotalTopics(),
      prevCount: await ctx.topicModel.getTotalTopics({ endDate: lastMonth().format('YYYY-MM-DD') }),
    };
  }),

  getTotalUsers: overviewProcedure.query(async ({ ctx }) => {
    return {
      count: await ctx.userModel.getTotalUsers(),
      prevCount: await ctx.userModel.getTotalUsers({
        endDate: lastMonth().format('YYYY-MM-DD'),
      }),
    };
  }),

  getWeeklyActiveDistribution: overviewProcedure
    .input(
      z.object({
        range: z.tuple([z.string(), z.string()]),
      }),
    )
    .query(async ({ ctx, input }) => {
      const freeUsers = await ctx.userModel.getFreeUsers();
      const subscriptionUsers = await ctx.userModel.getSubscriptionUsers();
      const activeUsers = await ctx.userModel.getActiveUsers();
      return {
        active: await ctx.distributionModel.getWeeklyActiveDistribution({
          range: input.range,
          userIds: activeUsers,
        }),
        all: await ctx.distributionModel.getWeeklyActiveDistribution({
          range: input.range,
        }),
        free: await ctx.distributionModel.getWeeklyActiveDistribution({
          range: input.range,
          userIds: freeUsers,
        }),
        subscription: await ctx.distributionModel.getWeeklyActiveDistribution({
          range: input.range,
          userIds: subscriptionUsers,
        }),
      };
    }),

  getWeeklyActiveRank: overviewProcedure
    .input(
      z.object({
        limit: z.number().optional(),
        range: z.tuple([z.string(), z.string()]),
      }),
    )
    .query(async ({ ctx, input }) => {
      return ctx.rankModel.getWeeklyActiveRank(input);
    }),
});

export type OverviewRouter = typeof overviewRouter;
