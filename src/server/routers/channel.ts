import { z } from 'zod';

import { SpendModel } from '@/database/models/spend';
import { authedProcedure, router } from '@/libs/trpc';
import { liteLLMServices } from '@/server/services/litellm';
import { SortQuerySchema } from '@/types/query';
import { SpendQuerySchema } from '@/types/spend';
import { lastMonth } from '@/utils/time';

const channelProcedure = authedProcedure.use(async (opts) => {
  return opts.next({
    ctx: { liteLLMService: new liteLLMServices(), spendModel: new SpendModel() },
  });
});

export const channelRouter = router({
  getChannels: channelProcedure.query(async ({ ctx }) => {
    return ctx.liteLLMService.getChannels();
  }),

  getModelGroups: channelProcedure.query(async ({ ctx }) => {
    return ctx.liteLLMService.getModelGroups();
  }),

  getModelInfoById: channelProcedure.input(z.string()).query(async ({ ctx, input }) => {
    return ctx.liteLLMService.getModelInfoById(input);
  }),

  getModels: channelProcedure.query(async ({ ctx }) => {
    return ctx.liteLLMService.getModels();
  }),

  getTotalModelCalls: channelProcedure.query(async ({ ctx }) => {
    return {
      count: await ctx.spendModel.getTotalModelCalls(),
      prevCount: await ctx.spendModel.getTotalModelCalls({
        endDate: lastMonth().format('YYYY-MM-DD'),
      }),
    };
  }),

  getTotalSpend: channelProcedure.query(async ({ ctx }) => {
    return {
      count: await ctx.spendModel.getTotalSpend(),
      prevCount: await ctx.spendModel.getTotalSpend({
        endDate: lastMonth().format('YYYY-MM-DD'),
      }),
    };
  }),

  getUserSpendList: channelProcedure
    .input(
      z.object({
        params: SpendQuerySchema.optional(),
        sorts: SortQuerySchema.optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const result = await ctx.spendModel.getUserSpendList(input.params, input.sorts);

      return { success: true, ...result };
    }),
});

export type ChannelRouter = typeof channelRouter;
