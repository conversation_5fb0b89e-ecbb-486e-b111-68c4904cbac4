/* eslint-disable sort-keys-fix/sort-keys-fix */
import { z } from 'zod';

import { INFRA_MODEL_ABILITIES } from '@/const/aiInfra';
import { AdminInfraModel } from '@/database/models/adminInfra';
import { AggregatedModelModel } from '@/database/models/aggregatedModel';
import { authedProcedure, router } from '@/libs/trpc';
import { liteLLMServices } from '@/server/services/litellm';
import { AdminProviderSelectItem } from '@/types/adminInfra';
import {
  AggregatedModelItem,
  AggregatedModelLitellmItem,
  AggregatedModelQuerySchema,
  AggregatedModelWithAssociationModels,
  ApiResponse,
  CreateAggregatedModelLitellmSchema,
  CreateAggregatedModelSchema,
  LiteLLMParamsSchema,
  ToggleModelStatusSchema,
  UpdateAggregatedModelSchema,
} from '@/types/aggregatedModel';
import { CreateModelParams } from '@/types/litellm';

import { KeyVaultsGateKeeper } from '../modules/KeyVaultsEncrypt';

// 精确除法，避免精度丢失
function preciseDivision(value: number, divisor: number): number {
  const factor = Math.pow(10, 12); // 12位精度
  return Math.round((value * factor) / divisor) / factor;
}

/**
 * 生成 LiteLLM 模型参数，在两个场景下使用：
 * 1.在聚合模型中添加基础的渠道&模型，用于创建 LiteLLM 模型
 * 2.在聚合模型中更新模型信息，用于更新关联的 LiteLLM 模型
 * @param aggregatedModel 聚合模型信息
 * @param provider 渠道信息
 * @param infraModelId 渠道模型 ID
 * @param existingLitellmParams 已存在的 LiteLLM 参数（用于更新场景）
 * @returns LiteLLM 模型参数
 */
function generateLiteLLMModelParams(
  aggregatedModel: AggregatedModelItem,
  provider: AdminProviderSelectItem,
  infraModelId: string,
  existingLitellmParams?: Partial<CreateModelParams['litellm_params']>,
) {
  const litellmProvider = provider.settings.sdkType || 'openai';
  const { input, output } = aggregatedModel.pricing || {};

  // 生成基础的 LiteLLM 参数
  const baseLitellmParams = {
    model: infraModelId,
    litellm_credential_name: provider.litellmCredential,
    // 将计价信息转换为 LiteLLM 的计价信息
    input_cost_per_token: input ? preciseDivision(input, 1e6) : undefined,
    output_cost_per_token: output ? preciseDivision(output, 1e6) : undefined,
  };

  // 将现有的 litellmParams 与基础参数合并，现有参数优先
  const mergedLitellmParams = {
    ...baseLitellmParams,
    ...existingLitellmParams,
  };

  return {
    model_name: aggregatedModel.id,
    litellm_params: mergedLitellmParams,
    model_info: {
      litellm_provider: litellmProvider,
      max_input_tokens: aggregatedModel?.contextWindowTokens,
      // 将模型能力映射到 LiteLLM 的模型信息中
      ...Object.entries(aggregatedModel?.abilities || {}).reduce(
        (acc: Record<string, boolean>, [key, value]) => {
          const ability = INFRA_MODEL_ABILITIES.find((ability) => ability.abilityKey === key);

          if (ability) {
            acc[ability.litellmKey] = value;
          }
          return acc;
        },
        {},
      ),
    },
    pricing: {
      input: aggregatedModel.pricing?.input,
      output: aggregatedModel.pricing?.output,
    },
  };
}

const aggregatedModelProcedure = authedProcedure.use(async (opts) => {
  return opts.next({
    ctx: {
      aggregatedModelModel: new AggregatedModelModel(),
      litellmServices: new liteLLMServices(),
      adminInfraModel: new AdminInfraModel(),
      gateKeeper: await KeyVaultsGateKeeper.initWithEnvKey(),
    },
  });
});

export const aggregatedModelRouter = router({
  /**
   * 获取数据库中的聚合模型列表
   */
  getAllModels: aggregatedModelProcedure
    .input(AggregatedModelQuerySchema)
    .query(async ({ ctx, input }): Promise<ApiResponse<AggregatedModelItem[]>> => {
      const data = await ctx.aggregatedModelModel.getAllModels(input);

      return { success: true, data };
    }),

  /**
   * 根据模型 ID 获取聚合模型及关联模型信息
   */
  getModelInfo: aggregatedModelProcedure
    .input(z.string())
    .query(
      async ({ ctx, input }): Promise<ApiResponse<AggregatedModelWithAssociationModels | null>> => {
        // 1. 通过ID查询聚合模型信息
        const aggregatedModel = await ctx.aggregatedModelModel.getModelById(input);

        if (!aggregatedModel) {
          return { success: true, data: null };
        }

        // 2. 通过模型ID查询关联的渠道与模型信息
        const models = await ctx.aggregatedModelModel.getModelsByAggregatedModelId(input);

        // 3. 获取聚合模型关联的模型、渠道和权重信息
        const associations = await ctx.aggregatedModelModel.getAssociationsByAggregatedModelId({
          aggregatedModelId: input,
        });

        const totalWeight = associations.reduce((acc, association) => {
          return acc + (association.litellmParams?.weight || 0);
        }, 0);

        // 3. 返回包含关联模型信息的聚合模型
        const data = {
          ...aggregatedModel,
          models: models.map((model) => {
            const { id: infraModelId, providerId: infraProviderId } = model;

            const associationWeight =
              associations.find(
                (association) =>
                  association.infraModelId === infraModelId &&
                  association.infraProviderId === infraProviderId,
              )?.litellmParams?.weight ?? 0;

            return {
              ...model,
              weight: associationWeight,
              balance: associationWeight
                ? (preciseDivision(associationWeight, totalWeight) * 100).toFixed(2) + '%'
                : null,
            };
          }),
        };

        return { success: true, data };
      },
    ),

  /**
   * 创建聚合模型信息
   */
  createModel: aggregatedModelProcedure
    .input(CreateAggregatedModelSchema)
    .mutation(async ({ ctx, input }): Promise<ApiResponse<AggregatedModelItem>> => {
      const data = await ctx.aggregatedModelModel.createModel(input);

      return { success: true, data };
    }),

  /**
   * 更新模型信息
   */
  updateModel: aggregatedModelProcedure
    .input(
      z.object({
        id: z.string(),
        data: UpdateAggregatedModelSchema,
      }),
    )
    .mutation(async ({ ctx, input }): Promise<ApiResponse<AggregatedModelItem | null>> => {
      const { id, data } = input;

      // 更新聚合模型信息
      const updatedAggregatedModel = await ctx.aggregatedModelModel.updateModel(id, data);

      if (!updatedAggregatedModel) {
        return { success: false, error: '模型不存在' };
      }

      // 更新关联的 LiteLLM 模型的计价信息
      const associations = await ctx.aggregatedModelModel.getAssociationsByAggregatedModelId({
        aggregatedModelId: id,
      });

      // 更新每个关联的 LiteLLM 模型信息
      for (const association of associations) {
        const provider = await ctx.adminInfraModel.getInfraProviderById(
          association.infraProviderId,
          ctx.gateKeeper,
        );

        if (!provider) {
          throw new Error('渠道不存在');
        }

        const updateParams = generateLiteLLMModelParams(
          updatedAggregatedModel,
          provider,
          association.infraModelId,
          association.litellmParams,
        );

        await ctx.litellmServices.updateModel(association.litellmModelId, updateParams);
      }

      return { success: true, data: updatedAggregatedModel };
    }),

  /**
   * 删除模型
   */
  deleteModel: aggregatedModelProcedure
    .input(z.string())
    .mutation(async ({ ctx, input }): Promise<ApiResponse<boolean>> => {
      // 1. 获取关联的 LiteLLM 模型
      const associations = await ctx.aggregatedModelModel.getAssociationsByAggregatedModelId({
        aggregatedModelId: input,
      });

      // 2. 删除 LiteLLM 中的模型
      for (const association of associations) {
        await ctx.litellmServices.deleteModel(association.litellmModelId);
      }

      // 3. 删除聚合模型，关联记录自动删除
      const result = await ctx.aggregatedModelModel.deleteModel(input);

      return { success: true, data: result };
    }),

  /**
   * 切换模型的激活状态
   */
  toggleModelStatus: aggregatedModelProcedure
    .input(ToggleModelStatusSchema)
    .mutation(async ({ ctx, input }): Promise<ApiResponse<AggregatedModelItem | null>> => {
      const { id, enabled } = input;

      const result = await ctx.aggregatedModelModel.toggleModelStatus(id, enabled);

      return { success: true, data: result };
    }),

  /**
   * 在聚合模型中添加基础的渠道模型关联
   */
  addInfraModelRelation: aggregatedModelProcedure
    .input(CreateAggregatedModelLitellmSchema)
    .mutation(async ({ ctx, input }): Promise<ApiResponse<AggregatedModelLitellmItem | null>> => {
      const { aggregatedModelId, infraProviderId, infraModelId } = input;

      // 1. 获取聚合模型信息
      const aggregatedModel = await ctx.aggregatedModelModel.getModelById(aggregatedModelId);

      if (!aggregatedModel) {
        throw new Error('聚合模型不存在');
      }

      // 2. 获取渠道信息
      const provider = await ctx.adminInfraModel.getInfraProviderById(
        infraProviderId,
        ctx.gateKeeper,
      );

      if (!provider) {
        throw new Error('渠道不存在');
      }

      // 3. 判断模型是否可以被关联
      const canBeAssociated = await ctx.aggregatedModelModel.canInfraModelBeAssociated(
        aggregatedModelId,
        infraProviderId,
        infraModelId,
      );

      if (!canBeAssociated) {
        return { success: false, error: '模型已存在，请勿重复关联' };
      }

      // 2. 在 LiteLLM 中添加对应的模型，model_name 取聚合模型 ID
      const litellmParams = generateLiteLLMModelParams(
        aggregatedModel,
        provider,
        infraModelId,
        input.litellmParams,
      );

      const litellmModel = await ctx.litellmServices.createModel(litellmParams);

      // 3. 在聚合模型关联 LiteLLM 表中添加对应的记录，包含 litellmParams
      const data = await ctx.aggregatedModelModel.createAssociation({
        aggregatedModelId,
        litellmModelId: litellmModel.model_id,
        infraProviderId,
        infraModelId,
        litellmParams: litellmParams.litellm_params,
      });

      return { success: true, data };
    }),

  /**
   * 获取关联模型的详细信息，包括LiteLLM参数
   */
  getInfraModelRelationDetail: aggregatedModelProcedure
    .input(
      z.object({
        aggregatedModelId: z.string(),
        infraProviderId: z.string(),
        infraModelId: z.string(),
      }),
    )
    .query(async ({ ctx, input }): Promise<ApiResponse<AggregatedModelLitellmItem | null>> => {
      const associations = await ctx.aggregatedModelModel.getAssociationsByAggregatedModelId({
        aggregatedModelId: input.aggregatedModelId,
        infraProviderId: input.infraProviderId,
        infraModelId: input.infraModelId,
      });

      if (!associations.length) {
        return { success: false, error: '关联模型不存在' };
      }

      return { success: true, data: associations[0] };
    }),

  /**
   * 更新关联模型的LiteLLM参数
   */
  updateInfraModelRelation: aggregatedModelProcedure
    .input(
      z.object({
        litellmModelId: z.string(),
        litellmParams: LiteLLMParamsSchema.partial().optional(),
      }),
    )
    .mutation(async ({ ctx, input }): Promise<ApiResponse<boolean>> => {
      try {
        // 1. 获取关联记录
        const associations = await ctx.aggregatedModelModel.getAssociationsByAggregatedModelId({
          litellmModelId: input.litellmModelId,
        });

        if (!associations.length) {
          return { success: false, error: '关联模型不存在' };
        }

        const association = associations[0];

        // 2. 获取聚合模型信息
        const aggregatedModel = await ctx.aggregatedModelModel.getModelById(
          association.aggregatedModelId,
        );
        if (!aggregatedModel) {
          return { success: false, error: '聚合模型不存在' };
        }

        // 3. 获取渠道信息
        const provider = await ctx.adminInfraModel.getInfraProviderById(
          association.infraProviderId,
          ctx.gateKeeper,
        );
        if (!provider) {
          return { success: false, error: '渠道不存在' };
        }

        // 4. 合并参数并更新LiteLLM模型
        const mergedParams = {
          ...association.litellmParams,
          ...input.litellmParams,
        };

        const updateParams = generateLiteLLMModelParams(
          aggregatedModel,
          provider,
          association.infraModelId,
          mergedParams,
        );

        await ctx.litellmServices.updateModel(input.litellmModelId, updateParams);

        // 5. 更新数据库中的litellmParams
        await ctx.aggregatedModelModel.updateAssociationParams(input.litellmModelId, mergedParams);

        return { success: true, data: true };
      } catch (error: any) {
        return { success: false, error: error.message };
      }
    }),

  /**
   * 在聚合模型中删除基础渠道&模型
   */
  deleteInfraModelRelation: aggregatedModelProcedure
    .input(
      z.object({
        aggregatedModelId: z.string().optional(),
        litellmModelId: z.string().optional(),
        infraProviderId: z.string().optional(),
        infraModelId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }): Promise<ApiResponse<boolean>> => {
      const association = await ctx.aggregatedModelModel.getAssociationsByAggregatedModelId(input);

      if (!association.length) {
        return { success: false, error: '关联模型不存在' };
      }

      const { litellmModelId } = association[0];

      // 1. 从 LiteLLM 中删除对应的模型
      await ctx.litellmServices.deleteModel(litellmModelId);

      // 2. 在关联关系表中删除对应的记录
      const result = await ctx.aggregatedModelModel.deleteAssociation(litellmModelId);

      return { success: true, data: result };
    }),
});

export type AggregatedModelRouter = typeof aggregatedModelRouter;
