import { z } from 'zod';

import { AiInfraMigrationRepos } from '@/database/repositories/aiInfraMigration';
import { authedProcedure, router } from '@/libs/trpc';
import { AiInfraMigrator } from '@/server/modules/AiInfraMigrator';
import { ProviderMigrationSchema } from '@/types/migration';

const migrationProcedure = authedProcedure.use(async (opts) => {
  return opts.next({
    ctx: {
      aiInfraMigration: new AiInfraMigrationRepos(),
    },
  });
});

export const migrationRouter = router({
  cleanMigratedUsersConfig: migrationProcedure
    .input(
      z.object({
        userIds: z.array(z.string()),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      return await ctx.aiInfraMigration.cleanMigratedUsersConfig(input.userIds);
    }),

  getMigrationProviders: migrationProcedure
    .input(
      z.object({
        keyVaultsSecret: z.string(),
        params: ProviderMigrationSchema.optional(),
        // sorts: SortQuerySchema.optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const data = await ctx.aiInfraMigration.getMigrationProviders(
        input.keyVaultsSecret,
        input.params,
      );

      return { success: true, ...data };
    }),

  getModelsDetail: migrationProcedure
    .input(
      z.object({
        providerId: z.string(),
        userId: z.string(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const data = await ctx.aiInfraMigration.getModelsDetail(input);

      return { data, success: true, total: data.length };
    }),

  getUserConfigAiInfra: migrationProcedure
    .input(
      z.object({
        keyVaultsSecret: z.string(),
        userId: z.string(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const data = await ctx.aiInfraMigration.getUserConfigAiInfra(
        input.userId,
        input.keyVaultsSecret,
      );

      return { data, success: true, total: data.length };
    }),

  runMigration: migrationProcedure
    .input(
      z.object({
        keyVaultsSecret: z.string(),
        userIds: z.array(z.string()).optional(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const migrator = new AiInfraMigrator(input.keyVaultsSecret);

      const userIds = input.userIds ?? (await ctx.aiInfraMigration.getNeedMigrationUsers());

      console.log('迁移总用户数：', userIds.length);
      console.time('迁移耗时');
      await migrator.migrate(userIds);
      console.timeEnd('迁移耗时');
    }),
});
