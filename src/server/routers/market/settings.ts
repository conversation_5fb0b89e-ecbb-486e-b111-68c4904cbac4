import { z } from 'zod';

import { router } from '@/libs/trpc';

import { marketProcedure } from './procedure';

export const settingsRouter = router({
  // 创建设置
  createSetting: marketProcedure
    .input(
      z.object({
        description: z.string().optional(),
        key: z.string(),
        value: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      return await ctx.marketAdmin.settings.createSetting({
        description: input.description,
        key: input.key,
        value: input.value,
      });
    }),

  // 删除设置
  deleteSetting: marketProcedure
    .input(
      z.object({
        key: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      return await ctx.marketAdmin.settings.deleteSetting(input.key);
    }),

  // 获取单个设置
  getSettingByKey: marketProcedure
    .input(
      z.object({
        key: z.string(),
      }),
    )
    .query(async ({ input, ctx }) => {
      return await ctx.marketAdmin.settings.getSettingByKey(input.key);
    }),

  // 获取设置列表
  getSettings: marketProcedure.query(async ({ ctx }) => {
    return await ctx.marketAdmin.settings.getSettings();
  }),

  // 更新设置
  updateSetting: marketProcedure
    .input(
      z.object({
        description: z.string().optional(),
        key: z.string(),
        value: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      return await ctx.marketAdmin.settings.updateSetting(input.key, {
        description: input.description,
        value: input.value,
      });
    }),
});
