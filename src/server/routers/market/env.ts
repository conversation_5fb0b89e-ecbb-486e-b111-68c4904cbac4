import { z } from 'zod';

import { router } from '@/libs/trpc';

import { marketProcedure } from './procedure';

export const envRouter = router({
  // 新增环境变量
  createPluginEnv: marketProcedure
    .input(
      z.object({
        description: z.string().optional(),
        identifier: z.string(),
        key: z.string(),
        value: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      return await ctx.marketAdmin.env.createPluginEnv(input);
    }),

  // 删除环境变量
  deletePluginEnv: marketProcedure
    .input(
      z.object({
        id: z.number(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      return await ctx.marketAdmin.env.deletePluginEnv(input.id);
    }),

  // 获取环境变量列表
  getPluginEnvs: marketProcedure
    .input(
      z
        .object({
          current: z.number().optional(),
          identifier: z.string().optional(),
          key: z.string().optional(),
          pageSize: z.number().optional(),
          sortField: z.string().optional(),
          sortOrder: z.enum(['ascend', 'descend']).optional(),
        })
        .optional(),
    )
    .query(async ({ input, ctx }) => {
      return await ctx.marketAdmin.env.getPluginEnvs(input || {});
    }),

  // 编辑环境变量
  updatePluginEnv: marketProcedure
    .input(
      z.object({
        data: z.object({
          description: z.string().optional(),
          key: z.string().optional(),
          value: z.string().optional(),
        }),
        id: z.number(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      return await ctx.marketAdmin.env.updatePluginEnv(input.id, input.data);
    }),
});
