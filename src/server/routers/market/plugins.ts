import {
  PluginUpdateParams,
  PluginVersionCreateParams,
  PluginVersionUpdateParams,
} from '@lobehub/market-sdk';
import { z } from 'zod';

import { router } from '@/libs/trpc';
import { workflowService } from '@/server/services/workflow';

import { marketProcedure } from './procedure';

export const pluginsRouter = router({
  addPlugins: marketProcedure
    .input(
      z.object({
        githubUrls: z.array(z.string().url()),
      }),
    )
    .mutation(async ({ input }) => {
      return await workflowService.trigger('add-mcp-plugins', { githubUrls: input.githubUrls });
    }),

  // 批量删除插件
  batchDeletePlugins: marketProcedure
    .input(
      z.object({
        ids: z.array(z.number()),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.marketAdmin.plugins.batchDeletePlugins(input.ids);
    }),

  // 批量更新插件状态
  batchUpdatePluginStatus: marketProcedure
    .input(
      z.object({
        ids: z.array(z.number()),
        status: z.enum(['published', 'draft', 'review', 'rejected']),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.marketAdmin.plugins.batchUpdatePluginStatus(input.ids, input.status);
    }),

  // 创建部署选项
  createDeploymentOption: marketProcedure
    .input(
      z.object({
        data: z.object({
          connectionArgs: z.array(z.string()).optional(),
          connectionCommand: z.string().optional(),
          connectionType: z.string(),
          description: z.string().optional(),
          installationDetails: z.any().optional(),
          installationMethod: z.string(),
          isRecommended: z.boolean().optional(),
        }),
        pluginId: z.number(),
        versionId: z.number(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { pluginId, versionId, data } = input;
      return ctx.marketAdmin.plugins.createDeploymentOption(pluginId, versionId, data as any);
    }),

  // 创建插件版本
  createPluginVersion: marketProcedure
    .input(
      z.object({
        data: z.object({
          isLatest: z.boolean().optional(),
          isValidated: z.boolean().optional(),
          manifest: z.any(),
          manifestUrl: z.string().optional(),
          meta: z.record(z.any()).optional(),
          version: z.string(),
        }),
        pluginId: z.number(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { pluginId, data } = input;
      return ctx.marketAdmin.plugins.createPluginVersion(
        pluginId,
        data as PluginVersionCreateParams,
      );
    }),

  // 删除部署选项
  deleteDeploymentOption: marketProcedure
    .input(
      z.object({
        optionId: z.number(),
        pluginId: z.number(),
        versionId: z.number(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { pluginId, versionId, optionId } = input;
      return ctx.marketAdmin.plugins.deleteDeploymentOption(pluginId, versionId, optionId);
    }),

  // 删除插件
  deletePlugin: marketProcedure
    .input(z.object({ id: z.number() }))
    .mutation(async ({ ctx, input }) => {
      return ctx.marketAdmin.plugins.deletePlugin(input.id);
    }),

  // 删除插件版本
  deletePluginVersion: marketProcedure
    .input(
      z.object({
        pluginId: z.number(),
        versionId: z.number(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { pluginId, versionId } = input;
      return ctx.marketAdmin.plugins.deletePluginVersion(pluginId, versionId);
    }),

  // 获取部署选项的系统依赖
  getDeploymentOptionSystemDependencies: marketProcedure
    .input(
      z.object({
        optionId: z.number(),
        pluginId: z.number(),
        versionId: z.number(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { pluginId, versionId, optionId } = input;
      return ctx.marketAdmin.plugins.getDeploymentOptionSystemDependencies(
        pluginId,
        versionId,
        optionId,
      );
    }),

  // 获取部署选项列表
  getDeploymentOptions: marketProcedure
    .input(
      z.object({
        pluginId: z.number(),
        versionId: z.number(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { pluginId, versionId } = input;
      return ctx.marketAdmin.plugins.getDeploymentOptions(pluginId, versionId);
    }),

  // 获取单个插件
  getPlugin: marketProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .query(async ({ input, ctx }) => {
      return await ctx.marketAdmin.plugins.getPlugin(input.id);
    }),

  // 获取插件版本列表
  getPluginVersions: marketProcedure
    .input(
      z.object({
        pluginId: z.number(),
      }),
    )
    .query(async ({ input, ctx }) => {
      return await ctx.marketAdmin.plugins.getPluginVersions(input.pluginId);
    }),

  // 获取插件列表
  getPlugins: marketProcedure
    .input(
      z.object({
        current: z.number().optional(),
        isFeatured: z.enum(['true', 'false'] as const).optional(),
        keyword: z.string().optional(),
        ownerId: z.number().optional(),
        pageSize: z.number().optional(),
        sortField: z.string().optional(),
        sortOrder: z.enum(['ascend', 'descend']).optional(),
        status: z.enum(['published', 'draft', 'review', 'rejected']).optional(),
        visibility: z.enum(['public', 'private', 'unlisted']).optional(),
      }),
    )
    .query(async ({ input, ctx }) => {
      const data = await ctx.marketAdmin.plugins.getPlugins(input);
      return { ...data, success: true };
    }),

  // 设置插件版本为最新版本
  setPluginVersionAsLatest: marketProcedure
    .input(
      z.object({
        pluginId: z.number(),
        versionId: z.number(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      return ctx.marketAdmin.plugins.setPluginVersionAsLatest(input.pluginId, input.versionId);
    }),

  // 更新部署选项
  updateDeploymentOption: marketProcedure
    .input(
      z.object({
        data: z.object({
          connectionArgs: z.array(z.string()).optional(),
          connectionCommand: z.string().optional(),
          connectionType: z.string().optional(),
          description: z.string().optional(),
          installationDetails: z.record(z.any()).optional(),
          installationMethod: z.string().optional(),
          isRecommended: z.boolean().optional(),
        }),
        optionId: z.number(),
        pluginId: z.number(),
        versionId: z.number(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { pluginId, versionId, optionId, data } = input;
      return ctx.marketAdmin.plugins.updateDeploymentOption(pluginId, versionId, optionId, data);
    }),

  // 更新插件信息
  updatePlugin: marketProcedure
    .input(
      z.object({
        data: z.object({
          identifier: z.string().optional(),
          isFeatured: z.boolean().optional(),
          meta: z.record(z.any()).optional(),
          status: z.enum(['published', 'unpublished', 'review', 'rejected']).optional(),
          tags: z.array(z.string()).optional(),
          visibility: z.enum(['public', 'private', 'unlisted']).optional(),
        }),
        id: z.number(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { id, data } = input;
      return ctx.marketAdmin.plugins.updatePlugin(id, data as PluginUpdateParams);
    }),

  // 更新插件版本
  updatePluginVersion: marketProcedure
    .input(
      z.object({
        data: z.object({
          isValidated: z.boolean().optional(),
          manifestUrl: z.string().optional(),
          meta: z.record(z.any()).optional(),
        }),
        pluginId: z.number(),
        versionId: z.number(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { pluginId, versionId, data } = input;
      return ctx.marketAdmin.plugins.updatePluginVersion(
        pluginId,
        versionId,
        data as PluginVersionUpdateParams,
      );
    }),
});
