import { z } from 'zod';

import { router } from '@/libs/trpc';

import { marketProcedure } from './procedure';

export const overviewRouter = router({
  // 获取安装失败聚合分析
  getInstallFailureAnalysis: marketProcedure
    .input(
      z.object({
        range: z.tuple([z.string(), z.string()]),
      }),
    )
    .query(async ({ input, ctx }) => {
      return await ctx.marketAdmin.analysis.getInstallFailureAnalysis(input);
    }),

  // 获取市场概览统计
  getMarketOverviewStats: marketProcedure.query(async ({ ctx }) => {
    return await ctx.marketAdmin.analysis.getMarketOverview({ period: '1mo' });
  }),

  // 获取插件调用次数趋势
  getRangeCalls: marketProcedure
    .input(
      z.object({
        display: z.string(),
        prevRange: z.tuple([z.string(), z.string()]).optional(),
        range: z.tuple([z.string(), z.string()]),
      }),
    )
    .query(async ({ input, ctx }) => {
      return await ctx.marketAdmin.analysis.getRangeCalls(input);
    }),

  // 获取设备增长趋势
  getRangeDevices: marketProcedure
    .input(
      z.object({
        display: z.string(),
        prevRange: z.tuple([z.string(), z.string()]).optional(),
        range: z.tuple([z.string(), z.string()]),
      }),
    )
    .query(async ({ input, ctx }) => {
      return await ctx.marketAdmin.analysis.getRangeDevices(input);
    }),

  // 获取安装量趋势
  getRangeInstalls: marketProcedure
    .input(
      z.object({
        display: z.string(),
        prevRange: z.tuple([z.string(), z.string()]).optional(),
        range: z.tuple([z.string(), z.string()]),
      }),
    )
    .query(async ({ input, ctx }) => {
      return await ctx.marketAdmin.analysis.getRangeInstalls(input);
    }),

  // 获取插件增长趋势
  getRangePlugins: marketProcedure
    .input(
      z.object({
        display: z.string(),
        prevRange: z.tuple([z.string(), z.string()]).optional(),
        range: z.tuple([z.string(), z.string()]),
      }),
    )
    .query(async ({ input, ctx }) => {
      return await ctx.marketAdmin.analysis.getRangePlugins(input);
    }),

  // 获取最火调用插件 Top10
  getTop10MostCalledPlugins: marketProcedure
    .input(
      z.object({
        range: z.tuple([z.string(), z.string()]),
      }),
    )
    .query(async ({ input, ctx }) => {
      return await ctx.marketAdmin.analysis.getTopPlugins({
        limit: 10,
        range: input.range,
        sortBy: 'calls',
      });
    }),

  // 获取最热门插件 Top10
  getTop10PopularPlugins: marketProcedure
    .input(
      z.object({
        range: z.tuple([z.string(), z.string()]),
      }),
    )
    .query(async ({ input, ctx }) => {
      return await ctx.marketAdmin.analysis.getTopPlugins({
        limit: 10,
        range: input.range,
        sortBy: 'installs',
      });
    }),
});
