import { router } from '@/libs/trpc';

import { dependenciesRouter } from './dependencies';
import { envRouter } from './env';
import { overviewRouter } from './overview';
import { pluginsRouter } from './plugins';
import { reviewRouter } from './review';
import { settingsRouter } from './settings';

export const marketRouter = router({
  dependencies: dependenciesRouter,
  env: envRouter,
  overview: overviewRouter,
  // organizations: organizationsRouter,
  plugins: pluginsRouter,
  review: reviewRouter,
  settings: settingsRouter,
  // users: usersRouter,
});
