import { AdminListQueryParams } from '@lobehub/market-sdk';
import { z } from 'zod';

import { router } from '@/libs/trpc';

import { marketProcedure } from './procedure';

export const reviewRouter = router({
  // 获取插件的审核历史
  getPluginReviews: marketProcedure
    .input(
      z.object({
        pluginId: z.number(),
      }),
    )
    .query(async ({ input, ctx }) => {
      return await ctx.marketAdmin.reviews.getPluginReviews(input.pluginId);
    }),

  // 获取单个审核
  getReviewById: marketProcedure
    .input(
      z.object({
        id: z.number(),
      }),
    )
    .query(async ({ input, ctx }) => {
      return await ctx.marketAdmin.reviews.getReviewById(input.id);
    }),

  // 获取审核列表
  getReviews: marketProcedure
    .input(
      z.object({
        current: z.number().optional(),
        keyword: z.string().optional(),
        pageSize: z.number().optional(),
        sortField: z.string().optional(),
        sortOrder: z.enum(['ascend', 'descend']).optional(),
        status: z.enum(['pending', 'approved', 'rejected']).optional(),
      }),
    )
    .query(async ({ input, ctx }) => {
      const params: AdminListQueryParams = {
        current: input.current,
        keyword: input.keyword,
        pageSize: input.pageSize,
        sortField: input.sortField,
        sortOrder: input.sortOrder,
      };

      const result = await ctx.marketAdmin.reviews.getReviews(params);
      return result;
    }),

  // 更新审核状态
  updateReview: marketProcedure
    .input(
      z.object({
        comment: z.string().optional(),
        id: z.number(),
        status: z.enum(['pending', 'approved', 'rejected']),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      return await ctx.marketAdmin.reviews.updateReview(input.id, {
        comment: input.comment,
        status: input.status,
      });
    }),
});
