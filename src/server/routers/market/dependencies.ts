import { SystemDependency } from '@lobehub/market-sdk';
import { z } from 'zod';

import { router } from '@/libs/trpc';

import { marketProcedure } from './procedure';

export const dependenciesRouter = router({
  // 创建系统依赖
  createSystemDependency: marketProcedure
    .input(
      z.object({
        checkCommand: z.string().optional(),
        description: z.string().optional(),
        installInstructions: z.record(z.string()).optional(),
        name: z.string(),
        requiredVersion: z.string().optional(),
        type: z.string().optional(),
        versionParsingRequired: z.boolean().optional(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const params: Omit<SystemDependency, 'id'> = {
        checkCommand: input.checkCommand,
        description: input.description,
        installInstructions: input.installInstructions,
        name: input.name,
        requiredVersion: input.requiredVersion,
        type: input.type,
        versionParsingRequired: input.versionParsingRequired,
      };

      return await ctx.marketAdmin.dependencies.createSystemDependency(params);
    }),

  // 删除系统依赖
  deleteSystemDependency: marketProcedure
    .input(
      z.object({
        id: z.number(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      return await ctx.marketAdmin.dependencies.deleteSystemDependency(input.id);
    }),

  // 获取系统依赖列表
  getSystemDependencies: marketProcedure.query(async ({ ctx }) => {
    return await ctx.marketAdmin.dependencies.getSystemDependencies();
  }),

  // 获取单个系统依赖
  getSystemDependency: marketProcedure
    .input(
      z.object({
        id: z.number(),
      }),
    )
    .query(async ({ input, ctx }) => {
      return await ctx.marketAdmin.dependencies.getSystemDependency(input.id);
    }),

  // 更新系统依赖
  updateSystemDependency: marketProcedure
    .input(
      z.object({
        checkCommand: z.string().optional(),
        description: z.string().optional(),
        id: z.number(),
        installInstructions: z.record(z.string()).optional(),
        name: z.string().optional(),
        requiredVersion: z.string().optional(),
        type: z.string().optional(),
        versionParsingRequired: z.boolean().optional(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const { id, ...data } = input;
      return await ctx.marketAdmin.dependencies.updateSystemDependency(id, data);
    }),
});
