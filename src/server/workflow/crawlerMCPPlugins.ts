import { MarketAdmin } from '@lobehub/market-sdk';
import { MCPCrawler, MCPManifest, MCPValidator, OverviewGenerator } from 'mcp-crawler';
import { Dirent, existsSync, mkdirSync, readdirSync, rmSync } from 'node:fs';
import { tmpdir } from 'node:os';
import { join } from 'node:path';

export interface CrawlRequest {
  forceRefresh?: boolean;
  githubUrls: string[];
  ownerId?: number;
}

export interface CrawlResult {
  error?: string;
  manifest?: any;
  success: boolean;
  url: string;
}

export interface ValidationResult {
  error?: string;
  manifest?: MCPManifest;
  message?: string;
  skipped?: boolean;
  success: boolean;
}

export class CrawlerMCPPlugins {
  private marketAdmin: MarketAdmin;

  constructor() {
    this.marketAdmin = new MarketAdmin({
      apiKey: process.env.MARKET_ADMIN_API_KEY,
      baseURL: process.env.MARKET_BASE_URL,
    });
  }

  /**
   * 检查缺失的环境变量
   * @param manifest MCPManifest
   * @returns string[]
   */
  checkMissingEnvVars(manifest: MCPManifest): string[] {
    const missingEnvVars: string[] = [];
    const deploymentOption =
      manifest.deploymentOptions.find((option) => option.isRecommended) ||
      manifest.deploymentOptions[0];

    if (deploymentOption?.connection?.configSchema?.properties) {
      const { required = [] } = deploymentOption.connection.configSchema;

      required.forEach((key: string) => {
        if (!process.env[key]) {
          missingEnvVars.push(key);
        }
      });
    }

    return missingEnvVars;
  }

  /**
   * 抓取单个 GitHub URL 的 MCP 清单
   * @param githubUrl 要抓取的 URL
   * @returns CrawlResult
   */
  async crawlSingleUrl(githubUrl: string): Promise<CrawlResult> {
    try {
      console.log(`开始抓取: ${githubUrl}`);

      const crawler = new MCPCrawler(githubUrl);
      const manifest = await crawler.crawl();

      if (manifest && manifest.identifier) {
        console.log(`成功抓取: ${githubUrl} (ID: ${manifest.identifier})`);
        return {
          manifest,
          success: true,
          url: githubUrl,
        };
      } else {
        return { error: '跳过该仓库或缺少 identifier 字段', success: false, url: githubUrl };
      }
    } catch (error) {
      console.error(`抓取出错: ${githubUrl}`, error);
      return {
        error: error instanceof Error ? error.message : '未知错误',
        success: false,
        url: githubUrl,
      };
    }
  }

  /**
   * 为已验证的 manifest 生成概览
   * @param manifest 已验证的 MCPManifest
   * @returns 更新后的 manifest 或原始 manifest
   */
  async generateOverview(manifest: MCPManifest): Promise<MCPManifest> {
    try {
      console.log(`🎯 开始为 ${manifest.name} 生成概览...`);

      const overviewGenerator = new OverviewGenerator({
        model: 'gpt-4.1-nano',
      });

      const updatedManifest = await overviewGenerator.updateManifestOverview(manifest);

      if (updatedManifest) {
        console.log(`✅ 成功为 ${manifest.name} 生成概览`);
        return updatedManifest;
      } else {
        console.warn(`⚠️ 为 ${manifest.name} 生成概览失败，返回原始 manifest`);
        return manifest;
      }
    } catch (error) {
      console.error(`❌ 为 ${manifest.name} 生成概览时出错:`, error);
      return manifest; // 失败时返回原始 manifest
    }
  }

  /**
   * 通过 API 导入插件
   */
  async importPluginsViaApi(manifests: any[], ownerId: number) {
    console.log(`正在通过 API 导入 ${manifests.length} 个清单文件`);

    // 预处理所有清单数据
    const processedManifests = manifests.map((item) => this.preprocessManifest(item));

    // 准备导入数据
    const importData = {
      manifests: processedManifests,
      ownerId,
    };

    const requestBody = JSON.stringify(importData);
    console.log(`请求体大小: ${(requestBody.length / 1024).toFixed(2)} KB`);

    // 发送请求
    const result = await this.marketAdmin.plugins.importPlugins(processedManifests, ownerId);

    console.log(
      `API 导入结果: 成功 ${result.success} 个，跳过 ${result.skipped || 0} 个，失败 ${result.failed} 个`,
    );

    if (result.failed > 0) {
      console.log('失败的插件 ID:', result.failedIds);
    }

    return result;
  }

  /**
   * 预处理清单数据，修复常见格式问题
   */
  preprocessManifest = (manifest: any): any => {
    // 创建深拷贝避免修改原始数据
    const result = structuredClone(manifest);

    // 确保 author 是对象类型
    if (typeof result.author === 'string') {
      result.author = { name: result.author };
    } else if (!result.author) {
      result.author = { name: '' };
    }

    // 确保基本字段存在
    result.description = result.description || '';
    result.tags = Array.isArray(result.tags) ? result.tags : [];

    // 确保 capabilities 是对象
    if (!result.capabilities) {
      result.capabilities = { prompts: false, resources: false, tools: false };
    }

    // 检查新字段存在性并同步 capabilities 标志
    if (result.tools !== undefined) {
      if (!Array.isArray(result.tools)) {
        result.tools = [];
      }
      if (result.tools.length > 0 && result.capabilities) {
        result.capabilities.tools = true;
      }
    }

    if (result.resources !== undefined) {
      if (!Array.isArray(result.resources)) {
        result.resources = [];
      }
      if (result.resources.length > 0 && result.capabilities) {
        result.capabilities.resources = true;
      }
    }

    if (result.prompts !== undefined) {
      if (!Array.isArray(result.prompts)) {
        result.prompts = [];
      }
      if (result.prompts.length > 0 && result.capabilities) {
        result.capabilities.prompts = true;
      }
    }

    // 处理验证相关字段
    if (result.isValidated !== undefined) {
      result.isValidated = !!result.isValidated;
    }

    if (
      result.validatedAt &&
      typeof result.validatedAt === 'string' &&
      isNaN(Date.parse(result.validatedAt))
    ) {
      result.validatedAt = null;
    }

    // 如果存在 deploymentOptions，确保其结构正确
    if (result.deploymentOptions && Array.isArray(result.deploymentOptions)) {
      result.deploymentOptions = result.deploymentOptions.map((option: any) => {
        if (!option.connection) {
          option.connection = { type: 'stdio' };
        }
        if (option.systemDependencies && !Array.isArray(option.systemDependencies)) {
          option.systemDependencies = [];
        }
        return option;
      });
    }

    return result;
  };

  /**
   * 验证插件
   */
  async validatePlugin(manifest: MCPManifest): Promise<ValidationResult> {
    console.log(`🔍 开始验证: ${manifest.name} (${manifest.identifier})`);

    // 1. 检查环境变量
    // const missingEnvVars = this.checkMissingEnvVars(manifest);
    // if (missingEnvVars.length > 0) {
    //   const error = `缺少环境变量: ${missingEnvVars.join(', ')}`;
    //   console.error(`  ❌ 验证失败: ${error}`);
    //   return {
    //     error,
    //     success: false,
    //   };
    // }

    // 2. 检查安装方式
    const isAutoInstallable = manifest.deploymentOptions?.some(
      (option) =>
        option.installationMethod === 'npm' ||
        (option.installationMethod === 'python' && option.connection?.command === 'uv'),
    );

    if (!isAutoInstallable) {
      const message = `该服务器需要手动安装，跳过验证`;
      console.log(`  ⚠️ ${message}`);
      return {
        message,
        skipped: true,
        success: true,
      };
    }

    try {
      // 3. 执行验证
      const workdir = join(tmpdir(), 'mcp-validator');
      if (existsSync(workdir)) {
        // 清理旧的 mcp-validator 目录，避免缓存问题
        const files: Dirent[] = readdirSync(workdir, { withFileTypes: true });
        files.forEach((file) => {
          if (file.isDirectory() && file.name.startsWith('mcp-validator-')) {
            const path = join(workdir, file.name);
            console.log(`  🧹 清理旧的验证目录: ${path}`);
            rmSync(path, { force: true, recursive: true });
          }
        });
      } else {
        mkdirSync(workdir, { recursive: true });
      }

      // 备份原始环境变量
      const originalEnv = {
        HOME: process.env.HOME,
        USERPROFILE: process.env.USERPROFILE,
        npm_config_cache: process.env.npm_config_cache,
        npm_config_prefix: process.env.npm_config_prefix,
      };

      try {
        // 临时设置环境变量，让 npm 使用我们的工作目录
        process.env.HOME = workdir;
        process.env.USERPROFILE = workdir; // Windows 兼容
        process.env.npm_config_cache = join(workdir, '.npm');
        process.env.npm_config_prefix = join(workdir, '.npm-global');

        const result = await MCPValidator.validate(manifest, {
          onProgress: (progress) => {
            if (progress.step === 'connected' || progress.step === 'complete') {
              console.log(
                `  📊 [${manifest.identifier}] ${progress.step} - ${progress.message || ''}`,
              );
            }
          },
          timeout: 60_000,
        });

        if (result.connected) {
          console.log(`  ✅ [${manifest.identifier}] 连接成功!`);
          return {
            manifest: result.manifest,
            success: true,
          };
        } else {
          console.error(`  ❌ [${manifest.identifier}] 连接失败: ${result.error}`);
          return {
            error: result.error,
            success: false,
          };
        }
      } finally {
        // 恢复原始环境变量
        if (originalEnv.HOME !== undefined) {
          process.env.HOME = originalEnv.HOME;
        } else {
          delete process.env.HOME;
        }
        if (originalEnv.USERPROFILE !== undefined) {
          process.env.USERPROFILE = originalEnv.USERPROFILE;
        } else {
          delete process.env.USERPROFILE;
        }
        if (originalEnv.npm_config_cache !== undefined) {
          process.env.npm_config_cache = originalEnv.npm_config_cache;
        } else {
          delete process.env.npm_config_cache;
        }
        if (originalEnv.npm_config_prefix !== undefined) {
          process.env.npm_config_prefix = originalEnv.npm_config_prefix;
        } else {
          delete process.env.npm_config_prefix;
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知验证错误';
      console.error(`  ❌ [${manifest.identifier}] 验证异常:`, error);
      return {
        error: errorMessage,
        success: false,
      };
    }
  }

  async checkIsDuplicate(url: string) {
    try {
      const res = await this.marketAdmin.plugins.getPluginByGithubUrl(url);
      return !!res;
    } catch {
      return false;
    }
  }
}
