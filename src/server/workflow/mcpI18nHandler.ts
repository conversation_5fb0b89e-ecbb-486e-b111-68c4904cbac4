import { MarketAdmin, MarketSDK } from '@lobehub/market-sdk';
import { LocalizationGenerator, MCPManifest, PluginLocalization } from 'mcp-crawler';

export interface McpI18nRequest {
  identifier: string;
  ownerId?: number;
  targetLocales?: string[];
  version?: string;
}

export interface McpI18nResult {
  error?: string;
  identifier: string;
  success: boolean;
  version: string;
}

export interface LocalizationResult {
  data?: any;
  error?: string;
  locale: string;
  success: boolean;
}

export class MCPi18nHandler {
  private marketAdmin: MarketAdmin;
  private market: MarketSDK;
  constructor() {
    this.marketAdmin = new MarketAdmin({
      apiKey: process.env.MARKET_ADMIN_API_KEY,
      baseURL: process.env.MARKET_BASE_URL,
    });
    this.market = new MarketSDK({ baseURL: process.env.MARKET_BASE_URL });
  }

  /**
   * 检查是否需要生成翻译
   * @param identifier 插件标识符
   * @param versionId 版本号
   * @param locales 目标语言列表
   * @returns Promise<string[]> 需要生成翻译的语言列表
   */
  async checkMissingLocalizations(
    identifier: string,
    versionId: number,
    locales: string[],
  ): Promise<string[]> {
    try {
      // 获取已存在的翻译
      const existingLocalizations = await this.marketAdmin.plugins.getPluginLocalizations(
        identifier,
        versionId,
      );
      const existingLocales = existingLocalizations?.map((l) => l.locale) || [];

      // 返回缺失的语言
      return locales.filter((locale) => !existingLocales.includes(locale));
    } catch (error) {
      console.warn('检查现有翻译时出错，将为所有语言生成翻译:', error);
      return locales; // 如果检查失败，为所有语言生成翻译
    }
  }

  /**
   * 获取插件的 manifest
   * @param identifier 插件标识符
   * @param version 版本号，默认为 'latest'
   * @returns Promise<MCPManifest>
   */
  async fetchPluginManifest(identifier: string, version: string = 'latest'): Promise<MCPManifest> {
    // 获取插件信息
    const plugin = await this.marketAdmin.plugins.getPlugin(identifier);
    if (!plugin) {
      throw new Error(`插件 ${identifier} 不存在`);
    }

    // 获取指定版本的 manifest
    const pluginVersion = plugin.versions?.find((v) => {
      if (version === 'latest') return v.isLatest;

      return v.version === version;
    });

    if (!pluginVersion) {
      throw new Error(`插件 ${identifier} 的版本 ${version} 不存在`);
    }

    return { ...plugin.manifest, overview: { summary: (plugin as any).summary } } as any;
  }

  async generateSingleLocalization(
    manifest: MCPManifest,
    locale: string,
  ): Promise<PluginLocalization | null> {
    console.log(`🌍 为 ${manifest.identifier}@${manifest.version} 生成 ${locale} 翻译...`);

    const localizationGenerator = new LocalizationGenerator({
      openaiModel: 'gpt-4.1-nano',
    });

    const localization = await localizationGenerator.generateLocalization(manifest, locale);

    if (localization) {
      console.log(`✅ 成功为 ${manifest.identifier} 生成 ${locale} 翻译`);
      return localization;
    } else {
      console.warn(`⚠️ 为 ${manifest.identifier} 生成 ${locale} 翻译失败`);
      return null;
    }
  }

  /**
   * 获取插件的默认支持语言列表
   */
  getDefaultTargetLocales(): string[] {
    return [
      'ar',
      'bg-BG',
      'de-DE',
      'en-US',
      'es-ES',
      'fr-FR',
      'ja-JP',
      'ko-KR',
      'pt-BR',
      'ru-RU',
      'tr-TR',
      'zh-CN',
      'zh-TW',
      'vi-VN',
      'fa-IR',
    ];
  }

  /**
   * 保存本地化翻译到数据库
   * @param identifier 插件标识符
   * @param version 版本号
   * @param localizations 本地化数据
   * @returns Promise<{saved: number, failed: number, errors: string[]}>
   */
  async saveLocalizations(
    identifier: string,
    version: string,
    localizations: PluginLocalization[],
  ) {
    return await this.marketAdmin.plugins.importPluginI18n({
      identifier,
      localizations: localizations,
      version,
    });
  }

  /**
   * 验证请求参数
   * @param request 请求对象
   * @returns 验证后的参数
   */
  async validateRequest(request: McpI18nRequest) {
    if (!request.identifier) {
      throw new Error('`identifier` 必须是一个有效的字符串');
    }

    const version = request.version;
    const ownerId = request.ownerId || 1;
    const targetLocales = request.targetLocales || this.getDefaultTargetLocales();

    const item = await this.marketAdmin.plugins.getPlugin(request.identifier);
    const itemVersion = item.versions.find((v) => {
      if (!version) return v.isLatest;

      return v.version === version;
    });

    if (!itemVersion) {
      throw new Error(`${request.identifier} 没有版本 ${version}`);
    }

    return {
      identifier: request.identifier,
      ownerId,
      targetLocales,
      version: itemVersion.version,
      versionId: itemVersion.id,
    };
  }
}

export const mcpI18nHandler = new MCPi18nHandler();
