import type { User } from '@clerk/nextjs/server';
import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
import qs from 'query-string';
import urlJoin from 'url-join';

import { genTable } from '@/database/utils/genTable';
import { clerkApiClient, clerkClient } from '@/server/modules/clerk';
import { SortEnum } from '@/types/query';
import { AllowlistItem, AllowlistQuery, BlocklistItem, BlocklistQuery } from '@/types/restriction';

dayjs.extend(isBetween);

export class clerkServices {
  countBannedUser = async (): Promise<number> => {
    const result = await clerkApiClient.GET(
      qs.stringifyUrl({
        query: {
          banned: true,
        },
        url: '/users/count',
      }) as any,
      {},
    );
    return result.data?.total_count || 0;
  };

  getBannedUserList = async ({
    lastActiveAtSince,
    pageSize = 10,
    current = 1,
    orderBy,
    userIds,
    username,
    emailAddress,
  }: {
    current?: number;
    emailAddress?: string[];
    lastActiveAtSince?: number;
    orderBy?: 'created_at' | 'updated_at' | 'last_active_at' | 'last_sign_in_at';
    pageSize?: number;
    userIds?: string[];
    username?: string[];
  } = {}): Promise<{
    data: User[];
    total: number;
  }> => {
    const { offset, limit } = genTable({ current, pageSize });
    const result = await clerkApiClient.GET(
      qs.stringifyUrl({
        query: {
          banned: true,
          email_address: emailAddress,
          last_active_at_after: lastActiveAtSince,
          limit,
          offset,
          order_by: orderBy,
          user_id: userIds,
          username,
        },
        url: '/users',
      }) as any,
      {},
    );
    return {
      data: result.data,
      total: result.data.length,
    };
  };

  getUserList = async ({
    lastActiveAtSince,
    pageSize = 10,
    current = 1,
    username,
    orderBy,
    userIds,
    emailAddress,
  }: {
    current?: number;
    emailAddress?: string[];
    lastActiveAtSince?: number;
    orderBy?: 'created_at' | 'updated_at' | 'last_active_at' | 'last_sign_in_at';
    pageSize?: number;
    userIds?: string[];
    username?: string[];
  } = {}): Promise<{
    data: User[];
    total: number;
  }> => {
    const { offset, limit } = genTable({ current, pageSize });
    const result = await clerkClient.users.getUserList({
      emailAddress,
      last_active_at_since: lastActiveAtSince,
      limit,
      offset,
      orderBy,
      userId: userIds,
      username,
    });
    return {
      data: result.data,
      total: result.totalCount,
    };
  };

  getUser = async (userId: string): Promise<User> => {
    return clerkClient.users.getUser(userId);
  };

  banUser = async (userId: string): Promise<boolean> => {
    const result = await clerkClient.users.banUser(userId);
    return result.banned;
  };

  unbanUser = async (userId: string): Promise<boolean> => {
    const result = await clerkClient.users.unbanUser(userId);
    return result.banned;
  };

  lockUser = async (userId: string): Promise<boolean> => {
    const result = await clerkClient.users.lockUser(userId);
    return result.locked;
  };

  unlockUser = async (userId: string): Promise<boolean> => {
    const result = await clerkClient.users.unlockUser(userId);
    return result.locked;
  };

  deleteUser = async (userId: string) => {
    console.log('TODO: deleteUser', userId);
    // return clerkClient.users.deleteUser(userId);
  };

  getAllowlist = async ({
    params,
    sorts,
  }: {
    params?: AllowlistQuery;
    sorts?: {
      createdAt?: SortEnum;
    };
  } = {}): Promise<{
    data: AllowlistItem[];
    total: number;
  }> => {
    const { pageSize = 10, current = 1, range, emailOrUsernameOrUserId } = params || {};
    const { createdAt = SortEnum.Des } = sorts || {};

    const { offset, limit } = genTable({ current, pageSize });
    const result = await clerkClient.allowlistIdentifiers.getAllowlistIdentifierList();
    return {
      data: result.data
        .filter((item) => {
          if (emailOrUsernameOrUserId) {
            return (
              item.identifier.toLowerCase().includes(emailOrUsernameOrUserId.toLowerCase()) ||
              item.id.toLowerCase().includes(emailOrUsernameOrUserId.toLowerCase())
            );
          }
          if (range) {
            const [start, end] = [dayjs(range[0]), dayjs(range[1])];
            const createdAt = dayjs(item.createdAt);
            return createdAt.isBetween(start, end);
          }
          return true;
        })
        .sort((a, b) =>
          createdAt === SortEnum.Asc ? a.createdAt - b.createdAt : b.createdAt - a.createdAt,
        )
        .slice(offset, offset + limit),
      total: result.totalCount,
    };
  };

  createAllowlistIdentifier = async (email: string) => {
    return clerkClient.allowlistIdentifiers.createAllowlistIdentifier({
      identifier: email,
      notify: true,
    });
  };

  deleteAllowlistIdentifier = async (id: string) => {
    return clerkClient.allowlistIdentifiers.deleteAllowlistIdentifier(id);
  };

  createBlocklistIdentifier = async (email: string) => {
    const result = await clerkApiClient.POST('/blocklist_identifiers', {
      body: {
        identifier: email,
      },
    });
    return result?.data;
  };

  deleteBlocklistIdentifier = async (id: string) => {
    const result = await clerkApiClient.DELETE(urlJoin('/blocklist_identifiers', id) as any, {});
    return result?.data;
  };

  getBlocklist = async ({
    params,
    sorts,
  }: {
    params?: BlocklistQuery;
    sorts?: {
      createdAt?: SortEnum;
    };
  } = {}): Promise<{
    data: BlocklistItem[];
    total: number;
  }> => {
    const { pageSize = 10, current = 1, range, emailOrUsernameOrUserId } = params || {};
    const { createdAt = SortEnum.Des } = sorts || {};

    const { offset, limit } = genTable({ current, pageSize });
    const res = await clerkApiClient.GET('/blocklist_identifiers');

    if (!res.data) {
      return {
        data: [],
        total: 0,
      };
    }

    const result = res.data;

    return {
      data: result.data
        .map((item) => ({
          createdAt: item.created_at ?? 0,
          id: item.id ?? '',
          identifier: item.identifier ?? '',
          instanceId: item.instance_id,
          updatedAt: item.updated_at ?? 0,
        }))
        .filter((item) => {
          if (emailOrUsernameOrUserId) {
            return (
              item?.identifier?.toLowerCase().includes(emailOrUsernameOrUserId.toLowerCase()) ||
              item?.id?.toLowerCase().includes(emailOrUsernameOrUserId.toLowerCase())
            );
          }
          if (range) {
            const [start, end] = [dayjs(range[0]), dayjs(range[1])];
            const createdAt = dayjs(item.createdAt);
            return createdAt.isBetween(start, end);
          }
          return true;
        })
        .sort((a, b) =>
          createdAt === SortEnum.Asc ? a.createdAt - b.createdAt : b.createdAt - a.createdAt,
        )
        .slice(offset, offset + limit),
      total: result.total_count,
    };
  };

  getRestrictionSettings = async (): Promise<{
    allowlist?: boolean;
    blocklist?: boolean;
  }> => {
    const result = await clerkApiClient.PATCH('/instance/restrictions');
    if (!result.data) {
      return {
        allowlist: false,
        blocklist: false,
      };
    }
    return result.data;
  };

  updateRestrictionSettings = async (settings: { allowlist?: boolean; blocklist?: boolean }) => {
    const result = await clerkApiClient.PATCH('/instance/restrictions', {
      body: settings,
    });

    return result.data;
  };
}
