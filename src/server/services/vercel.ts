import { get } from '@vercel/edge-config';
import url<PERSON>oin from 'url-join';

import { vercelEnv } from '@/envs/vercel';
import { vercelClient } from '@/server/modules/vercel';

export class VercelServices {
  updateEdgeConfig = async (
    key: string,
    value: any,
    operation: 'create' | 'update' | 'upsert' = 'upsert',
  ) => {
    if (!vercelEnv.VERCEL_EDGE_CONFIG_ID) {
      throw new Error('EDGE_CONFIG is not defined');
    }
    try {
      const res = await vercelClient.PATCH(
        urlJoin('edge-config', vercelEnv.VERCEL_EDGE_CONFIG_ID, 'items'),
        {
          items: [
            {
              key,
              operation,
              value,
            },
          ],
        },
      );
      const json = await res.json();
      if (json.status !== 'ok') {
        console.error(json);
        throw new Error('Failed to update edge config');
      }
      return true;
    } catch (error) {
      console.error(error);
      throw new Error('Failed to update edge config');
    }
  };

  getEdgeConfig = async (key: string) => {
    if (!vercelEnv.VERCEL_EDGE_CONFIG_ID) {
      throw new Error('EDGE_CONFIG is not defined');
    }
    try {
      return await get(key);
    } catch (error) {
      console.error(error);
      throw new Error('Failed to get edge config');
    }
  };
}
