import { groupBy, uniqBy } from 'lodash-es';
import qs from 'query-string';

import { liteLLMClient } from '@/server/modules/litellm';
import type { components } from '@/server/modules/litellm/openapi';
import {
  ChannelItem,
  CreateCredentialParams,
  CreateCredentialResponse,
  CreateModelParams,
  CreateModelResponse,
  LiteLLMKeyUpdateParams,
  ModelInfoItem,
  ModelItem,
  UpdateCredentialParams,
  UpdateModelParams,
} from '@/types/litellm';
import { formatApiBase } from '@/utils/format';

// 使用openapi生成的类型
type NewTeamRequest = components['schemas']['NewTeamRequest'];
type UpdateTeamRequest = components['schemas']['UpdateTeamRequest'];
type GenerateKeyRequest = components['schemas']['GenerateKeyRequest'];
type UpdateKeyRequest = components['schemas']['UpdateKeyRequest'];
type TeamMemberAddRequest = components['schemas']['TeamMemberAddRequest'];
type TeamMemberDeleteRequest = components['schemas']['TeamMemberDeleteRequest'];
type LiteLLM_TeamTable = components['schemas']['LiteLLM_TeamTable'];
type GenerateKeyResponse = components['schemas']['GenerateKeyResponse'];
type TeamModelAddRequest = components['schemas']['TeamModelAddRequest'];
type TeamModelRemoveRequest = components['schemas']['TeamModelRemoveRequest'];

export class liteLLMServices {
  getModels = async (): Promise<ModelItem[]> => {
    const res: any = await liteLLMClient.GET('/models');
    return res.data?.data?.sort((a: ModelItem, b: ModelItem) => a.id.localeCompare(b.id));
  };

  getModelInfos = async (): Promise<ModelInfoItem[]> => {
    const res: any = await liteLLMClient.GET('/model/info');
    return res.data?.data?.sort((a: ModelInfoItem, b: ModelInfoItem) =>
      a.model_name.localeCompare(b.model_name),
    );
  };

  updateBudgetKey = async (key: string, params: LiteLLMKeyUpdateParams): Promise<void> => {
    const res = await liteLLMClient.POST('/key/update', {
      body: { key, ...params },
    });

    if (res.error) {
      if ('error' in res.error) throw res.error.error;

      throw res.error;
    }
  };

  getModelGroups = async (): Promise<{ [key: string]: ModelInfoItem[] }> => {
    const data = await this.getModelInfos();
    return groupBy(data, 'model_name');
  };

  getModelInfoByName = async (model_name: string): Promise<ModelInfoItem[]> => {
    const infos = await this.getModelInfos();
    return infos.filter((info) => info.model_name === model_name);
  };

  getModelInfoById = async (litellm_model_id: string): Promise<ModelInfoItem> => {
    const res: any = await liteLLMClient.GET(
      qs.stringifyUrl({ query: { litellm_model_id }, url: '/model/info' }) as '/model/info',
    );
    return res.data?.data;
  };

  getChannels = async (): Promise<ChannelItem[]> => {
    const infos = await this.getModelInfos();
    const channels: any = {};
    infos.forEach((info) => {
      const isChannel = !!info.litellm_params.api_base;
      const name = isChannel
        ? formatApiBase(info.litellm_params.api_base)
        : info.model_info.litellm_provider;
      if (!channels[name]) {
        channels[name] = {
          api_base: info.litellm_params.api_base,
          models: [info],
          name,
        };
      } else {
        channels[name].models.push(info);
      }
    });

    return Object.values(channels)
      .map((item: any) => ({ ...item, models: uniqBy(item.models, 'model_name') }))
      .sort((a: any, b: any) => b?.models?.length - a?.models?.length) as ChannelItem[];
  };

  // ==================== Team Management Methods ====================

  // 创建team
  createTeam = async (params: NewTeamRequest): Promise<LiteLLM_TeamTable> => {
    const res = await liteLLMClient.POST('/team/new', {
      body: params,
    });

    if (res.error) {
      if ('error' in res.error) throw res.error.error;
      throw res.error;
    }

    return res.data as LiteLLM_TeamTable;
  };

  // 更新team信息
  updateTeam = async (params: UpdateTeamRequest): Promise<LiteLLM_TeamTable> => {
    const res = await liteLLMClient.POST('/team/update', {
      body: params,
    });

    if (res.error) {
      if ('error' in res.error) throw res.error.error;
      throw res.error;
    }

    return res.data as LiteLLM_TeamTable;
  };

  // 获取team信息
  getTeamInfo = async (
    teamId: string,
  ): Promise<{
    keys: string[];
    team_id: string;
    team_info: LiteLLM_TeamTable;
    team_memberships: [];
  }> => {
    const res = await liteLLMClient.GET('/team/info', {
      params: {
        query: {
          team_id: teamId,
        },
      },
    });

    if (res.error) {
      if ('error' in res.error) throw res.error.error;
      throw res.error;
    }

    return res.data as any;
  };

  // 删除team
  deleteTeam = async (teamIds: string[]): Promise<void> => {
    const res = await liteLLMClient.POST('/team/delete', {
      body: {
        team_ids: teamIds,
      },
    });

    if (res.error) {
      if ('error' in res.error) throw res.error.error;
      throw res.error;
    }
  };

  // 向team中新增用户
  addTeamUser = async (params: TeamMemberAddRequest): Promise<void> => {
    const res = await liteLLMClient.POST('/team/member_add', {
      body: params,
    });

    if (res.error) {
      if ('error' in res.error) throw res.error.error;
      throw res.error;
    }
  };

  // 删除team中的用户
  deleteTeamUser = async (params: TeamMemberDeleteRequest): Promise<void> => {
    const res = await liteLLMClient.POST('/team/member_delete', {
      body: params,
    });

    if (res.error) {
      if ('error' in res.error) throw res.error.error;
      throw res.error;
    }
  };

  // 向team中新增模型
  addTeamModel = async (params: TeamModelAddRequest): Promise<void> => {
    const res = await liteLLMClient.POST('/team/model/add', {
      body: params,
    });

    if (res.error) {
      if ('error' in res.error) throw res.error.error;
      throw res.error;
    }
  };

  // 从team中删除模型
  deleteTeamModel = async (params: TeamModelRemoveRequest): Promise<void> => {
    const res = await liteLLMClient.POST('/team/model/delete', {
      body: params,
    });

    if (res.error) {
      if ('error' in res.error) throw res.error.error;
      throw res.error;
    }
  };

  // ==================== API Key Management Methods ====================

  // 创建API Key
  createApiKey = async (params: GenerateKeyRequest): Promise<GenerateKeyResponse> => {
    const res = await liteLLMClient.POST('/key/generate', {
      body: params,
    });

    if (res.error) {
      if ('error' in res.error) throw res.error.error;
      throw res.error;
    }

    return res.data as GenerateKeyResponse;
  };

  // 更新API Key
  updateApiKey = async (params: UpdateKeyRequest): Promise<void> => {
    const res = await liteLLMClient.POST('/key/update', {
      body: params,
    });

    if (res.error) {
      if ('error' in res.error) throw res.error.error;
      throw res.error;
    }
  };

  // 删除API Key
  deleteApiKey = async (keys: string[]): Promise<void> => {
    const res = await liteLLMClient.POST('/key/delete', {
      body: {
        keys,
      },
    });

    if (res.error) {
      if ('error' in res.error) throw res.error.error;
      throw res.error;
    }
  };

  // 获取API Key信息
  getApiKeyInfo = async (key: string): Promise<any> => {
    const res = await liteLLMClient.GET('/key/info', {
      params: {
        query: {
          key,
        },
      },
    });

    if (res.error) {
      if ('error' in res.error) throw res.error.error;
      throw res.error;
    }

    return res.data;
  };

  // ==================== Credential Management Methods ====================

  // 创建凭证
  createCredential = async (params: CreateCredentialParams): Promise<CreateCredentialResponse> => {
    const requestBody = {
      credential_info: {
        description: params.description || '',
      },
      credential_name: params.credentialName,
      credential_values: {
        api_base: params.credentialValues.api_base,
        api_key: params.credentialValues.api_key,
        api_version: params.credentialValues.api_version,
      },
    };

    const res = await liteLLMClient.POST('/credentials', {
      body: requestBody,
    });

    if (res.error) {
      if ('error' in res.error) throw res.error.error;
      throw res.error;
    }

    return {
      credential_name: params.credentialName,
    };
  };

  // 更新凭证
  updateCredential = async (
    credentialName: string,
    params: UpdateCredentialParams,
  ): Promise<void> => {
    const requestBody = {
      credential_info: {
        description: params.description,
      },
      credential_name: credentialName,
      credential_values: params.credentialValues,
    };

    const res = await liteLLMClient.PATCH('/credentials/{credential_name}', {
      body: requestBody,
      params: {
        path: {
          credential_name: credentialName,
        },
      },
    });

    if (res.error) {
      if ('error' in res.error) throw res.error.error;
      throw res.error;
    }
  };

  // 删除凭证
  deleteCredential = async (credentialName: string): Promise<void> => {
    const res = await liteLLMClient.DELETE('/credentials/{credential_name}', {
      params: {
        path: {
          credential_name: credentialName,
        },
      },
    });

    if (res.error) {
      if ('error' in res.error) throw res.error.error;
      throw res.error;
    }
  };

  // ==================== Model Management Methods ====================

  // 创建模型
  createModel = async (params: CreateModelParams): Promise<CreateModelResponse> => {
    const res = await liteLLMClient.POST('/model/new', {
      body: params,
    });

    if (res.error) {
      if ('error' in res.error) throw res.error.error;
      throw res.error;
    }

    return res.data as CreateModelResponse;
  };

  // 更新模型信息
  updateModel = async (modelId: string, params: UpdateModelParams): Promise<void> => {
    const res = await liteLLMClient.POST('/model/update', {
      body: {
        ...params,
        model_info: {
          ...params.model_info,
          id: modelId,
        },
      },
    });

    if (res.error) {
      if ('error' in res.error) throw res.error.error;
      throw res.error;
    }
  };

  // 删除模型
  deleteModel = async (modelId: string): Promise<void> => {
    const res = await liteLLMClient.POST('/model/delete', {
      body: {
        id: modelId,
      },
    });

    if (res.error) {
      if ('error' in res.error) throw res.error.error;
      throw res.error;
    }
  };
}
