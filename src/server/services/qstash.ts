import { Client } from '@upstash/qstash';

import { checkQStashEnv, qstashEnv } from '@/envs/qstash';

/**
 * QStash 服务
 *
 * @description 封装 QStash 客户端，提供任务发布和验证功能
 */
export class QStashService {
  private client: Client | null = null;

  private getClient(): Client {
    if (!checkQStashEnv()) {
      throw new Error(
        'QStash environment variables are not properly configured. Please set QSTASH_TOKEN, QSTASH_CURRENT_SIGNING_KEY, and QSTASH_NEXT_SIGNING_KEY.',
      );
    }

    if (!this.client) {
      this.client = new Client({
        token: qstashEnv.QSTASH_TOKEN!,
      });
    }

    return this.client;
  }

  /**
   * 发布一个任务到 QStash
   *
   * @param taskName - 任务名称，用于在 webhook 中区分
   * @param payload - 任务的载荷
   * @param options - QStash 发布选项，例如延迟、重试等
   * @returns {Promise<{ messageId: string }>} - 返回消息 ID
   */
  async publish<T extends object>({
    taskName,
    payload,
    ...options
  }: {
    delay?: number;
    payload: T;
    retries?: number;
    taskName: string;
  }) {
    const client = this.getClient();

    // 任务将发送到我们的 webhook 接口
    const url = `${process.env.BASE_URL}/api/webhooks/qstash`;

    // 在载荷中加入任务名称，便于分发
    const body = { taskName, ...payload };

    return client.publishJSON({ body, url, ...options });
  }
}

export const qstash = new QStashService();
