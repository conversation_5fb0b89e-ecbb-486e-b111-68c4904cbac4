import { FlowControl } from '@upstash/qstash';
import { Client } from '@upstash/workflow';

/**
 * QStash 服务
 *
 * @description 封装 QStash 客户端，提供任务发布和验证功能
 */
export class WorkflowService {
  private client: Client | null = null;

  private getClient(): Client {
    if (!process.env.QSTASH_TOKEN) {
      throw new Error(
        'QStash environment variables are not properly configured. Please set QSTASH_TOKEN, QSTASH_CURRENT_SIGNING_KEY, and QSTASH_NEXT_SIGNING_KEY.',
      );
    }

    if (!this.client) {
      this.client = new Client({ token: process.env.QSTASH_TOKEN });
    }

    return this.client;
  }

  async trigger<T extends object>(
    taskId: string,
    payload: T,
    options?: {
      delay?: number;
      flowControl?: FlowControl;
      retries?: number;
    },
  ) {
    const client = this.getClient();

    // 任务将发送到我们的 webhook 接口
    const url = `${process.env.BASE_URL}/api/workflows/${taskId}`;

    return client.trigger({ body: payload, url, ...options });
  }
}

export const workflowService = new WorkflowService();
