import { Redis } from '@upstash/redis';

/**
 * Upstash Redis 服务
 * 用于存储已处理的 MCP 插件 URL，实现去重功能
 */
export class RedisService {
  private client: Redis | null = null;
  private readonly PROCESSED_URLS_SET = 'mcp-processed-urls';
  private readonly LAST_UPDATED_KEY = 'mcp-last-updated';

  private getClient(): Redis {
    if (!this.client) {
      this.client = new Redis({
        token: process.env.UPSTASH_REDIS_REST_TOKEN!,
        url: process.env.UPSTASH_REDIS_REST_URL!,
      });
    }

    return this.client;
  }

  /**
   * 添加已处理的URL到集合中
   * @param repoUrls GitHub 仓库 URL 列表
   */
  async addProcessedUrls(repoUrls: string[]): Promise<void> {
    if (repoUrls.length === 0) return;

    const client = this.getClient();
    const normalizedUrls = repoUrls.map((url) => this.normalizeUrl(url));

    // 使用 Redis SET 数据结构批量添加
    await client.sadd(this.PROCESSED_URLS_SET, '', ...normalizedUrls);
  }

  /**
   * 检查URL是否已处理
   * @param repoUrl GitHub 仓库 URL
   * @returns 是否已处理
   */
  async isUrlProcessed(repoUrl: string): Promise<boolean> {
    const client = this.getClient();
    const normalizedUrl = this.normalizeUrl(repoUrl);
    const result = await client.sismember(this.PROCESSED_URLS_SET, normalizedUrl);
    return Boolean(result);
  }

  /**
   * 批量检查哪些URL已经处理过
   * @param repoUrls GitHub 仓库 URL 列表
   * @returns 已处理的URL列表
   */
  async getProcessedUrls(repoUrls: string[]): Promise<string[]> {
    if (repoUrls.length === 0) return [];

    const client = this.getClient();
    const processed: string[] = [];

    // 批量检查每个URL
    for (const url of repoUrls) {
      const normalizedUrl = this.normalizeUrl(url);
      const isProcessed = await client.sismember(this.PROCESSED_URLS_SET, normalizedUrl);
      if (isProcessed) {
        processed.push(url);
      }
    }

    return processed;
  }

  /**
   * 获取新的（未处理的）URL列表
   * @param repoUrls GitHub 仓库 URL 列表
   * @returns 新的URL列表
   */
  async getNewUrls(repoUrls: string[]): Promise<string[]> {
    const processedUrls = await this.getProcessedUrls(repoUrls);
    const processedSet = new Set(processedUrls);
    return repoUrls.filter((url) => !processedSet.has(url));
  }

  /**
   * 获取所有已处理的URL
   * @returns 已处理的URL列表
   */
  async getAllProcessedUrls(): Promise<string[]> {
    const client = this.getClient();
    return await client.smembers(this.PROCESSED_URLS_SET);
  }

  /**
   * 删除已处理的URL
   * @param repoUrls GitHub 仓库 URL 列表
   */
  async removeProcessedUrls(repoUrls: string[]): Promise<void> {
    if (repoUrls.length === 0) return;

    const client = this.getClient();
    const normalizedUrls = repoUrls.map((url) => this.normalizeUrl(url));
    await client.srem(this.PROCESSED_URLS_SET, ...normalizedUrls);
  }

  /**
   * 清空所有已处理的URL
   */
  async clearAllProcessedUrls(): Promise<void> {
    const client = this.getClient();
    await client.del(this.PROCESSED_URLS_SET);
  }

  /**
   * 获取统计信息
   */
  async getStats(): Promise<{
    lastUpdated: Date | null;
    total: number;
  }> {
    const client = this.getClient();
    const total = await client.scard(this.PROCESSED_URLS_SET);
    const lastUpdated = await client.get(this.LAST_UPDATED_KEY);

    return {
      lastUpdated: lastUpdated ? new Date(lastUpdated as string) : null,
      total,
    };
  }

  /**
   * 更新最后更新时间
   */
  async updateLastUpdated(): Promise<void> {
    const client = this.getClient();
    await client.set(this.LAST_UPDATED_KEY, new Date().toISOString());
  }

  /**
   * 标准化URL - 移除尾部斜杠和.git后缀
   * @param url GitHub 仓库 URL
   * @returns 标准化的URL
   */
  private normalizeUrl(url: string): string {
    return url
      .trim()
      .replace(/\.git$/, '')
      .replace(/\/$/, '');
  }
}

// 导出单例实例
export const redisService = new RedisService();
