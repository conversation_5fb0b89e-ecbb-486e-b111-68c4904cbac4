import { lobeConsoleEnv } from '@/envs/lobeconsole';
import { QueryLicenseFeaturesResponse } from '@/types/console';
import type {
  LicenseStatus,
  LicenseValidationResponse,
  LicenseValidationResult,
} from '@/types/license';

// 缓存接口
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

// 内存缓存
const cache = new Map<string, CacheEntry<any>>();

// 缓存工具函数
function getCacheKey(method: string, ...args: any[]): string {
  return `${method}:${JSON.stringify(args)}`;
}

function getFromCache<T>(key: string): T | null {
  const entry = cache.get(key);
  if (!entry) return null;

  if (Date.now() - entry.timestamp > entry.ttl) {
    cache.delete(key);
    return null;
  }

  return entry.data;
}

function setCache<T>(key: string, data: T, ttl: number = 5 * 60 * 1000): void {
  cache.set(key, {
    data,
    timestamp: Date.now(),
    ttl,
  });
}

/**
 * Console 服务类，用于与 lobe-console 进行交互
 */
export class ConsoleService {
  private consoleUrl: string;
  private license: string;

  constructor(consoleUrl: string, license: string) {
    this.consoleUrl = consoleUrl;
    this.license = license;
  }

  /**
   * 验证许可证
   */
  async validateLicense(): Promise<LicenseValidationResult> {
    const cacheKey = getCacheKey('validateLicense', this.consoleUrl, this.license);

    // 尝试从缓存获取
    const cached = getFromCache<LicenseValidationResult>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const url = `${this.consoleUrl}/api/v1/license/${this.license}/validate`;

      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
        },
        method: 'GET',
        // 设置 5 秒超时
        signal: AbortSignal.timeout(5000),
      });

      if (!response.ok) {
        const result = {
          isValid: false,
          message: `HTTP ${response.status}: ${response.statusText}`,
          status: 'error' as LicenseStatus,
        };
        // 错误结果缓存较短时间（30秒）
        setCache(cacheKey, result, 30 * 1000);
        return result;
      }

      const data: LicenseValidationResponse = await response.json();

      if (!data.success) {
        const result = {
          expiresIn: data.data?.expiresIn,
          isValid: false,
          message: data.data?.message || '验证失败',
          status: data.data?.status || ('error' as LicenseStatus),
        };
        // 失败结果缓存较短时间（30秒）
        setCache(cacheKey, result, 30 * 1000);
        return result;
      }

      const result = {
        expiresIn: data.data.expiresIn,
        isValid: data.data.isValid,
        message: data.data.message,
        status: data.data.status,
      };

      // 成功结果缓存较长时间（5分钟）
      setCache(cacheKey, result, 5 * 60 * 1000);
      return result;
    } catch (error) {
      // 网络错误或超时
      const result = {
        isValid: false,
        message: error instanceof Error ? `验证失败: ${error.message}` : '许可证验证过程中发生未知错误',
        status: 'error' as LicenseStatus,
      };

      // 错误结果缓存很短时间（10秒）
      setCache(cacheKey, result, 10 * 1000);
      return result;
    }
  }

  /**
   * 获取功能开关配置
   */
  async getFeatureFlags(): Promise<{ grantFeatures: string[]; revokeFeatures: string[] } | null> {
    const cacheKey = getCacheKey('getFeatureFlags', this.consoleUrl, this.license);

    // 尝试从缓存获取
    const cached = getFromCache<{ grantFeatures: string[]; revokeFeatures: string[] }>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const response = await fetch(`${this.consoleUrl}/api/v1/${this.license}/features`, {
        headers: {
          'Content-Type': 'application/json',
        },
        method: 'GET',
      });

      if (!response.ok) {
        console.error('Failed to fetch feature flags:', response.statusText);
        return null;
      }

      const { success, data } = (await response.json()) as QueryLicenseFeaturesResponse;

      if (success) {
        const result = {
          grantFeatures: data.grantFeatures.map((feature) => feature.code),
          revokeFeatures: data.revokeFeatures.map((feature) => feature.code),
        };

        // 功能开关缓存较长时间（10分钟）
        setCache(cacheKey, result, 10 * 60 * 1000);
        return result;
      } else {
        console.warn('Invalid feature flags data format:', data);
        return null;
      }
    } catch (error) {
      console.error('Error fetching feature flags:', error);
      return null;
    }
  }
}

/**
 * 创建 Console 服务实例
 */
export function createConsoleService(consoleUrl?: string, license?: string): ConsoleService | null {
  if (!consoleUrl || !license) {
    return null;
  }

  return new ConsoleService(consoleUrl, license);
}

// 缓存的服务实例
let _consoleServiceInstance: ConsoleService | null | undefined;

/**
 * 获取 Console 服务实例（使用缓存和 lobeConsoleEnv）
 */
export function getConsoleService(): ConsoleService | null {
  if (_consoleServiceInstance === undefined) {
    try {
      console.log(lobeConsoleEnv, 'lobeConsoleEnv')

      _consoleServiceInstance = createConsoleService(
        lobeConsoleEnv.LOBE_CONSOLE_URL,
        lobeConsoleEnv.LOBE_LICENSE,
      );
    } catch {
      // 如果环境变量未配置，返回 null
      _consoleServiceInstance = null;
    }
  }
  return _consoleServiceInstance;
}

/**
 * 验证许可证的便捷函数
 */
export async function validateLicense(
  consoleUrl?: string,
  license?: string,
): Promise<LicenseValidationResult> {
  let service: ConsoleService | null;

  if (consoleUrl && license) {
    // 如果提供了参数，创建临时服务实例
    service = createConsoleService(consoleUrl, license);
  } else {
    // 否则使用默认服务实例
    service = getConsoleService();
  }

  if (!service) {
    return {
      isValid: false,
      message: '无效的控制台配置',
      status: 'error' as LicenseStatus,
    };
  }

  return service.validateLicense();
}

/**
 * 获取功能开关的便捷函数
 */
export async function getFeatureFlags(
  consoleUrl?: string,
  license?: string,
): Promise<{
  grantFeatures: string[];
  revokeFeatures: string[];
} | null> {
  let service: ConsoleService | null;

  if (consoleUrl && license) {
    // 如果提供了参数，创建临时服务实例
    service = createConsoleService(consoleUrl, license);
  } else {
    // 否则使用默认服务实例
    service = getConsoleService();
  }

  if (!service) {
    return null;
  }

  return service.getFeatureFlags();
}
