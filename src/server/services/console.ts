import type {
  LicenseStatus,
  LicenseValidationResponse,
  LicenseValidationResult,
} from '@/types/license';

/**
 * Console 服务类，用于与 lobe-console 进行交互
 */
export class ConsoleService {
  private consoleUrl: string;
  private license: string;

  constructor(consoleUrl: string, license: string) {
    this.consoleUrl = consoleUrl;
    this.license = license;
  }

  /**
   * 验证许可证
   */
  async validateLicense(): Promise<LicenseValidationResult> {
    try {
      const url = `${this.consoleUrl}/api/v1/license/${this.license}/validate`;

      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
        },
        method: 'GET',
        // 设置 5 秒超时
        signal: AbortSignal.timeout(5000),
      });

      if (!response.ok) {
        return {
          isValid: false,
          message: `HTTP ${response.status}: ${response.statusText}`,
          status: 'error' as LicenseStatus,
        };
      }

      const data: LicenseValidationResponse = await response.json();

      if (!data.success) {
        return {
          expiresIn: data.data?.expiresIn,
          isValid: false,
          message: data.data?.message || '验证失败',
          status: data.data?.status || ('error' as LicenseStatus),
        };
      }

      return {
        expiresIn: data.data.expiresIn,
        isValid: data.data.isValid,
        message: data.data.message,
        status: data.data.status,
      };
    } catch (error) {
      // 网络错误或超时
      if (error instanceof Error) {
        return {
          isValid: false,
          message: `验证失败: ${error.message}`,
          status: 'error' as LicenseStatus,
        };
      }

      return {
        isValid: false,
        message: '许可证验证过程中发生未知错误',
        status: 'error' as LicenseStatus,
      };
    }
  }

  /**
   * 获取功能开关配置
   */
  async getFeatureFlags(): Promise<{ grantFeatures: string[]; revokeFeatures: string[] } | null> {
    try {
      const response = await fetch(`${this.consoleUrl}/api/v1/${this.license}/features`, {
        headers: {
          'Content-Type': 'application/json',
        },
        method: 'GET',
      });

      if (!response.ok) {
        console.error('Failed to fetch feature flags:', response.statusText);
        return null;
      }

      const data = await response.json();

      // 假设 API 返回的数据格式为 { grantFeatures: string[], revokeFeatures: string[] }
      if (data && typeof data === 'object') {
        return {
          grantFeatures: Array.isArray(data.grantFeatures) ? data.grantFeatures : [],
          revokeFeatures: Array.isArray(data.revokeFeatures) ? data.revokeFeatures : [],
        };
      } else {
        console.warn('Invalid feature flags data format:', data);
        return null;
      }
    } catch (error) {
      console.error('Error fetching feature flags:', error);
      return null;
    }
  }
}

/**
 * 创建 Console 服务实例
 */
export function createConsoleService(consoleUrl?: string, license?: string): ConsoleService | null {
  if (!consoleUrl || !license) {
    return null;
  }

  return new ConsoleService(consoleUrl, license);
}

/**
 * 默认的 Console 服务实例（使用环境变量）
 */
export const consoleService = createConsoleService(
  process.env.LOBE_CONSOLE_URL,
  process.env.LOBE_LICENSE,
);

/**
 * 验证许可证的便捷函数
 */
export async function validateLicense(
  consoleUrl: string,
  license: string,
): Promise<LicenseValidationResult> {
  const service = createConsoleService(consoleUrl, license);
  if (!service) {
    return {
      isValid: false,
      message: '无效的控制台配置',
      status: 'error' as LicenseStatus,
    };
  }

  return service.validateLicense();
}

/**
 * 获取功能开关的便捷函数
 */
export async function getFeatureFlags(
  consoleUrl: string,
  license: string,
): Promise<{ grantFeatures: string[]; revokeFeatures: string[] } | null> {
  const service = createConsoleService(consoleUrl, license);
  if (!service) {
    return null;
  }
  return service.getFeatureFlags();
}
