import { createClerkClient } from '@clerk/nextjs/server';
import createClient from 'openapi-fetch';

import { clerkEnv } from '@/envs/clerk';

import type { paths } from './openapi';

export const clerkClient = createClerkClient({ secretKey: clerkEnv.CHAT_CLERK_SECRET_KEY });

export const clerkApiClient = createClient<paths>({
  baseUrl: 'https://api.clerk.com/v1',
  headers: {
    Authorization: `Bearer ${clerkEnv.CHAT_CLERK_SECRET_KEY}`,
  },
});
