{"components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "description": "Secret key, obtained under \"API Keys\" in the Clerk Dashboard.", "bearerFormat": "sk_<environment>_<secret value>"}}, "schemas": {"JWKS": {"type": "object", "additionalProperties": false, "properties": {"keys": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"use": {"type": "string"}, "kty": {"type": "string"}, "kid": {"type": "string"}, "alg": {"type": "string"}, "n": {"type": "string"}, "e": {"type": "string"}, "x": {"type": "string"}, "crv": {"type": "string"}}}}}}, "Session": {"type": "object", "additionalProperties": false, "properties": {"object": {"type": "string", "description": "String representing the object's type. Objects of the same type share the same value.\n", "enum": ["session"]}, "id": {"type": "string"}, "user_id": {"type": "string"}, "client_id": {"type": "string"}, "actor": {"type": "object", "nullable": true}, "status": {"type": "string", "enum": ["active", "revoked", "ended", "expired", "removed", "abandoned", "replaced"]}, "last_active_organization_id": {"type": "string", "nullable": true}, "last_active_at": {"type": "integer"}, "latest_activity": {"type": "object", "nullable": true, "required": ["id", "object"], "properties": {"object": {"type": "string"}, "id": {"type": "string"}, "device_type": {"type": "string"}, "is_mobile": {"type": "boolean"}, "browser_name": {"type": "string"}, "browser_version": {"type": "string"}, "ip_address": {"type": "string"}, "city": {"type": "string"}, "country": {"type": "string"}}}, "expire_at": {"type": "integer"}, "abandon_at": {"type": "integer"}, "updated_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of last update.\n"}, "created_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of creation.\n"}}, "required": ["object", "id", "user_id", "client_id", "status", "last_active_at", "expire_at", "abandon_at", "updated_at", "created_at"]}, "Client": {"type": "object", "additionalProperties": false, "properties": {"object": {"type": "string", "description": "String representing the object's type. Objects of the same type share the same value.\n", "enum": ["client"]}, "id": {"type": "string", "description": "String representing the identifier of the session.\n"}, "session_ids": {"type": "array", "items": {"type": "string"}}, "sessions": {"type": "array", "items": {"$ref": "#/components/schemas/Session"}}, "sign_in_id": {"type": "string", "nullable": true}, "sign_up_id": {"type": "string", "nullable": true}, "last_active_session_id": {"nullable": true, "type": "string", "description": "Last active session_id.\n"}, "updated_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of last update.\n"}, "created_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of creation.\n"}}, "required": ["object", "id", "session_ids", "sessions", "sign_in_id", "sign_up_id", "last_active_session_id", "updated_at", "created_at"]}, "ClerkError": {"type": "object", "properties": {"message": {"type": "string"}, "long_message": {"type": "string"}, "code": {"type": "string"}, "meta": {"type": "object"}, "clerk_trace_id": {"type": "string"}}, "required": ["message", "long_message", "code"]}, "ClerkErrors": {"type": "object", "properties": {"errors": {"type": "array", "items": {"$ref": "#/components/schemas/ClerkError"}}, "meta": {"type": "object"}}, "required": ["errors"]}, "OTP": {"type": "object", "additionalProperties": false, "properties": {"status": {"type": "string", "enum": ["unverified", "verified", "failed", "expired"]}, "strategy": {"type": "string", "enum": ["phone_code", "email_code", "reset_password_email_code"]}, "attempts": {"type": "integer"}, "expire_at": {"type": "integer"}}, "required": ["status", "attempts", "strategy", "expire_at"]}, "Admin": {"type": "object", "additionalProperties": false, "properties": {"status": {"type": "string", "enum": ["verified"]}, "strategy": {"type": "string", "enum": ["admin"]}, "attempts": {"type": "integer", "nullable": true}, "expire_at": {"type": "integer", "nullable": true}}, "required": ["status", "strategy"]}, "FromOAuth": {"type": "object", "additionalProperties": false, "properties": {"status": {"type": "string", "enum": ["unverified", "verified"]}, "strategy": {"type": "string", "pattern": "^from_oauth_(?:(?:token_)|(?:custom_))?[a-z]+$"}, "error": {"type": "object", "nullable": true, "oneOf": [{"$ref": "#/components/schemas/ClerkError"}]}, "expire_at": {"type": "integer", "nullable": true}, "attempts": {"type": "integer", "nullable": true}}, "required": ["status", "strategy", "expire_at"]}, "IdentificationLink": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string", "enum": ["oauth_google", "oauth_mock", "saml"]}, "id": {"type": "string"}}, "required": ["type", "id"]}, "EmailAddress": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string"}, "object": {"type": "string", "description": "String representing the object's type. Objects of the same type share the same value.\n", "enum": ["email_address"]}, "email_address": {"type": "string"}, "reserved": {"type": "boolean"}, "verification": {"type": "object", "nullable": true, "oneOf": [{"$ref": "#/components/schemas/OTP"}, {"$ref": "#/components/schemas/Admin"}, {"$ref": "#/components/schemas/FromOAuth"}]}, "linked_to": {"type": "array", "items": {"$ref": "#/components/schemas/IdentificationLink"}}, "matches_sso_connection": {"description": "Indicates whether this email address domain matches an active enterprise connection.\n", "type": "boolean"}, "created_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of creation\n"}, "updated_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of creation\n"}}, "required": ["object", "email_address", "verification", "linked_to", "reserved", "created_at", "updated_at"]}, "DeletedObject": {"type": "object", "additionalProperties": false, "properties": {"object": {"type": "string"}, "id": {"type": "string"}, "slug": {"type": "string"}, "deleted": {"type": "boolean"}}, "required": ["object", "deleted"]}, "PhoneNumber": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string"}, "object": {"type": "string", "description": "String representing the object's type. Objects of the same type share the same value.\n", "enum": ["phone_number"]}, "phone_number": {"type": "string"}, "reserved_for_second_factor": {"type": "boolean"}, "default_second_factor": {"type": "boolean"}, "reserved": {"type": "boolean"}, "verification": {"type": "object", "nullable": true, "oneOf": [{"$ref": "#/components/schemas/OTP"}, {"$ref": "#/components/schemas/Admin"}]}, "linked_to": {"type": "array", "items": {"$ref": "#/components/schemas/IdentificationLink"}}, "backup_codes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "created_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of creation\n"}, "updated_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of creation\n"}}, "required": ["object", "phone_number", "verification", "linked_to", "reserved", "created_at", "updated_at"]}, "Template": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string"}, "object": {"type": "string", "description": "String representing the object's type. Objects of the same type share the same value.\n", "enum": ["template"]}, "instance_id": {"nullable": true, "type": "string", "description": "the id of the instance the template belongs to"}, "resource_type": {"type": "string", "description": "whether this is a system (default) or user overridden) template"}, "template_type": {"type": "string", "description": "whether this is an email or SMS template"}, "name": {"type": "string", "description": "user-friendly name of the template"}, "slug": {"type": "string", "description": "machine-friendly name of the template"}, "position": {"type": "integer", "description": "position with the listing of templates"}, "can_revert": {"type": "boolean", "description": "whether this template can be reverted to the corresponding system default"}, "can_delete": {"type": "boolean", "description": "whether this template can be deleted"}, "can_toggle": {"type": "boolean", "description": "whether this template can be enabled or disabled, true only for notification SMS templates"}, "subject": {"type": "string", "description": "email subject", "nullable": true}, "markup": {"type": "string", "description": "the editor markup used to generate the body of the template"}, "body": {"type": "string", "description": "the template body before variable interpolation"}, "available_variables": {"type": "array", "description": "list of variables that are available for use in the template body", "items": {"type": "string"}}, "required_variables": {"type": "array", "description": "list of variables that must be contained in the template body", "items": {"type": "string"}}, "from_email_name": {"type": "string"}, "reply_to_email_name": {"type": "string"}, "delivered_by_clerk": {"type": "boolean"}, "enabled": {"type": "boolean"}, "updated_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of last update.\n"}, "created_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of creation.\n"}}}, "Web3Signature": {"type": "object", "additionalProperties": false, "properties": {"status": {"type": "string", "enum": ["unverified", "verified", "failed", "expired"]}, "strategy": {"type": "string", "enum": ["web3_metamask_signature", "web3_coinbase_wallet_signature", "web3_okx_wallet_signature"]}, "nonce": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "attempts": {"type": "integer", "nullable": true}, "expire_at": {"type": "integer", "nullable": true}}, "required": ["status", "strategy"]}, "Web3Wallet": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string"}, "object": {"type": "string", "description": "String representing the object's type. Objects of the same type share the same value.\n", "enum": ["web3_wallet"]}, "web3_wallet": {"type": "string"}, "verification": {"type": "object", "nullable": true, "oneOf": [{"$ref": "#/components/schemas/Web3Signature"}, {"$ref": "#/components/schemas/Admin"}]}, "created_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of creation\n"}, "updated_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of creation\n"}}, "required": ["object", "web3_wallet", "verification", "created_at", "updated_at"]}, "Passkey": {"type": "object", "additionalProperties": false, "properties": {"status": {"type": "string", "enum": ["verified"]}, "strategy": {"type": "string", "enum": ["passkey"]}, "nonce": {"type": "string", "enum": ["nonce"]}, "attempts": {"type": "integer", "nullable": true}, "expire_at": {"type": "integer", "nullable": true}}, "required": ["status", "strategy"]}, "schemas-Passkey": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string"}, "object": {"type": "string", "description": "String representing the object's type. Objects of the same type share the same value.\n", "enum": ["passkey"]}, "name": {"type": "string"}, "last_used_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of when the passkey was last used.\n"}, "verification": {"type": "object", "nullable": true, "oneOf": [{"$ref": "#/components/schemas/Passkey"}]}}, "required": ["object", "name", "last_used_at", "verification"]}, "SAML": {"type": "object", "additionalProperties": false, "properties": {"status": {"type": "string", "enum": ["unverified", "verified", "failed", "expired", "transferable"]}, "strategy": {"type": "string", "enum": ["saml"]}, "external_verification_redirect_url": {"nullable": true, "type": "string"}, "error": {"nullable": true, "type": "object", "oneOf": [{"$ref": "#/components/schemas/ClerkError"}]}, "expire_at": {"type": "integer"}, "attempts": {"type": "integer", "nullable": true}}, "required": ["status", "strategy", "external_verification_redirect_url", "expire_at"]}, "Ticket": {"type": "object", "additionalProperties": false, "properties": {"status": {"type": "string", "enum": ["unverified", "verified", "expired"]}, "strategy": {"type": "string", "enum": ["ticket"]}, "attempts": {"type": "integer", "nullable": true}, "expire_at": {"type": "integer", "nullable": true}}, "required": ["status", "strategy"]}, "SAMLConnection": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "domain": {"type": "string"}, "active": {"type": "boolean"}, "provider": {"type": "string"}, "sync_user_attributes": {"type": "boolean"}, "allow_subdomains": {"type": "boolean"}, "allow_idp_initiated": {"type": "boolean"}, "disable_additional_identifications": {"type": "boolean"}, "created_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of creation.\n"}, "updated_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of last update.\n"}}, "required": ["id", "name", "domain", "active", "provider", "sync_user_attributes", "created_at", "updated_at"]}, "SAMLAccount": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string"}, "object": {"type": "string", "description": "String representing the object's type. Objects of the same type share the same value.\n", "enum": ["saml_account"]}, "provider": {"type": "string"}, "active": {"type": "boolean"}, "email_address": {"type": "string"}, "first_name": {"type": "string", "nullable": true}, "last_name": {"type": "string", "nullable": true}, "provider_user_id": {"type": "string", "nullable": true}, "public_metadata": {"type": "object", "additionalProperties": true}, "verification": {"type": "object", "nullable": true, "oneOf": [{"$ref": "#/components/schemas/SAML"}, {"$ref": "#/components/schemas/Ticket"}]}, "saml_connection": {"type": "object", "nullable": true, "oneOf": [{"$ref": "#/components/schemas/SAMLConnection"}]}}, "required": ["id", "object", "provider", "active", "email_address", "verification"]}, "User": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string"}, "object": {"type": "string", "description": "String representing the object's type. Objects of the same type share the same value.\n", "enum": ["user"]}, "external_id": {"nullable": true, "type": "string"}, "primary_email_address_id": {"nullable": true, "type": "string"}, "primary_phone_number_id": {"nullable": true, "type": "string"}, "primary_web3_wallet_id": {"nullable": true, "type": "string"}, "username": {"nullable": true, "type": "string"}, "first_name": {"nullable": true, "type": "string"}, "last_name": {"nullable": true, "type": "string"}, "profile_image_url": {"type": "string", "deprecated": true}, "image_url": {"type": "string"}, "has_image": {"type": "boolean"}, "public_metadata": {"type": "object", "additionalProperties": true}, "private_metadata": {"nullable": true, "type": "object", "additionalProperties": true}, "unsafe_metadata": {"type": "object", "additionalProperties": true}, "email_addresses": {"type": "array", "items": {"$ref": "#/components/schemas/EmailAddress"}}, "phone_numbers": {"type": "array", "items": {"$ref": "#/components/schemas/PhoneNumber"}}, "web3_wallets": {"type": "array", "items": {"$ref": "#/components/schemas/Web3Wallet"}}, "passkeys": {"type": "array", "items": {"$ref": "#/components/schemas/schemas-Passkey"}}, "password_enabled": {"type": "boolean"}, "two_factor_enabled": {"type": "boolean"}, "totp_enabled": {"type": "boolean"}, "backup_code_enabled": {"type": "boolean"}, "mfa_enabled_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of when MFA was last enabled for this user. It should be noted that this field is not nullified if M<PERSON> is disabled.\n", "nullable": true}, "mfa_disabled_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of when MFA was last disabled for this user. It should be noted that this field is not nullified if MFA is enabled again.\n", "nullable": true}, "external_accounts": {"type": "array", "items": {"type": "object"}}, "saml_accounts": {"type": "array", "items": {"$ref": "#/components/schemas/SAMLAccount"}}, "last_sign_in_at": {"type": "integer", "format": "int64", "nullable": true, "description": "Unix timestamp of last sign-in.\n"}, "banned": {"type": "boolean", "description": "Flag to denote whether user is banned or not.\n"}, "locked": {"type": "boolean", "description": "Flag to denote whether user is currently locked, i.e. restricted from signing in or not.\n"}, "lockout_expires_in_seconds": {"type": "integer", "format": "int64", "nullable": true, "description": "The number of seconds remaining until the lockout period expires for a locked user. A null value for a locked user indicates that lockout never expires.\n"}, "verification_attempts_remaining": {"type": "integer", "format": "int64", "nullable": true, "description": "The number of verification attempts remaining until the user is locked. Null if account lockout is not enabled. Note: if a user is locked explicitly via the Backend API, they may still have verification attempts remaining.\n"}, "updated_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of last update.\n"}, "created_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of creation.\n"}, "delete_self_enabled": {"type": "boolean", "description": "If enabled, user can delete themselves via FAPI.\n"}, "create_organization_enabled": {"type": "boolean", "description": "If enabled, user can create organizations via FAPI.\n"}, "create_organizations_limit": {"type": "integer", "description": "The maximum number of organizations the user can create. 0 means unlimited.\n", "nullable": true}, "last_active_at": {"type": "integer", "format": "int64", "nullable": true, "description": "Unix timestamp of the latest session activity, with day precision.\n", "example": *************}, "legal_accepted_at": {"type": "integer", "format": "int64", "nullable": true, "description": "Unix timestamp of when the user accepted the legal requirements.\n", "example": *************}}}, "PasswordHasher": {"type": "string", "description": "The hashing algorithm that was used to generate the password digest.\n\nThe algorithms we support at the moment are [`bcrypt`](https://en.wikipedia.org/wiki/Bcrypt), [`bcrypt_sha256_django`](https://docs.djangoproject.com/en/4.0/topics/auth/passwords/), [`md5`](https://en.wikipedia.org/wiki/MD5), `pbkdf2_sha1`, `pbkdf2_sha256`, [`pbkdf2_sha256_django`](https://docs.djangoproject.com/en/4.0/topics/auth/passwords/),\n[`phpass`](https://www.openwall.com/phpass/), [`scrypt_firebase`](https://firebaseopensource.com/projects/firebase/scrypt/),\n[`scrypt_werkzeug`](https://werkzeug.palletsprojects.com/en/3.0.x/utils/#werkzeug.security.generate_password_hash), [`sha256`](https://en.wikipedia.org/wiki/SHA-2),\nand the [`argon2`](https://argon2.online/) variants: `argon2i` and `argon2id`.\n\nEach of the supported hashers expects the incoming digest to be in a particular format. See the [Clerk docs](https://clerk.com/docs/references/backend/user/create-user) for more information."}, "TotalCount": {"type": "object", "additionalProperties": false, "properties": {"object": {"type": "string", "description": "String representing the object's type. Objects of the same type share the same value.\n", "enum": ["total_count"]}, "total_count": {"type": "integer", "format": "int64"}}, "required": ["object", "total_count"]}, "Organization": {"type": "object", "properties": {"object": {"type": "string", "enum": ["organization"]}, "id": {"type": "string"}, "name": {"type": "string"}, "slug": {"type": "string"}, "members_count": {"type": "integer", "nullable": true}, "max_allowed_memberships": {"type": "integer"}, "admin_delete_enabled": {"type": "boolean"}, "public_metadata": {"type": "object", "additionalProperties": true}, "private_metadata": {"type": "object", "additionalProperties": true}, "created_by": {"type": "string", "nullable": true}, "created_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of creation.\n"}, "updated_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of last update.\n"}}, "required": ["object", "id", "name", "slug", "max_allowed_memberships", "public_metadata", "private_metadata", "created_at", "updated_at"]}, "OrganizationMembership": {"description": "Hello world", "type": "object", "properties": {"id": {"type": "string"}, "object": {"type": "string", "description": "String representing the object's type. Objects of the same type share the same value.\n", "enum": ["organization_membership"]}, "role": {"type": "string"}, "role_name": {"type": "string"}, "permissions": {"type": "array", "items": {"type": "string"}}, "public_metadata": {"type": "object", "description": "Metadata saved on the organization membership, accessible from both Frontend and Backend APIs", "additionalProperties": true}, "private_metadata": {"type": "object", "description": "Metadata saved on the organization membership, accessible only from the Backend API", "additionalProperties": true}, "organization": {"type": "object", "nullable": false, "allOf": [{"$ref": "#/components/schemas/Organization"}]}, "public_user_data": {"type": "object", "additionalProperties": false, "properties": {"user_id": {"type": "string", "nullable": false}, "first_name": {"type": "string", "nullable": true}, "last_name": {"type": "string", "nullable": true}, "profile_image_url": {"type": "string", "nullable": true, "deprecated": true}, "image_url": {"type": "string"}, "has_image": {"type": "boolean"}, "identifier": {"type": "string", "nullable": true}}}, "created_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of creation."}, "updated_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of last update."}}}, "OrganizationMemberships": {"type": "object", "additionalProperties": false, "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/OrganizationMembership"}}, "total_count": {"type": "integer", "format": "int64", "description": "Total number of organization memberships\n"}}, "required": ["data", "total_count"]}, "OrganizationInvitationWithPublicOrganizationData": {"description": "An organization invitation with public organization data populated", "type": "object", "properties": {"id": {"type": "string"}, "object": {"type": "string", "description": "String representing the object's type. Objects of the same type share the same value.\n", "enum": ["organization_invitation"]}, "email_address": {"type": "string"}, "role": {"type": "string"}, "role_name": {"type": "string"}, "organization_id": {"type": "string"}, "status": {"type": "string"}, "public_metadata": {"type": "object", "additionalProperties": true}, "private_metadata": {"type": "object", "additionalProperties": true}, "public_organization_data": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "slug": {"type": "string"}, "image_url": {"type": "string"}, "has_image": {"type": "boolean"}}}, "created_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of creation."}, "updated_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of last update."}}}, "OrganizationInvitationsWithPublicOrganizationData": {"type": "object", "additionalProperties": false, "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/OrganizationInvitationWithPublicOrganizationData"}}, "total_count": {"type": "integer", "format": "int64", "description": "Total number of organization invitations\n"}}, "required": ["data", "total_count"]}, "TOTP": {"type": "object", "additionalProperties": true, "properties": {"object": {"type": "string"}, "id": {"type": "string"}, "secret": {"type": "string", "nullable": true}, "uri": {"type": "string", "nullable": true}, "verified": {"type": "boolean"}, "backup_codes": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "required": ["object", "id", "secret", "uri", "verified"]}, "Invitation": {"type": "object", "additionalProperties": false, "properties": {"object": {"type": "string", "enum": ["invitation"]}, "id": {"type": "string"}, "email_address": {"type": "string", "format": "email"}, "public_metadata": {"type": "object", "additionalProperties": true}, "revoked": {"type": "boolean", "example": false}, "status": {"type": "string", "enum": ["pending", "accepted", "revoked", "expired"], "example": "pending"}, "url": {"type": "string", "nullable": true}, "expires_at": {"type": "integer", "format": "int64", "nullable": true, "description": "Unix timestamp of expiration.\n"}, "created_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of creation.\n"}, "updated_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of last update.\n"}}, "required": ["object", "id", "email_address", "status", "created_at", "updated_at"]}, "AllowlistIdentifier": {"type": "object", "additionalProperties": false, "properties": {"object": {"type": "string", "description": "String representing the object's type. Objects of the same type share the same value.\n", "enum": ["allowlist_identifier"]}, "id": {"type": "string"}, "invitation_id": {"type": "string"}, "identifier": {"type": "string", "description": "An email address or a phone number.\n"}, "identifier_type": {"type": "string", "enum": ["email_address", "phone_number", "web3_wallet"]}, "instance_id": {"type": "string"}, "created_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of creation\n"}, "updated_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of last update.\n"}}}, "BlocklistIdentifier": {"type": "object", "additionalProperties": false, "properties": {"object": {"type": "string", "description": "String representing the object's type. Objects of the same type share the same value.\n", "enum": ["blocklist_identifier"]}, "id": {"type": "string"}, "identifier": {"type": "string", "description": "An email address, email domain, phone number or web3 wallet.\n"}, "identifier_type": {"type": "string", "enum": ["email_address", "phone_number", "web3_wallet"]}, "instance_id": {"type": "string"}, "created_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of creation\n"}, "updated_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of last update.\n"}}}, "BlocklistIdentifiers": {"type": "object", "additionalProperties": false, "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/BlocklistIdentifier"}}, "total_count": {"type": "integer", "format": "int64", "description": "Total number of blocklist identifiers\n"}}, "required": ["data", "total_count"]}, "ActorToken": {"type": "object", "additionalProperties": false, "properties": {"object": {"type": "string", "enum": ["actor_token"]}, "id": {"type": "string"}, "status": {"type": "string", "enum": ["pending", "accepted", "revoked"]}, "user_id": {"type": "string"}, "actor": {"type": "object"}, "token": {"type": "string", "nullable": true}, "url": {"type": "string", "nullable": true}, "created_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of creation.\n"}, "updated_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of last update.\n"}}, "required": ["object", "id", "user_id", "actor", "status", "created_at", "updated_at"]}, "CNameTarget": {"type": "object", "additionalProperties": false, "properties": {"host": {"type": "string"}, "value": {"type": "string"}, "required": {"description": "Denotes whether this CNAME target is required to be set in order for the domain to be considered deployed.\n", "type": "boolean"}}, "required": ["host", "value", "required"]}, "Domain": {"type": "object", "properties": {"object": {"type": "string", "enum": ["domain"]}, "id": {"type": "string"}, "name": {"type": "string"}, "is_satellite": {"type": "boolean"}, "frontend_api_url": {"type": "string"}, "accounts_portal_url": {"type": "string", "nullable": true, "description": "Null for satellite domains.\n"}, "proxy_url": {"type": "string", "nullable": true}, "development_origin": {"type": "string"}, "cname_targets": {"type": "array", "items": {"$ref": "#/components/schemas/CNameTarget"}, "nullable": true}}, "required": ["object", "id", "name", "is_satellite", "frontend_api_url", "development_origin"]}, "Domains": {"type": "object", "additionalProperties": false, "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Domain"}}, "total_count": {"type": "integer", "format": "int64", "description": "Total number of domains\n"}}, "required": ["data", "total_count"]}, "Instance": {"type": "object", "properties": {"object": {"type": "string", "description": "String representing the object's type. Objects of the same type share the same value.", "enum": ["instance"]}, "id": {"type": "string"}, "environment_type": {"type": "string", "example": "development"}, "allowed_origins": {"type": "array", "nullable": true, "items": {"type": "string"}, "example": ["http://localhost:3000", "https://some-domain"]}}}, "InstanceRestrictions": {"type": "object", "properties": {"object": {"type": "string", "description": "String representing the object's type. Objects of the same type share the same value.", "enum": ["instance_restrictions"]}, "allowlist": {"type": "boolean"}, "blocklist": {"type": "boolean"}, "block_email_subaddresses": {"type": "boolean"}, "ignore_dots_for_gmail_addresses": {"type": "boolean"}}}, "OrganizationSettings": {"type": "object", "additionalProperties": false, "properties": {"object": {"type": "string", "description": "String representing the object's type. Objects of the same type share the same value.", "enum": ["organization_settings"]}, "enabled": {"type": "boolean"}, "max_allowed_memberships": {"type": "integer"}, "max_allowed_roles": {"type": "integer"}, "max_allowed_permissions": {"type": "integer"}, "creator_role": {"type": "string", "description": "The role key that a user will be assigned after creating an organization."}, "admin_delete_enabled": {"type": "boolean", "description": "The default for whether an admin can delete an organization with the Frontend API."}, "domains_enabled": {"type": "boolean"}, "domains_enrollment_modes": {"type": "array", "items": {"type": "string", "enum": ["manual_invitation", "automatic_invitation", "automatic_suggestion"]}}, "domains_default_role": {"type": "string", "description": "The role key that it will be used in order to create an organization invitation or suggestion."}}, "required": ["object", "enabled", "max_allowed_memberships", "creator_role", "admin_delete_enabled", "domains_enabled", "domains_enrollment_modes", "domains_default_role"]}, "SvixURL": {"type": "object", "additionalProperties": false, "properties": {"svix_url": {"type": "string"}}, "required": ["svix_url"]}, "JWTTemplate": {"type": "object", "additionalProperties": false, "properties": {"object": {"type": "string", "enum": ["jwt_template"]}, "id": {"type": "string"}, "name": {"type": "string"}, "claims": {"type": "object"}, "lifetime": {"type": "integer"}, "allowed_clock_skew": {"type": "integer"}, "custom_signing_key": {"type": "boolean"}, "signing_algorithm": {"type": "string"}, "created_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of creation.\n"}, "updated_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of last update.\n"}}, "required": ["object", "id", "name", "claims", "lifetime", "allowed_clock_skew", "created_at", "updated_at"]}, "Organizations": {"type": "object", "additionalProperties": false, "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Organization"}}, "total_count": {"type": "integer", "format": "int64", "description": "Total number of organizations\n"}}, "required": ["data", "total_count"]}, "OrganizationWithLogo": {"type": "object", "allOf": [{"$ref": "#/components/schemas/Organization"}, {"type": "object", "properties": {"logo_url": {"type": "string", "deprecated": true}, "image_url": {"type": "string"}, "has_image": {"type": "boolean"}}, "required": ["image_url"]}]}, "OrganizationInvitation": {"description": "An organization invitation", "type": "object", "properties": {"id": {"type": "string"}, "object": {"type": "string", "description": "String representing the object's type. Objects of the same type share the same value.\n", "enum": ["organization_invitation"]}, "email_address": {"type": "string"}, "role": {"type": "string"}, "role_name": {"type": "string"}, "organization_id": {"type": "string"}, "status": {"type": "string"}, "public_metadata": {"type": "object", "additionalProperties": true}, "private_metadata": {"type": "object", "additionalProperties": true}, "created_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of creation."}, "updated_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of last update."}}}, "OrganizationInvitations": {"type": "object", "additionalProperties": false, "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/OrganizationInvitation"}}, "total_count": {"type": "integer", "format": "int64", "description": "Total number of organization invitations\n"}}, "required": ["data", "total_count"]}, "OrganizationDomainVerification": {"description": "The verification object from an organization domain", "type": "object", "properties": {"status": {"type": "string", "description": "Status of the verification. It can be `unverified` or `verified`", "enum": ["unverified", "verified"]}, "strategy": {"type": "string", "description": "Name of the strategy used to verify the domain"}, "attempts": {"type": "integer", "description": "How many attempts have been made to verify the domain"}, "expire_at": {"type": "integer", "nullable": true, "format": "int64", "description": "Unix timestamp of when the verification will expire"}}}, "OrganizationDomain": {"description": "An organization domain", "type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the organization domain"}, "object": {"type": "string", "description": "String representing the object's type. Objects of the same type share the same value. Always `organization_domain`\n", "enum": ["organization_domain"]}, "organization_id": {"type": "string", "description": "Unique identifier for the organization"}, "name": {"type": "string", "description": "Name of the organization domain"}, "enrollment_mode": {"type": "string", "description": "Mode of enrollment for the domain", "enum": ["manual_invitation", "automatic_invitation", "automatic_suggestion"]}, "affiliation_email_address": {"type": "string", "nullable": true, "description": "Affiliation email address for the domain, if available."}, "verification": {"type": "object", "nullable": true, "allOf": [{"$ref": "#/components/schemas/OrganizationDomainVerification"}], "description": "Verification details for the domain"}, "total_pending_invitations": {"type": "integer", "format": "int32", "description": "Total number of pending invitations associated with this domain"}, "total_pending_suggestions": {"type": "integer", "format": "int32", "description": "Total number of pending suggestions associated with this domain"}, "created_at": {"type": "integer", "format": "int64", "description": "Unix timestamp when the domain was created"}, "updated_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of the last update to the domain"}}}, "OrganizationDomains": {"type": "object", "additionalProperties": false, "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/OrganizationDomain"}}, "total_count": {"type": "integer", "format": "int64", "description": "Total number of organization domains\n"}}, "required": ["data", "total_count"]}, "ProxyCheck": {"type": "object", "additionalProperties": false, "properties": {"object": {"type": "string", "enum": ["proxy_check"]}, "id": {"type": "string"}, "domain_id": {"type": "string"}, "last_run_at": {"type": "integer"}, "proxy_url": {"type": "string"}, "successful": {"type": "boolean"}, "created_at": {"type": "integer"}, "updated_at": {"type": "integer"}}, "required": ["object", "id", "domain_id", "last_run_at", "proxy_url", "successful", "created_at", "updated_at"]}, "RedirectURL": {"type": "object", "additionalProperties": false, "properties": {"object": {"type": "string", "enum": ["redirect_url"]}, "id": {"type": "string"}, "url": {"type": "string"}, "created_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of creation.\n"}, "updated_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of last update.\n"}}, "required": ["object", "id", "url", "created_at", "updated_at"]}, "SignInToken": {"type": "object", "additionalProperties": false, "properties": {"object": {"type": "string", "enum": ["sign_in_token"]}, "id": {"type": "string"}, "status": {"type": "string", "enum": ["pending", "accepted", "revoked"]}, "user_id": {"type": "string"}, "token": {"type": "string"}, "url": {"type": "string", "nullable": true}, "created_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of creation.\n"}, "updated_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of last update.\n"}}, "required": ["object", "id", "user_id", "status", "created_at", "updated_at"]}, "SignUp": {"type": "object", "additionalProperties": false, "properties": {"object": {"type": "string", "enum": ["sign_up_attempt"]}, "id": {"type": "string"}, "status": {"type": "string", "enum": ["missing_requirements", "complete", "abandoned"]}, "required_fields": {"type": "array", "items": {"type": "string"}}, "optional_fields": {"type": "array", "items": {"type": "string"}}, "missing_fields": {"type": "array", "items": {"type": "string"}}, "unverified_fields": {"type": "array", "items": {"type": "string"}}, "verifications": {"type": "object"}, "username": {"type": "string", "nullable": true}, "email_address": {"type": "string", "nullable": true}, "phone_number": {"type": "string", "nullable": true}, "web3_wallet": {"type": "string", "nullable": true}, "password_enabled": {"type": "boolean"}, "first_name": {"type": "string", "nullable": true}, "last_name": {"type": "string", "nullable": true}, "unsafe_metadata": {"type": "object", "additionalProperties": true}, "public_metadata": {"type": "object", "additionalProperties": true}, "custom_action": {"type": "boolean"}, "external_id": {"type": "string", "nullable": true}, "created_session_id": {"type": "string", "nullable": true}, "created_user_id": {"type": "string", "nullable": true}, "abandon_at": {"type": "integer"}, "legal_accepted_at": {"type": "integer", "format": "int64", "nullable": true, "description": "Unix timestamp at which the user accepted the legal requirements.\n", "example": *************}, "external_account": {"type": "object"}}, "required": ["object", "id", "status", "password_enabled", "custom_action", "abandon_at"]}, "OAuthApplication": {"type": "object", "properties": {"object": {"type": "string", "enum": ["oauth_application"]}, "id": {"type": "string"}, "instance_id": {"type": "string"}, "name": {"type": "string"}, "client_id": {"type": "string"}, "public": {"type": "boolean"}, "scopes": {"type": "string"}, "callback_url": {"type": "string"}, "authorize_url": {"type": "string"}, "token_fetch_url": {"type": "string"}, "user_info_url": {"type": "string"}, "created_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of creation.\n"}, "updated_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of last update.\n"}}, "required": ["object", "id", "instance_id", "name", "public", "client_id", "scopes", "callback_url", "authorize_url", "token_fetch_url", "user_info_url", "created_at", "updated_at"]}, "OAuthApplications": {"type": "object", "additionalProperties": false, "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/OAuthApplication"}}, "total_count": {"type": "integer", "format": "int64", "description": "Total number of OAuth applications\n"}}, "required": ["data", "total_count"]}, "OAuthApplicationWithSecret": {"allOf": [{"$ref": "#/components/schemas/OAuthApplication"}, {"type": "object", "properties": {"client_secret": {"type": "string", "description": "Empty if public client.\n"}}}]}, "schemas-SAMLConnection": {"type": "object", "properties": {"object": {"type": "string", "enum": ["saml_connection"]}, "id": {"type": "string"}, "name": {"type": "string"}, "domain": {"type": "string"}, "idp_entity_id": {"type": "string", "nullable": true}, "idp_sso_url": {"type": "string", "nullable": true}, "idp_certificate": {"type": "string", "nullable": true}, "idp_metadata_url": {"type": "string", "nullable": true}, "idp_metadata": {"type": "string", "nullable": true}, "acs_url": {"type": "string"}, "sp_entity_id": {"type": "string"}, "sp_metadata_url": {"type": "string"}, "organization_id": {"type": "string", "nullable": true}, "attribute_mapping": {"type": "object", "additionalProperties": false, "properties": {"user_id": {"type": "string"}, "email_address": {"type": "string"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}}}, "active": {"type": "boolean"}, "provider": {"type": "string"}, "user_count": {"type": "integer"}, "sync_user_attributes": {"type": "boolean"}, "allow_subdomains": {"type": "boolean"}, "allow_idp_initiated": {"type": "boolean"}, "disable_additional_identifications": {"type": "boolean"}, "created_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of creation.\n"}, "updated_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of last update.\n"}}, "required": ["object", "id", "name", "domain", "idp_entity_id", "idp_sso_url", "idp_certificate", "acs_url", "sp_entity_id", "sp_metadata_url", "active", "provider", "user_count", "sync_user_attributes", "created_at", "updated_at"]}, "SAMLConnections": {"type": "object", "additionalProperties": false, "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/schemas-SAMLConnection"}}, "total_count": {"type": "integer", "format": "int64", "description": "Total number of SAML Connections\n"}}, "required": ["data", "total_count"]}, "TestingToken": {"type": "object", "properties": {"object": {"type": "string", "enum": ["testing_token"]}, "token": {"type": "string", "description": "The actual token. This value is meant to be passed in the `__clerk_testing_token` query parameter with requests to the Frontend API.", "example": "**********-c_2J2MvPu9PnXcuhbPZNao0LOXqK9A7YrnBn0HmIWxy"}, "expires_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of the token's expiration time.\n", "example": **********}}, "required": ["object", "token", "expires_at"]}, "WaitlistEntry": {"type": "object", "additionalProperties": false, "properties": {"object": {"type": "string", "enum": ["waitlist_entry"]}, "id": {"type": "string"}, "email_address": {"type": "string", "format": "email"}, "status": {"type": "string", "enum": ["pending", "invited", "rejected", "completed"], "example": "pending"}, "created_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of creation.\n"}, "updated_at": {"type": "integer", "format": "int64", "description": "Unix timestamp of last update.\n"}, "invitation": {"type": "object", "nullable": true, "allOf": [{"$ref": "#/components/schemas/Invitation"}]}}, "required": ["object", "id", "email_address", "status", "created_at", "updated_at"]}}, "responses": {"WellKnown.JWKS": {"description": "Get the JSON Web Key Set", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JWKS"}}}}, "Client.List": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Client"}}}}}, "ClerkErrors": {"description": "Request was not successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClerkErrors"}}}}, "AuthenticationInvalid": {"description": "Authentication invalid", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClerkErrors"}}}}, "DeprecatedEndpoint": {"description": "The endpoint is considered deprecated and is pending removal.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClerkErrors"}}}}, "UnprocessableEntity": {"description": "Invalid request parameters", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClerkErrors"}}}}, "Client": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Client"}}}}, "ResourceNotFound": {"description": "Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClerkErrors"}}}}, "EmailAddress": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmailAddress"}}}}, "AuthorizationInvalid": {"description": "Authorization invalid", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClerkErrors"}}}}, "DeletedObject": {"description": "Deleted Object", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeletedObject"}}}}, "PhoneNumber": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PhoneNumber"}}}}, "Session.List": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Session"}}}}}, "Session": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Session"}}}}, "Template.List": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Template"}}}}}, "Template": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Template"}}}}, "PaymentRequired": {"description": "Payment required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClerkErrors"}}}}, "User.List": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}}}}, "User": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "User.Count": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TotalCount"}}}}, "OrganizationMemberships": {"description": "A list of organization memberships", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationMemberships"}}}}, "OrganizationInvitationsWithPublicOrganizationData": {"description": "A list of organization invitations with public organization data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationInvitationsWithPublicOrganizationData"}}}}, "TOTP": {"description": "A TOTP (Time-based One-Time Password)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TOTP"}}}}, "Invitation.List": {"description": "List of invitations", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Invitation"}}}}}, "Invitation": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Invitation"}}}}, "Invitation.Revoked": {"description": "Success", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Invitation"}, {"type": "object", "properties": {"revoked": {"type": "boolean", "enum": [true], "example": true}, "status": {"type": "string", "enum": ["revoked"], "example": "revoked"}}}]}}}}, "AllowlistIdentifier.List": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AllowlistIdentifier"}}}}}, "AllowlistIdentifier": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AllowlistIdentifier"}}}}, "BlocklistIdentifier.List": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlocklistIdentifiers"}}}}, "BlocklistIdentifier": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlocklistIdentifier"}}}}, "InstanceSettings": {"description": "InstanceSettings Server API", "content": {"application/json": {"schema": {"type": "object", "properties": {"object": {"type": "string", "description": "String representing the object's type. Objects of the same type share the same value.", "enum": ["instance_settings"]}, "id": {"type": "string"}, "restricted_to_allowlist": {"type": "boolean"}, "from_email_address": {"type": "string"}, "progressive_sign_up": {"type": "boolean"}, "enhanced_email_deliverability": {"type": "boolean"}}}}}}, "ActorToken": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActorToken"}}}}, "Domains": {"description": "A list of domains", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Domains"}}}}, "Domain": {"description": "A domain", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Domain"}}}}, "Instance": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Instance"}}}}, "InstanceRestrictions": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InstanceRestrictions"}}}}, "OrganizationSettings": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationSettings"}}}}, "SvixURL": {"description": "Response that contains a temporary Svix URL to access management dashboard", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SvixURL"}}}}, "JWTTemplate.List": {"description": "List of JWT templates", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/JWTTemplate"}}}}}, "JWTTemplate": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JWTTemplate"}}}}, "Organizations": {"description": "A list of organizations", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Organizations"}}}}, "Organization": {"description": "An organization", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Organization"}}}}, "OrganizationWithLogo": {"description": "An organization with a logo URL.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationWithLogo"}}}}, "OrganizationInvitations": {"description": "A list of organization invitations", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationInvitations"}}}}, "OrganizationInvitation": {"description": "An organization invitation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationInvitation"}}}}, "OrganizationMembership": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationMembership"}}}}, "OrganizationDomains": {"description": "A list of organization domains", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationDomains"}}}}, "OrganizationDomain": {"description": "An organization domain", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationDomain"}}}}, "ProxyCheck": {"description": "Health check information about a domain's proxy configuration validation attempt.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProxyCheck"}}}}, "RedirectURL.List": {"description": "List of Redirect URLs", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RedirectURL"}}}}}, "RedirectURL": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RedirectURL"}}}}, "SignInToken": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SignInToken"}}}}, "SignUp": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SignUp"}}}}, "OAuthApplications": {"description": "A list of OAuth applications", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OAuthApplications"}}}}, "OAuthApplicationWithSecret": {"description": "An OAuth application with client secret", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OAuthApplicationWithSecret"}}}}, "OAuthApplication": {"description": "An OAuth application", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OAuthApplication"}}}}, "SAMLConnections": {"description": "A list of SAML Connections", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SAMLConnections"}}}}, "SAMLConnection": {"description": "A SAML Connection", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/schemas-SAMLConnection"}}}}, "TestingToken": {"description": "A Testing Token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestingToken"}}}}, "WaitlistEntries": {"description": "List of waitlist entries", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/WaitlistEntry"}}, "total_count": {"type": "integer", "description": "Total number of waitlist entries"}}, "required": ["data", "total_count"]}}}}, "WaitlistEntry": {"description": "A Waitlist Entry", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WaitlistEntry"}}}}}, "parameters": {"LimitParameter": {"name": "limit", "in": "query", "description": "Applies a limit to the number of results returned.\nCan be used for paginating the results together with `offset`.", "required": false, "schema": {"type": "number", "default": 10, "minimum": 1, "maximum": 500}}, "OffsetParameter": {"name": "offset", "in": "query", "description": "Skip the first `offset` results when paginating.\nNeeds to be an integer greater or equal to zero.\nTo be used in conjunction with `limit`.", "required": false, "schema": {"type": "number", "default": 0, "minimum": 0}}}}, "externalDocs": {"url": "https://clerk.com/docs"}, "info": {"title": "Clerk Backend API", "x-logo": {"url": "https://clerk.com/_next/image?url=%2Fimages%2Fclerk-logo.svg&w=96&q=75", "altText": "Clerk docs", "href": "https://clerk.com/docs"}, "contact": {"email": "<EMAIL>", "name": "Clerk Platform Team", "url": "https://clerk.com/support"}, "description": "The Clerk REST Backend API, meant to be accessed by backend\nservers.\n\n### Versions\n\nWhen the API changes in a way that isn't compatible with older versions, a new version is released.\nEach version is identified by its release date, e.g. `2021-02-05`. For more information, please see [Clerk API Versions](https://clerk.com/docs/backend-requests/versioning/overview).\n\n\nPlease see https://clerk.com/docs for more information.", "version": "v1", "termsOfService": "https://clerk.com/terms", "license": {"name": "MIT", "url": "https://github.com/clerkinc/clerk-sdk-go/blob/main/LICENSE"}}, "openapi": "3.0.3", "paths": {"/public/interstitial": {"get": {"security": [], "summary": "Returns the markup for the interstitial page", "description": "The Clerk interstitial endpoint serves an html page that loads clerk.js in order to check the user's authentication state.\nIt is used by Clerk SDKs when the user's authentication state cannot be immediately determined.", "operationId": "GetPublicInterstitial", "tags": ["Miscellaneous"], "parameters": [{"name": "frontendApi", "in": "query", "required": false, "description": "The Frontend API key of your instance", "schema": {"type": "string"}}, {"name": "publishable_key", "in": "query", "required": false, "description": "The publishable key of your instance", "schema": {"type": "string"}}], "responses": {"200": {"description": "The interstitial page markup"}, "400": {"description": "A required query parameter is missing"}, "500": {"description": "An infinite redirect loop was detected"}}}}, "/jwks": {"get": {"operationId": "GetJWKS", "summary": "Retrieve the JSON Web Key Set of the instance", "description": "Retrieve the JSON Web Key Set of the instance", "tags": ["JWKS"], "responses": {"200": {"$ref": "#/components/responses/WellKnown.JWKS"}}}}, "/clients": {"get": {"deprecated": true, "operationId": "GetClientList", "tags": ["Clients"], "summary": "List all clients", "description": "Returns a list of all clients. The clients are returned sorted by creation date,\nwith the newest clients appearing first.\nWarning: the endpoint is being deprecated and will be removed in future versions.", "parameters": [{"$ref": "#/components/parameters/LimitParameter"}, {"$ref": "#/components/parameters/OffsetParameter"}], "responses": {"200": {"$ref": "#/components/responses/Client.List"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "410": {"$ref": "#/components/responses/DeprecatedEndpoint"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}, "/clients/verify": {"post": {"operationId": "VerifyClient", "tags": ["Clients"], "summary": "Verify a client", "description": "Verifies the client in the provided token", "requestBody": {"description": "Parameters.", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"token": {"type": "string", "description": "A JWT that represents the active client."}}}}}}, "responses": {"200": {"$ref": "#/components/responses/Client"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}}, "/clients/{client_id}": {"get": {"operationId": "GetClient", "tags": ["Clients"], "summary": "Get a client", "description": "Returns the details of a client.", "parameters": [{"name": "client_id", "in": "path", "description": "Client ID.", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Client"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}}, "/email_addresses": {"post": {"operationId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tags": ["Email Addresses"], "summary": "Create an email address", "description": "Create a new email address", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"user_id": {"type": "string", "description": "The ID representing the user"}, "email_address": {"type": "string", "description": "The new email address. Must adhere to the RFC 5322 specification for email address format."}, "verified": {"type": "boolean", "description": "When created, the email address will be marked as verified.", "nullable": true}, "primary": {"type": "boolean", "description": "Create this email address as the primary email address for the user.\nDefault: false, unless it is the first email address.", "nullable": true}}}}}}, "responses": {"200": {"$ref": "#/components/responses/EmailAddress"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}, "/email_addresses/{email_address_id}": {"get": {"operationId": "GetEmailAddress", "tags": ["Email Addresses"], "summary": "Retrieve an email address", "description": "Returns the details of an email address.", "parameters": [{"name": "email_address_id", "in": "path", "description": "The ID of the email address to retrieve", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/EmailAddress"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}, "delete": {"operationId": "DeleteEmailAddress", "tags": ["Email Addresses"], "summary": "Delete an email address", "description": "Delete the email address with the given ID", "parameters": [{"name": "email_address_id", "in": "path", "description": "The ID of the email address to delete", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/DeletedObject"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}, "patch": {"operationId": "UpdateE<PERSON><PERSON><PERSON><PERSON>", "tags": ["Email Addresses"], "summary": "Update an email address", "description": "Updates an email address.", "parameters": [{"name": "email_address_id", "in": "path", "description": "The ID of the email address to update", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"verified": {"type": "boolean", "description": "The email address will be marked as verified.", "nullable": true}, "primary": {"type": "boolean", "description": "Set this email address as the primary email address for the user.", "nullable": true}}}}}}, "responses": {"200": {"$ref": "#/components/responses/EmailAddress"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}}, "/phone_numbers": {"post": {"operationId": "CreatePhoneNumber", "tags": ["Phone Numbers"], "summary": "Create a phone number", "description": "Create a new phone number", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"user_id": {"type": "string", "description": "The ID representing the user"}, "phone_number": {"type": "string", "description": "The new phone number. Must adhere to the E.164 standard for phone number format."}, "verified": {"type": "boolean", "description": "When created, the phone number will be marked as verified.", "nullable": true}, "primary": {"type": "boolean", "description": "Create this phone number as the primary phone number for the user.\nDefault: false, unless it is the first phone number.", "nullable": true}, "reserved_for_second_factor": {"type": "boolean", "description": "Create this phone number as reserved for multi-factor authentication.\nThe phone number must also be verified.\nIf there are no other reserved second factors, the phone number will be set as the default second factor.", "nullable": true}}}}}}, "responses": {"200": {"$ref": "#/components/responses/PhoneNumber"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}, "/phone_numbers/{phone_number_id}": {"get": {"operationId": "GetPhoneNumber", "tags": ["Phone Numbers"], "summary": "Retrieve a phone number", "description": "Returns the details of a phone number", "parameters": [{"name": "phone_number_id", "in": "path", "description": "The ID of the phone number to retrieve", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/PhoneNumber"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}, "delete": {"operationId": "DeletePhoneNumber", "tags": ["Phone Numbers"], "summary": "Delete a phone number", "description": "Delete the phone number with the given ID", "parameters": [{"name": "phone_number_id", "in": "path", "description": "The ID of the phone number to delete", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/DeletedObject"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}, "patch": {"operationId": "UpdatePhoneNumber", "tags": ["Phone Numbers"], "summary": "Update a phone number", "description": "Updates a phone number", "parameters": [{"name": "phone_number_id", "in": "path", "description": "The ID of the phone number to update", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"verified": {"type": "boolean", "description": "The phone number will be marked as verified.", "nullable": true}, "primary": {"type": "boolean", "description": "Set this phone number as the primary phone number for the user.", "nullable": true}, "reserved_for_second_factor": {"type": "boolean", "description": "Set this phone number as reserved for multi-factor authentication.\nThe phone number must also be verified.\nIf there are no other reserved second factors, the phone number will be set as the default second factor.", "nullable": true}}}}}}, "responses": {"200": {"$ref": "#/components/responses/PhoneNumber"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}}, "/sessions": {"get": {"operationId": "GetSessionList", "tags": ["Sessions"], "summary": "List all sessions", "description": "Returns a list of all sessions.\nThe sessions are returned sorted by creation date, with the newest sessions appearing first.\n**Deprecation Notice (2024-01-01):** All parameters were initially considered optional, however\nmoving forward at least one of `client_id` or `user_id` parameters should be provided.", "parameters": [{"name": "client_id", "in": "query", "required": false, "description": "List sessions for the given client", "schema": {"type": "string"}}, {"name": "user_id", "in": "query", "required": false, "description": "List sessions for the given user", "schema": {"type": "string"}}, {"name": "status", "in": "query", "required": false, "description": "Filter sessions by the provided status", "schema": {"type": "string", "enum": ["abandoned", "active", "ended", "expired", "removed", "replaced", "revoked"]}}, {"$ref": "#/components/parameters/LimitParameter"}, {"$ref": "#/components/parameters/OffsetParameter"}], "responses": {"200": {"$ref": "#/components/responses/Session.List"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}, "post": {"operationId": "createSession", "tags": ["Sessions"], "summary": "Create a new active session", "description": "Create a new active session for the provided user ID.\n\nThis operation is only available for Clerk Development instances.", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"user_id": {"type": "string", "description": "The ID representing the user"}}}}}}, "responses": {"200": {"$ref": "#/components/responses/Session"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "404": {"$ref": "#/components/responses/ClerkErrors"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}, "/sessions/{session_id}": {"get": {"operationId": "GetSession", "tags": ["Sessions"], "summary": "Retrieve a session", "description": "Retrieve the details of a session", "parameters": [{"name": "session_id", "in": "path", "description": "The ID of the session", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Session"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}}, "/sessions/{session_id}/revoke": {"post": {"operationId": "RevokeSession", "tags": ["Sessions"], "summary": "Revoke a session", "description": "Sets the status of a session as \"revoked\", which is an unauthenticated state.\nIn multi-session mode, a revoked session will still be returned along with its client object, however the user will need to sign in again.", "parameters": [{"name": "session_id", "in": "path", "description": "The ID of the session", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Session"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}}, "/sessions/{session_id}/verify": {"post": {"deprecated": true, "operationId": "VerifySession", "tags": ["Sessions"], "summary": "Verify a session", "description": "Returns the session if it is authenticated, otherwise returns an error.\nWARNING: This endpoint is deprecated and will be removed in future versions. We strongly recommend switching to networkless verification using short-lived session tokens,\n         which is implemented transparently in all recent SDK versions (e.g. [NodeJS SDK](https://clerk.com/docs/backend-requests/handling/nodejs#clerk-express-require-auth)).\n         For more details on how networkless verification works, refer to our [Session Tokens documentation](https://clerk.com/docs/backend-requests/resources/session-tokens).", "parameters": [{"name": "session_id", "in": "path", "description": "The ID of the session", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "Parameters.", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"token": {"type": "string", "description": "The JWT that is sent via the `__session` cookie from your frontend.\nNote: this JWT must be associated with the supplied session ID."}}}}}}, "responses": {"200": {"$ref": "#/components/responses/Session"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}, "410": {"$ref": "#/components/responses/DeprecatedEndpoint"}}}}, "/sessions/{session_id}/tokens": {"post": {"operationId": "CreateSessionToken", "tags": ["Sessions"], "summary": "Create a session token", "description": "Creates a session JSON Web Token (JWT) based on a session.", "parameters": [{"name": "session_id", "in": "path", "description": "The ID of the session", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"expires_in_seconds": {"type": "number", "minimum": 30, "maximum": 315360000, "description": "Use this parameter to override the default session token lifetime.", "nullable": true}}}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"object": {"type": "string", "enum": ["token"]}, "jwt": {"type": "string"}}}}}}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}}, "/sessions/{session_id}/tokens/{template_name}": {"post": {"operationId": "CreateSessionTokenFromTemplate", "tags": ["Sessions"], "summary": "Create a session token from a jwt template", "description": "Creates a JSON Web Token(JWT) based on a session and a JWT Template name defined for your instance", "parameters": [{"name": "session_id", "in": "path", "description": "The ID of the session", "required": true, "schema": {"type": "string"}}, {"name": "template_name", "in": "path", "description": "The name of the JWT Template defined in your instance (e.g. `custom_hasura`).", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"expires_in_seconds": {"type": "number", "minimum": 30, "maximum": 315360000, "description": "Use this parameter to override the JWT token lifetime.", "nullable": true}}}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"object": {"type": "string", "enum": ["token"]}, "jwt": {"type": "string"}}}}}}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}}, "/templates/{template_type}": {"get": {"deprecated": true, "operationId": "GetTemplateList", "summary": "List all templates", "description": "Returns a list of all templates.\nThe templates are returned sorted by position.", "tags": ["Email & SMS Templates"], "parameters": [{"name": "template_type", "in": "path", "description": "The type of templates to list (email or SMS)", "required": true, "schema": {"type": "string", "enum": ["email", "sms"]}}], "responses": {"200": {"$ref": "#/components/responses/Template.List"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}, "/templates/{template_type}/{slug}": {"get": {"deprecated": true, "operationId": "GetTemplate", "summary": "Retrieve a template", "description": "Returns the details of a template", "tags": ["Email & SMS Templates"], "parameters": [{"name": "template_type", "in": "path", "description": "The type of templates to retrieve (email or SMS)", "required": true, "schema": {"type": "string", "enum": ["email", "sms"]}}, {"name": "slug", "in": "path", "description": "The slug (i.e. machine-friendly name) of the template to retrieve", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Template"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}, "put": {"deprecated": true, "operationId": "UpsertTemplate", "summary": "Update a template for a given type and slug", "description": "Updates the existing template of the given type and slug", "tags": ["Email & SMS Templates"], "parameters": [{"name": "template_type", "in": "path", "description": "The type of template to update", "required": true, "schema": {"type": "string", "enum": ["email", "sms"]}}, {"name": "slug", "in": "path", "description": "The slug of the template to update", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string", "description": "The user-friendly name of the template"}, "subject": {"type": "string", "description": "The email subject.\nApplicable only to email templates.", "nullable": true}, "markup": {"type": "string", "description": "The editor markup used to generate the body of the template", "nullable": true}, "body": {"type": "string", "description": "The template body before variable interpolation"}, "delivered_by_clerk": {"type": "boolean", "description": "Whether Clerk should deliver emails or SMS messages based on the current template", "nullable": true}, "from_email_name": {"type": "string", "description": "The local part of the From email address that will be used for emails.\nFor example, in the address '<EMAIL>', the local part is 'hello'.\nApplicable only to email templates."}, "reply_to_email_name": {"type": "string", "description": "The local part of the Reply To email address that will be used for emails.\nFor example, in the address '<EMAIL>', the local part is 'hello'.\nApplicable only to email templates."}}}}}}, "responses": {"200": {"$ref": "#/components/responses/Template"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "402": {"$ref": "#/components/responses/PaymentRequired"}, "403": {"$ref": "#/components/responses/ClerkErrors"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}, "/templates/{template_type}/{slug}/revert": {"post": {"deprecated": true, "operationId": "RevertTemplate", "summary": "Revert a template", "description": "Reverts an updated template to its default state", "tags": ["Email & SMS Templates"], "parameters": [{"name": "template_type", "in": "path", "description": "The type of template to revert", "required": true, "schema": {"type": "string", "enum": ["email", "sms"]}}, {"name": "slug", "in": "path", "description": "The slug of the template to revert", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Template"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "402": {"$ref": "#/components/responses/PaymentRequired"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}}, "/templates/{template_type}/{slug}/preview": {"post": {"deprecated": true, "operationId": "PreviewTemplate", "summary": "Preview changes to a template", "description": "Returns a preview of a template for a given template_type, slug and body", "tags": ["Email & SMS Templates"], "parameters": [{"name": "template_type", "in": "path", "description": "The type of template to preview", "required": true, "schema": {"type": "string"}}, {"name": "slug", "in": "path", "description": "The slug of the template to preview", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "Required parameters", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"subject": {"type": "string", "description": "The email subject.\nApplicable only to email templates.", "nullable": true}, "body": {"type": "string", "description": "The template body before variable interpolation"}, "from_email_name": {"type": "string", "description": "The local part of the From email address that will be used for emails.\nFor example, in the address '<EMAIL>', the local part is 'hello'.\nApplicable only to email templates."}, "reply_to_email_name": {"type": "string", "description": "The local part of the Reply To email address that will be used for emails.\nFor example, in the address '<EMAIL>', the local part is 'hello'.\nApplicable only to email templates."}}}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object"}}}}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}, "/templates/{template_type}/{slug}/toggle_delivery": {"post": {"deprecated": true, "operationId": "ToggleTemplateDelivery", "summary": "Toggle the delivery by Clerk for a template of a given type and slug", "description": "Toggles the delivery by <PERSON> for a template of a given type and slug.\nIf disabled, <PERSON> will not deliver the resulting email or SMS.\nThe app developer will need to listen to the `email.created` or `sms.created` webhooks in order to handle delivery themselves.", "tags": ["Email & SMS Templates"], "parameters": [{"name": "template_type", "in": "path", "description": "The type of template to toggle delivery for", "required": true, "schema": {"type": "string", "enum": ["email", "sms"]}}, {"name": "slug", "in": "path", "description": "The slug of the template for which to toggle delivery", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"delivered_by_clerk": {"type": "boolean", "description": "Whether Clerk should deliver emails or SMS messages based on the current template", "nullable": true}}}}}}, "responses": {"200": {"$ref": "#/components/responses/Template"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}}, "/users": {"get": {"operationId": "GetUserList", "summary": "List all users", "description": "Returns a list of all users.\nThe users are returned sorted by creation date, with the newest users appearing first.", "tags": ["Users"], "parameters": [{"name": "email_address", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}, "description": "Returns users with the specified email addresses.\nAccepts up to 100 email addresses.\nAny email addresses not found are ignored.", "required": false}, {"name": "phone_number", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}, "description": "Returns users with the specified phone numbers.\nAccepts up to 100 phone numbers.\nAny phone numbers not found are ignored.", "required": false}, {"name": "external_id", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}, "description": "Returns users with the specified external ids.\nFor each external id, the `+` and `-` can be\nprepended to the id, which denote whether the\nrespective external id should be included or\nexcluded from the result set.\nAccepts up to 100 external ids.\nAny external ids not found are ignored.", "required": false}, {"name": "username", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}, "description": "Returns users with the specified usernames.\nAccepts up to 100 usernames.\nAny usernames not found are ignored.", "required": false}, {"name": "web3_wallet", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}, "description": "Returns users with the specified web3 wallet addresses.\nAccepts up to 100 web3 wallet addresses.\nAny web3 wallet addressed not found are ignored.", "required": false}, {"name": "user_id", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}, "description": "Returns users with the user ids specified.\nFor each user id, the `+` and `-` can be\nprepended to the id, which denote whether the\nrespective user id should be included or\nexcluded from the result set.\nAccepts up to 100 user ids.\nAny user ids not found are ignored.", "required": false}, {"name": "organization_id", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}, "description": "Returns users that have memberships to the\ngiven organizations.\nFor each organization id, the `+` and `-` can be\nprepended to the id, which denote whether the\nrespective organization should be included or\nexcluded from the result set.\nAccepts up to 100 organization ids.", "required": false}, {"name": "query", "in": "query", "description": "Returns users that match the given query.\nFor possible matches, we check the email addresses, phone numbers, usernames, web3 wallets, user ids, first and last names.\nThe query value doesn't need to match the exact value you are looking for, it is capable of partial matches as well.", "schema": {"type": "string", "minLength": 3}, "required": false}, {"name": "email_address_query", "in": "query", "description": "Returns users with emails that match the given query, via case-insensitive partial match.\nFor example, `email_address_query=ello` will match a user with the email `<EMAIL>`.", "schema": {"type": "string", "minLength": 3}, "required": false}, {"name": "phone_number_query", "in": "query", "description": "Returns users with phone numbers that match the given query, via case-insensitive partial match.\nFor example, `phone_number_query=555` will match a user with the phone number `+1555xxxxxxx`.", "schema": {"type": "string", "minLength": 3}, "required": false}, {"name": "username_query", "in": "query", "description": "Returns users with usernames that match the given query, via case-insensitive partial match.\nFor example, `username_query=CoolUser` will match a user with the username `SomeCoolUser`.", "schema": {"type": "string", "minLength": 3}}, {"name": "name_query", "in": "query", "description": "Returns users with names that match the given query, via case-insensitive partial match.", "schema": {"type": "string", "minLength": 3}}, {"name": "last_active_at_before", "in": "query", "description": "Returns users whose last session activity was before the given date (with millisecond precision).\nExample: use ************* to retrieve users whose last session activity was before 2023-11-23.", "example": *************, "schema": {"type": "integer"}}, {"name": "last_active_at_after", "in": "query", "description": "Returns users whose last session activity was after the given date (with millisecond precision).\nExample: use ************* to retrieve users whose last session activity was after 2023-11-23.", "example": *************, "schema": {"type": "integer"}}, {"name": "last_active_at_since", "in": "query", "deprecated": true, "description": "Returns users that had session activity since the given date.\nExample: use ************* to retrieve users that had session activity from 2023-11-23 until the current day.\nDeprecated in favor of `last_active_at_after`.", "example": *************, "schema": {"type": "integer"}}, {"name": "created_at_before", "in": "query", "description": "Returns users who have been created before the given date (with millisecond precision).\nExample: use 1730160000000 to retrieve users who have been created before 2024-10-29.", "example": 1730160000000, "schema": {"type": "integer"}}, {"name": "created_at_after", "in": "query", "description": "Returns users who have been created after the given date (with millisecond precision).\nExample: use 1730160000000 to retrieve users who have been created after 2024-10-29.", "example": 1730160000000, "schema": {"type": "integer"}}, {"$ref": "#/components/parameters/LimitParameter"}, {"$ref": "#/components/parameters/OffsetParameter"}, {"name": "order_by", "in": "query", "description": "Allows to return users in a particular order.\nAt the moment, you can order the returned users by their `created_at`,`updated_at`,`email_address`,`web3wallet`,`first_name`,`last_name`,`phone_number`,`username`,`last_active_at`,`last_sign_in_at`.\nIn order to specify the direction, you can use the `+/-` symbols prepended in the property to order by.\nFor example, if you want users to be returned in descending order according to their `created_at` property, you can use `-created_at`.\nIf you don't use `+` or `-`, then `+` is implied. We only support one `order_by` parameter, and if multiple `order_by` parameters are provided, we will only keep the first one. For example,\nif you pass `order_by=username&order_by=created_at`, we will consider only the first `order_by` parameter, which is `username`. The `created_at` parameter will be ignored in this case.", "schema": {"type": "string", "default": "-created_at"}, "required": false}], "responses": {"200": {"$ref": "#/components/responses/User.List"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}, "post": {"operationId": "CreateUser", "summary": "Create a new user", "description": "Creates a new user. Your user management settings determine how you should setup your user model.\n\nAny email address and phone number created using this method will be marked as verified.\n\nNote: If you are performing a migration, check out our guide on [zero downtime migrations](https://clerk.com/docs/deployments/migrate-overview).\n\nA rate limit rule of 20 requests per 10 seconds is applied to this endpoint.", "tags": ["Users"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"external_id": {"type": "string", "description": "The ID of the user as used in your external systems or your previous authentication solution.\nMust be unique across your instance.", "nullable": true}, "first_name": {"type": "string", "description": "The first name to assign to the user", "nullable": true}, "last_name": {"type": "string", "description": "The last name to assign to the user", "nullable": true}, "email_address": {"type": "array", "items": {"type": "string"}, "description": "Email addresses to add to the user.\nMust be unique across your instance.\nThe first email address will be set as the user's primary email address."}, "phone_number": {"type": "array", "items": {"type": "string"}, "description": "Phone numbers to add to the user.\nMust be unique across your instance.\nThe first phone number will be set as the user's primary phone number."}, "web3_wallet": {"type": "array", "items": {"type": "string"}, "description": "Web3 wallets to add to the user.\nMust be unique across your instance.\nThe first wallet will be set as the user's primary wallet."}, "username": {"type": "string", "description": "The username to give to the user.\nIt must be unique across your instance.", "nullable": true}, "password": {"type": "string", "description": "The plaintext password to give the user.\nMust be at least 8 characters long, and can not be in any list of hacked passwords.", "nullable": true}, "password_digest": {"type": "string", "description": "In case you already have the password digests and not the passwords, you can use them for the newly created user via this property.\nThe digests should be generated with one of the supported algorithms.\nThe hashing algorithm can be specified using the `password_hasher` property."}, "password_hasher": {"$ref": "#/components/schemas/PasswordHasher"}, "skip_password_checks": {"type": "boolean", "description": "When set to `true` all password checks are skipped.\nIt is recommended to use this method only when migrating plaintext passwords to Clerk.\nUpon migration the user base should be prompted to pick stronger password."}, "skip_password_requirement": {"type": "boolean", "description": "When set to `true`, `password` is not required anymore when creating the user and can be omitted.\nThis is useful when you are trying to create a user that doesn't have a password, in an instance that is using passwords.\nPlease note that you cannot use this flag if password is the only way for a user to sign into your instance."}, "totp_secret": {"type": "string", "description": "In case TOTP is configured on the instance, you can provide the secret to enable it on the newly created user without the need to reset it.\nPlease note that currently the supported options are:\n* Period: 30 seconds\n* Code length: 6 digits\n* Algorithm: SHA1"}, "backup_codes": {"type": "array", "items": {"type": "string"}, "description": "If Backup Codes are configured on the instance, you can provide them to enable it on the newly created user without the need to reset them.\nYou must provide the backup codes in plain format or the corresponding bcrypt digest."}, "public_metadata": {"type": "object", "description": "Metadata saved on the user, that is visible to both your Frontend and Backend APIs", "additionalProperties": true}, "private_metadata": {"type": "object", "description": "Metadata saved on the user, that is only visible to your Backend API", "additionalProperties": true}, "unsafe_metadata": {"type": "object", "description": "Metadata saved on the user, that can be updated from both the Frontend and Backend APIs.\nNote: Since this data can be modified from the frontend, it is not guaranteed to be safe.", "additionalProperties": true}, "delete_self_enabled": {"type": "boolean", "description": "If enabled, user can delete themselves via FAPI.\n", "nullable": true}, "legal_accepted_at": {"type": "string", "description": "A custom timestamp denoting _when_ the user accepted legal requirements, specified in RFC3339 format (e.g. `2012-10-20T07:15:20.902Z`).", "nullable": true}, "skip_legal_checks": {"type": "boolean", "description": "When set to `true` all legal checks are skipped.\nIt is not recommended to skip legal checks unless you are migrating a user to Clerk.", "nullable": true}, "create_organization_enabled": {"type": "boolean", "description": "If enabled, user can create organizations via FAPI.\n", "nullable": true}, "create_organizations_limit": {"type": "integer", "description": "The maximum number of organizations the user can create. 0 means unlimited.\n", "nullable": true}, "created_at": {"type": "string", "description": "A custom date/time denoting _when_ the user signed up to the application, specified in RFC3339 format (e.g. `2012-10-20T07:15:20.902Z`)."}}}}}}, "responses": {"200": {"$ref": "#/components/responses/User"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "403": {"$ref": "#/components/responses/AuthenticationInvalid"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}, "/users/count": {"get": {"operationId": "GetUsersCount", "summary": "Count users", "description": "Returns a total count of all users that match the given filtering criteria.", "tags": ["Users"], "parameters": [{"name": "email_address", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}, "description": "Counts users with the specified email addresses.\nAccepts up to 100 email addresses.\nAny email addresses not found are ignored.", "required": false}, {"name": "phone_number", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}, "description": "Counts users with the specified phone numbers.\nAccepts up to 100 phone numbers.\nAny phone numbers not found are ignored.", "required": false}, {"name": "external_id", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}, "description": "Counts users with the specified external ids.\nAccepts up to 100 external ids.\nAny external ids not found are ignored.", "required": false}, {"name": "username", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}, "description": "Counts users with the specified usernames.\nAccepts up to 100 usernames.\nAny usernames not found are ignored.", "required": false}, {"name": "web3_wallet", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}, "description": "Counts users with the specified web3 wallet addresses.\nAccepts up to 100 web3 wallet addresses.\nAny web3 wallet addressed not found are ignored.", "required": false}, {"name": "user_id", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}, "description": "Counts users with the user ids specified.\nAccepts up to 100 user ids.\nAny user ids not found are ignored.", "required": false}, {"name": "query", "in": "query", "description": "Counts users that match the given query.\nFor possible matches, we check the email addresses, phone numbers, usernames, web3 wallets, user ids, first and last names.\nThe query value doesn't need to match the exact value you are looking for, it is capable of partial matches as well.", "schema": {"type": "string", "minLength": 3}, "required": false}, {"name": "email_address_query", "in": "query", "description": "Counts users with emails that match the given query, via case-insensitive partial match.\nFor example, `email_address_query=ello` will match a user with the email `<EMAIL>`,\nand will be included in the resulting count.", "schema": {"type": "string", "minLength": 3}, "required": false}, {"name": "phone_number_query", "in": "query", "description": "Counts users with phone numbers that match the given query, via case-insensitive partial match.\nFor example, `phone_number_query=555` will match a user with the phone number `+1555xxxxxxx`,\nand will be included in the resulting count.", "schema": {"type": "string", "minLength": 3}, "required": false}, {"name": "username_query", "in": "query", "description": "Counts users with usernames that match the given query, via case-insensitive partial match.\nFor example, `username_query=CoolUser` will match a user with the username `SomeCoolUser`,\nand will be included in the resulting count.", "schema": {"type": "string", "minLength": 3}}], "responses": {"200": {"$ref": "#/components/responses/User.Count"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}, "/users/{user_id}": {"get": {"operationId": "GetUser", "summary": "Retrieve a user", "description": "Retrieve the details of a user", "tags": ["Users"], "parameters": [{"name": "user_id", "in": "path", "description": "The ID of the user to retrieve", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/User"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}, "patch": {"operationId": "UpdateUser", "summary": "Update a user", "description": "Update a user's attributes.\n\nYou can set the user's primary contact identifiers (email address and phone numbers) by updating the `primary_email_address_id` and `primary_phone_number_id` attributes respectively.\nBoth IDs should correspond to verified identifications that belong to the user.\n\nYou can remove a user's username by setting the username attribute to null or the blank string \"\".\nThis is a destructive action; the identification will be deleted forever.\nUsernames can be removed only if they are optional in your instance settings and there's at least one other identifier which can be used for authentication.\n\nThis endpoint allows changing a user's password. When passing the `password` parameter directly you have two further options.\nYou can ignore the password policy checks for your instance by setting the `skip_password_checks` parameter to `true`.\nYou can also choose to sign the user out of all their active sessions on any device once the password is updated. Just set `sign_out_of_other_sessions` to `true`.", "tags": ["Users"], "parameters": [{"name": "user_id", "in": "path", "description": "The ID of the user to update", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"external_id": {"type": "string", "description": "The ID of the user as used in your external systems or your previous authentication solution.\nMust be unique across your instance.", "nullable": true}, "first_name": {"type": "string", "description": "The first name to assign to the user", "nullable": true}, "last_name": {"type": "string", "description": "The last name to assign to the user", "nullable": true}, "primary_email_address_id": {"type": "string", "description": "The ID of the email address to set as primary.\nIt must be verified, and present on the current user."}, "notify_primary_email_address_changed": {"type": "boolean", "description": "If set to `true`, the user will be notified that their primary email address has changed.\nBy default, no notification is sent.", "default": false}, "primary_phone_number_id": {"type": "string", "description": "The ID of the phone number to set as primary.\nIt must be verified, and present on the current user."}, "primary_web3_wallet_id": {"type": "string", "description": "The ID of the web3 wallets to set as primary.\nIt must be verified, and present on the current user."}, "username": {"type": "string", "description": "The username to give to the user.\nIt must be unique across your instance.", "nullable": true}, "profile_image_id": {"type": "string", "description": "The ID of the image to set as the user's profile image", "nullable": true}, "password": {"type": "string", "description": "The plaintext password to give the user.\nMust be at least 8 characters long, and can not be in any list of hacked passwords.", "nullable": true}, "password_digest": {"type": "string", "description": "In case you already have the password digests and not the passwords, you can use them for the newly created user via this property.\nThe digests should be generated with one of the supported algorithms.\nThe hashing algorithm can be specified using the `password_hasher` property."}, "password_hasher": {"$ref": "#/components/schemas/PasswordHasher"}, "skip_password_checks": {"type": "boolean", "description": "Set it to `true` if you're updating the user's password and want to skip any password policy settings check. This parameter can only be used when providing a `password`.", "nullable": true}, "sign_out_of_other_sessions": {"type": "boolean", "description": "Set to `true` to sign out the user from all their active sessions once their password is updated. This parameter can only be used when providing a `password`.", "nullable": true}, "totp_secret": {"type": "string", "description": "In case TOTP is configured on the instance, you can provide the secret to enable it on the specific user without the need to reset it.\nPlease note that currently the supported options are:\n* Period: 30 seconds\n* Code length: 6 digits\n* Algorithm: SHA1"}, "backup_codes": {"type": "array", "items": {"type": "string"}, "description": "If Backup Codes are configured on the instance, you can provide them to enable it on the specific user without the need to reset them.\nYou must provide the backup codes in plain format or the corresponding bcrypt digest."}, "public_metadata": {"type": "object", "description": "Metadata saved on the user, that is visible to both your Frontend and Backend APIs", "additionalProperties": true}, "private_metadata": {"type": "object", "description": "Metadata saved on the user, that is only visible to your Backend API", "additionalProperties": true}, "unsafe_metadata": {"type": "object", "description": "Metadata saved on the user, that can be updated from both the Frontend and Backend APIs.\nNote: Since this data can be modified from the frontend, it is not guaranteed to be safe.", "additionalProperties": true}, "delete_self_enabled": {"type": "boolean", "description": "If true, the user can delete themselves with the Frontend API.", "nullable": true}, "create_organization_enabled": {"type": "boolean", "description": "If true, the user can create organizations with the Frontend API.", "nullable": true}, "legal_accepted_at": {"type": "string", "description": "A custom timestamps denoting _when_ the user accepted legal requirements, specified in RFC3339 format (e.g. `2012-10-20T07:15:20.902Z`).", "nullable": true}, "skip_legal_checks": {"type": "boolean", "description": "When set to `true` all legal checks are skipped.\nIt is not recommended to skip legal checks unless you are migrating a user to Clerk.", "nullable": true}, "create_organizations_limit": {"type": "integer", "description": "The maximum number of organizations the user can create. 0 means unlimited.", "nullable": true}, "created_at": {"type": "string", "description": "A custom date/time denoting _when_ the user signed up to the application, specified in RFC3339 format (e.g. `2012-10-20T07:15:20.902Z`)."}}}}}}, "responses": {"200": {"$ref": "#/components/responses/User"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}, "delete": {"operationId": "DeleteUser", "summary": "Delete a user", "description": "Delete the specified user", "tags": ["Users"], "parameters": [{"name": "user_id", "in": "path", "description": "The ID of the user to delete", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/DeletedObject"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}}, "/users/{user_id}/ban": {"post": {"operationId": "BanUser", "summary": "Ban a user", "description": "Marks the given user as banned, which means that all their sessions are revoked and they are not allowed to sign in again.", "tags": ["Users"], "parameters": [{"name": "user_id", "in": "path", "description": "The ID of the user to ban", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/User"}, "402": {"$ref": "#/components/responses/PaymentRequired"}}}}, "/users/{user_id}/unban": {"post": {"operationId": "UnbanUser", "summary": "Unban a user", "description": "Removes the ban mark from the given user.", "tags": ["Users"], "parameters": [{"name": "user_id", "in": "path", "description": "The ID of the user to unban", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/User"}, "402": {"$ref": "#/components/responses/PaymentRequired"}}}}, "/users/{user_id}/lock": {"post": {"operationId": "LockUser", "summary": "Lock a user", "description": "Marks the given user as locked, which means they are not allowed to sign in again until the lock expires.\nLock duration can be configured in the instance's restrictions settings.", "tags": ["Users"], "parameters": [{"name": "user_id", "in": "path", "description": "The ID of the user to lock", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/User"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}}}}, "/users/{user_id}/unlock": {"post": {"operationId": "UnlockUser", "summary": "Unlock a user", "description": "Removes the lock from the given user.", "tags": ["Users"], "parameters": [{"name": "user_id", "in": "path", "description": "The ID of the user to unlock", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/User"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}}}}, "/users/{user_id}/profile_image": {"post": {"operationId": "SetUserProfileImage", "summary": "Set user profile image", "description": "Update a user's profile image", "tags": ["Users"], "parameters": [{"name": "user_id", "in": "path", "description": "The ID of the user to update the profile image for", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"$ref": "#/components/responses/User"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "401": {"$ref": "#/components/responses/ClerkErrors"}, "404": {"$ref": "#/components/responses/ClerkErrors"}}}, "delete": {"operationId": "DeleteUserProfileImage", "summary": "Delete user profile image", "description": "Delete a user's profile image", "tags": ["Users"], "parameters": [{"name": "user_id", "in": "path", "description": "The ID of the user to delete the profile image for", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/User"}, "404": {"$ref": "#/components/responses/ClerkErrors"}}}}, "/users/{user_id}/metadata": {"patch": {"operationId": "UpdateUserMetadata", "summary": "Merge and update a user's metadata", "description": "Update a user's metadata attributes by merging existing values with the provided parameters.\n\nThis endpoint behaves differently than the *Update a user* endpoint.\nMetadata values will not be replaced entirely.\nInstead, a deep merge will be performed.\nDeep means that any nested JSON objects will be merged as well.\n\nYou can remove metadata keys at any level by setting their value to `null`.", "tags": ["Users"], "parameters": [{"name": "user_id", "in": "path", "description": "The ID of the user whose metadata will be updated and merged", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"public_metadata": {"type": "object", "additionalProperties": true, "description": "Metadata saved on the user, that is visible to both your frontend and backend.\nThe new object will be merged with the existing value."}, "private_metadata": {"type": "object", "additionalProperties": true, "description": "Metadata saved on the user that is only visible to your backend.\nThe new object will be merged with the existing value."}, "unsafe_metadata": {"type": "object", "additionalProperties": true, "description": "Metadata saved on the user, that can be updated from both the Frontend and Backend APIs.\nThe new object will be merged with the existing value.\n\nNote: Since this data can be modified from the frontend, it is not guaranteed to be safe."}}}}}}, "responses": {"200": {"$ref": "#/components/responses/User"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}, "/users/{user_id}/oauth_access_tokens/{provider}": {"get": {"operationId": "GetOAuthAccessToken", "summary": "Retrieve the OAuth access token of a user", "description": "Fetch the corresponding OAuth access token for a user that has previously authenticated with a particular OAuth provider.\nFor OAuth 2.0, if the access token has expired and we have a corresponding refresh token, the access token will be refreshed transparently the new one will be returned.", "tags": ["Users"], "parameters": [{"name": "user_id", "in": "path", "description": "The ID of the user for which to retrieve the OAuth access token", "required": true, "schema": {"type": "string"}}, {"name": "provider", "in": "path", "description": "The ID of the OAuth provider (e.g. `oauth_google`)", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "The OAuth access token of the user, if any.", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"object": {"type": "string"}, "external_account_id": {"description": "External account ID", "type": "string"}, "provider_user_id": {"description": "The unique ID of the user in the external provider's system", "type": "string"}, "token": {"type": "string", "description": "The access token"}, "provider": {"type": "string", "description": "The ID of the provider"}, "public_metadata": {"type": "object", "additionalProperties": true}, "label": {"type": "string", "nullable": true}, "scopes": {"type": "array", "items": {"type": "string"}, "description": "The list of scopes that the token is valid for.\nOnly present for OAuth 2.0 tokens."}, "token_secret": {"type": "string", "description": "The token secret. Only present for OAuth 1.0 tokens."}, "expires_at": {"type": "integer", "format": "int64", "nullable": true, "description": "Unix timestamp of the access token expiration."}}}}}}}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}, "/users/{user_id}/organization_memberships": {"get": {"operationId": "UsersGetOrganizationMemberships", "summary": "Retrieve all memberships for a user", "description": "Retrieve a paginated list of the user's organization memberships", "tags": ["Users"], "parameters": [{"name": "user_id", "in": "path", "description": "The ID of the user whose organization memberships we want to retrieve", "required": true, "schema": {"type": "string"}}, {"$ref": "#/components/parameters/LimitParameter"}, {"$ref": "#/components/parameters/OffsetParameter"}], "responses": {"200": {"$ref": "#/components/responses/OrganizationMemberships"}, "403": {"$ref": "#/components/responses/ClerkErrors"}}}}, "/users/{user_id}/organization_invitations": {"get": {"operationId": "UsersGetOrganizationInvitations", "summary": "Retrieve all invitations for a user", "description": "Retrieve a paginated list of the user's organization invitations", "tags": ["Users"], "parameters": [{"name": "user_id", "in": "path", "description": "The ID of the user whose organization invitations we want to retrieve", "required": true, "schema": {"type": "string"}}, {"$ref": "#/components/parameters/LimitParameter"}, {"$ref": "#/components/parameters/OffsetParameter"}, {"in": "query", "name": "status", "description": "Filter organization invitations based on their status", "required": false, "schema": {"type": "string", "enum": ["pending", "accepted", "revoked"]}}], "responses": {"200": {"$ref": "#/components/responses/OrganizationInvitationsWithPublicOrganizationData"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "403": {"$ref": "#/components/responses/ClerkErrors"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}}, "/users/{user_id}/verify_password": {"post": {"operationId": "VerifyPassword", "summary": "Verify the password of a user", "description": "Check that the user's password matches the supplied input.\nUseful for custom auth flows and re-verification.", "tags": ["Users"], "parameters": [{"name": "user_id", "in": "path", "description": "The ID of the user for whom to verify the password", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"password": {"type": "string", "description": "The user password to verify"}}, "required": ["password"]}}}}, "responses": {"200": {"description": "The provided password was correct.", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"verified": {"type": "boolean", "nullable": false}}}}}}, "400": {"description": "The user does not have a password set."}, "404": {"description": "The user does not exist."}, "422": {"description": "The provided password was incorrect."}, "500": {"$ref": "#/components/responses/ClerkErrors"}}}}, "/users/{user_id}/verify_totp": {"post": {"operationId": "VerifyTOTP", "summary": "Verify a TOTP or backup code for a user", "description": "Verify that the provided TOTP or backup code is valid for the user.\nVerifying a backup code will result it in being consumed (i.e. it will\nbecome invalid).\nUseful for custom auth flows and re-verification.", "tags": ["Users"], "parameters": [{"name": "user_id", "in": "path", "description": "The ID of the user for whom to verify the TOTP", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"code": {"type": "string", "description": "The TOTP or backup code to verify"}}, "required": ["code"]}}}}, "responses": {"200": {"description": "The provided TOTP or backup code was correct.", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"verified": {"type": "boolean", "nullable": false}, "code_type": {"type": "string", "nullable": false, "enum": ["totp", "backup_code"]}}}}}}, "400": {"description": "The user does not have TOTP configured for their account."}, "404": {"description": "The user does not exist."}, "422": {"description": "The provided TOTP or backup code was incorrect."}, "500": {"$ref": "#/components/responses/ClerkErrors"}}}}, "/users/{user_id}/mfa": {"delete": {"operationId": "DisableMFA", "summary": "Disable a user's MFA methods", "description": "Disable all of a user's MFA methods (e.g. OTP sent via SMS, TOTP on their authenticator app) at once.", "tags": ["Users"], "parameters": [{"name": "user_id", "in": "path", "description": "The ID of the user whose MFA methods are to be disabled", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Successful operation.", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"user_id": {"type": "string", "nullable": false}}}}}}, "404": {"$ref": "#/components/responses/ResourceNotFound"}, "500": {"$ref": "#/components/responses/ClerkErrors"}}}}, "/users/{user_id}/backup_code": {"delete": {"operationId": "DeleteBackupCode", "summary": "Disable all user's Backup codes", "description": "Disable all of a user's backup codes.", "tags": ["Users"], "parameters": [{"name": "user_id", "in": "path", "description": "The ID of the user whose backup codes are to be deleted.", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Successful operation.", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"user_id": {"type": "string", "nullable": false}}}}}}, "404": {"$ref": "#/components/responses/ResourceNotFound"}, "500": {"$ref": "#/components/responses/ClerkErrors"}}}}, "/users/{user_id}/passkeys/{passkey_identification_id}": {"delete": {"operationId": "UserPasskeyDelete", "summary": "Delete a user passkey", "description": "Delete the passkey identification for a given user and notify them through email.", "tags": ["Users"], "parameters": [{"name": "user_id", "in": "path", "description": "The ID of the user that owns the passkey identity", "required": true, "schema": {"type": "string"}}, {"name": "passkey_identification_id", "in": "path", "description": "The ID of the passkey identity to be deleted", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/DeletedObject"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}, "500": {"$ref": "#/components/responses/ClerkErrors"}}}}, "/users/{user_id}/web3_wallets/{web3_wallet_identification_id}": {"delete": {"operationId": "UserWeb3WalletDelete", "summary": "Delete a user web3 wallet", "description": "Delete the web3 wallet identification for a given user.", "tags": ["Users"], "parameters": [{"name": "user_id", "in": "path", "description": "The ID of the user that owns the web3 wallet", "required": true, "schema": {"type": "string"}}, {"name": "web3_wallet_identification_id", "in": "path", "description": "The ID of the web3 wallet identity to be deleted", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/DeletedObject"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}, "500": {"$ref": "#/components/responses/ClerkErrors"}}}}, "/users/{user_id}/totp": {"post": {"operationId": "CreateUserTOTP", "summary": "Create a TOTP for a user", "description": "Creates a TOTP (Time-based One-Time Password) for a given user, returning both the TOTP secret and the URI.\n", "tags": ["Users"], "parameters": [{"name": "user_id", "in": "path", "description": "The ID of the user for whom the TOTP is being created.", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/TOTP"}, "403": {"$ref": "#/components/responses/ClerkErrors"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}, "500": {"$ref": "#/components/responses/ClerkErrors"}}}, "delete": {"operationId": "DeleteTOTP", "summary": "Delete all the user's TOTPs", "description": "Deletes all of the user's TOTPs.", "tags": ["Users"], "parameters": [{"name": "user_id", "in": "path", "description": "The ID of the user whose TOTPs are to be deleted", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Successful operation.", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"user_id": {"type": "string", "nullable": false}}}}}}, "404": {"$ref": "#/components/responses/ResourceNotFound"}, "500": {"$ref": "#/components/responses/ClerkErrors"}}}}, "/users/{user_id}/external_accounts/{external_account_id}": {"delete": {"operationId": "DeleteExternalAccount", "summary": "Delete External Account", "description": "Delete an external account by ID.", "tags": ["Users"], "parameters": [{"name": "user_id", "in": "path", "description": "The ID of the user's external account", "required": true, "schema": {"type": "string"}}, {"name": "external_account_id", "in": "path", "description": "The ID of the external account to delete", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/DeletedObject"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "403": {"$ref": "#/components/responses/ClerkErrors"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}, "500": {"$ref": "#/components/responses/ClerkErrors"}}}}, "/invitations": {"post": {"operationId": "CreateInvitation", "summary": "Create an invitation", "description": "Creates a new invitation for the given email address and sends the invitation email.\nKeep in mind that you cannot create an invitation if there is already one for the given email address.\nAlso, trying to create an invitation for an email address that already exists in your application will result to an error.", "tags": ["Invitations"], "requestBody": {"description": "Required parameters", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"email_address": {"type": "string", "description": "The email address the invitation will be sent to"}, "public_metadata": {"type": "object", "description": "Metadata that will be attached to the newly created invitation.\nThe value of this property should be a well-formed JSON object.\nOnce the user accepts the invitation and signs up, these metadata will end up in the user's public metadata.", "additionalProperties": true}, "redirect_url": {"type": "string", "description": "Optional URL which specifies where to redirect the user once they click the invitation link.\nThis is only required if you have implemented a [custom flow](https://clerk.com/docs/authentication/invitations#custom-flow) and you're not using Clerk Hosted Pages or Clerk Components."}, "notify": {"type": "boolean", "description": "Optional flag which denotes whether an email invitation should be sent to the given email address.\nDefaults to `true`.", "nullable": true, "default": true}, "ignore_existing": {"type": "boolean", "description": "Whether an invitation should be created if there is already an existing invitation for this email address, or it's claimed by another user.", "nullable": true, "default": false}, "expires_in_days": {"type": "integer", "description": "The number of days the invitation will be valid for. By default, the invitation expires after 30 days.", "nullable": true, "minimum": 1, "maximum": 365}, "template_slug": {"type": "string", "enum": ["invitation", "waitlist_invitation"], "description": "The slug of the email template to use for the invitation email.\nIf not provided, the \"invitation\" template will be used.", "nullable": true}}, "required": ["email_address"]}}}}, "responses": {"200": {"$ref": "#/components/responses/Invitation"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}, "get": {"summary": "List all invitations", "description": "Returns all non-revoked invitations for your application, sorted by creation date", "operationId": "ListInvitations", "parameters": [{"$ref": "#/components/parameters/LimitParameter"}, {"$ref": "#/components/parameters/OffsetParameter"}, {"in": "query", "name": "status", "description": "Filter invitations based on their status", "required": false, "schema": {"type": "string", "enum": ["pending", "accepted", "revoked", "expired"]}}, {"in": "query", "required": false, "name": "query", "description": "Filter invitations based on their `email_address` or `id`", "schema": {"type": "string"}}], "tags": ["Invitations"], "responses": {"200": {"$ref": "#/components/responses/Invitation.List"}}}}, "/invitations/bulk": {"post": {"operationId": "CreateBulkInvitations", "summary": "Create multiple invitations", "description": "Use this API operation to create multiple invitations for the provided email addresses. You can choose to send the\ninvitations as emails by setting the `notify` parameter to `true`. There cannot be an existing invitation for any\nof the email addresses you provide unless you set `ignore_existing` to `true` for specific email addresses. Please\nnote that there must be no existing user for any of the email addresses you provide, and this rule cannot be bypassed.", "tags": ["Invitations"], "requestBody": {"description": "Required parameters", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"email_address": {"type": "string", "description": "The email address the invitation will be sent to"}, "public_metadata": {"type": "object", "description": "Metadata that will be attached to the newly created invitation.\nThe value of this property should be a well-formed JSON object.\nOnce the user accepts the invitation and signs up, these metadata will end up in the user's public metadata.", "additionalProperties": true}, "redirect_url": {"type": "string", "description": "Optional URL which specifies where to redirect the user once they click the invitation link.\nThis is only required if you have implemented a [custom flow](https://clerk.com/docs/authentication/invitations#custom-flow) and you're not using Clerk Hosted Pages or Clerk Components."}, "notify": {"type": "boolean", "description": "Optional flag which denotes whether an email invitation should be sent to the given email address.\nDefaults to true.", "nullable": true, "default": true}, "ignore_existing": {"type": "boolean", "description": "Whether an invitation should be created if there is already an existing invitation for this email\naddress, or it's claimed by another user.", "nullable": true, "default": false}, "expires_in_days": {"type": "integer", "description": "The number of days the invitation will be valid for. By default, the invitation expires after 30 days.", "nullable": true, "minimum": 1, "maximum": 365}}, "required": ["email_address"]}}}}}, "responses": {"200": {"$ref": "#/components/responses/Invitation.List"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}, "/invitations/{invitation_id}/revoke": {"post": {"operationId": "RevokeInvitation", "summary": "Revokes an invitation", "description": "Revokes the given invitation.\nRevoking an invitation will prevent the user from using the invitation link that was sent to them.\nHowever, it doesn't prevent the user from signing up if they follow the sign up flow.\nOnly active (i.e. non-revoked) invitations can be revoked.", "tags": ["Invitations"], "parameters": [{"name": "invitation_id", "in": "path", "description": "The ID of the invitation to be revoked", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Invitation.Revoked"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}}, "/organization_invitations": {"get": {"operationId": "ListInstanceOrganizationInvitations", "summary": "Get a list of organization invitations for the current instance", "description": "This request returns the list of organization invitations for the instance.\nResults can be paginated using the optional `limit` and `offset` query parameters.\nYou can filter them by providing the 'status' query parameter, that accepts multiple values.\nYou can change the order by providing the 'order' query parameter, that accepts multiple values.\nYou can filter by the invited user email address providing the `query` query parameter.\nThe organization invitations are ordered by descending creation date by default.", "tags": ["Organization Invitations"], "parameters": [{"$ref": "#/components/parameters/LimitParameter"}, {"$ref": "#/components/parameters/OffsetParameter"}, {"in": "query", "name": "order_by", "required": false, "description": "Allows to return organization invitations in a particular order.\nAt the moment, you can order the returned organization invitations either by their `created_at` or `email_address`.\nIn order to specify the direction, you can use the `+/-` symbols prepended in the property to order by.\nFor example, if you want organization invitations to be returned in descending order according to their `created_at` property, you can use `-created_at`.\nIf you don't use `+` or `-`, then `+` is implied.\nDefaults to `-created_at`.", "schema": {"type": "string", "default": "-created_at"}}, {"in": "query", "name": "status", "description": "Filter organization invitations based on their status", "required": false, "schema": {"type": "string", "enum": ["pending", "accepted", "revoked"]}}, {"in": "query", "required": false, "name": "query", "description": "Filter organization invitations based on their `email_address`", "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/OrganizationInvitationsWithPublicOrganizationData"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}, "422": {"$ref": "#/components/responses/ClerkErrors"}, "500": {"$ref": "#/components/responses/ClerkErrors"}}}}, "/allowlist_identifiers": {"get": {"operationId": "ListAllowlistIdentifiers", "summary": "List all identifiers on the allow-list", "description": "Get a list of all identifiers allowed to sign up to an instance", "tags": ["Allow-list / Block-list"], "responses": {"200": {"$ref": "#/components/responses/AllowlistIdentifier.List"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "402": {"$ref": "#/components/responses/PaymentRequired"}}}, "post": {"operationId": "CreateAllowlistIdentifier", "summary": "Add identifier to the allow-list", "description": "Create an identifier allowed to sign up to an instance", "tags": ["Allow-list / Block-list"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"identifier": {"type": "string", "description": "The identifier to be added in the allow-list.\nThis can be an email address, a phone number or a web3 wallet."}, "notify": {"type": "boolean", "description": "This flag denotes whether the given identifier will receive an invitation to join the application.\nNote that this only works for email address and phone number identifiers.", "default": false}}, "required": ["identifier"]}}}}, "responses": {"200": {"$ref": "#/components/responses/AllowlistIdentifier"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "402": {"$ref": "#/components/responses/PaymentRequired"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}, "/allowlist_identifiers/{identifier_id}": {"delete": {"operationId": "DeleteAllowlistIdentifier", "summary": "Delete identifier from allow-list", "description": "Delete an identifier from the instance allow-list", "tags": ["Allow-list / Block-list"], "parameters": [{"name": "identifier_id", "in": "path", "description": "The ID of the identifier to delete from the allow-list", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/DeletedObject"}, "402": {"$ref": "#/components/responses/PaymentRequired"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}}, "/blocklist_identifiers": {"get": {"operationId": "ListBlocklistIdentifiers", "summary": "List all identifiers on the block-list", "description": "Get a list of all identifiers which are not allowed to access an instance", "tags": ["Allow-list / Block-list"], "responses": {"200": {"$ref": "#/components/responses/BlocklistIdentifier.List"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "402": {"$ref": "#/components/responses/PaymentRequired"}}}, "post": {"operationId": "CreateBlocklistIdentifier", "summary": "Add identifier to the block-list", "description": "Create an identifier that is blocked from accessing an instance", "tags": ["Allow-list / Block-list"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"identifier": {"type": "string", "description": "The identifier to be added in the block-list.\nThis can be an email address, a phone number or a web3 wallet."}}, "required": ["identifier"]}}}}, "responses": {"200": {"$ref": "#/components/responses/BlocklistIdentifier"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "402": {"$ref": "#/components/responses/PaymentRequired"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}, "/blocklist_identifiers/{identifier_id}": {"delete": {"operationId": "DeleteBlocklistIdentifier", "summary": "Delete identifier from block-list", "description": "Delete an identifier from the instance block-list", "tags": ["Allow-list / Block-list"], "parameters": [{"name": "identifier_id", "in": "path", "description": "The ID of the identifier to delete from the block-list", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/DeletedObject"}, "402": {"$ref": "#/components/responses/PaymentRequired"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}}, "/beta_features/instance_settings": {"patch": {"operationId": "UpdateInstanceAuthConfig", "summary": "Update instance settings", "description": "Updates the settings of an instance", "tags": ["Beta Features"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"restricted_to_allowlist": {"type": "boolean", "description": "Whether sign up is restricted to email addresses, phone numbers and usernames that are on the allowlist.", "nullable": true, "default": false}, "from_email_address": {"type": "string", "description": "The local part of the email address from which authentication-related emails (e.g. OTP code, magic links) will be sent.\nOnly alphanumeric values are allowed.\nNote that this value should contain only the local part of the address (e.g. `foo` for `<EMAIL>`).", "nullable": true}, "progressive_sign_up": {"type": "boolean", "description": "Enable the Progressive Sign Up algorithm. Refer to the [docs](https://clerk.com/docs/upgrade-guides/progressive-sign-up) for more info.", "nullable": true}, "session_token_template": {"type": "string", "description": "The name of the JWT Template used to augment your session tokens. To disable this, pass an empty string.", "nullable": true}, "enhanced_email_deliverability": {"type": "boolean", "description": "The \"enhanced_email_deliverability\" feature will send emails from \"<EMAIL>\" instead of your domain.\nThis can be helpful if you do not have a high domain reputation.", "nullable": true}, "test_mode": {"type": "boolean", "description": "Toggles test mode for this instance, allowing the use of test email addresses and phone numbers.\nDefaults to true for development instances.", "nullable": true}}}}}}, "responses": {"200": {"$ref": "#/components/responses/InstanceSettings"}, "402": {"$ref": "#/components/responses/PaymentRequired"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}, "/beta_features/domain": {"put": {"deprecated": true, "operationId": "UpdateProductionInstanceDomain", "summary": "Update production instance domain", "description": "Change the domain of a production instance.\n\nChanging the domain requires updating the [DNS records](https://clerk.com/docs/deployments/overview#dns-records) accordingly, deploying new [SSL certificates](https://clerk.com/docs/deployments/overview#deploy), updating your Social Connection's redirect URLs and setting the new keys in your code.\n\nWARNING: Changing your domain will invalidate all current user sessions (i.e. users will be logged out). Also, while your application is being deployed, a small downtime is expected to occur.", "tags": ["Beta Features"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"home_url": {"type": "string", "description": "The new home URL of the production instance e.g. https://www.example.com"}}}}}}, "responses": {"202": {"description": "Accepted"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "422": {"$ref": "#/components/responses/ClerkErrors"}}}}, "/actor_tokens": {"post": {"summary": "Create actor token", "description": "Create an actor token that can be used to impersonate the given user.\nThe `actor` parameter needs to include at least a \"sub\" key whose value is the ID of the actor (impersonating) user.", "operationId": "CreateActorToken", "tags": ["Actor <PERSON><PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"user_id": {"type": "string", "description": "The ID of the user being impersonated."}, "actor": {"type": "object", "description": "The actor payload. It needs to include a sub property which should contain the ID of the actor.\nThis whole payload will be also included in the JWT session token.", "example": {"sub": "user_2OEpKhcCN1Lat9NQ0G6puh7q5Rb"}}, "expires_in_seconds": {"type": "integer", "description": "Optional parameter to specify the life duration of the actor token in seconds.\nBy default, the duration is 1 hour.", "default": 3600}, "session_max_duration_in_seconds": {"type": "integer", "description": "The maximum duration that the session which will be created by the generated actor token should last.\nBy default, the duration of a session created via an actor token, lasts 30 minutes.", "default": 1800}}, "required": ["user_id", "actor"]}}}}, "responses": {"200": {"$ref": "#/components/responses/ActorToken"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "402": {"$ref": "#/components/responses/PaymentRequired"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}, "/actor_tokens/{actor_token_id}/revoke": {"post": {"summary": "Revoke actor token", "description": "Revokes a pending actor token.", "operationId": "RevokeActorToken", "tags": ["Actor <PERSON><PERSON><PERSON>"], "parameters": [{"name": "actor_token_id", "in": "path", "description": "The ID of the actor token to be revoked.", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/ActorToken"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}}, "/domains": {"get": {"summary": "List all instance domains", "description": "Use this endpoint to get a list of all domains for an instance.\nThe response will contain the primary domain for the instance and any satellite domains. Each domain in the response contains information about the URLs where <PERSON> operates and the required CNAME targets.", "operationId": "ListDomains", "tags": ["Domains"], "responses": {"200": {"$ref": "#/components/responses/Domains"}}}, "post": {"summary": "Add a domain", "description": "Add a new domain for your instance.\nUseful in the case of multi-domain instances, allows adding satellite domains to an instance.\nThe new domain must have a `name`. The domain name can contain the port for development instances, like `localhost:3000`.\nAt the moment, instances can have only one primary domain, so the `is_satellite` parameter must be set to `true`.\nIf you're planning to configure the new satellite domain to run behind a proxy, pass the `proxy_url` parameter accordingly.", "operationId": "AddDomain", "tags": ["Domains"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "description": "The new domain name. Can contain the port for development instances."}, "is_satellite": {"type": "boolean", "description": "Marks the new domain as satellite. Only `true` is accepted at the moment.", "enum": [true]}, "proxy_url": {"type": "string", "description": "The full URL of the proxy which will forward requests to the Clerk Frontend API for this domain. Applicable only to production instances."}}, "required": ["name", "is_satellite"]}}}}, "responses": {"200": {"$ref": "#/components/responses/Domain"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "402": {"$ref": "#/components/responses/ClerkErrors"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}, "/domains/{domain_id}": {"delete": {"operationId": "DeleteDomain", "summary": "Delete a satellite domain", "description": "Deletes a satellite domain for the instance.\nIt is currently not possible to delete the instance's primary domain.", "tags": ["Domains"], "parameters": [{"in": "path", "name": "domain_id", "required": true, "schema": {"type": "string"}, "description": "The ID of the domain that will be deleted. Must be a satellite domain."}], "responses": {"200": {"$ref": "#/components/responses/DeletedObject"}, "403": {"$ref": "#/components/responses/ClerkErrors"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}, "patch": {"operationId": "UpdateDomain", "summary": "Update a domain", "description": "The `proxy_url` can be updated only for production instances.\nUpdate one of the instance's domains. Both primary and satellite domains can be updated.\nIf you choose to use <PERSON> via proxy, use this endpoint to specify the `proxy_url`.\nWhenever you decide you'd rather switch to DNS setup for <PERSON>, simply set `proxy_url`\nto `null` for the domain. When you update a production instance's primary domain name,\nyou have to make sure that you've completed all the necessary setup steps for DNS and\nemails to work. Expect downtime otherwise. Updating a primary domain's name will also\nupdate the instance's home origin, affecting the default application paths.", "tags": ["Domains"], "parameters": [{"in": "path", "name": "domain_id", "required": true, "schema": {"type": "string"}, "description": "The ID of the domain that will be updated."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string", "description": "The new domain name. For development instances, can contain the port,\ni.e `myhostname:3000`. For production instances, must be a valid FQDN,\ni.e `mysite.com`. Cannot contain protocol scheme.", "nullable": true}, "proxy_url": {"type": "string", "description": "The full URL of the proxy that will forward requests to Clerk's Frontend API.\nCan only be updated for production instances.", "nullable": true}, "is_secondary": {"type": "boolean", "description": "Whether this is a domain for a secondary app, meaning that any subdomain provided is significant and\nwill be stored as part of the domain. This is useful for supporting multiple apps (one primary and\nmultiple secondaries) on the same root domain (eTLD+1).", "nullable": true}}}}}}, "responses": {"200": {"$ref": "#/components/responses/Domain"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}, "/instance": {"get": {"operationId": "GetInstance", "summary": "Fetch the current instance", "description": "Fetches the current instance", "tags": ["Instance Settings"], "responses": {"200": {"$ref": "#/components/responses/Instance"}}}, "patch": {"operationId": "UpdateInstance", "summary": "Update instance settings", "description": "Updates the settings of an instance", "tags": ["Instance Settings"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"test_mode": {"type": "boolean", "description": "Toggles test mode for this instance, allowing the use of test email addresses and phone numbers.\nDefaults to true for development instances.", "nullable": true}, "hibp": {"type": "boolean", "description": "Whether the instance should be using the HIBP service to check passwords for breaches", "nullable": true}, "enhanced_email_deliverability": {"type": "boolean", "description": "The \"enhanced_email_deliverability\" feature will send emails from \"<EMAIL>\" instead of your domain.\nThis can be helpful if you do not have a high domain reputation.", "nullable": true}, "support_email": {"type": "string", "nullable": true}, "clerk_js_version": {"type": "string", "nullable": true}, "development_origin": {"type": "string", "nullable": true}, "allowed_origins": {"type": "array", "items": {"type": "string"}, "description": "For browser-like stacks such as browser extensions, Electron, or Capacitor.js the instance allowed origins need to be updated with the request origin value.\nFor Chrome extensions popup, background, or service worker pages the origin is chrome-extension://extension_uiid. For Electron apps the default origin is http://localhost:3000. For Capacitor, the origin is capacitor://localhost."}, "cookieless_dev": {"type": "boolean", "description": "Whether the instance should operate in cookieless development mode (i.e. without third-party cookies).\nDeprecated: Please use `url_based_session_syncing` instead.", "deprecated": true}, "url_based_session_syncing": {"type": "boolean", "description": "Whether the instance should use URL-based session syncing in development mode (i.e. without third-party cookies)."}}}}}}, "responses": {"204": {"description": "Accepted"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}, "/instance/restrictions": {"patch": {"operationId": "UpdateInstanceRestrictions", "summary": "Update instance restrictions", "description": "Updates the restriction settings of an instance", "tags": ["Instance Settings"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"allowlist": {"type": "boolean", "nullable": true}, "blocklist": {"type": "boolean", "nullable": true}, "block_email_subaddresses": {"type": "boolean", "nullable": true}, "block_disposable_email_domains": {"type": "boolean", "nullable": true}, "ignore_dots_for_gmail_addresses": {"type": "boolean", "nullable": true}}}}}}, "responses": {"200": {"$ref": "#/components/responses/InstanceRestrictions"}, "402": {"$ref": "#/components/responses/PaymentRequired"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}, "/instance/change_domain": {"post": {"operationId": "ChangeProductionInstanceDomain", "summary": "Update production instance domain", "description": "Change the domain of a production instance.\n\nChanging the domain requires updating the [DNS records](https://clerk.com/docs/deployments/overview#dns-records) accordingly, deploying new [SSL certificates](https://clerk.com/docs/deployments/overview#deploy), updating your Social Connection's redirect URLs and setting the new keys in your code.\n\nWARNING: Changing your domain will invalidate all current user sessions (i.e. users will be logged out). Also, while your application is being deployed, a small downtime is expected to occur.", "tags": ["Beta Features"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"home_url": {"type": "string", "description": "The new home URL of the production instance e.g. https://www.example.com"}, "is_secondary": {"type": "boolean", "description": "Whether this is a domain for a secondary app, meaning that any subdomain provided is significant and\nwill be stored as part of the domain. This is useful for supporting multiple apps (one primary and\nmultiple secondaries) on the same root domain (eTLD+1)."}}}}}}, "responses": {"202": {"description": "Accepted"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "422": {"$ref": "#/components/responses/ClerkErrors"}}}}, "/instance/organization_settings": {"patch": {"operationId": "UpdateInstanceOrganizationSettings", "summary": "Update instance organization settings", "description": "Updates the organization settings of the instance", "tags": ["Instance Settings"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "nullable": true}, "max_allowed_memberships": {"type": "integer", "nullable": true}, "admin_delete_enabled": {"type": "boolean", "nullable": true}, "domains_enabled": {"type": "boolean", "nullable": true}, "domains_enrollment_modes": {"type": "array", "items": {"type": "string"}, "description": "Specify which enrollment modes to enable for your Organization Domains.\nSupported modes are 'automatic_invitation' & 'automatic_suggestion'."}, "creator_role_id": {"type": "string", "description": "Specify what the default organization role is for an organization creator."}, "domains_default_role_id": {"type": "string", "description": "Specify what the default organization role is for the organization domains."}}}}}}, "responses": {"200": {"$ref": "#/components/responses/OrganizationSettings"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "402": {"$ref": "#/components/responses/PaymentRequired"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}, "/webhooks/svix": {"post": {"operationId": "CreateSvixApp", "summary": "Create a Svix app", "description": "Create a Svix app and associate it with the current instance", "tags": ["Webhooks"], "responses": {"200": {"$ref": "#/components/responses/SvixURL"}, "400": {"$ref": "#/components/responses/ClerkErrors"}}}, "delete": {"operationId": "DeleteSvixApp", "summary": "Delete a Svix app", "description": "Delete a Svix app and disassociate it from the current instance", "tags": ["Webhooks"], "responses": {"204": {"description": "Svix app was successfully deleted"}, "400": {"$ref": "#/components/responses/ClerkErrors"}}}}, "/webhooks/svix_url": {"post": {"operationId": "GenerateSvixAuthURL", "summary": "Create a Svix Dashboard URL", "description": "Generate a new url for accessing the Svix's management dashboard for that particular instance", "tags": ["Webhooks"], "responses": {"200": {"$ref": "#/components/responses/SvixURL"}, "400": {"$ref": "#/components/responses/ClerkErrors"}}}}, "/jwt_templates": {"get": {"summary": "List all templates", "operationId": "ListJWTTemplates", "tags": ["JWT Templates"], "responses": {"200": {"$ref": "#/components/responses/JWTTemplate.List"}}}, "post": {"operationId": "CreateJWTTemplate", "summary": "Create a JWT template", "description": "Create a new JWT template", "tags": ["JWT Templates"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "description": "JWT template name", "nullable": false}, "claims": {"type": "object", "description": "JWT template claims in JSON format", "nullable": false}, "lifetime": {"type": "number", "minimum": 30, "maximum": 315360000, "description": "JWT token lifetime", "nullable": true}, "allowed_clock_skew": {"type": "number", "minimum": 0, "maximum": 300, "description": "JWT token allowed clock skew", "nullable": true}, "custom_signing_key": {"type": "boolean", "description": "Whether a custom signing key/algorithm is also provided for this template", "nullable": false}, "signing_algorithm": {"type": "string", "description": "The custom signing algorithm to use when minting JWTs", "nullable": true}, "signing_key": {"type": "string", "description": "The custom signing private key to use when minting JWTs", "nullable": true}}}}}}, "responses": {"200": {"$ref": "#/components/responses/JWTTemplate"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "402": {"$ref": "#/components/responses/PaymentRequired"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}, "/jwt_templates/{template_id}": {"get": {"operationId": "GetJWTTemplate", "summary": "Retrieve a template", "description": "Retrieve the details of a given JWT template", "tags": ["JWT Templates"], "parameters": [{"name": "template_id", "in": "path", "description": "JWT Template ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/JWTTemplate"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}, "patch": {"operationId": "UpdateJWTTemplate", "summary": "Update a JWT template", "description": "Updates an existing JWT template", "tags": ["JWT Templates"], "parameters": [{"name": "template_id", "in": "path", "description": "The ID of the JWT template to update", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "description": "JWT template name", "nullable": false}, "claims": {"type": "object", "description": "JWT template claims in JSON format", "nullable": false}, "lifetime": {"type": "number", "minimum": 30, "maximum": 315360000, "description": "JWT token lifetime", "nullable": true}, "allowed_clock_skew": {"type": "number", "minimum": 0, "maximum": 300, "description": "JWT token allowed clock skew", "nullable": true}, "custom_signing_key": {"type": "boolean", "description": "Whether a custom signing key/algorithm is also provided for this template", "nullable": false}, "signing_algorithm": {"type": "string", "description": "The custom signing algorithm to use when minting JWTs", "nullable": true}, "signing_key": {"type": "string", "description": "The custom signing private key to use when minting JWTs", "nullable": true}}}}}}, "responses": {"200": {"$ref": "#/components/responses/JWTTemplate"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "402": {"$ref": "#/components/responses/PaymentRequired"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}, "delete": {"operationId": "DeleteJWTTemplate", "summary": "Delete a Template", "description": "", "tags": ["JWT Templates"], "parameters": [{"name": "template_id", "in": "path", "description": "JWT Template ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/DeletedObject"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}}, "/organizations": {"get": {"operationId": "ListOrganizations", "summary": "Get a list of organizations for an instance", "description": "This request returns the list of organizations for an instance.\nResults can be paginated using the optional `limit` and `offset` query parameters.\nThe organizations are ordered by descending creation date.\nMost recent organizations will be returned first.", "tags": ["Organizations"], "parameters": [{"$ref": "#/components/parameters/LimitParameter"}, {"$ref": "#/components/parameters/OffsetParameter"}, {"in": "query", "required": false, "name": "include_members_count", "description": "Flag to denote whether the member counts of each organization should be included in the response or not.", "schema": {"type": "boolean"}}, {"in": "query", "required": false, "name": "query", "description": "Returns organizations with ID, name, or slug that match the given query.\nUses exact match for organization ID and partial match for name and slug.", "schema": {"type": "string"}}, {"in": "query", "name": "order_by", "description": "Allows to return organizations in a particular order.\nAt the moment, you can order the returned organizations either by their `name`, `created_at` or `members_count`.\nIn order to specify the direction, you can use the `+/-` symbols prepended in the property to order by.\nFor example, if you want organizations to be returned in descending order according to their `created_at` property, you can use `-created_at`.\nIf you don't use `+` or `-`, then `+` is implied.\nDefaults to `-created_at`.", "schema": {"type": "string", "default": "-created_at"}, "required": false}], "responses": {"200": {"$ref": "#/components/responses/Organizations"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}, "post": {"operationId": "CreateOrganization", "summary": "Create an organization", "description": "Creates a new organization with the given name for an instance.\nYou can specify an optional slug for the new organization.\nIf provided, the organization slug can contain only lowercase alphanumeric characters (letters and digits) and the dash \"-\".\nOrganization slugs must be unique for the instance.\nYou can provide additional metadata for the organization and set any custom attribute you want.\nOrganizations support private and public metadata.\nPrivate metadata can only be accessed from the Backend API.\nPublic metadata can be accessed from the Backend API, and are read-only from the Frontend API.\nThe `created_by` user will see this as their [active organization] (https://clerk.com/docs/organizations/overview#active-organization)\nthe next time they create a session, presuming they don't explicitly set a different organization as active before then.", "tags": ["Organizations"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the new organization.\nMay not contain URLs or HTML."}, "created_by": {"type": "string", "description": "The ID of the User who will become the administrator for the new organization"}, "private_metadata": {"type": "object", "description": "Metadata saved on the organization, accessible only from the Backend API", "additionalProperties": true}, "public_metadata": {"type": "object", "description": "Metadata saved on the organization, read-only from the Frontend API and fully accessible (read/write) from the Backend API", "additionalProperties": true}, "slug": {"type": "string", "description": "A slug for the new organization.\nCan contain only lowercase alphanumeric characters and the dash \"-\".\nMust be unique for the instance."}, "max_allowed_memberships": {"type": "integer", "description": "The maximum number of memberships allowed for this organization"}, "created_at": {"type": "string", "description": "A custom date/time denoting _when_ the organization was created, specified in RFC3339 format (e.g. `2012-10-20T07:15:20.902Z`)."}}, "required": ["name"]}}}}, "responses": {"200": {"$ref": "#/components/responses/Organization"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}, "/organizations/{organization_id}": {"get": {"operationId": "GetOrganization", "summary": "Retrieve an organization by ID or slug", "description": "Fetches the organization whose ID or slug matches the provided `id_or_slug` URL query parameter.", "tags": ["Organizations"], "parameters": [{"in": "path", "name": "organization_id", "required": true, "schema": {"type": "string"}, "description": "The ID or slug of the organization"}, {"in": "query", "required": false, "name": "include_members_count", "description": "Flag to denote whether or not the organization's members count should be included in the response.", "schema": {"type": "boolean"}}], "responses": {"200": {"$ref": "#/components/responses/Organization"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}, "patch": {"operationId": "UpdateOrganization", "summary": "Update an organization", "description": "Updates an existing organization", "tags": ["Organizations"], "parameters": [{"in": "path", "name": "organization_id", "required": true, "schema": {"type": "string"}, "description": "The ID of the organization to update"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"public_metadata": {"type": "object", "description": "<PERSON><PERSON><PERSON> saved on the organization, that is visible to both your frontend and backend.", "additionalProperties": true}, "private_metadata": {"type": "object", "description": "<PERSON><PERSON><PERSON> saved on the organization that is only visible to your backend.", "additionalProperties": true}, "name": {"type": "string", "description": "The new name of the organization.\nMay not contain URLs or HTML.", "nullable": true}, "slug": {"type": "string", "description": "The new slug of the organization, which needs to be unique in the instance", "nullable": true}, "max_allowed_memberships": {"type": "integer", "description": "The maximum number of memberships allowed for this organization", "nullable": true}, "admin_delete_enabled": {"type": "boolean", "description": "If true, an admin can delete this organization with the Frontend API.", "nullable": true}, "created_at": {"type": "string", "description": "A custom date/time denoting _when_ the organization was created, specified in RFC3339 format (e.g. `2012-10-20T07:15:20.902Z`)."}}}}}}, "responses": {"200": {"$ref": "#/components/responses/Organization"}, "402": {"$ref": "#/components/responses/ResourceNotFound"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}, "delete": {"operationId": "DeleteOrganization", "summary": "Delete an organization", "description": "Deletes the given organization.\nPlease note that deleting an organization will also delete all memberships and invitations.\nThis is not reversible.", "tags": ["Organizations"], "parameters": [{"in": "path", "name": "organization_id", "required": true, "schema": {"type": "string"}, "description": "The ID of the organization to delete"}], "responses": {"200": {"$ref": "#/components/responses/DeletedObject"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}}, "/organizations/{organization_id}/metadata": {"patch": {"operationId": "MergeOrganizationMetadata", "summary": "Merge and update metadata for an organization", "description": "Update organization metadata attributes by merging existing values with the provided parameters.\nMetadata values will be updated via a deep merge.\nDeep meaning that any nested JSON objects will be merged as well.\nYou can remove metadata keys at any level by setting their value to `null`.", "tags": ["Organizations"], "parameters": [{"name": "organization_id", "in": "path", "description": "The ID of the organization for which metadata will be merged or updated", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"public_metadata": {"type": "object", "description": "Metadata saved on the organization, that is visible to both your frontend and backend.\nThe new object will be merged with the existing value.", "additionalProperties": true}, "private_metadata": {"type": "object", "description": "Metadata saved on the organization that is only visible to your backend.\nThe new object will be merged with the existing value.", "additionalProperties": true}}}}}}, "responses": {"200": {"$ref": "#/components/responses/Organization"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}, "/organizations/{organization_id}/logo": {"put": {"operationId": "UploadOrganizationLogo", "summary": "Upload a logo for the organization", "description": "Set or replace an organization's logo, by uploading an image file.\nThis endpoint uses the `multipart/form-data` request content type and accepts a file of image type.\nThe file size cannot exceed 10MB.\nOnly the following file content types are supported: `image/jpeg`, `image/png`, `image/gif`, `image/webp`, `image/x-icon`, `image/vnd.microsoft.icon`.", "tags": ["Organizations"], "parameters": [{"name": "organization_id", "in": "path", "description": "The ID of the organization for which to upload a logo", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"uploader_user_id": {"type": "string", "description": "The ID of the user that will be credited with the image upload."}, "file": {"type": "string", "format": "binary"}}, "required": ["file"]}}}}, "responses": {"200": {"$ref": "#/components/responses/OrganizationWithLogo"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}, "413": {"$ref": "#/components/responses/ClerkErrors"}}}, "delete": {"operationId": "DeleteOrganizationLogo", "description": "Delete the organization's logo.", "tags": ["Organizations"], "parameters": [{"name": "organization_id", "in": "path", "description": "The ID of the organization for which the logo will be deleted.", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Organization"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}}, "/organizations/{organization_id}/invitations": {"post": {"operationId": "CreateOrganizationInvitation", "summary": "Create and send an organization invitation", "description": "Creates a new organization invitation and sends an email to the provided `email_address` with a link to accept the invitation and join the organization.\nYou can specify the `role` for the invited organization member.\n\nNew organization invitations get a \"pending\" status until they are revoked by an organization administrator or accepted by the invitee.\n\nThe request body supports passing an optional `redirect_url` parameter.\nWhen the invited user clicks the link to accept the invitation, they will be redirected to the URL provided.\nUse this parameter to implement a custom invitation acceptance flow.\n\nYou can specify the ID of the user that will send the invitation with the `inviter_user_id` parameter.\nThat user must be a member with administrator privileges in the organization.\nOnly \"admin\" members can create organization invitations.\n\nYou can optionally provide public and private metadata for the organization invitation.\nThe public metadata are visible by both the Frontend and the Backend whereas the private ones only by the Backend.\nWhen the organization invitation is accepted, the metadata will be transferred to the newly created organization membership.", "tags": ["Organization Invitations"], "parameters": [{"in": "path", "required": true, "name": "organization_id", "schema": {"type": "string"}, "description": "The ID of the organization for which to send the invitation"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"email_address": {"type": "string", "description": "The email address of the new member that is going to be invited to the organization"}, "inviter_user_id": {"type": "string", "nullable": true, "description": "The ID of the user that invites the new member to the organization.\nMust be an administrator in the organization."}, "role": {"type": "string", "description": "The role of the new member in the organization"}, "public_metadata": {"type": "object", "description": "Metadata saved on the organization invitation, read-only from the Frontend API and fully accessible (read/write) from the Backend API.\nWhen the organization invitation is accepted, the metadata will be transferred to the newly created organization membership.", "additionalProperties": true}, "private_metadata": {"type": "object", "description": "Metadata saved on the organization invitation, fully accessible (read/write) from the Backend API but not visible from the Frontend API.\nWhen the organization invitation is accepted, the metadata will be transferred to the newly created organization membership.", "additionalProperties": true}, "redirect_url": {"type": "string", "description": "Optional URL that the invitee will be redirected to once they accept the invitation by clicking the join link in the invitation email."}}, "required": ["email_address", "role"]}}}}, "responses": {"200": {"$ref": "#/components/responses/OrganizationInvitation"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}, "get": {"operationId": "ListOrganizationInvitations", "summary": "Get a list of organization invitations", "description": "This request returns the list of organization invitations.\nResults can be paginated using the optional `limit` and `offset` query parameters.\nYou can filter them by providing the 'status' query parameter, that accepts multiple values.\nThe organization invitations are ordered by descending creation date.\nMost recent invitations will be returned first.\nAny invitations created as a result of an Organization Domain are not included in the results.", "tags": ["Organization Invitations"], "parameters": [{"in": "path", "required": true, "name": "organization_id", "schema": {"type": "string"}, "description": "The organization ID."}, {"$ref": "#/components/parameters/LimitParameter"}, {"$ref": "#/components/parameters/OffsetParameter"}, {"in": "query", "name": "status", "description": "Filter organization invitations based on their status", "required": false, "schema": {"type": "string", "enum": ["pending", "accepted", "revoked"]}}], "responses": {"200": {"$ref": "#/components/responses/OrganizationInvitations"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}}, "/organizations/{organization_id}/invitations/bulk": {"post": {"operationId": "CreateOrganizationInvitationBulk", "summary": "Bulk create and send organization invitations", "description": "Creates new organization invitations in bulk and sends out emails to the provided email addresses with a link to accept the invitation and join the organization.\nYou can specify a different `role` for each invited organization member.\nNew organization invitations get a \"pending\" status until they are revoked by an organization administrator or accepted by the invitee.\nThe request body supports passing an optional `redirect_url` parameter for each invitation.\nWhen the invited user clicks the link to accept the invitation, they will be redirected to the provided URL.\nUse this parameter to implement a custom invitation acceptance flow.\nYou can specify the ID of the user that will send the invitation with the `inviter_user_id` parameter. Each invitation\ncan have a different inviter user.\nInviter users must be members with administrator privileges in the organization.\nOnly \"admin\" members can create organization invitations.\nYou can optionally provide public and private metadata for each organization invitation. The public metadata are visible\nby both the Frontend and the Backend, whereas the private metadata are only visible by the Backend.\nWhen the organization invitation is accepted, the metadata will be transferred to the newly created organization membership.", "tags": ["Organization Invitations"], "parameters": [{"in": "path", "required": true, "name": "organization_id", "schema": {"type": "string"}, "description": "The organization ID."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"email_address": {"type": "string", "description": "The email address of the new member that is going to be invited to the organization"}, "inviter_user_id": {"type": "string", "nullable": true, "description": "The ID of the user that invites the new member to the organization.\nMust be an administrator in the organization."}, "role": {"type": "string", "description": "The role of the new member in the organization."}, "public_metadata": {"type": "object", "description": "Metadata saved on the organization invitation, read-only from the Frontend API and fully accessible (read/write) from the Backend API.\nWhen the organization invitation is accepted, the metadata will be transferred to the newly created organization membership.", "additionalProperties": true}, "private_metadata": {"type": "object", "description": "Metadata saved on the organization invitation, fully accessible (read/write) from the Backend API but not visible from the Frontend API.\nWhen the organization invitation is accepted, the metadata will be transferred to the newly created organization membership.", "additionalProperties": true}, "redirect_url": {"type": "string", "description": "Optional URL that the invitee will be redirected to once they accept the invitation by clicking the join link in the invitation email."}}, "required": ["email_address", "role"]}}}}}, "responses": {"200": {"$ref": "#/components/responses/OrganizationInvitations"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}, "/organizations/{organization_id}/invitations/pending": {"get": {"deprecated": true, "operationId": "ListPendingOrganizationInvitations", "summary": "Get a list of pending organization invitations", "description": "This request returns the list of organization invitations with \"pending\" status.\nThese are the organization invitations that can still be used to join the organization, but have not been accepted by the invited user yet.\nResults can be paginated using the optional `limit` and `offset` query parameters.\nThe organization invitations are ordered by descending creation date.\nMost recent invitations will be returned first.\nAny invitations created as a result of an Organization Domain are not included in the results.", "tags": ["Organization Invitations"], "parameters": [{"in": "path", "required": true, "name": "organization_id", "schema": {"type": "string"}, "description": "The organization ID."}, {"$ref": "#/components/parameters/LimitParameter"}, {"$ref": "#/components/parameters/OffsetParameter"}], "responses": {"200": {"$ref": "#/components/responses/OrganizationInvitations"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}}, "/organizations/{organization_id}/invitations/{invitation_id}": {"get": {"operationId": "GetOrganizationInvitation", "summary": "Retrieve an organization invitation by ID", "description": "Use this request to get an existing organization invitation by ID.", "tags": ["Organization Invitations"], "parameters": [{"in": "path", "required": true, "name": "organization_id", "schema": {"type": "string"}, "description": "The organization ID."}, {"in": "path", "required": true, "name": "invitation_id", "schema": {"type": "string"}, "description": "The organization invitation ID."}], "responses": {"200": {"$ref": "#/components/responses/OrganizationInvitation"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}}, "/organizations/{organization_id}/invitations/{invitation_id}/revoke": {"post": {"operationId": "RevokeOrganizationInvitation", "summary": "Revoke a pending organization invitation", "description": "Use this request to revoke a previously issued organization invitation.\nRevoking an organization invitation makes it invalid; the invited user will no longer be able to join the organization with the revoked invitation.\nOnly organization invitations with \"pending\" status can be revoked.\nThe request accepts the `requesting_user_id` parameter to specify the user which revokes the invitation.\nOnly users with \"admin\" role can revoke invitations.", "tags": ["Organization Invitations"], "parameters": [{"in": "path", "required": true, "name": "organization_id", "schema": {"type": "string"}, "description": "The organization ID."}, {"in": "path", "required": true, "name": "invitation_id", "schema": {"type": "string"}, "description": "The organization invitation ID."}], "requestBody": {"required": false, "content": {"application/json": {"schema": {"type": "object", "properties": {"requesting_user_id": {"type": "string", "nullable": true, "description": "The ID of the user that revokes the invitation.\nMust be an administrator in the organization."}}}}}}, "responses": {"200": {"$ref": "#/components/responses/OrganizationInvitation"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}}, "/organizations/{organization_id}/memberships": {"post": {"operationId": "CreateOrganizationMembership", "summary": "Create a new organization membership", "description": "Adds a user as a member to the given organization.\nOnly users in the same instance as the organization can be added as members.\n\nThis organization will be the user's [active organization] (https://clerk.com/docs/organizations/overview#active-organization)\nthe next time they create a session, presuming they don't explicitly set a\ndifferent organization as active before then.", "tags": ["Organization Memberships"], "parameters": [{"in": "path", "required": true, "name": "organization_id", "schema": {"type": "string"}, "description": "The ID of the organization where the new membership will be created"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"user_id": {"type": "string", "description": "The ID of the user that will be added as a member in the organization.\nThe user needs to exist in the same instance as the organization and must not be a member of the given organization already."}, "role": {"type": "string", "description": "The role that the new member will have in the organization."}}, "required": ["user_id", "role"]}}}}, "responses": {"200": {"$ref": "#/components/responses/OrganizationMembership"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}, "get": {"operationId": "ListOrganizationMemberships", "summary": "Get a list of all members of an organization", "description": "Retrieves all user memberships for the given organization", "tags": ["Organization Memberships"], "parameters": [{"in": "path", "required": true, "name": "organization_id", "schema": {"type": "string"}, "description": "The organization ID."}, {"$ref": "#/components/parameters/LimitParameter"}, {"$ref": "#/components/parameters/OffsetParameter"}, {"in": "query", "required": false, "name": "order_by", "description": "Sorts organizations memberships by phone_number, email_address, created_at, first_name, last_name or username.\nBy prepending one of those values with + or -,\nwe can choose to sort in ascending (ASC) or descending (DESC) order.\"", "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/OrganizationMemberships"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}, "/organizations/{organization_id}/memberships/{user_id}": {"patch": {"operationId": "UpdateOrganizationMembership", "summary": "Update an organization membership", "description": "Updates the properties of an existing organization membership", "tags": ["Organization Memberships"], "parameters": [{"in": "path", "required": true, "name": "organization_id", "schema": {"type": "string"}, "description": "The ID of the organization the membership belongs to"}, {"in": "path", "required": true, "name": "user_id", "schema": {"type": "string"}, "description": "The ID of the user that this membership belongs to"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"role": {"type": "string", "description": "The new role of the given membership."}}, "required": ["role"]}}}}, "responses": {"200": {"$ref": "#/components/responses/OrganizationMembership"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}, "delete": {"operationId": "DeleteOrganizationMembership", "summary": "Remove a member from an organization", "description": "Removes the given membership from the organization", "tags": ["Organization Memberships"], "parameters": [{"in": "path", "required": true, "name": "organization_id", "schema": {"type": "string"}, "description": "The ID of the organization the membership belongs to"}, {"in": "path", "required": true, "name": "user_id", "schema": {"type": "string"}, "description": "The ID of the user that this membership belongs to"}], "responses": {"200": {"$ref": "#/components/responses/OrganizationMembership"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}}, "/organizations/{organization_id}/memberships/{user_id}/metadata": {"patch": {"operationId": "UpdateOrganizationMembershipMetadata", "summary": "Merge and update organization membership metadata", "description": "Update an organization membership's metadata attributes by merging existing values with the provided parameters.\nMetadata values will be updated via a deep merge. Deep means that any nested JSON objects will be merged as well.\nYou can remove metadata keys at any level by setting their value to `null`.", "tags": ["Organization Memberships"], "parameters": [{"in": "path", "required": true, "name": "organization_id", "schema": {"type": "string"}, "description": "The ID of the organization the membership belongs to"}, {"in": "path", "required": true, "name": "user_id", "schema": {"type": "string"}, "description": "The ID of the user that this membership belongs to"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"public_metadata": {"type": "object", "description": "Metadata saved on the organization membership, that is visible to both your frontend and backend.\nThe new object will be merged with the existing value.", "additionalProperties": true}, "private_metadata": {"type": "object", "description": "Metadata saved on the organization membership that is only visible to your backend.\nThe new object will be merged with the existing value.", "additionalProperties": true}}}}}}, "responses": {"200": {"$ref": "#/components/responses/OrganizationMembership"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}, "/organizations/{organization_id}/domains": {"post": {"operationId": "CreateOrganizationDomain", "summary": "Create a new organization domain.", "description": "Creates a new organization domain. By default the domain is verified, but can be optionally set to unverified.", "tags": ["Organization Domains"], "parameters": [{"in": "path", "required": true, "name": "organization_id", "schema": {"type": "string"}, "description": "The ID of the organization where the new domain will be created."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string", "description": "The name of the new domain", "nullable": false}, "enrollment_mode": {"type": "string", "description": "The enrollment_mode for the new domain. This can be `automatic_invitation`, `automatic_suggestion` or `manual_invitation`", "nullable": false}, "verified": {"type": "boolean", "description": "The status of domain's verification. Defaults to true", "nullable": true}}}}}}, "responses": {"200": {"$ref": "#/components/responses/OrganizationDomain"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}, "get": {"operationId": "ListOrganizationDomains", "summary": "Get a list of all domains of an organization.", "description": "Get a list of all domains of an organization.", "tags": ["Organization Domains"], "parameters": [{"in": "path", "required": true, "name": "organization_id", "schema": {"type": "string"}, "description": "The organization ID."}, {"$ref": "#/components/parameters/LimitParameter"}, {"$ref": "#/components/parameters/OffsetParameter"}, {"in": "query", "required": false, "name": "verified", "description": "Filter domains by their verification status. `true` or `false`", "schema": {"type": "string"}}, {"in": "query", "required": false, "name": "enrollment_mode", "description": "Filter domains by their enrollment mode", "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/OrganizationDomains"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}, "/organizations/{organization_id}/domains/{domain_id}": {"patch": {"operationId": "UpdateOrganizationDomain", "summary": "Update an organization domain.", "description": "Updates the properties of an existing organization domain.", "tags": ["Organization Domains"], "parameters": [{"in": "path", "required": true, "name": "organization_id", "schema": {"type": "string"}, "description": "The ID of the organization the domain belongs to"}, {"in": "path", "required": true, "name": "domain_id", "schema": {"type": "string"}, "description": "The ID of the domain"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"enrollment_mode": {"type": "string", "description": "The enrollment_mode for the new domain. This can be `automatic_invitation`, `automatic_suggestion` or `manual_invitation`", "nullable": true}, "verified": {"type": "boolean", "description": "The status of the domain's verification", "nullable": true}}}}}}, "responses": {"200": {"$ref": "#/components/responses/OrganizationDomain"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}, "delete": {"operationId": "DeleteOrganizationDomain", "summary": "Remove a domain from an organization.", "description": "Removes the given domain from the organization.", "tags": ["Organization Domains"], "parameters": [{"in": "path", "required": true, "name": "organization_id", "schema": {"type": "string"}, "description": "The ID of the organization the domain belongs to"}, {"in": "path", "required": true, "name": "domain_id", "schema": {"type": "string"}, "description": "The ID of the domain"}], "responses": {"200": {"$ref": "#/components/responses/DeletedObject"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}}, "/proxy_checks": {"post": {"summary": "Verify the proxy configuration for your domain", "description": "This endpoint can be used to validate that a proxy-enabled domain is operational.\nIt tries to verify that the proxy URL provided in the parameters maps to a functional proxy that can reach the Clerk Frontend API.\n\nYou can use this endpoint before you set a proxy URL for a domain. This way you can ensure that switching to proxy-based\nconfiguration will not lead to downtime for your instance.\n\nThe `proxy_url` parameter allows for testing proxy configurations for domains that don't have a proxy URL yet, or operate on\na different proxy URL than the one provided. It can also be used to re-validate a domain that is already configured to work with a proxy.", "operationId": "VerifyDomainProxy", "tags": ["Proxy checks"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"domain_id": {"type": "string", "description": "The ID of the domain that will be updated."}, "proxy_url": {"type": "string", "description": "The full URL of the proxy which will forward requests to the Clerk Frontend API for this domain. e.g. https://example.com/__clerk"}}}}}}, "responses": {"200": {"$ref": "#/components/responses/ProxyCheck"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}, "/redirect_urls": {"get": {"summary": "List all redirect URLs", "description": "Lists all whitelisted redirect_urls for the instance", "operationId": "ListRedirectURLs", "tags": ["Redirect URLs"], "responses": {"200": {"$ref": "#/components/responses/RedirectURL.List"}}}, "post": {"summary": "Create a redirect URL", "description": "Create a redirect URL", "operationId": "CreateRedirectURL", "tags": ["Redirect URLs"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"url": {"type": "string", "description": "The full url value prefixed with `https://` or a custom scheme e.g. `\"https://my-app.com/oauth-callback\"` or `\"my-app://oauth-callback\"`", "nullable": false}}}}}}, "responses": {"200": {"$ref": "#/components/responses/RedirectURL"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}, "/redirect_urls/{id}": {"get": {"summary": "Retrieve a redirect URL", "description": "Retrieve the details of the redirect URL with the given ID", "operationId": "GetRedirectURL", "tags": ["Redirect URLs"], "parameters": [{"name": "id", "in": "path", "description": "The ID of the redirect URL", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/RedirectURL"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}, "delete": {"summary": "Delete a redirect URL", "description": "Remove the selected redirect URL from the whitelist of the instance", "operationId": "DeleteRedirectURL", "tags": ["Redirect URLs"], "parameters": [{"name": "id", "in": "path", "description": "The ID of the redirect URL", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/DeletedObject"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}}, "/sign_in_tokens": {"post": {"summary": "Create sign-in token", "description": "Creates a new sign-in token and associates it with the given user.\nBy default, sign-in tokens expire in 30 days.\nYou can optionally supply a different duration in seconds using the `expires_in_seconds` property.", "operationId": "CreateSignInToken", "tags": ["Sign-in Tokens"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"user_id": {"type": "string", "description": "The ID of the user that can use the newly created sign in token"}, "expires_in_seconds": {"type": "integer", "description": "Optional parameter to specify the life duration of the sign in token in seconds.\nBy default, the duration is 30 days.", "default": 2592000}}}}}}, "responses": {"200": {"$ref": "#/components/responses/SignInToken"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}, "/sign_in_tokens/{sign_in_token_id}/revoke": {"post": {"summary": "Revoke the given sign-in token", "description": "Revokes a pending sign-in token", "operationId": "RevokeSignInToken", "tags": ["Sign-in Tokens"], "parameters": [{"name": "sign_in_token_id", "in": "path", "description": "The ID of the sign-in token to be revoked", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/SignInToken"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}}, "/sign_ups/{id}": {"patch": {"operationId": "UpdateSignUp", "summary": "Update a sign-up", "description": "Update the sign-up with the given ID", "tags": ["Sign-ups"], "parameters": [{"name": "id", "in": "path", "description": "The ID of the sign-up to update", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"external_id": {"type": "string", "description": "The ID of the guest attempting to sign up as used in your external systems or your previous authentication solution.\nThis will be copied to the resulting user when the sign-up is completed.", "nullable": true}}}}}}, "responses": {"200": {"$ref": "#/components/responses/SignUp"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}}}}, "/oauth_applications": {"get": {"operationId": "ListOAuthApplications", "summary": "Get a list of OAuth applications for an instance", "description": "This request returns the list of OAuth applications for an instance.\nResults can be paginated using the optional `limit` and `offset` query parameters.\nThe OAuth applications are ordered by descending creation date.\nMost recent OAuth applications will be returned first.", "tags": ["OAuth Applications"], "parameters": [{"$ref": "#/components/parameters/LimitParameter"}, {"$ref": "#/components/parameters/OffsetParameter"}], "responses": {"200": {"$ref": "#/components/responses/OAuthApplications"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}, "post": {"operationId": "CreateOAuthApplication", "summary": "Create an OAuth application", "description": "Creates a new OAuth application with the given name and callback URL for an instance.\nThe callback URL must be a valid url.\nAll URL schemes are allowed such as `http://`, `https://`, `myapp://`, etc...", "tags": ["OAuth Applications"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the new OAuth application"}, "callback_url": {"type": "string", "description": "The callback URL of the new OAuth application"}, "scopes": {"type": "string", "description": "Define the allowed scopes for the new OAuth applications that dictate the user payload of the OAuth user info endpoint. Available scopes are `profile`, `email`, `public_metadata`, `private_metadata`. Provide the requested scopes as a string, separated by spaces.", "default": "profile email", "example": "profile email public_metadata"}, "public": {"type": "boolean", "description": "If true, this client is public and cannot securely store a client secret.\nOnly the authorization code flow with proof key for code exchange (PKCE) may be used.\nPublic clients cannot be updated to be confidential clients, and vice versa."}}, "required": ["name", "callback_url"]}}}}, "responses": {"200": {"$ref": "#/components/responses/OAuthApplicationWithSecret"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}, "/oauth_applications/{oauth_application_id}": {"get": {"operationId": "GetOAuthApplication", "summary": "Retrieve an OAuth application by ID", "description": "Fetches the OAuth application whose ID matches the provided `id` in the path.", "tags": ["OAuth Applications"], "parameters": [{"in": "path", "name": "oauth_application_id", "required": true, "schema": {"type": "string"}, "description": "The ID of the OAuth application"}], "responses": {"200": {"$ref": "#/components/responses/OAuthApplication"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}, "patch": {"operationId": "UpdateOAuthApplication", "summary": "Update an OAuth application", "description": "Updates an existing OAuth application", "tags": ["OAuth Applications"], "parameters": [{"in": "path", "name": "oauth_application_id", "required": true, "schema": {"type": "string"}, "description": "The ID of the OAuth application to update"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string", "description": "The new name of the OAuth application"}, "callback_url": {"type": "string", "description": "The new callback URL of the OAuth application"}, "scopes": {"type": "string", "description": "Define the allowed scopes for the new OAuth applications that dictate the user payload of the OAuth user info endpoint. Available scopes are `profile`, `email`, `public_metadata`, `private_metadata`. Provide the requested scopes as a string, separated by spaces.", "default": "profile email", "example": "profile email public_metadata private_metadata"}}}}}}, "responses": {"200": {"$ref": "#/components/responses/OAuthApplication"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}, "delete": {"operationId": "DeleteOAuthApplication", "summary": "Delete an OAuth application", "description": "Deletes the given OAuth application.\nThis is not reversible.", "tags": ["OAuth Applications"], "parameters": [{"in": "path", "name": "oauth_application_id", "required": true, "schema": {"type": "string"}, "description": "The ID of the OAuth application to delete"}], "responses": {"200": {"$ref": "#/components/responses/DeletedObject"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}}, "/oauth_applications/{oauth_application_id}/rotate_secret": {"post": {"operationId": "RotateOAuthApplicationSecret", "summary": "Rotate the client secret of the given OAuth application", "description": "Rotates the OAuth application's client secret.\nWhen the client secret is rotated, make sure to update it in authorized OAuth clients.", "tags": ["OAuth Applications"], "parameters": [{"name": "oauth_application_id", "in": "path", "description": "The ID of the OAuth application for which to rotate the client secret", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/OAuthApplicationWithSecret"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}}, "/saml_connections": {"get": {"operationId": "ListSAMLConnections", "summary": "Get a list of SAML Connections for an instance", "description": "Returns the list of SAML Connections for an instance.\nResults can be paginated using the optional `limit` and `offset` query parameters.\nThe SAML Connections are ordered by descending creation date and the most recent will be returned first.", "tags": ["SAML Connections"], "parameters": [{"$ref": "#/components/parameters/LimitParameter"}, {"$ref": "#/components/parameters/OffsetParameter"}], "responses": {"200": {"$ref": "#/components/responses/SAMLConnections"}, "402": {"$ref": "#/components/responses/PaymentRequired"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}, "post": {"operationId": "CreateSAMLConnection", "summary": "Create a SAML Connection", "description": "Create a new SAML Connection.", "tags": ["SAML Connections"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string", "description": "The name to use as a label for this SAML Connection"}, "domain": {"type": "string", "description": "The domain of your organization. Sign in flows using an email with this domain, will use this SAML Connection."}, "provider": {"type": "string", "description": "The IdP provider of the connection.", "enum": ["saml_custom", "saml_okta", "saml_google", "saml_microsoft"]}, "idp_entity_id": {"type": "string", "description": "The Entity ID as provided by the IdP", "nullable": true}, "idp_sso_url": {"type": "string", "description": "The Single-Sign On URL as provided by the IdP", "nullable": true}, "idp_certificate": {"type": "string", "description": "The X.509 certificate as provided by the IdP", "nullable": true}, "idp_metadata_url": {"type": "string", "description": "The URL which serves the IdP metadata. If present, it takes priority over the corresponding individual properties", "nullable": true}, "idp_metadata": {"type": "string", "description": "The XML content of the IdP metadata file. If present, it takes priority over the corresponding individual properties", "nullable": true}, "organization_id": {"type": "string", "description": "The ID of the organization to which users of this SAML Connection will be added", "nullable": true}, "attribute_mapping": {"type": "object", "description": "Define the attribute name mapping between Identity Provider and Clerk's user properties", "nullable": true, "additionalProperties": false, "properties": {"user_id": {"type": "string"}, "email_address": {"type": "string"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}}}}, "required": ["name", "domain", "provider"]}}}}, "responses": {"200": {"$ref": "#/components/responses/SAMLConnection"}, "402": {"$ref": "#/components/responses/PaymentRequired"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}, "/saml_connections/{saml_connection_id}": {"get": {"operationId": "GetSAMLConnection", "summary": "Retrieve a SAML Connection by ID", "description": "Fetches the SAML Connection whose ID matches the provided `saml_connection_id` in the path.", "tags": ["SAML Connections"], "parameters": [{"in": "path", "name": "saml_connection_id", "required": true, "schema": {"type": "string"}, "description": "The ID of the SAML Connection"}], "responses": {"200": {"$ref": "#/components/responses/SAMLConnection"}, "402": {"$ref": "#/components/responses/PaymentRequired"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}, "patch": {"operationId": "UpdateSAMLConnection", "summary": "Update a SAML Connection", "description": "Updates the SAML Connection whose ID matches the provided `id` in the path.", "tags": ["SAML Connections"], "parameters": [{"in": "path", "name": "saml_connection_id", "required": true, "schema": {"type": "string"}, "description": "The ID of the SAML Connection to update"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string", "description": "The name of the new SAML Connection", "nullable": true}, "domain": {"type": "string", "description": "The domain to use for the new SAML Connection", "nullable": true}, "idp_entity_id": {"type": "string", "description": "The entity id as provided by the IdP", "nullable": true}, "idp_sso_url": {"type": "string", "description": "The SSO url as provided by the IdP", "nullable": true}, "idp_certificate": {"type": "string", "description": "The x509 certificated as provided by the IdP", "nullable": true}, "idp_metadata_url": {"type": "string", "description": "The URL which serves the IdP metadata. If present, it takes priority over the corresponding individual properties and replaces them", "nullable": true}, "idp_metadata": {"type": "string", "description": "The XML content of the IdP metadata file. If present, it takes priority over the corresponding individual properties", "nullable": true}, "organization_id": {"type": "string", "description": "The ID of the organization to which users of this SAML Connection will be added", "nullable": true}, "attribute_mapping": {"type": "object", "description": "Define the atrtibute name mapping between Identity Provider and Clerk's user properties", "nullable": true, "additionalProperties": false, "properties": {"user_id": {"type": "string"}, "email_address": {"type": "string"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}}}, "active": {"type": "boolean", "description": "Activate or de-activate the SAML Connection", "nullable": true}, "sync_user_attributes": {"type": "boolean", "description": "Controls whether to update the user's attributes in each sign-in", "nullable": true}, "allow_subdomains": {"type": "boolean", "description": "Allow users with an email address subdomain to use this connection in order to authenticate", "nullable": true}, "allow_idp_initiated": {"type": "boolean", "description": "Enable or deactivate IdP-initiated flows", "nullable": true}, "disable_additional_identifications": {"type": "boolean", "description": "Enable or deactivate additional identifications", "nullable": true}}}}}}, "responses": {"200": {"$ref": "#/components/responses/SAMLConnection"}, "402": {"$ref": "#/components/responses/PaymentRequired"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}, "delete": {"operationId": "DeleteSAMLConnection", "summary": "Delete a SAML Connection", "description": "Deletes the SAML Connection whose ID matches the provided `id` in the path.", "tags": ["SAML Connections"], "parameters": [{"in": "path", "name": "saml_connection_id", "required": true, "schema": {"type": "string"}, "description": "The ID of the SAML Connection to delete"}], "responses": {"200": {"$ref": "#/components/responses/DeletedObject"}, "402": {"$ref": "#/components/responses/PaymentRequired"}, "403": {"$ref": "#/components/responses/AuthorizationInvalid"}, "404": {"$ref": "#/components/responses/ResourceNotFound"}}}}, "/testing_tokens": {"post": {"operationId": "CreateTestingToken", "summary": "Retrieve a new testing token", "description": "Retrieve a new testing token.", "tags": ["Testing Tokens"], "responses": {"200": {"$ref": "#/components/responses/TestingToken"}}}}, "/organization_memberships": {"get": {"operationId": "InstanceGetOrganizationMemberships", "summary": "Get a list of all organization memberships within an instance.", "description": "Retrieves all organization user memberships for the given instance.", "tags": ["Organization Memberships"], "parameters": [{"$ref": "#/components/parameters/LimitParameter"}, {"$ref": "#/components/parameters/OffsetParameter"}, {"in": "query", "required": false, "name": "order_by", "description": "Sorts organizations memberships by phone_number, email_address, created_at, first_name, last_name or username.\nBy prepending one of those values with + or -,\nwe can choose to sort in ascending (ASC) or descending (DESC) order.", "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/OrganizationMemberships"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "401": {"$ref": "#/components/responses/AuthenticationInvalid"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}, "500": {"$ref": "#/components/responses/ClerkErrors"}}}}, "/waitlist_entries": {"get": {"operationId": "ListWaitlistEntries", "summary": "List all waitlist entries", "description": "Retrieve a list of waitlist entries for the instance.\nEntries are ordered by creation date in descending order by default.\nSupports filtering by email address or status and pagination with limit and offset parameters.", "tags": ["Waitlist Entries"], "parameters": [{"$ref": "#/components/parameters/LimitParameter"}, {"$ref": "#/components/parameters/OffsetParameter"}, {"in": "query", "name": "query", "description": "Filter waitlist entries by email address", "required": false, "schema": {"type": "string"}}, {"in": "query", "name": "status", "description": "Filter waitlist entries by their status", "required": false, "schema": {"type": "string", "enum": ["pending", "invited", "completed", "rejected"]}}, {"in": "query", "name": "order_by", "required": false, "description": "Specify the order of results. Supported values are:\n- `created_at`\n- `email_address`\n- `invited_at`\n\nUse `+` for ascending or `-` for descending order. Defaults to `-created_at`.", "schema": {"type": "string", "default": "-created_at"}}], "responses": {"200": {"$ref": "#/components/responses/WaitlistEntries"}}}, "post": {"operationId": "CreateWaitlistEntry", "summary": "Create a waitlist entry", "description": "Creates a new waitlist entry for the given email address.\nIf the email address is already on the waitlist, no new entry will be created and the existing waitlist entry will be returned.", "tags": ["Waitlist Entries"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"email_address": {"type": "string", "description": "The email address to add to the waitlist"}, "notify": {"type": "boolean", "description": "Optional flag which denotes whether an email invitation should be sent to the given email address.\nDefaults to `true`."}}, "required": ["email_address"]}}}}, "responses": {"200": {"$ref": "#/components/responses/WaitlistEntry"}, "400": {"$ref": "#/components/responses/ClerkErrors"}, "422": {"$ref": "#/components/responses/UnprocessableEntity"}}}}}, "security": [{"bearerAuth": []}], "servers": [{"url": "https://api.clerk.com/v1"}], "tags": [{"name": "Actor <PERSON><PERSON><PERSON>", "description": "Allow your users to sign in on behalf of other users.", "externalDocs": {"url": "https://clerk.com/docs/authentication/user-impersonation#actor-tokens"}}, {"name": "Allow-list / Block-list", "description": "Allow-lists and Block-lists allow you to control who can sign up or sign in\nto your application, by restricting access based on the user's email\naddress or phone number.", "externalDocs": {"url": "https://clerk.com/docs/authentication/allowlist"}}, {"name": "Clients", "description": "The Client object tracks sessions, as well as the state of any sign in and sign up attempts, for a given device.", "externalDocs": {"url": "https://clerk.com/docs/reference/clerkjs/client"}}, {"name": "Domains", "description": "Domains represent each instance's URLs and DNS setup."}, {"name": "Email Addresses", "description": "A user can be associated with one or more email addresses, which allows them to be contacted via email.", "externalDocs": {"url": "https://clerk.com/docs/reference/clerkjs/emailaddress"}}, {"name": "Email & SMS Templates", "description": "Email & SMS templates allow you to customize the theming and wording of emails & SMS messages that are sent by your instance.", "externalDocs": {"url": "https://clerk.com/docs/authentication/email-sms-templates"}}, {"name": "Instance Settings", "description": "Modify the settings of your instance."}, {"name": "Invitations", "description": "Invitations allow you to invite someone to sign up to your application, via email.", "externalDocs": {"url": "https://clerk.com/docs/authentication/invitations"}}, {"name": "JWKS", "description": "Retrieve the JSON Web Key Set which can be used to verify the token signatures of the instance."}, {"name": "JWT Templates", "description": "JWT Templates allow you to generate custom authentication tokens\ntied to authenticated sessions, enabling you to integrate with third-party\nservices.", "externalDocs": {"url": "https://clerk.com/docs/request-authentication/jwt-templates"}}, {"name": "OAuth Applications", "description": "OAuth applications contain data for clients using Clerk as an OAuth2 identity provider."}, {"name": "Organizations", "description": "Organizations are used to group members under a common entity and provide shared access to resources.", "externalDocs": {"url": "https://clerk.com/docs/organizations/overview"}}, {"name": "Organization Invitations", "description": "Invite users to an organization.", "externalDocs": {"url": "https://clerk.com/docs/organizations/invite-users"}}, {"name": "Organization Memberships", "description": "Manage member roles in an organization.", "externalDocs": {"url": "https://clerk.com/docs/organizations/manage-member-roles"}}, {"name": "Phone Numbers", "description": "A user can be associated with one or more phone numbers, which allows them to be contacted via SMS.", "externalDocs": {"url": "https://clerk.com/docs/reference/clerkjs/phonenumber"}}, {"name": "Redirect URLs", "description": "Redirect URLs are whitelisted URLs that facilitate secure authentication flows in native applications (e.g. React Native, Expo).\nIn these contexts, Clerk ensures that security-critical nonces are passed only to the whitelisted URLs."}, {"name": "SAML Connections", "description": "A SAML Connection holds configuration data required for facilitating a SAML SSO flow between your\nClerk Instance (SP) and a particular SAML IdP."}, {"name": "Sessions", "description": "The Session object is an abstraction over an HTTP session.\nIt models the period of information exchange between a user and the server.\nSessions are created when a user successfully goes through the sign in or sign up flows.", "externalDocs": {"url": "https://clerk.com/docs/reference/clerkjs/session"}}, {"name": "Sign-in Tokens", "description": "Sign-in tokens are JWTs that can be used to sign in to an application without specifying any credentials.\nA sign-in token can be used at most once and they can be consumed from the Frontend API using the `ticket` strategy."}, {"name": "Testing Tokens", "description": "Tokens meant for use by end-to-end test suites in requests to the Frontend API, so as to bypass bot detection measures.", "externalDocs": {"url": "https://clerk.com/docs/testing/overview#testing-tokens"}}, {"name": "Users", "description": "The user object represents a user that has successfully signed up to your application.", "externalDocs": {"url": "https://clerk.com/docs/reference/clerkjs/user"}}, {"name": "Webhooks", "description": "You can configure webhooks to be notified about various events that happen on your instance.", "externalDocs": {"url": "https://clerk.com/docs/integration/webhooks"}}, {"name": "Miscellaneous", "description": "Various endpoints that do not belong in any particular category."}, {"name": "Beta Features", "description": "Modify instance settings that are currently in beta."}]}