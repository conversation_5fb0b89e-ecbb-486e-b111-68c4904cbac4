import type { APIEmbed } from 'discord-api-types/v10';

import { BRANDING_LOGO_URL, BRANDING_NAME } from '@/const/branding';
import { OFFICIAL_URL } from '@/const/url';
import { DEFAULT_WEBHOOK_SETTINGS, type WebhookSettings } from '@/const/webhook';
import { SpendModel } from '@/database/models/spend';
import { SubscriptionModel } from '@/database/models/subscription';
import { UserModel } from '@/database/models/user';
import { translation } from '@/server/translation';
import { formatIntergerNumber, formatPrice } from '@/utils/format';
import { merge } from '@/utils/merge';
import { thisMonth, today } from '@/utils/time';

export class DiscordClient {
  config: WebhookSettings & { avatar: string; username: string } = {
    avatar:
      BRANDING_LOGO_URL ||
      'https://registry.npmmirror.com/@lobehub/assets-logo/1.2.0/files/assets/logo-3d.webp',
    username: [BRANDING_NAME, 'Admin'].join(' '),
    ...DEFAULT_WEBHOOK_SETTINGS,
  };

  ctx = {
    spendModel: new SpendModel(),
    subscriptionModel: new SubscriptionModel(),
    userModel: new UserModel(),
  };

  constructor(config: WebhookSettings) {
    if (!config.webhookUrl) {
      throw new Error('Missing DISCORD_WEBHOOK_URL environment variable');
    }
    this.config = merge(this.config, config);
  }

  send = async () => {
    return this.sendMessages();
  };

  sendTest = async () => {
    return this.sendMessages();
  };

  private sendMessages = async () => {
    const { avatar, username } = this.config;
    const embed = await this.getEmbed();

    try {
      const response = await fetch(this.config.webhookUrl, {
        body: JSON.stringify({
          avatarURL: avatar,
          embeds: [embed as APIEmbed],
          username,
        }),
        headers: {
          'Content-Type': 'application/json',
        },
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error(`Error sending Discord message: ${response.statusText}`);
      }

      return response;
    } catch (error) {
      console.error('Failed to send Discord message:', error);
      throw error;
    }
  };

  private getEmbed = async (): Promise<Partial<APIEmbed>> => {
    const { t } = await translation('common', this.config.lang);
    const routes = await translation('routes', this.config.lang);
    const { users, spend, subscriptions, modelCalls } = await this.getOverview();

    return {
      author: this.getAuthor(),
      fields: [
        {
          name: t('statistics.newUsers'),
          value: await this.genValue(users, { formater: formatIntergerNumber }),
        },
        {
          name: t('statistics.newSubscriptions'),
          value: await this.genValue(subscriptions, { formater: formatIntergerNumber }),
        },
        {
          name: t('statistics.spend'),
          value: await this.genValue(spend, { formater: formatPrice, prefix: '$' }),
        },
        {
          name: t('statistics.modelCalls'),
          value: await this.genValue(modelCalls, { formater: formatIntergerNumber }),
        },
      ],
      thumbnail: {
        url: 'https://registry.npmmirror.com/@lobehub/fluent-emoji-3d/latest/files/assets/1f4c8.webp',
      },
      timestamp: new Date().toISOString(),
      title: [today().format('YYYY-MM-DD'), routes.t('overview')].join(' '),
      url: OFFICIAL_URL,
    };
  };

  private genValue = async (
    data: { current: string | number | null; sum: string | number | null },
    config: {
      formater: (v: any) => string;
      prefix?: string;
    },
  ): Promise<string> => {
    const { t } = await translation('common', this.config.lang);
    const prefix = config.prefix || '';
    return [
      `**${prefix + config.formater(data.current)}** \`${t('time.today')}\``,
      `**${prefix + config.formater(data.sum)}** \`${t('time.thisMonth')}\``,
    ].join('  /  ');
  };

  private getOverview = async () => {
    const now = today().format('YYYY-MM-DD');
    const month = thisMonth().format('YYYY-MM-DD');
    return {
      modelCalls: {
        current: await this.ctx.spendModel.getTotalModelCalls({ startDate: now }),
        sum: await this.ctx.spendModel.getTotalModelCalls({ startDate: month }),
      },
      spend: {
        current: await this.ctx.spendModel.getTotalSpend({ startDate: now }),
        sum: await this.ctx.spendModel.getTotalSpend({ startDate: month }),
      },
      subscriptions: {
        current: await this.ctx.subscriptionModel.getTotalSubscriptions({ startDate: now }),
        sum: await this.ctx.subscriptionModel.getTotalSubscriptions({ startDate: month }),
      },
      users: {
        current: await this.ctx.userModel.getTotalUsers({ startDate: now }),
        sum: await this.ctx.userModel.getTotalUsers({ startDate: month }),
      },
    };
  };

  private getAuthor = () => {
    const { avatar, username } = this.config;
    return {
      icon_url: avatar,
      name: username,
      url: OFFICIAL_URL,
    };
  };

  private getColor = (hex: string): number => {
    let color = hex.replace('#', '');
    if (color.length === 3) {
      color = color
        .split('')
        .map((char) => char + char)
        .join('');
    }
    return parseInt(color, 16);
  };
}
