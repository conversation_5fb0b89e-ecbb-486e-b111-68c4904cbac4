/* eslint-disable typescript-sort-keys/interface, sort-keys-fix/sort-keys-fix */
/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
  '/sso/key/generate': {
    /**
     * Google Login
     * @description Create Proxy API Keys using Google Workspace SSO. Requires setting PROXY_BASE_URL in .env
     * PROXY_BASE_URL should be the your deployed proxy endpoint, e.g. PROXY_BASE_URL="https://litellm-production-7002.up.railway.app/"
     * Example:
     */
    get: operations['google_login_sso_key_generate_get'];
  };
  '/sso/callback': {
    /**
     * Auth Callback
     * @description Verify login
     */
    get: operations['auth_callback_sso_callback_get'];
  };
  '/models': {
    /** Model List */
    get: operations['model_list_models_get'];
  };
  '/credentials': {
    /** Create Credential */
    post: operations['create_credential_credentials_post'];
  };
  '/credentials/{credential_name}': {
    /** Delete Credential */
    delete: operations['delete_credential_credentials_delete'];
    /** Update Credential */
    patch: operations['update_credential_credentials_patch'];
  };
  '/v1/models': {
    /** Model List */
    get: operations['model_list_v1_models_get'];
  };
  '/openai/deployments/{model}/chat/completions': {
    /** Chat Completion */
    post: operations['chat_completion_openai_deployments__model__chat_completions_post'];
  };
  '/engines/{model}/chat/completions': {
    /** Chat Completion */
    post: operations['chat_completion_engines__model__chat_completions_post'];
  };
  '/chat/completions': {
    /** Chat Completion */
    post: operations['chat_completion_chat_completions_post'];
  };
  '/v1/chat/completions': {
    /** Chat Completion */
    post: operations['chat_completion_v1_chat_completions_post'];
  };
  '/openai/deployments/{model}/completions': {
    /** Completion */
    post: operations['completion_openai_deployments__model__completions_post'];
  };
  '/engines/{model}/completions': {
    /** Completion */
    post: operations['completion_engines__model__completions_post'];
  };
  '/completions': {
    /** Completion */
    post: operations['completion_completions_post'];
  };
  '/v1/completions': {
    /** Completion */
    post: operations['completion_v1_completions_post'];
  };
  '/openai/deployments/{model}/embeddings': {
    /** Embeddings */
    post: operations['embeddings_openai_deployments__model__embeddings_post'];
  };
  '/embeddings': {
    /** Embeddings */
    post: operations['embeddings_embeddings_post'];
  };
  '/v1/embeddings': {
    /** Embeddings */
    post: operations['embeddings_v1_embeddings_post'];
  };
  '/images/generations': {
    /** Image Generation */
    post: operations['image_generation_images_generations_post'];
  };
  '/v1/images/generations': {
    /** Image Generation */
    post: operations['image_generation_v1_images_generations_post'];
  };
  '/audio/speech': {
    /**
     * Audio Speech
     * @description Same params as:
     *
     * https://platform.openai.com/docs/api-reference/audio/createSpeech
     */
    post: operations['audio_speech_audio_speech_post'];
  };
  '/v1/audio/speech': {
    /**
     * Audio Speech
     * @description Same params as:
     *
     * https://platform.openai.com/docs/api-reference/audio/createSpeech
     */
    post: operations['audio_speech_v1_audio_speech_post'];
  };
  '/audio/transcriptions': {
    /**
     * Audio Transcriptions
     * @description Same params as:
     *
     * https://platform.openai.com/docs/api-reference/audio/createTranscription?lang=curl
     */
    post: operations['audio_transcriptions_audio_transcriptions_post'];
  };
  '/v1/audio/transcriptions': {
    /**
     * Audio Transcriptions
     * @description Same params as:
     *
     * https://platform.openai.com/docs/api-reference/audio/createTranscription?lang=curl
     */
    post: operations['audio_transcriptions_v1_audio_transcriptions_post'];
  };
  '/assistants': {
    /**
     * Get Assistants
     * @description Returns a list of assistants.
     *
     * API Reference docs - https://platform.openai.com/docs/api-reference/assistants/listAssistants
     */
    get: operations['get_assistants_assistants_get'];
  };
  '/v1/assistants': {
    /**
     * Get Assistants
     * @description Returns a list of assistants.
     *
     * API Reference docs - https://platform.openai.com/docs/api-reference/assistants/listAssistants
     */
    get: operations['get_assistants_v1_assistants_get'];
  };
  '/threads': {
    /**
     * Create Threads
     * @description Create a thread.
     *
     * API Reference - https://platform.openai.com/docs/api-reference/threads/createThread
     */
    post: operations['create_threads_threads_post'];
  };
  '/v1/threads': {
    /**
     * Create Threads
     * @description Create a thread.
     *
     * API Reference - https://platform.openai.com/docs/api-reference/threads/createThread
     */
    post: operations['create_threads_v1_threads_post'];
  };
  '/threads/{thread_id}': {
    /**
     * Get Thread
     * @description Retrieves a thread.
     *
     * API Reference - https://platform.openai.com/docs/api-reference/threads/getThread
     */
    get: operations['get_thread_threads__thread_id__get'];
  };
  '/v1/threads/{thread_id}': {
    /**
     * Get Thread
     * @description Retrieves a thread.
     *
     * API Reference - https://platform.openai.com/docs/api-reference/threads/getThread
     */
    get: operations['get_thread_v1_threads__thread_id__get'];
  };
  '/threads/{thread_id}/messages': {
    /**
     * Get Messages
     * @description Returns a list of messages for a given thread.
     *
     * API Reference - https://platform.openai.com/docs/api-reference/messages/listMessages
     */
    get: operations['get_messages_threads__thread_id__messages_get'];
    /**
     * Add Messages
     * @description Create a message.
     *
     * API Reference - https://platform.openai.com/docs/api-reference/messages/createMessage
     */
    post: operations['add_messages_threads__thread_id__messages_post'];
  };
  '/v1/threads/{thread_id}/messages': {
    /**
     * Get Messages
     * @description Returns a list of messages for a given thread.
     *
     * API Reference - https://platform.openai.com/docs/api-reference/messages/listMessages
     */
    get: operations['get_messages_v1_threads__thread_id__messages_get'];
    /**
     * Add Messages
     * @description Create a message.
     *
     * API Reference - https://platform.openai.com/docs/api-reference/messages/createMessage
     */
    post: operations['add_messages_v1_threads__thread_id__messages_post'];
  };
  '/threads/{thread_id}/runs': {
    /**
     * Run Thread
     * @description Create a run.
     *
     * API Reference: https://platform.openai.com/docs/api-reference/runs/createRun
     */
    get: operations['run_thread_threads__thread_id__runs_get'];
  };
  '/v1/threads/{thread_id}/runs': {
    /**
     * Run Thread
     * @description Create a run.
     *
     * API Reference: https://platform.openai.com/docs/api-reference/runs/createRun
     */
    get: operations['run_thread_v1_threads__thread_id__runs_get'];
  };
  '/batches': {
    /**
     * Create Batch
     * @description Create large batches of API requests for asynchronous processing.
     * This is the equivalent of POST https://api.openai.com/v1/batch
     * Supports Identical Params as: https://platform.openai.com/docs/api-reference/batch
     *
     * Example Curl
     * ```
     * curl http://localhost:4000/v1/batches         -H "Authorization: Bearer sk-1234"         -H "Content-Type: application/json"         -d '{
     *         "input_file_id": "file-abc123",
     *         "endpoint": "/v1/chat/completions",
     *         "completion_window": "24h"
     * }'
     * ```
     */
    post: operations['create_batch_batches_post'];
  };
  '/v1/batches': {
    /**
     * Create Batch
     * @description Create large batches of API requests for asynchronous processing.
     * This is the equivalent of POST https://api.openai.com/v1/batch
     * Supports Identical Params as: https://platform.openai.com/docs/api-reference/batch
     *
     * Example Curl
     * ```
     * curl http://localhost:4000/v1/batches         -H "Authorization: Bearer sk-1234"         -H "Content-Type: application/json"         -d '{
     *         "input_file_id": "file-abc123",
     *         "endpoint": "/v1/chat/completions",
     *         "completion_window": "24h"
     * }'
     * ```
     */
    post: operations['create_batch_v1_batches_post'];
  };
  '/batches{batch_id}': {
    /**
     * Retrieve Batch
     * @description Retrieves a batch.
     * This is the equivalent of GET https://api.openai.com/v1/batches/{batch_id}
     * Supports Identical Params as: https://platform.openai.com/docs/api-reference/batch/retrieve
     *
     * Example Curl
     * ```
     * curl http://localhost:4000/v1/batches/batch_abc123     -H "Authorization: Bearer sk-1234"     -H "Content-Type: application/json"
     * ```
     */
    get: operations['retrieve_batch_batches_batch_id__get'];
  };
  '/v1/batches{batch_id}': {
    /**
     * Retrieve Batch
     * @description Retrieves a batch.
     * This is the equivalent of GET https://api.openai.com/v1/batches/{batch_id}
     * Supports Identical Params as: https://platform.openai.com/docs/api-reference/batch/retrieve
     *
     * Example Curl
     * ```
     * curl http://localhost:4000/v1/batches/batch_abc123     -H "Authorization: Bearer sk-1234"     -H "Content-Type: application/json"
     * ```
     */
    get: operations['retrieve_batch_v1_batches_batch_id__get'];
  };
  '/files': {
    /**
     * Create File
     * @description Upload a file that can be used across - Assistants API, Batch API
     * This is the equivalent of POST https://api.openai.com/v1/files
     *
     * Supports Identical Params as: https://platform.openai.com/docs/api-reference/files/create
     *
     * Example Curl
     * ```
     * curl https://api.openai.com/v1/files         -H "Authorization: Bearer sk-1234"         -F purpose="batch"         -F file="@mydata.jsonl"
     *
     * ```
     */
    post: operations['create_file_files_post'];
  };
  '/v1/files': {
    /**
     * Create File
     * @description Upload a file that can be used across - Assistants API, Batch API
     * This is the equivalent of POST https://api.openai.com/v1/files
     *
     * Supports Identical Params as: https://platform.openai.com/docs/api-reference/files/create
     *
     * Example Curl
     * ```
     * curl https://api.openai.com/v1/files         -H "Authorization: Bearer sk-1234"         -F purpose="batch"         -F file="@mydata.jsonl"
     *
     * ```
     */
    post: operations['create_file_v1_files_post'];
  };
  '/moderations': {
    /**
     * Moderations
     * @description The moderations endpoint is a tool you can use to check whether content complies with an LLM Providers policies.
     *
     * Quick Start
     * ```
     * curl --location 'http://0.0.0.0:4000/moderations'     --header 'Content-Type: application/json'     --header 'Authorization: Bearer sk-1234'     --data '{"input": "Sample text goes here", "model": "text-moderation-stable"}'
     * ```
     */
    post: operations['moderations_moderations_post'];
  };
  '/v1/moderations': {
    /**
     * Moderations
     * @description The moderations endpoint is a tool you can use to check whether content complies with an LLM Providers policies.
     *
     * Quick Start
     * ```
     * curl --location 'http://0.0.0.0:4000/moderations'     --header 'Content-Type: application/json'     --header 'Authorization: Bearer sk-1234'     --data '{"input": "Sample text goes here", "model": "text-moderation-stable"}'
     * ```
     */
    post: operations['moderations_v1_moderations_post'];
  };
  '/utils/token_counter': {
    /** Token Counter */
    post: operations['token_counter_utils_token_counter_post'];
  };
  '/utils/supported_openai_params': {
    /**
     * Supported Openai Params
     * @description Returns supported openai params for a given litellm model name
     *
     * e.g. `gpt-4` vs `gpt-3.5-turbo`
     *
     * Example curl:
     * ```
     * curl -X GET --location 'http://localhost:4000/utils/supported_openai_params?model=gpt-3.5-turbo-16k'         --header 'Authorization: Bearer sk-1234'
     * ```
     */
    get: operations['supported_openai_params_utils_supported_openai_params_get'];
  };
  '/key/generate': {
    /**
     * Generate Key Fn
     * @description Generate an API key based on the provided data.
     *
     * Docs: https://docs.litellm.ai/docs/proxy/virtual_keys
     *
     * Parameters:
     * - duration: Optional[str] - Specify the length of time the token is valid for. You can set duration as seconds ("30s"), minutes ("30m"), hours ("30h"), days ("30d").
     * - key_alias: Optional[str] - User defined key alias
     * - team_id: Optional[str] - The team id of the key
     * - user_id: Optional[str] - The user id of the key
     * - models: Optional[list] - Model_name's a user is allowed to call. (if empty, key is allowed to call all models)
     * - aliases: Optional[dict] - Any alias mappings, on top of anything in the config.yaml model list. - https://docs.litellm.ai/docs/proxy/virtual_keys#managing-auth---upgradedowngrade-models
     * - config: Optional[dict] - any key-specific configs, overrides config in config.yaml
     * - spend: Optional[int] - Amount spent by key. Default is 0. Will be updated by proxy whenever key is used. https://docs.litellm.ai/docs/proxy/virtual_keys#managing-auth---tracking-spend
     * - send_invite_email: Optional[bool] - Whether to send an invite email to the user_id, with the generate key
     * - max_budget: Optional[float] - Specify max budget for a given key.
     * - max_parallel_requests: Optional[int] - Rate limit a user based on the number of parallel requests. Raises 429 error, if user's parallel requests > x.
     * - metadata: Optional[dict] - Metadata for key, store information for key. Example metadata = {"team": "core-infra", "app": "app2", "email": "<EMAIL>" }
     * - permissions: Optional[dict] - key-specific permissions. Currently just used for turning off pii masking (if connected). Example - {"pii": false}
     * - model_max_budget: Optional[dict] - key-specific model budget in USD. Example - {"text-davinci-002": 0.5, "gpt-3.5-turbo": 0.5}. IF null or {} then no model specific budget.
     *
     * Examples:
     *
     * 1. Allow users to turn on/off pii masking
     *
     * ```bash
     * curl --location 'http://0.0.0.0:8000/key/generate'         --header 'Authorization: Bearer sk-1234'         --header 'Content-Type: application/json'         --data '{
     *         "permissions": {"allow_pii_controls": true}
     * }'
     * ```
     *
     * Returns:
     * - key: (str) The generated api key
     * - expires: (datetime) Datetime object for when key expires.
     * - user_id: (str) Unique user id - used for tracking spend across multiple keys for same user id.
     */
    post: operations['generate_key_fn_key_generate_post'];
  };
  '/key/update': {
    /**
     * Update Key Fn
     * @description Update an existing key
     */
    post: operations['update_key_fn_key_update_post'];
  };
  '/key/delete': {
    /**
     * Delete Key Fn
     * @description Delete a key from the key management system.
     *
     * Parameters::
     * - keys (List[str]): A list of keys or hashed keys to delete. Example {"keys": ["sk-QWrxEynunsNpV1zT48HIrw", "837e17519f44683334df5291321d97b8bf1098cd490e49e215f6fea935aa28be"]}
     *
     * Returns:
     * - deleted_keys (List[str]): A list of deleted keys. Example {"deleted_keys": ["sk-QWrxEynunsNpV1zT48HIrw", "837e17519f44683334df5291321d97b8bf1098cd490e49e215f6fea935aa28be"]}
     *
     *
     * Raises:
     *     HTTPException: If an error occurs during key deletion.
     */
    post: operations['delete_key_fn_key_delete_post'];
  };
  '/v2/key/info': {
    /**
     * Info Key Fn V2
     * @description Retrieve information about a list of keys.
     *
     * **New endpoint**. Currently admin only.
     * Parameters:
     *     keys: Optional[list] = body parameter representing the key(s) in the request
     *     user_api_key_dict: UserAPIKeyAuth = Dependency representing the user's API key
     * Returns:
     *     Dict containing the key and its associated information
     *
     * Example Curl:
     * ```
     * curl -X GET "http://0.0.0.0:8000/key/info"     -H "Authorization: Bearer sk-1234"     -d {"keys": ["sk-1", "sk-2", "sk-3"]}
     * ```
     */
    post: operations['info_key_fn_v2_v2_key_info_post'];
  };
  '/key/info': {
    /**
     * Info Key Fn
     * @description Retrieve information about a key.
     * Parameters:
     *     key: Optional[str] = Query parameter representing the key in the request
     *     user_api_key_dict: UserAPIKeyAuth = Dependency representing the user's API key
     * Returns:
     *     Dict containing the key and its associated information
     *
     * Example Curl:
     * ```
     * curl -X GET "http://0.0.0.0:8000/key/info?key=sk-02Wr4IAlN3NvPXvL5JVvDA" -H "Authorization: Bearer sk-1234"
     * ```
     *
     * Example Curl - if no key is passed, it will use the Key Passed in Authorization Header
     * ```
     * curl -X GET "http://0.0.0.0:8000/key/info" -H "Authorization: Bearer sk-02Wr4IAlN3NvPXvL5JVvDA"
     * ```
     */
    get: operations['info_key_fn_key_info_get'];
  };
  '/spend/keys': {
    /**
     * Spend Key Fn
     * @description View all keys created, ordered by spend
     *
     * Example Request:
     * ```
     * curl -X GET "http://0.0.0.0:8000/spend/keys" -H "Authorization: Bearer sk-1234"
     * ```
     */
    get: operations['spend_key_fn_spend_keys_get'];
  };
  '/spend/users': {
    /**
     * Spend User Fn
     * @description View all users created, ordered by spend
     *
     * Example Request:
     * ```
     * curl -X GET "http://0.0.0.0:8000/spend/users" -H "Authorization: Bearer sk-1234"
     * ```
     *
     * View User Table row for user_id
     * ```
     * curl -X GET "http://0.0.0.0:8000/spend/users?user_id=1234" -H "Authorization: Bearer sk-1234"
     * ```
     */
    get: operations['spend_user_fn_spend_users_get'];
  };
  '/spend/tags': {
    /**
     * View Spend Tags
     * @description LiteLLM Enterprise - View Spend Per Request Tag
     *
     * Example Request:
     * ```
     * curl -X GET "http://0.0.0.0:8000/spend/tags" -H "Authorization: Bearer sk-1234"
     * ```
     *
     * Spend with Start Date and End Date
     * ```
     * curl -X GET "http://0.0.0.0:8000/spend/tags?start_date=2022-01-01&end_date=2022-02-01" -H "Authorization: Bearer sk-1234"
     * ```
     */
    get: operations['view_spend_tags_spend_tags_get'];
  };
  '/global/activity': {
    /**
     * Get Global Activity
     * @description Get number of API Requests, total tokens through proxy
     *
     * {
     *     "daily_data": [
     *             const chartdata = [
     *             {
     *             date: 'Jan 22',
     *             api_requests: 10,
     *             total_tokens: 2000
     *             },
     *             {
     *             date: 'Jan 23',
     *             api_requests: 10,
     *             total_tokens: 12
     *             },
     *     ],
     *     "sum_api_requests": 20,
     *     "sum_total_tokens": 2012
     * }
     */
    get: operations['get_global_activity_global_activity_get'];
  };
  '/global/activity/model': {
    /**
     * Get Global Activity Model
     * @description Get number of API Requests, total tokens through proxy - Grouped by MODEL
     *
     * [
     *     {
     *         "model": "gpt-4",
     *         "daily_data": [
     *                 const chartdata = [
     *                 {
     *                 date: 'Jan 22',
     *                 api_requests: 10,
     *                 total_tokens: 2000
     *                 },
     *                 {
     *                 date: 'Jan 23',
     *                 api_requests: 10,
     *                 total_tokens: 12
     *                 },
     *         ],
     *         "sum_api_requests": 20,
     *         "sum_total_tokens": 2012
     *
     *     },
     *     {
     *         "model": "azure/gpt-4-turbo",
     *         "daily_data": [
     *                 const chartdata = [
     *                 {
     *                 date: 'Jan 22',
     *                 api_requests: 10,
     *                 total_tokens: 2000
     *                 },
     *                 {
     *                 date: 'Jan 23',
     *                 api_requests: 10,
     *                 total_tokens: 12
     *                 },
     *         ],
     *         "sum_api_requests": 20,
     *         "sum_total_tokens": 2012
     *
     *     },
     * ]
     */
    get: operations['get_global_activity_model_global_activity_model_get'];
  };
  '/global/activity/exceptions/deployment': {
    /**
     * Get Global Activity Exceptions Per Deployment
     * @description Get number of 429 errors - Grouped by deployment
     *
     * [
     *     {
     *         "deployment": "https://azure-us-east-1.openai.azure.com/",
     *         "daily_data": [
     *                 const chartdata = [
     *                 {
     *                 date: 'Jan 22',
     *                 num_rate_limit_exceptions: 10
     *                 },
     *                 {
     *                 date: 'Jan 23',
     *                 num_rate_limit_exceptions: 12
     *                 },
     *         ],
     *         "sum_num_rate_limit_exceptions": 20,
     *
     *     },
     *     {
     *         "deployment": "https://azure-us-east-1.openai.azure.com/",
     *         "daily_data": [
     *                 const chartdata = [
     *                 {
     *                 date: 'Jan 22',
     *                 num_rate_limit_exceptions: 10,
     *                 },
     *                 {
     *                 date: 'Jan 23',
     *                 num_rate_limit_exceptions: 12
     *                 },
     *         ],
     *         "sum_num_rate_limit_exceptions": 20,
     *
     *     },
     * ]
     */
    get: operations['get_global_activity_exceptions_per_deployment_global_activity_exceptions_deployment_get'];
  };
  '/global/activity/exceptions': {
    /**
     * Get Global Activity Exceptions
     * @description Get number of API Requests, total tokens through proxy
     *
     * {
     *     "daily_data": [
     *             const chartdata = [
     *             {
     *             date: 'Jan 22',
     *             num_rate_limit_exceptions: 10,
     *             },
     *             {
     *             date: 'Jan 23',
     *             num_rate_limit_exceptions: 10,
     *             },
     *     ],
     *     "sum_api_exceptions": 20,
     * }
     */
    get: operations['get_global_activity_exceptions_global_activity_exceptions_get'];
  };
  '/spend/calculate': {
    /**
     * Calculate Spend
     * @description Accepts all the params of completion_cost.
     *
     * Calculate spend **before** making call:
     *
     * Note: If you see a spend of $0.0 you need to set custom_pricing for your model: https://docs.litellm.ai/docs/proxy/custom_pricing
     *
     * ```
     * curl --location 'http://localhost:4000/spend/calculate'
     * --header 'Authorization: Bearer sk-1234'
     * --header 'Content-Type: application/json'
     * --data '{
     *     "model": "anthropic.claude-v2",
     *     "messages": [{"role": "user", "content": "Hey, how'''s it going?"}]
     * }'
     * ```
     *
     * Calculate spend **after** making call:
     *
     * ```
     * curl --location 'http://localhost:4000/spend/calculate'
     * --header 'Authorization: Bearer sk-1234'
     * --header 'Content-Type: application/json'
     * --data '{
     *     "completion_response": {
     *         "id": "chatcmpl-123",
     *         "object": "chat.completion",
     *         "created": 1677652288,
     *         "model": "gpt-3.5-turbo-0125",
     *         "system_fingerprint": "fp_44709d6fcb",
     *         "choices": [{
     *             "index": 0,
     *             "message": {
     *                 "role": "assistant",
     *                 "content": "Hello there, how may I assist you today?"
     *             },
     *             "logprobs": null,
     *             "finish_reason": "stop"
     *         }]
     *         "usage": {
     *             "prompt_tokens": 9,
     *             "completion_tokens": 12,
     *             "total_tokens": 21
     *         }
     *     }
     * }'
     * ```
     */
    post: operations['calculate_spend_spend_calculate_post'];
  };
  '/spend/logs': {
    /**
     * View Spend Logs
     * @description View all spend logs, if request_id is provided, only logs for that request_id will be returned
     *
     * Example Request for all logs
     * ```
     * curl -X GET "http://0.0.0.0:8000/spend/logs" -H "Authorization: Bearer sk-1234"
     * ```
     *
     * Example Request for specific request_id
     * ```
     * curl -X GET "http://0.0.0.0:8000/spend/logs?request_id=chatcmpl-6dcb2540-d3d7-4e49-bb27-291f863f112e" -H "Authorization: Bearer sk-1234"
     * ```
     *
     * Example Request for specific api_key
     * ```
     * curl -X GET "http://0.0.0.0:8000/spend/logs?api_key=sk-Fn8Ej39NkBQmUagFEoUWPQ" -H "Authorization: Bearer sk-1234"
     * ```
     *
     * Example Request for specific user_id
     * ```
     * curl -X GET "http://0.0.0.0:8000/spend/logs?user_id=<EMAIL>" -H "Authorization: Bearer sk-1234"
     * ```
     */
    get: operations['view_spend_logs_spend_logs_get'];
  };
  '/global/spend/reset': {
    /**
     * Global Spend Reset
     * @description ADMIN ONLY / MASTER KEY Only Endpoint
     *
     * Globally reset spend for All API Keys and Teams, maintain LiteLLM_SpendLogs
     *
     * 1. LiteLLM_SpendLogs will maintain the logs on spend, no data gets deleted from there
     * 2. LiteLLM_VerificationTokens spend will be set = 0
     * 3. LiteLLM_TeamTable spend will be set = 0
     */
    post: operations['global_spend_reset_global_spend_reset_post'];
  };
  '/global/spend/logs': {
    /**
     * Global Spend Logs
     * @description [BETA] This is a beta endpoint. It will change.
     *
     * Use this to get global spend (spend per day for last 30d). Admin-only endpoint
     *
     * More efficient implementation of /spend/logs, by creating a view over the spend logs table.
     */
    get: operations['global_spend_logs_global_spend_logs_get'];
  };
  '/global/spend': {
    /**
     * Global Spend
     * @description [BETA] This is a beta endpoint. It will change.
     *
     * View total spend across all proxy keys
     */
    get: operations['global_spend_global_spend_get'];
  };
  '/global/spend/keys': {
    /**
     * Global Spend Keys
     * @description [BETA] This is a beta endpoint. It will change.
     *
     * Use this to get the top 'n' keys with the highest spend, ordered by spend.
     */
    get: operations['global_spend_keys_global_spend_keys_get'];
  };
  '/global/spend/teams': {
    /**
     * Global Spend Per Team
     * @description [BETA] This is a beta endpoint. It will change.
     *
     * Use this to get daily spend, grouped by `team_id` and `date`
     */
    get: operations['global_spend_per_team_global_spend_teams_get'];
  };
  '/global/spend/end_users': {
    /**
     * Global Spend End Users
     * @description [BETA] This is a beta endpoint. It will change.
     *
     * Use this to get the top 'n' keys with the highest spend, ordered by spend.
     */
    post: operations['global_spend_end_users_global_spend_end_users_post'];
  };
  '/global/spend/models': {
    /**
     * Global Spend Models
     * @description [BETA] This is a beta endpoint. It will change.
     *
     * Use this to get the top 'n' keys with the highest spend, ordered by spend.
     */
    get: operations['global_spend_models_global_spend_models_get'];
  };
  '/global/predict/spend/logs': {
    /** Global Predict Spend Logs */
    post: operations['global_predict_spend_logs_global_predict_spend_logs_post'];
  };
  '/user/new': {
    /**
     * New User
     * @description Use this to create a new INTERNAL user with a budget.
     * Internal Users can access LiteLLM Admin UI to make keys, request access to models.
     * This creates a new user and generates a new api key for the new user. The new api key is returned.
     *
     * Returns user id, budget + new key.
     *
     * Parameters:
     * - user_id: Optional[str] - Specify a user id. If not set, a unique id will be generated.
     * - user_alias: Optional[str] - A descriptive name for you to know who this user id refers to.
     * - teams: Optional[list] - specify a list of team id's a user belongs to.
     * - organization_id: Optional[str] - specify the org a user belongs to.
     * - user_email: Optional[str] - Specify a user email.
     * - send_invite_email: Optional[bool] - Specify if an invite email should be sent.
     * - user_role: Optional[str] - Specify a user role - "admin", "app_owner", "app_user"
     * - max_budget: Optional[float] - Specify max budget for a given user.
     * - models: Optional[list] - Model_name's a user is allowed to call. (if empty, key is allowed to call all models)
     * - tpm_limit: Optional[int] - Specify tpm limit for a given user (Tokens per minute)
     * - rpm_limit: Optional[int] - Specify rpm limit for a given user (Requests per minute)
     * - auto_create_key: bool - Default=True. Flag used for returning a key as part of the /user/new response
     *
     * Returns:
     * - key: (str) The generated api key for the user
     * - expires: (datetime) Datetime object for when key expires.
     * - user_id: (str) Unique user id - used for tracking spend across multiple keys for same user id.
     * - max_budget: (float|None) Max budget for given user.
     */
    post: operations['new_user_user_new_post'];
  };
  '/user/auth': {
    /**
     * User Auth
     * @description Allows UI ("https://dashboard.litellm.ai/", or self-hosted - os.getenv("LITELLM_HOSTED_UI")) to request a magic link to be sent to user email, for auth to proxy.
     *
     * Only allows emails from accepted email subdomains.
     *
     * Rate limit: 1 request every 60s.
     *
     * Only works, if you enable 'allow_user_auth' in general settings:
     * e.g.:
     * ```yaml
     * general_settings:
     *     allow_user_auth: true
     * ```
     *
     * Requirements:
     * SMTP server details saved in .env:
     * - os.environ["SMTP_HOST"]
     * - os.environ["SMTP_PORT"]
     * - os.environ["SMTP_USERNAME"]
     * - os.environ["SMTP_PASSWORD"]
     * - os.environ["SMTP_SENDER_EMAIL"]
     */
    post: operations['user_auth_user_auth_post'];
  };
  '/user/info': {
    /**
     * User Info
     * @description Use this to get user information. (user row + all user key info)
     *
     * Example request
     * ```
     * curl -X GET 'http://localhost:8000/user/info?user_id=krrish7%40berri.ai'     --header 'Authorization: Bearer sk-1234'
     * ```
     */
    get: operations['user_info_user_info_get'];
  };
  '/user/update': {
    /**
     * User Update
     * @description Example curl
     *
     * ```
     * curl --location 'http://0.0.0.0:4000/user/update'     --header 'Authorization: Bearer sk-1234'     --header 'Content-Type: application/json'     --data '{
     *     "user_id": "test-litellm-user-4",
     *     "user_role": "proxy_admin_viewer"
     * }'
     *
     * See below for all params
     * ```
     */
    post: operations['user_update_user_update_post'];
  };
  '/user/request_model': {
    /**
     * User Request Model
     * @description Allow a user to create a request to access a model
     */
    post: operations['user_request_model_user_request_model_post'];
  };
  '/user/get_requests': {
    /**
     * User Get Requests
     * @description Get all "Access" requests made by proxy users, access requests are requests for accessing models
     */
    get: operations['user_get_requests_user_get_requests_get'];
  };
  '/user/get_users': {
    /**
     * Get Users
     * @description [BETA] This could change without notice. Give feedback - https://github.com/BerriAI/litellm/issues
     *
     * Get all users who are a specific `user_role`.
     *
     * Used by the UI to populate the user lists.
     *
     * Currently - admin-only endpoint.
     */
    get: operations['get_users_user_get_users_get'];
  };
  '/customer/block': {
    /**
     * Block User
     * @description [BETA] Reject calls with this end-user id
     *
     *     (any /chat/completion call with this user={end-user-id} param, will be rejected.)
     *
     *     ```
     *     curl -X POST "http://0.0.0.0:8000/user/block"
     *     -H "Authorization: Bearer sk-1234"
     *     -D '{
     *     "user_ids": [<user_id>, ...]
     *     }'
     *     ```
     */
    post: operations['block_user_customer_block_post'];
  };
  '/customer/unblock': {
    /**
     * Unblock User
     * @description [BETA] Unblock calls with this user id
     *
     * Example
     * ```
     * curl -X POST "http://0.0.0.0:8000/user/unblock"
     * -H "Authorization: Bearer sk-1234"
     * -D '{
     * "user_ids": [<user_id>, ...]
     * }'
     * ```
     */
    post: operations['unblock_user_customer_unblock_post'];
  };
  '/customer/new': {
    /**
     * New End User
     * @description Allow creating a new Customer
     * NOTE: This used to be called `/end_user/new`, we will still be maintaining compatibility for /end_user/XXX for these endpoints
     *
     * - Allow specifying allowed regions
     * - Allow specifying default model
     *
     * Example curl:
     * ```
     * curl --location 'http://0.0.0.0:4000/customer/new'         --header 'Authorization: Bearer sk-1234'         --header 'Content-Type: application/json'         --data '{
     *         "user_id" : "ishaan-jaff-3",
     *         "allowed_region": "eu",
     *         "budget_id": "free_tier",
     *         "default_model": "azure/gpt-3.5-turbo-eu" <- all calls from this user, use this model?
     *     }'
     *
     *     # return end-user object
     * ```
     */
    post: operations['new_end_user_customer_new_post'];
  };
  '/customer/info': {
    /** End User Info */
    get: operations['end_user_info_customer_info_get'];
  };
  '/customer/update': {
    /**
     * Update End User
     * @description Example curl
     *
     * ```
     * curl --location 'http://0.0.0.0:4000/customer/update'     --header 'Authorization: Bearer sk-1234'     --header 'Content-Type: application/json'     --data '{
     *     "user_id": "test-litellm-user-4",
     *     "budget_id": "paid_tier"
     * }'
     *
     * See below for all params
     * ```
     */
    post: operations['update_end_user_customer_update_post'];
  };
  '/customer/delete': {
    /**
     * Delete End User
     * @description Example curl
     *
     * ```
     * curl --location 'http://0.0.0.0:4000/customer/delete'         --header 'Authorization: Bearer sk-1234'         --header 'Content-Type: application/json'         --data '{
     *         "user_ids" :["ishaan-jaff-5"]
     * }'
     *
     * See below for all params
     * ```
     */
    post: operations['delete_end_user_customer_delete_post'];
  };
  '/team/new': {
    /**
     * New Team
     * @description Allow users to create a new team. Apply user permissions to their team.
     *
     * [ASK FOR HELP](https://github.com/BerriAI/litellm/issues)
     *
     * Parameters:
     * - team_alias: Optional[str] - User defined team alias
     * - team_id: Optional[str] - The team id of the user. If none passed, we'll generate it.
     * - members_with_roles: List[{"role": "admin" or "user", "user_id": "<user-id>"}] - A list of users and their roles in the team. Get user_id when making a new user via `/user/new`.
     * - metadata: Optional[dict] - Metadata for team, store information for team. Example metadata = {"team": "core-infra", "app": "app2", "email": "<EMAIL>" }
     * - tpm_limit: Optional[int] - The TPM (Tokens Per Minute) limit for this team - all keys with this team_id will have at max this TPM limit
     * - rpm_limit: Optional[int] - The RPM (Requests Per Minute) limit for this team - all keys associated with this team_id will have at max this RPM limit
     * - max_budget: Optional[float] - The maximum budget allocated to the team - all keys for this team_id will have at max this max_budget
     * - models: Optional[list] - A list of models associated with the team - all keys for this team_id will have at most, these models. If empty, assumes all models are allowed.
     * - blocked: bool - Flag indicating if the team is blocked or not - will stop all calls from keys with this team_id.
     *
     * Returns:
     * - team_id: (str) Unique team id - used for tracking spend across multiple keys for same team id.
     *
     * _deprecated_params:
     * - admins: list - A list of user_id's for the admin role
     * - users: list - A list of user_id's for the user role
     *
     * Example Request:
     * ```
     * curl --location 'http://0.0.0.0:4000/team/new'
     * --header 'Authorization: Bearer sk-1234'
     * --header 'Content-Type: application/json'
     * --data '{
     *   "team_alias": "my-new-team_2",
     *   "members_with_roles": [{"role": "admin", "user_id": "user-1234"},
     *     {"role": "user", "user_id": "user-2434"}]
     * }'
     *
     * ```
     */
    post: operations['new_team_team_new_post'];
  };
  '/team/update': {
    /**
     * Update Team
     * @description Use `/team/member_add` AND `/team/member/delete` to add/remove new team members
     *
     * You can now update team budget / rate limits via /team/update
     *
     * Parameters:
     * - team_id: str - The team id of the user. Required param.
     * - team_alias: Optional[str] - User defined team alias
     * - metadata: Optional[dict] - Metadata for team, store information for team. Example metadata = {"team": "core-infra", "app": "app2", "email": "<EMAIL>" }
     * - tpm_limit: Optional[int] - The TPM (Tokens Per Minute) limit for this team - all keys with this team_id will have at max this TPM limit
     * - rpm_limit: Optional[int] - The RPM (Requests Per Minute) limit for this team - all keys associated with this team_id will have at max this RPM limit
     * - max_budget: Optional[float] - The maximum budget allocated to the team - all keys for this team_id will have at max this max_budget
     * - models: Optional[list] - A list of models associated with the team - all keys for this team_id will have at most, these models. If empty, assumes all models are allowed.
     * - blocked: bool - Flag indicating if the team is blocked or not - will stop all calls from keys with this team_id.
     *
     * Example - update team TPM Limit
     *
     * ```
     * curl --location 'http://0.0.0.0:8000/team/update'
     * --header 'Authorization: Bearer sk-1234'
     * --header 'Content-Type: application/json'
     * --data-raw '{
     *     "team_id": "litellm-test-client-id-new",
     *     "tpm_limit": 100
     * }'
     * ```
     */
    post: operations['update_team_team_update_post'];
  };
  '/team/member_add': {
    /**
     * Team Member Add
     * @description [BETA]
     *
     * Add new members (either via user_email or user_id) to a team
     *
     * If user doesn't exist, new user row will also be added to User Table
     *
     * ```
     *
     * curl -X POST 'http://0.0.0.0:4000/team/member_add'     -H 'Authorization: Bearer sk-1234'     -H 'Content-Type: application/json'     -d '{"team_id": "45e3e396-ee08-4a61-a88e-16b3ce7e0849", "member": {"role": "user", "user_id": "<EMAIL>"}}'
     *
     * ```
     */
    post: operations['team_member_add_team_member_add_post'];
  };
  '/team/member_delete': {
    /**
     * Team Member Delete
     * @description [BETA]
     *
     * delete members (either via user_email or user_id) from a team
     *
     * If user doesn't exist, an exception will be raised
     * ```
     * curl -X POST 'http://0.0.0.0:8000/team/update'
     * -H 'Authorization: Bearer sk-1234'
     * -H 'Content-Type: application/json'
     * -D '{
     *     "team_id": "45e3e396-ee08-4a61-a88e-16b3ce7e0849",
     *     "user_id": "<EMAIL>"
     * }'
     * ```
     */
    post: operations['team_member_delete_team_member_delete_post'];
  };
  '/team/model/add': {
    /**
     * Team Model Add
     * @description Add models to a team
     */
    post: operations['team_model_add_team_model_add_post'];
  };
  '/team/model/delete': {
    /**
     * Team Model Delete
     * @description Remove models from a team
     */
    post: operations['team_model_delete_team_model_delete_post'];
  };
  '/team/delete': {
    /**
     * Delete Team
     * @description delete team and associated team keys
     *
     * ```
     * curl --location 'http://0.0.0.0:8000/team/delete'
     * --header 'Authorization: Bearer sk-1234'
     * --header 'Content-Type: application/json'
     * --data-raw '{
     *     "team_ids": ["45e3e396-ee08-4a61-a88e-16b3ce7e0849"]
     * }'
     * ```
     */
    post: operations['delete_team_team_delete_post'];
  };
  '/team/info': {
    /**
     * Team Info
     * @description get info on team + related keys
     *
     * ```
     * curl --location 'http://localhost:4000/team/info'     --header 'Authorization: Bearer sk-1234'     --header 'Content-Type: application/json'     --data '{
     *     "teams": ["<team-id>",..]
     * }'
     * ```
     */
    get: operations['team_info_team_info_get'];
  };
  '/team/block': {
    /**
     * Block Team
     * @description Blocks all calls from keys with this team id.
     */
    post: operations['block_team_team_block_post'];
  };
  '/team/unblock': {
    /**
     * Unblock Team
     * @description Blocks all calls from keys with this team id.
     */
    post: operations['unblock_team_team_unblock_post'];
  };
  '/team/list': {
    /**
     * List Team
     * @description [Admin-only] List all available teams
     *
     * ```
     * curl --location --request GET 'http://0.0.0.0:4000/team/list'         --header 'Authorization: Bearer sk-1234'
     * ```
     */
    get: operations['list_team_team_list_get'];
  };
  '/organization/new': {
    /**
     * New Organization
     * @description Allow orgs to own teams
     *
     * Set org level budgets + model access.
     *
     * Only admins can create orgs.
     *
     * # Parameters
     *
     * - `organization_alias`: *str* = The name of the organization.
     * - `models`: *List* = The models the organization has access to.
     * - `budget_id`: *Optional[str]* = The id for a budget (tpm/rpm/max budget) for the organization.
     * ### IF NO BUDGET ID - CREATE ONE WITH THESE PARAMS ###
     * - `max_budget`: *Optional[float]* = Max budget for org
     * - `tpm_limit`: *Optional[int]* = Max tpm limit for org
     * - `rpm_limit`: *Optional[int]* = Max rpm limit for org
     * - `model_max_budget`: *Optional[dict]* = Max budget for a specific model
     * - `budget_duration`: *Optional[str]* = Frequency of reseting org budget
     *
     * Case 1: Create new org **without** a budget_id
     *
     * ```bash
     * curl --location 'http://0.0.0.0:4000/organization/new'
     * --header 'Authorization: Bearer sk-1234'
     * --header 'Content-Type: application/json'
     * --data '{
     *     "organization_alias": "my-secret-org",
     *     "models": ["model1", "model2"],
     *     "max_budget": 100
     * }'
     *
     *
     * ```
     *
     * Case 2: Create new org **with** a budget_id
     *
     * ```bash
     * curl --location 'http://0.0.0.0:4000/organization/new'
     * --header 'Authorization: Bearer sk-1234'
     * --header 'Content-Type: application/json'
     * --data '{
     *     "organization_alias": "my-secret-org",
     *     "models": ["model1", "model2"],
     *     "budget_id": "428eeaa8-f3ac-4e85-a8fb-7dc8d7aa8689"
     * }'
     * ```
     */
    post: operations['new_organization_organization_new_post'];
  };
  '/organization/update': {
    /**
     * Update Organization
     * @description [TODO] Not Implemented yet. Let us know if you need this - https://github.com/BerriAI/litellm/issues
     */
    post: operations['update_organization_organization_update_post'];
  };
  '/organization/delete': {
    /**
     * Delete Organization
     * @description [TODO] Not Implemented yet. Let us know if you need this - https://github.com/BerriAI/litellm/issues
     */
    post: operations['delete_organization_organization_delete_post'];
  };
  '/organization/info': {
    /**
     * Info Organization
     * @description Get the org specific information
     */
    post: operations['info_organization_organization_info_post'];
  };
  '/budget/new': {
    /**
     * New Budget
     * @description Create a new budget object. Can apply this to teams, orgs, end-users, keys.
     */
    post: operations['new_budget_budget_new_post'];
  };
  '/budget/info': {
    /**
     * Info Budget
     * @description Get the budget id specific information
     */
    post: operations['info_budget_budget_info_post'];
  };
  '/budget/settings': {
    /**
     * Budget Settings
     * @description Get list of configurable params + current value for a budget item + description of each field
     *
     * Used on Admin UI.
     */
    get: operations['budget_settings_budget_settings_get'];
  };
  '/budget/list': {
    /**
     * List Budget
     * @description List all the created budgets in proxy db. Used on Admin UI.
     */
    get: operations['list_budget_budget_list_get'];
  };
  '/budget/delete': {
    /**
     * Delete Budget
     * @description Delete budget
     */
    post: operations['delete_budget_budget_delete_post'];
  };
  '/model/new': {
    /**
     * Add New Model
     * @description Allows adding new models to the model list in the config.yaml
     */
    post: operations['add_new_model_model_new_post'];
  };
  '/model/update': {
    /**
     * Update Model
     * @description Edit existing model params
     */
    post: operations['update_model_model_update_post'];
  };
  '/v1/model/info': {
    /**
     * Model Info V1
     * @description Provides more info about each model in /models, including config.yaml descriptions (except api key and api base)
     */
    get: operations['model_info_v1_v1_model_info_get'];
  };
  '/model/info': {
    /**
     * Model Info V1
     * @description Provides more info about each model in /models, including config.yaml descriptions (except api key and api base)
     */
    get: operations['model_info_v1_model_info_get'];
  };
  '/model_group/info': {
    /**
     * Model Group Info
     * @description Provides more info about each model in /models, including config.yaml descriptions (except api key and api base)
     */
    get: operations['model_group_info_model_group_info_get'];
  };
  '/model/delete': {
    /**
     * Delete Model
     * @description Allows deleting models in the model list in the config.yaml
     */
    post: operations['delete_model_model_delete_post'];
  };
  '/queue/chat/completions': {
    /** Async Queue Request */
    post: operations['async_queue_request_queue_chat_completions_post'];
  };
  '/queue/info': {
    /**
     * Queue Info
     * @description Help user know the status of an item in the queue
     */
    get: operations['queue_info_queue_info_get'];
  };
  '/ollama_logs': {
    /** Retrieve Server Log */
    get: operations['retrieve_server_log_ollama_logs_get'];
  };
  '/invitation/new': {
    /**
     * New Invitation
     * @description Allow admin to create invite links, to onboard new users to Admin UI.
     *
     * ```
     * curl -X POST 'http://localhost:4000/invitation/new'         -H 'Content-Type: application/json'         -D '{
     *         "user_id": "1234" // 👈 id of user in 'LiteLLM_UserTable'
     *     }'
     * ```
     */
    post: operations['new_invitation_invitation_new_post'];
  };
  '/invitation/info': {
    /**
     * Invitation Info
     * @description Allow admin to create invite links, to onboard new users to Admin UI.
     *
     * ```
     * curl -X POST 'http://localhost:4000/invitation/new'         -H 'Content-Type: application/json'         -D '{
     *         "user_id": "1234" // 👈 id of user in 'LiteLLM_UserTable'
     *     }'
     * ```
     */
    get: operations['invitation_info_invitation_info_get'];
  };
  '/invitation/update': {
    /**
     * Invitation Update
     * @description Update when invitation is accepted
     *
     * ```
     * curl -X POST 'http://localhost:4000/invitation/update'         -H 'Content-Type: application/json'         -D '{
     *         "invitation_id": "1234" // 👈 id of invitation in 'LiteLLM_InvitationTable'
     *         "is_accepted": True // when invitation is accepted
     *     }'
     * ```
     */
    post: operations['invitation_update_invitation_update_post'];
  };
  '/invitation/delete': {
    /**
     * Invitation Delete
     * @description Delete invitation link
     *
     * ```
     * curl -X POST 'http://localhost:4000/invitation/delete'         -H 'Content-Type: application/json'         -D '{
     *         "invitation_id": "1234" // 👈 id of invitation in 'LiteLLM_InvitationTable'
     *     }'
     * ```
     */
    post: operations['invitation_delete_invitation_delete_post'];
  };
  '/config/update': {
    /**
     * Update Config
     * @description For Admin UI - allows admin to update config via UI
     *
     * Currently supports modifying General Settings + LiteLLM settings
     */
    post: operations['update_config_config_update_post'];
  };
  '/config/field/update': {
    /**
     * Update Config General Settings
     * @description Update a specific field in litellm general settings
     */
    post: operations['update_config_general_settings_config_field_update_post'];
  };
  '/config/field/info': {
    /** Get Config General Settings */
    get: operations['get_config_general_settings_config_field_info_get'];
  };
  '/config/list': {
    /**
     * Get Config List
     * @description List the available fields + current values for a given type of setting (currently just 'general_settings'user_api_key_dict: UserAPIKeyAuth = Depends(user_api_key_auth),)
     */
    get: operations['get_config_list_config_list_get'];
  };
  '/config/field/delete': {
    /**
     * Delete Config General Settings
     * @description Delete the db value of this field in litellm general settings. Resets it to it's initial default value on litellm.
     */
    post: operations['delete_config_general_settings_config_field_delete_post'];
  };
  '/config/yaml': {
    /**
     * Config Yaml Endpoint
     * @description This is a mock endpoint, to show what you can set in config.yaml details in the Swagger UI.
     *
     * Parameters:
     *
     * The config.yaml object has the following attributes:
     * - **model_list**: *Optional[List[ModelParams]]* - A list of supported models on the server, along with model-specific configurations. ModelParams includes "model_name" (name of the model), "litellm_params" (litellm-specific parameters for the model), and "model_info" (additional info about the model such as id, mode, cost per token, etc).
     *
     * - **litellm_settings**: *Optional[dict]*: Settings for the litellm module. You can specify multiple properties like "drop_params", "set_verbose", "api_base", "cache".
     *
     * - **general_settings**: *Optional[ConfigGeneralSettings]*: General settings for the server like "completion_model" (default model for chat completion calls), "use_azure_key_vault" (option to load keys from azure key vault), "master_key" (key required for all calls to proxy), and others.
     *
     * Please, refer to each class's description for a better understanding of the specific attributes within them.
     *
     * Note: This is a mock endpoint primarily meant for demonstration purposes, and does not actually provide or change any configurations.
     */
    get: operations['config_yaml_endpoint_config_yaml_get'];
  };
  '/test': {
    /**
     * Test Endpoint
     * @description [DEPRECATED] use `/health/liveliness` instead.
     *
     * A test endpoint that pings the proxy server to check if it's healthy.
     *
     * Parameters:
     *     request (Request): The incoming request.
     *
     * Returns:
     *     dict: A dictionary containing the route of the request URL.
     */
    get: operations['test_endpoint_test_get'];
  };
  '/health': {
    /**
     * Health Endpoint
     * @description 🚨 USE `/health/liveliness` to health check the proxy 🚨
     *
     * See more 👉 https://docs.litellm.ai/docs/proxy/health
     *
     *
     * Check the health of all the endpoints in config.yaml
     *
     * To run health checks in the background, add this to config.yaml:
     * ```
     * general_settings:
     *     # ... other settings
     *     background_health_checks: True
     * ```
     * else, the health checks will be run on models when /health is called.
     */
    get: operations['health_endpoint_health_get'];
  };
  '/active/callbacks': {
    /**
     * Active Callbacks
     * @description Returns a list of active callbacks on litellm.callbacks, litellm.input_callback, litellm.failure_callback, litellm.success_callback
     */
    get: operations['active_callbacks_active_callbacks_get'];
  };
  '/health/readiness': {
    /**
     * Health Readiness
     * @description Unprotected endpoint for checking if worker can receive requests
     */
    get: operations['health_readiness_health_readiness_get'];
  };
  '/health/liveliness': {
    /**
     * Health Liveliness
     * @description Unprotected endpoint for checking if worker is alive
     */
    get: operations['health_liveliness_health_liveliness_get'];
  };
  '/cache/ping': {
    /**
     * Cache Ping
     * @description Endpoint for checking if cache can be pinged
     */
    get: operations['cache_ping_cache_ping_get'];
  };
  '/cache/delete': {
    /**
     * Cache Delete
     * @description Endpoint for deleting a key from the cache. All responses from litellm proxy have `x-litellm-cache-key` in the headers
     *
     * Parameters:
     * - **keys**: *Optional[List[str]]* - A list of keys to delete from the cache. Example {"keys": ["key1", "key2"]}
     *
     * ```shell
     * curl -X POST "http://0.0.0.0:4000/cache/delete"     -H "Authorization: Bearer sk-1234"     -d '{"keys": ["key1", "key2"]}'
     * ```
     */
    post: operations['cache_delete_cache_delete_post'];
  };
  '/cache/redis/info': {
    /**
     * Cache Redis Info
     * @description Endpoint for getting /redis/info
     */
    get: operations['cache_redis_info_cache_redis_info_get'];
  };
  '/cache/flushall': {
    /**
     * Cache Flushall
     * @description A function to flush all items from the cache. (All items will be deleted from the cache with this)
     * Raises HTTPException if the cache is not initialized or if the cache type does not support flushing.
     * Returns a dictionary with the status of the operation.
     *
     * Usage:
     * ```
     * curl -X POST http://0.0.0.0:4000/cache/flushall -H "Authorization: Bearer sk-1234"
     * ```
     */
    post: operations['cache_flushall_cache_flushall_post'];
  };
  '/': {
    /** Home */
    get: operations['home__get'];
  };
  '/routes': {
    /**
     * Get Routes
     * @description Get a list of available routes in the FastAPI application.
     */
    get: operations['get_routes_routes_get'];
  };
  '/token/generate': {
    /**
     * Token Generate
     * @description Test endpoint. Admin-only access. Meant for generating admin tokens with specific claims and testing if they work for creating keys, etc.
     */
    get: operations['token_generate_token_generate_get'];
  };
}

export type webhooks = Record<string, never>;

export interface components {
  schemas: {
    /** CreateCredentialRequest */
    CreateCredentialRequest: {
      /** Credential Name */
      credential_name: string;
      /** Credential Values */
      credential_values: {
        api_base?: string | null;
        api_key?: string;
        api_version?: string;
      };
      /** Credential Info */
      credential_info: {
        description?: string;
      };
    };
    /** UpdateCredentialRequest */
    UpdateCredentialRequest: {
      /** Credential Values */
      credential_values: {
        api_base?: string | null;
        api_key?: string;
        api_version?: string;
      };
      /** Credential Info */
      credential_info: {
        description?: string;
      };
    };
    /** BlockTeamRequest */
    BlockTeamRequest: {
      /** Team Id */
      team_id: string;
    };
    /** BlockUsers */
    BlockUsers: {
      /** User Ids */
      user_ids: string[];
    };
    /** Body_audio_transcriptions_audio_transcriptions_post */
    Body_audio_transcriptions_audio_transcriptions_post: {
      /**
       * File
       * Format: binary
       */
      file: string;
    };
    /** Body_audio_transcriptions_v1_audio_transcriptions_post */
    Body_audio_transcriptions_v1_audio_transcriptions_post: {
      /**
       * File
       * Format: binary
       */
      file: string;
    };
    /** BudgetDeleteRequest */
    BudgetDeleteRequest: {
      /** Id */
      id: string;
    };
    /** BudgetNew */
    BudgetNew: {
      /**
       * Budget Id
       * @description The unique budget id.
       */
      budget_id?: string;
      /**
       * Max Budget
       * @description Requests will fail if this budget (in USD) is exceeded.
       */
      max_budget?: number | null;
      /**
       * Soft Budget
       * @description Requests will NOT fail if this is exceeded. Will fire alerting though.
       */
      soft_budget?: number | null;
      /**
       * Max Parallel Requests
       * @description Max concurrent requests allowed for this budget id.
       */
      max_parallel_requests?: number | null;
      /**
       * Tpm Limit
       * @description Max tokens per minute, allowed for this budget id.
       */
      tpm_limit?: number | null;
      /**
       * Rpm Limit
       * @description Max requests per minute, allowed for this budget id.
       */
      rpm_limit?: number | null;
      /**
       * Budget Duration
       * @description Max duration budget should be set for (e.g. '1hr', '1d', '28d')
       */
      budget_duration?: string | null;
    };
    /** BudgetRequest */
    BudgetRequest: {
      /** Budgets */
      budgets: string[];
    };
    /** ConfigFieldDelete */
    ConfigFieldDelete: {
      /**
       * Config Type
       * @constant
       * @enum {string}
       */
      config_type: 'general_settings';
      /** Field Name */
      field_name: string;
    };
    /** ConfigFieldInfo */
    ConfigFieldInfo: {
      /** Field Name */
      field_name: string;
      /** Field Value */
      field_value: unknown;
    };
    /** ConfigFieldUpdate */
    ConfigFieldUpdate: {
      /** Field Name */
      field_name: string;
      /** Field Value */
      field_value: unknown;
      /**
       * Config Type
       * @constant
       * @enum {string}
       */
      config_type: 'general_settings';
    };
    /**
     * ConfigGeneralSettings
     * @description Documents all the fields supported by `general_settings` in config.yaml
     */
    ConfigGeneralSettings: {
      /**
       * Completion Model
       * @description proxy level default model for all chat completion calls
       */
      completion_model?: string | null;
      /** @description key manager to load keys from / decrypt keys with */
      key_management_system?: components['schemas']['KeyManagementSystem'] | null;
      /**
       * Use Google Kms
       * @description decrypt keys with google kms
       */
      use_google_kms?: boolean | null;
      /**
       * Use Azure Key Vault
       * @description load keys from azure key vault
       */
      use_azure_key_vault?: boolean | null;
      /**
       * Master Key
       * @description require a key for all calls to proxy
       */
      master_key?: string | null;
      /**
       * Database Url
       * @description connect to a postgres db - needed for generating temporary keys + tracking spend / key
       */
      database_url?: string | null;
      /**
       * Database Connection Pool Limit
       * @description default connection pool for prisma client connecting to postgres db
       * @default 100
       */
      database_connection_pool_limit?: number | null;
      /**
       * Database Connection Timeout
       * @description default timeout for a connection to the database
       * @default 60
       */
      database_connection_timeout?: number | null;
      /**
       * Database Type
       * @description to use dynamodb instead of postgres db
       */
      database_type?: 'dynamo_db' | null;
      /** @description custom args for instantiating dynamodb client - e.g. billing provision */
      database_args?: components['schemas']['DynamoDBArgs'] | null;
      /**
       * Otel
       * @description [BETA] OpenTelemetry support - this might change, use with caution.
       */
      otel?: boolean | null;
      /**
       * Custom Auth
       * @description override user_api_key_auth with your own auth script - https://docs.litellm.ai/docs/proxy/virtual_keys#custom-auth
       */
      custom_auth?: string | null;
      /**
       * Max Parallel Requests
       * @description maximum parallel requests for each api key
       */
      max_parallel_requests?: number | null;
      /**
       * Global Max Parallel Requests
       * @description global max parallel requests to allow for a proxy instance.
       */
      global_max_parallel_requests?: number | null;
      /**
       * Infer Model From Keys
       * @description for `/models` endpoint, infers available model based on environment keys (e.g. OPENAI_API_KEY)
       */
      infer_model_from_keys?: boolean | null;
      /**
       * Background Health Checks
       * @description run health checks in background
       */
      background_health_checks?: boolean | null;
      /**
       * Health Check Interval
       * @description background health check interval in seconds
       * @default 300
       */
      health_check_interval?: number;
      /**
       * Alerting
       * @description List of alerting integrations. Today, just slack - `alerting: ['slack']`
       */
      alerting?: unknown[] | null;
      /**
       * Alert Types
       * @description List of alerting types. By default it is all alerts
       */
      alert_types?:
        | (
            | 'llm_exceptions'
            | 'llm_too_slow'
            | 'llm_requests_hanging'
            | 'budget_alerts'
            | 'db_exceptions'
            | 'daily_reports'
            | 'spend_reports'
            | 'cooldown_deployment'
            | 'new_model_added'
            | 'outage_alerts'
            | 'region_outage_alerts'
          )[]
        | null;
      /**
       * Alert To Webhook Url
       * @description Mapping of alert type to webhook url. e.g. `alert_to_webhook_url: {'budget_alerts': '*****************************************************************************'}`
       */
      alert_to_webhook_url?: Record<string, never> | null;
      /**
       * Alerting Args
       * @description Controllable params for slack alerting - e.g. ttl in cache.
       */
      alerting_args?: Record<string, never> | null;
      /**
       * Alerting Threshold
       * @description sends alerts if requests hang for 5min+
       */
      alerting_threshold?: number | null;
      /**
       * Ui Access Mode
       * @description Control access to the Proxy UI
       * @default all
       */
      ui_access_mode?: ('admin_only' | 'all') | null;
      /**
       * Allowed Routes
       * @description Proxy API Endpoints you want users to be able to access
       */
      allowed_routes?: unknown[] | null;
      /**
       * Enable Public Model Hub
       * @description Public model hub for users to see what models they have access to, supported openai params, etc.
       * @default false
       */
      enable_public_model_hub?: boolean;
    };
    /** ConfigList */
    ConfigList: {
      /** Field Name */
      field_name: string;
      /** Field Type */
      field_type: string;
      /** Field Description */
      field_description: string;
      /** Field Value */
      field_value: unknown;
      /** Stored In Db */
      stored_in_db: boolean | null;
      /** Field Default Value */
      field_default_value: unknown;
      /**
       * Premium Field
       * @default false
       */
      premium_field?: boolean;
    };
    /**
     * ConfigYAML
     * @description Documents all the fields supported by the config.yaml
     */
    ConfigYAML: {
      /**
       * Environment Variables
       * @description Object to pass in additional environment variables via POST request
       */
      environment_variables?: Record<string, never> | null;
      /**
       * Model List
       * @description List of supported models on the server, with model-specific configs
       */
      model_list?: components['schemas']['ModelParams'][] | null;
      /**
       * Litellm Settings
       * @description litellm Module settings. See __init__.py for all, example litellm.drop_params=True, litellm.set_verbose=True, litellm.api_base, litellm.cache
       */
      litellm_settings?: Record<string, never> | null;
      general_settings?: components['schemas']['ConfigGeneralSettings'] | null;
      /** @description litellm router object settings. See router.py __init__ for all, example router.num_retries=5, router.timeout=5, router.max_retries=5, router.retry_after=5 */
      router_settings?: components['schemas']['UpdateRouterConfig'] | null;
    };
    /**
     * DeleteCustomerRequest
     * @description Delete multiple Customers
     */
    DeleteCustomerRequest: {
      /** User Ids */
      user_ids: string[];
    };
    /** DeleteTeamRequest */
    DeleteTeamRequest: {
      /** Team Ids */
      team_ids: string[];
    };
    /** Deployment */
    Deployment: {
      /** Model Name */
      model_name: string;
      litellm_params: components['schemas']['LiteLLM_Params'];
      model_info: components['schemas']['litellm__types__router__ModelInfo'];
      [key: string]: unknown;
    };
    /** DynamoDBArgs */
    DynamoDBArgs: {
      /**
       * Billing Mode
       * @enum {string}
       */
      billing_mode: 'PROVISIONED_THROUGHPUT' | 'PAY_PER_REQUEST';
      /** Read Capacity Units */
      read_capacity_units?: number | null;
      /** Write Capacity Units */
      write_capacity_units?: number | null;
      /** Ssl Verify */
      ssl_verify?: boolean | null;
      /** Region Name */
      region_name: string;
      /**
       * User Table Name
       * @default LiteLLM_UserTable
       */
      user_table_name?: string;
      /**
       * Key Table Name
       * @default LiteLLM_VerificationToken
       */
      key_table_name?: string;
      /**
       * Config Table Name
       * @default LiteLLM_Config
       */
      config_table_name?: string;
      /**
       * Spend Table Name
       * @default LiteLLM_SpendLogs
       */
      spend_table_name?: string;
      /** Aws Role Name */
      aws_role_name?: string | null;
      /** Aws Session Name */
      aws_session_name?: string | null;
      /** Aws Web Identity Token */
      aws_web_identity_token?: string | null;
      /** Aws Provider Id */
      aws_provider_id?: string | null;
      /** Aws Policy Arns */
      aws_policy_arns?: string[] | null;
      /** Aws Policy */
      aws_policy?: string | null;
      /** Aws Duration Seconds */
      aws_duration_seconds?: number | null;
      /** Assume Role Aws Role Name */
      assume_role_aws_role_name?: string | null;
      /** Assume Role Aws Session Name */
      assume_role_aws_session_name?: string | null;
    };
    /** GenerateKeyRequest */
    GenerateKeyRequest: {
      /**
       * Models
       * @default []
       */
      models?: unknown[] | null;
      /**
       * Spend
       * @default 0
       */
      spend?: number | null;
      /** Max Budget */
      max_budget?: number | null;
      /** User Id */
      user_id?: string | null;
      /** Team Id */
      team_id?: string | null;
      /** Max Parallel Requests */
      max_parallel_requests?: number | null;
      /**
       * Metadata
       * @default {}
       */
      metadata?: Record<string, any> | null;
      /** Tpm Limit */
      tpm_limit?: number | null;
      /** Rpm Limit */
      rpm_limit?: number | null;
      /** Budget Duration */
      budget_duration?: string | null;
      /**
       * Allowed Cache Controls
       * @default []
       */
      allowed_cache_controls?: unknown[] | null;
      /** Soft Budget */
      soft_budget?: number | null;
      /** Key Alias */
      key_alias?: string | null;
      /** Duration */
      duration?: string | null;
      /**
       * Aliases
       * @default {}
       */
      aliases?: Record<string, never> | null;
      /**
       * Config
       * @default {}
       */
      config?: Record<string, never> | null;
      /**
       * Permissions
       * @default {}
       */
      permissions?: Record<string, never> | null;
      /**
       * Model Max Budget
       * @default {}
       */
      model_max_budget?: Record<string, never> | null;
      /** Send Invite Email */
      send_invite_email?: boolean | null;
    };
    /** GenerateKeyResponse */
    GenerateKeyResponse: {
      /**
       * Models
       * @default []
       */
      models?: unknown[] | null;
      /**
       * Spend
       * @default 0
       */
      spend?: number | null;
      /** Max Budget */
      max_budget?: number | null;
      /** User Id */
      user_id?: string | null;
      /** Team Id */
      team_id?: string | null;
      /** Max Parallel Requests */
      max_parallel_requests?: number | null;
      /**
       * Metadata
       * @default {}
       */
      metadata?: Record<string, never> | null;
      /** Tpm Limit */
      tpm_limit?: number | null;
      /** Rpm Limit */
      rpm_limit?: number | null;
      /** Budget Duration */
      budget_duration?: string | null;
      /**
       * Allowed Cache Controls
       * @default []
       */
      allowed_cache_controls?: unknown[] | null;
      /** Soft Budget */
      soft_budget?: number | null;
      /** Key Alias */
      key_alias?: string | null;
      /** Duration */
      duration?: string | null;
      /**
       * Aliases
       * @default {}
       */
      aliases?: Record<string, never> | null;
      /**
       * Config
       * @default {}
       */
      config?: Record<string, never> | null;
      /**
       * Permissions
       * @default {}
       */
      permissions?: Record<string, never> | null;
      /**
       * Model Max Budget
       * @default {}
       */
      model_max_budget?: Record<string, never> | null;
      /** Send Invite Email */
      send_invite_email?: boolean | null;
      /** Key */
      key: string;
      /** Key Name */
      key_name?: string | null;
      /** Expires */
      expires: string | null;
      /** Token Id */
      token_id?: string | null;
    };
    /** GlobalEndUsersSpend */
    GlobalEndUsersSpend: {
      /** Api Key */
      api_key?: string | null;
      /** Starttime */
      startTime?: string | null;
      /** Endtime */
      endTime?: string | null;
    };
    /** HTTPValidationError */
    HTTPValidationError: {
      /** Detail */
      detail?: components['schemas']['ValidationError'][];
    };
    /** InvitationDelete */
    InvitationDelete: {
      /** Invitation Id */
      invitation_id: string;
    };
    /** InvitationModel */
    InvitationModel: {
      /** Id */
      id: string;
      /** User Id */
      user_id: string;
      /** Is Accepted */
      is_accepted: boolean;
      /** Accepted At */
      accepted_at: string | null;
      /**
       * Expires At
       * Format: date-time
       */
      expires_at: string;
      /**
       * Created At
       * Format: date-time
       */
      created_at: string;
      /** Created By */
      created_by: string;
      /**
       * Updated At
       * Format: date-time
       */
      updated_at: string;
      /** Updated By */
      updated_by: string;
    };
    /** InvitationNew */
    InvitationNew: {
      /** User Id */
      user_id: string;
    };
    /** InvitationUpdate */
    InvitationUpdate: {
      /** Invitation Id */
      invitation_id: string;
      /** Is Accepted */
      is_accepted: boolean;
    };
    /**
     * KeyManagementSystem
     * @enum {string}
     */
    KeyManagementSystem: 'google_kms' | 'azure_key_vault' | 'aws_secret_manager' | 'local';
    /** KeyRequest */
    KeyRequest: {
      /** Keys */
      keys: string[];
    };
    /**
     * LiteLLM_BudgetTable
     * @description Represents user-controllable params for a LiteLLM_BudgetTable record
     */
    LiteLLM_BudgetTable: {
      /** Soft Budget */
      soft_budget?: number | null;
      /** Max Budget */
      max_budget?: number | null;
      /** Max Parallel Requests */
      max_parallel_requests?: number | null;
      /** Tpm Limit */
      tpm_limit?: number | null;
      /** Rpm Limit */
      rpm_limit?: number | null;
      /** Model Max Budget */
      model_max_budget?: Record<string, never> | null;
      /** Budget Duration */
      budget_duration?: string | null;
    };
    /** LiteLLM_EndUserTable */
    LiteLLM_EndUserTable: {
      /** User Id */
      user_id: string;
      /** Blocked */
      blocked: boolean;
      /** Alias */
      alias?: string | null;
      /**
       * Spend
       * @default 0
       */
      spend?: number;
      /** Allowed Model Region */
      allowed_model_region?: 'eu' | null;
      /** Default Model */
      default_model?: string | null;
      litellm_budget_table?: components['schemas']['LiteLLM_BudgetTable'] | null;
    };
    /**
     * LiteLLM_Params
     * @description LiteLLM Params with 'model' requirement - used for completions
     */
    LiteLLM_Params: {
      /** Custom Llm Provider */
      custom_llm_provider?: string | null;
      /** Tpm */
      tpm?: number | null;
      /** Rpm */
      rpm?: number | null;
      /** Api Key */
      api_key?: string | null;
      /** Api Base */
      api_base?: string | null;
      /** Api Version */
      api_version?: string | null;
      /** Timeout */
      timeout?: number | string | null;
      /** Stream Timeout */
      stream_timeout?: number | string | null;
      /** Max Retries */
      max_retries?: number | null;
      /** Organization */
      organization?: string | null;
      /** Region Name */
      region_name?: string | null;
      /** Vertex Project */
      vertex_project?: string | null;
      /** Vertex Location */
      vertex_location?: string | null;
      /** Aws Access Key Id */
      aws_access_key_id?: string | null;
      /** Aws Secret Access Key */
      aws_secret_access_key?: string | null;
      /** Aws Region Name */
      aws_region_name?: string | null;
      /** Watsonx Region Name */
      watsonx_region_name?: string | null;
      /** Input Cost Per Token */
      input_cost_per_token?: number | null;
      /** Output Cost Per Token */
      output_cost_per_token?: number | null;
      /** Input Cost Per Second */
      input_cost_per_second?: number | null;
      /** Output Cost Per Second */
      output_cost_per_second?: number | null;
      /** Model */
      model: string;
      [key: string]: unknown;
    };
    /** LiteLLM_SpendLogs */
    LiteLLM_SpendLogs: {
      /** Request Id */
      request_id: string;
      /** Api Key */
      api_key: string;
      /**
       * Model
       * @default
       */
      model?: string | null;
      /**
       * Api Base
       * @default
       */
      api_base?: string | null;
      /** Call Type */
      call_type: string;
      /**
       * Spend
       * @default 0
       */
      spend?: number | null;
      /**
       * Total Tokens
       * @default 0
       */
      total_tokens?: number | null;
      /**
       * Prompt Tokens
       * @default 0
       */
      prompt_tokens?: number | null;
      /**
       * Completion Tokens
       * @default 0
       */
      completion_tokens?: number | null;
      /** Starttime */
      startTime: string | null;
      /** Endtime */
      endTime: string | null;
      /**
       * User
       * @default
       */
      user?: string | null;
      /**
       * Metadata
       * @default {}
       */
      metadata?: Record<string, never> | null;
      /**
       * Cache Hit
       * @default False
       */
      cache_hit?: string | null;
      /** Cache Key */
      cache_key?: string | null;
      /** Request Tags */
      request_tags?: string | null;
    };
    /** LiteLLM_TeamTable */
    LiteLLM_TeamTable: {
      /** Team Alias */
      team_alias?: string | null;
      /** Team Id */
      team_id?: string | null;
      /** Organization Id */
      organization_id?: string | null;
      /**
       * Admins
       * @default []
       */
      admins?: unknown[];
      /**
       * Members
       * @default []
       */
      members?: unknown[];
      /**
       * Members With Roles
       * @default []
       */
      members_with_roles?: components['schemas']['Member'][];
      /** Metadata */
      metadata?: Record<string, never> | null;
      /** Tpm Limit */
      tpm_limit?: number | null;
      /** Rpm Limit */
      rpm_limit?: number | null;
      /** Max Budget */
      max_budget?: number | null;
      /** Budget Duration */
      budget_duration?: string | null;
      /**
       * Models
       * @default []
       */
      models?: unknown[];
      /**
       * Blocked
       * @default false
       */
      blocked?: boolean;
      /** Spend */
      spend?: number | null;
      /** Max Parallel Requests */
      max_parallel_requests?: number | null;
      /** Budget Reset At */
      budget_reset_at?: string | null;
      /** Model Id */
      model_id?: number | null;
    };
    /** Member */
    Member: {
      /**
       * Role
       * @enum {string}
       */
      role: 'admin' | 'user';
      /** User Id */
      user_id?: string | null;
      /** User Email */
      user_email?: string | null;
    };
    /** ModelInfoDelete */
    ModelInfoDelete: {
      /** Id */
      id: string;
    };
    /** ModelParams */
    ModelParams: {
      /** Model Name */
      model_name: string;
      /** Litellm Params */
      litellm_params: Record<string, never>;
      model_info: components['schemas']['litellm__proxy___types__ModelInfo'];
    };
    /**
     * NewCustomerRequest
     * @description Create a new customer, allocate a budget to them
     */
    NewCustomerRequest: {
      /** User Id */
      user_id: string;
      /** Alias */
      alias?: string | null;
      /**
       * Blocked
       * @default false
       */
      blocked?: boolean;
      /** Max Budget */
      max_budget?: number | null;
      /** Budget Id */
      budget_id?: string | null;
      /** Allowed Model Region */
      allowed_model_region?: 'eu' | null;
      /** Default Model */
      default_model?: string | null;
    };
    /** NewOrganizationRequest */
    NewOrganizationRequest: {
      /** Soft Budget */
      soft_budget?: number | null;
      /** Max Budget */
      max_budget?: number | null;
      /** Max Parallel Requests */
      max_parallel_requests?: number | null;
      /** Tpm Limit */
      tpm_limit?: number | null;
      /** Rpm Limit */
      rpm_limit?: number | null;
      /** Model Max Budget */
      model_max_budget?: Record<string, never> | null;
      /** Budget Duration */
      budget_duration?: string | null;
      /** Organization Id */
      organization_id?: string | null;
      /** Organization Alias */
      organization_alias: string;
      /**
       * Models
       * @default []
       */
      models?: unknown[];
      /** Budget Id */
      budget_id?: string | null;
    };
    /** NewOrganizationResponse */
    NewOrganizationResponse: {
      /** Organization Id */
      organization_id: string;
      /** Organization Alias */
      organization_alias?: string | null;
      /** Budget Id */
      budget_id: string;
      /** Metadata */
      metadata?: Record<string, never> | null;
      /** Models */
      models: string[];
      /** Created By */
      created_by: string;
      /** Updated By */
      updated_by: string;
      /**
       * Created At
       * Format: date-time
       */
      created_at: string;
      /**
       * Updated At
       * Format: date-time
       */
      updated_at: string;
    };
    /** NewTeamRequest */
    NewTeamRequest: {
      /** Team Alias */
      team_alias?: string | null;
      /** Team Id */
      team_id?: string | null;
      /** Organization Id */
      organization_id?: string | null;
      /**
       * Admins
       * @default []
       */
      admins?: unknown[];
      /**
       * Members
       * @default []
       */
      members?: unknown[];
      /**
       * Members With Roles
       * @default []
       */
      members_with_roles?: components['schemas']['Member'][];
      /** Metadata */
      metadata?: Record<string, never> | null;
      /** Tpm Limit */
      tpm_limit?: number | null;
      /** Rpm Limit */
      rpm_limit?: number | null;
      /** Max Budget */
      max_budget?: number | null;
      /** Budget Duration */
      budget_duration?: string | null;
      /**
       * Models
       * @default []
       */
      models?: unknown[];
      /**
       * Blocked
       * @default false
       */
      blocked?: boolean;
      /** Model Aliases */
      model_aliases?: Record<string, never> | null;
    };
    /** NewUserRequest */
    NewUserRequest: {
      /**
       * Models
       * @default []
       */
      models?: unknown[] | null;
      /**
       * Spend
       * @default 0
       */
      spend?: number | null;
      /** Max Budget */
      max_budget?: number | null;
      /** User Id */
      user_id?: string | null;
      /** Team Id */
      team_id?: string | null;
      /** Max Parallel Requests */
      max_parallel_requests?: number | null;
      /**
       * Metadata
       * @default {}
       */
      metadata?: Record<string, never> | null;
      /** Tpm Limit */
      tpm_limit?: number | null;
      /** Rpm Limit */
      rpm_limit?: number | null;
      /** Budget Duration */
      budget_duration?: string | null;
      /**
       * Allowed Cache Controls
       * @default []
       */
      allowed_cache_controls?: unknown[] | null;
      /** Soft Budget */
      soft_budget?: number | null;
      /** Key Alias */
      key_alias?: string | null;
      /** Duration */
      duration?: string | null;
      /**
       * Aliases
       * @default {}
       */
      aliases?: Record<string, never> | null;
      /**
       * Config
       * @default {}
       */
      config?: Record<string, never> | null;
      /**
       * Permissions
       * @default {}
       */
      permissions?: Record<string, never> | null;
      /**
       * Model Max Budget
       * @default {}
       */
      model_max_budget?: Record<string, never> | null;
      /** Send Invite Email */
      send_invite_email?: boolean | null;
      /** User Email */
      user_email?: string | null;
      /** User Role */
      user_role?:
        | (
            | 'proxy_admin'
            | 'proxy_admin_viewer'
            | 'internal_user'
            | 'internal_user_viewer'
            | 'team'
            | 'customer'
          )
        | null;
      /** Teams */
      teams?: unknown[] | null;
      /** Organization Id */
      organization_id?: string | null;
      /**
       * Auto Create Key
       * @default true
       */
      auto_create_key?: boolean;
    };
    /** NewUserResponse */
    NewUserResponse: {
      /**
       * Models
       * @default []
       */
      models?: unknown[] | null;
      /**
       * Spend
       * @default 0
       */
      spend?: number | null;
      /** Max Budget */
      max_budget?: number | null;
      /** User Id */
      user_id?: string | null;
      /** Team Id */
      team_id?: string | null;
      /** Max Parallel Requests */
      max_parallel_requests?: number | null;
      /**
       * Metadata
       * @default {}
       */
      metadata?: Record<string, never> | null;
      /** Tpm Limit */
      tpm_limit?: number | null;
      /** Rpm Limit */
      rpm_limit?: number | null;
      /** Budget Duration */
      budget_duration?: string | null;
      /**
       * Allowed Cache Controls
       * @default []
       */
      allowed_cache_controls?: unknown[] | null;
      /** Soft Budget */
      soft_budget?: number | null;
      /** Key Alias */
      key_alias?: string | null;
      /** Duration */
      duration?: string | null;
      /**
       * Aliases
       * @default {}
       */
      aliases?: Record<string, never> | null;
      /**
       * Config
       * @default {}
       */
      config?: Record<string, never> | null;
      /**
       * Permissions
       * @default {}
       */
      permissions?: Record<string, never> | null;
      /**
       * Model Max Budget
       * @default {}
       */
      model_max_budget?: Record<string, never> | null;
      /** Send Invite Email */
      send_invite_email?: boolean | null;
      /** Key */
      key: string;
      /** Key Name */
      key_name?: string | null;
      /** Expires */
      expires: string | null;
      /** Token Id */
      token_id?: string | null;
      /** User Email */
      user_email?: string | null;
      /** User Role */
      user_role?:
        | (
            | 'proxy_admin'
            | 'proxy_admin_viewer'
            | 'internal_user'
            | 'internal_user_viewer'
            | 'team'
            | 'customer'
          )
        | null;
      /** Teams */
      teams?: unknown[] | null;
      /** Organization Id */
      organization_id?: string | null;
    };
    /** OrganizationRequest */
    OrganizationRequest: {
      /** Organizations */
      organizations: string[];
    };
    /** TeamMemberAddRequest */
    TeamMemberAddRequest: {
      /** Team Id */
      team_id: string;
      member: components['schemas']['Member'];
      /** Max Budget In Team */
      max_budget_in_team?: number | null;
    };
    /** TeamModelAddRequest */
    TeamModelAddRequest: {
      /** Team Id */
      team_id: string;
      /** Model */
      models: string[];
    };
    /** TeamModelRemoveRequest */
    TeamModelRemoveRequest: {
      /** Team Id */
      team_id: string;
      /** Model */
      models: string[];
    };
    /** TeamMemberDeleteRequest */
    TeamMemberDeleteRequest: {
      /** Team Id */
      team_id: string;
      /** User Id */
      user_id?: string | null;
      /** User Email */
      user_email?: string | null;
    };
    /** TokenCountRequest */
    TokenCountRequest: {
      /** Model */
      model: string;
      /** Prompt */
      prompt?: string | null;
      /** Messages */
      messages?: Record<string, never>[] | null;
    };
    /** TokenCountResponse */
    TokenCountResponse: {
      /** Total Tokens */
      total_tokens: number;
      /** Request Model */
      request_model: string;
      /** Model Used */
      model_used: string;
      /** Tokenizer Type */
      tokenizer_type: string;
    };
    /**
     * UpdateCustomerRequest
     * @description Update a Customer, use this to update customer budgets etc
     */
    UpdateCustomerRequest: {
      /** User Id */
      user_id: string;
      /** Alias */
      alias?: string | null;
      /**
       * Blocked
       * @default false
       */
      blocked?: boolean;
      /** Max Budget */
      max_budget?: number | null;
      /** Budget Id */
      budget_id?: string | null;
      /** Allowed Model Region */
      allowed_model_region?: 'eu' | null;
      /** Default Model */
      default_model?: string | null;
    };
    /** UpdateKeyRequest */
    UpdateKeyRequest: {
      /**
       * Models
       * @default []
       */
      models?: unknown[] | null;
      /** Spend */
      spend?: number | null;
      /** Max Budget */
      max_budget?: number | null;
      /** User Id */
      user_id?: string | null;
      /** Team Id */
      team_id?: string | null;
      /** Max Parallel Requests */
      max_parallel_requests?: number | null;
      /** Metadata */
      metadata?: Record<string, never> | null;
      /** Tpm Limit */
      tpm_limit?: number | null;
      /** Rpm Limit */
      rpm_limit?: number | null;
      /** Budget Duration */
      budget_duration?: string | null;
      /**
       * Allowed Cache Controls
       * @default []
       */
      allowed_cache_controls?: unknown[] | null;
      /** Soft Budget */
      soft_budget?: number | null;
      /** Key Alias */
      key_alias?: string | null;
      /** Duration */
      duration?: string | null;
      /**
       * Aliases
       * @default {}
       */
      aliases?: Record<string, never> | null;
      /**
       * Config
       * @default {}
       */
      config?: Record<string, never> | null;
      /**
       * Permissions
       * @default {}
       */
      permissions?: Record<string, never> | null;
      /**
       * Model Max Budget
       * @default {}
       */
      model_max_budget?: Record<string, never> | null;
      /** Send Invite Email */
      send_invite_email?: boolean | null;
      /** Key */
      key: string | null;
      /**
       * blocked
       * @default false
       */
      blocked?: boolean;
    };
    /**
     * UpdateRouterConfig
     * @description Set of params that you can modify via `router.update_settings()`.
     */
    UpdateRouterConfig: {
      /** Routing Strategy Args */
      routing_strategy_args?: Record<string, never> | null;
      /** Routing Strategy */
      routing_strategy?: string | null;
      /** Model Group Retry Policy */
      model_group_retry_policy?: Record<string, never> | null;
      /** Allowed Fails */
      allowed_fails?: number | null;
      /** Cooldown Time */
      cooldown_time?: number | null;
      /** Num Retries */
      num_retries?: number | null;
      /** Timeout */
      timeout?: number | null;
      /** Max Retries */
      max_retries?: number | null;
      /** Retry After */
      retry_after?: number | null;
      /** Fallbacks */
      fallbacks?: Record<string, never>[] | null;
      /** Context Window Fallbacks */
      context_window_fallbacks?: Record<string, never>[] | null;
    };
    /**
     * UpdateTeamRequest
     * @description UpdateTeamRequest, used by /team/update when you need to update a team
     *
     * team_id: str
     * team_alias: Optional[str] = None
     * organization_id: Optional[str] = None
     * metadata: Optional[dict] = None
     * tpm_limit: Optional[int] = None
     * rpm_limit: Optional[int] = None
     * max_budget: Optional[float] = None
     * models: Optional[list] = None
     * blocked: Optional[bool] = None
     * budget_duration: Optional[str] = None
     */
    UpdateTeamRequest: {
      /** Team Id */
      team_id: string;
      /** Team Alias */
      team_alias?: string | null;
      /** Organization Id */
      organization_id?: string | null;
      /** Metadata */
      metadata?: Record<string, never> | null;
      /** Tpm Limit */
      tpm_limit?: number | null;
      /** Rpm Limit */
      rpm_limit?: number | null;
      /** Max Budget */
      max_budget?: number | null;
      /** Models */
      models?: unknown[] | null;
      /** Blocked */
      blocked?: boolean | null;
      /** Budget Duration */
      budget_duration?: string | null;
    };
    /** UpdateUserRequest */
    UpdateUserRequest: {
      /**
       * Models
       * @default []
       */
      models?: unknown[] | null;
      /** Spend */
      spend?: number | null;
      /** Max Budget */
      max_budget?: number | null;
      /** User Id */
      user_id?: string | null;
      /** Team Id */
      team_id?: string | null;
      /** Max Parallel Requests */
      max_parallel_requests?: number | null;
      /** Metadata */
      metadata?: Record<string, never> | null;
      /** Tpm Limit */
      tpm_limit?: number | null;
      /** Rpm Limit */
      rpm_limit?: number | null;
      /** Budget Duration */
      budget_duration?: string | null;
      /**
       * Allowed Cache Controls
       * @default []
       */
      allowed_cache_controls?: unknown[] | null;
      /** Soft Budget */
      soft_budget?: number | null;
      /** Password */
      password?: string | null;
      /** User Email */
      user_email?: string | null;
      /** User Role */
      user_role?:
        | (
            | 'proxy_admin'
            | 'proxy_admin_viewer'
            | 'internal_user'
            | 'internal_user_viewer'
            | 'team'
            | 'customer'
          )
        | null;
    };
    /** ValidationError */
    ValidationError: {
      /** Location */
      loc: (string | number)[];
      /** Message */
      msg: string;
      /** Error Type */
      type: string;
    };
    /** ModelInfo */
    litellm__proxy___types__ModelInfo: {
      /** Id */
      id: string | null;
      /** Mode */
      mode: ('embedding' | 'chat' | 'completion') | null;
      /**
       * Input Cost Per Token
       * @default 0
       */
      input_cost_per_token?: number | null;
      /**
       * Output Cost Per Token
       * @default 0
       */
      output_cost_per_token?: number | null;
      /**
       * Max Tokens
       * @default 2048
       */
      max_tokens?: number | null;
      /** Base Model */
      base_model:
        | (
            | 'gpt-4-1106-preview'
            | 'gpt-4-32k'
            | 'gpt-4'
            | 'gpt-3.5-turbo-16k'
            | 'gpt-3.5-turbo'
            | 'text-embedding-ada-002'
          )
        | null;
      [key: string]: unknown;
    };
    /** ModelInfo */
    litellm__types__router__ModelInfo: {
      /** Id */
      id?: string | null;
      /**
       * Db Model
       * @default false
       */
      db_model?: boolean;
      /** Updated At */
      updated_at?: string | null;
      /** Updated By */
      updated_by?: string | null;
      /** Created At */
      created_at?: string | null;
      /** Created By */
      created_by?: string | null;
      /** Base Model */
      base_model?: string | null;
      [key: string]: unknown;
    };
    /** updateDeployment */
    updateDeployment: {
      /** Model Name */
      model_name?: string | null;
      litellm_params?: components['schemas']['updateLiteLLMParams'] | null;
      model_info?: components['schemas']['litellm__types__router__ModelInfo'] | null;
    };
    /** updateLiteLLMParams */
    updateLiteLLMParams: {
      /** Custom Llm Provider */
      custom_llm_provider?: string | null;
      /** Tpm */
      tpm?: number | null;
      /** Rpm */
      rpm?: number | null;
      /** Api Key */
      api_key?: string | null;
      /** Api Base */
      api_base?: string | null;
      /** Api Version */
      api_version?: string | null;
      /** Timeout */
      timeout?: number | string | null;
      /** Stream Timeout */
      stream_timeout?: number | string | null;
      /** Max Retries */
      max_retries?: number | null;
      /** Organization */
      organization?: string | null;
      /** Region Name */
      region_name?: string | null;
      /** Vertex Project */
      vertex_project?: string | null;
      /** Vertex Location */
      vertex_location?: string | null;
      /** Aws Access Key Id */
      aws_access_key_id?: string | null;
      /** Aws Secret Access Key */
      aws_secret_access_key?: string | null;
      /** Aws Region Name */
      aws_region_name?: string | null;
      /** Watsonx Region Name */
      watsonx_region_name?: string | null;
      /** Input Cost Per Token */
      input_cost_per_token?: number | null;
      /** Output Cost Per Token */
      output_cost_per_token?: number | null;
      /** Input Cost Per Second */
      input_cost_per_second?: number | null;
      /** Output Cost Per Second */
      output_cost_per_second?: number | null;
      /** Model */
      model?: string | null;
      [key: string]: unknown;
    };
  };
  responses: never;
  parameters: never;
  requestBodies: never;
  headers: never;
  pathItems: never;
}

export type $defs = Record<string, never>;

export type external = Record<string, never>;

export interface operations {
  /**
   * Google Login
   * @description Create Proxy API Keys using Google Workspace SSO. Requires setting PROXY_BASE_URL in .env
   * PROXY_BASE_URL should be the your deployed proxy endpoint, e.g. PROXY_BASE_URL="https://litellm-production-7002.up.railway.app/"
   * Example:
   */
  google_login_sso_key_generate_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * Auth Callback
   * @description Verify login
   */
  auth_callback_sso_callback_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /** Model List */
  model_list_models_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /** List Credentials */
  list_credentials_credentials_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /** Delete Credential */
  delete_credential_credentials_delete: {
    parameters: {
      path: {
        credential_name: string | null;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /** Create Credential */
  create_credential_credentials_post: {
    requestBody: {
      content: {
        'application/json': components['schemas']['CreateCredentialRequest'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /** Update Credential */
  update_credential_credentials_patch: {
    parameters: {
      path: {
        credential_name: string | null;
      };
    };
    requestBody: {
      content: {
        'application/json': components['schemas']['UpdateCredentialRequest'];
      };
    };
    responses: {
       /** @description Successful Response */
       200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /** Model List */
  model_list_v1_models_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /** Chat Completion */
  chat_completion_openai_deployments__model__chat_completions_post: {
    parameters: {
      path: {
        model: string | null;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /** Chat Completion */
  chat_completion_engines__model__chat_completions_post: {
    parameters: {
      path: {
        model: string | null;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /** Chat Completion */
  chat_completion_chat_completions_post: {
    parameters: {
      query?: {
        model?: string | null;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /** Chat Completion */
  chat_completion_v1_chat_completions_post: {
    parameters: {
      query?: {
        model?: string | null;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /** Completion */
  completion_openai_deployments__model__completions_post: {
    parameters: {
      path: {
        model: string | null;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /** Completion */
  completion_engines__model__completions_post: {
    parameters: {
      path: {
        model: string | null;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /** Completion */
  completion_completions_post: {
    parameters: {
      query?: {
        model?: string | null;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /** Completion */
  completion_v1_completions_post: {
    parameters: {
      query?: {
        model?: string | null;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /** Embeddings */
  embeddings_openai_deployments__model__embeddings_post: {
    parameters: {
      path: {
        model: string | null;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /** Embeddings */
  embeddings_embeddings_post: {
    parameters: {
      query?: {
        model?: string | null;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /** Embeddings */
  embeddings_v1_embeddings_post: {
    parameters: {
      query?: {
        model?: string | null;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /** Image Generation */
  image_generation_images_generations_post: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /** Image Generation */
  image_generation_v1_images_generations_post: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * Audio Speech
   * @description Same params as:
   *
   * https://platform.openai.com/docs/api-reference/audio/createSpeech
   */
  audio_speech_audio_speech_post: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * Audio Speech
   * @description Same params as:
   *
   * https://platform.openai.com/docs/api-reference/audio/createSpeech
   */
  audio_speech_v1_audio_speech_post: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * Audio Transcriptions
   * @description Same params as:
   *
   * https://platform.openai.com/docs/api-reference/audio/createTranscription?lang=curl
   */
  audio_transcriptions_audio_transcriptions_post: {
    requestBody: {
      content: {
        'multipart/form-data': components['schemas']['Body_audio_transcriptions_audio_transcriptions_post'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Audio Transcriptions
   * @description Same params as:
   *
   * https://platform.openai.com/docs/api-reference/audio/createTranscription?lang=curl
   */
  audio_transcriptions_v1_audio_transcriptions_post: {
    requestBody: {
      content: {
        'multipart/form-data': components['schemas']['Body_audio_transcriptions_v1_audio_transcriptions_post'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Get Assistants
   * @description Returns a list of assistants.
   *
   * API Reference docs - https://platform.openai.com/docs/api-reference/assistants/listAssistants
   */
  get_assistants_assistants_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * Get Assistants
   * @description Returns a list of assistants.
   *
   * API Reference docs - https://platform.openai.com/docs/api-reference/assistants/listAssistants
   */
  get_assistants_v1_assistants_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * Create Threads
   * @description Create a thread.
   *
   * API Reference - https://platform.openai.com/docs/api-reference/threads/createThread
   */
  create_threads_threads_post: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * Create Threads
   * @description Create a thread.
   *
   * API Reference - https://platform.openai.com/docs/api-reference/threads/createThread
   */
  create_threads_v1_threads_post: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * Get Thread
   * @description Retrieves a thread.
   *
   * API Reference - https://platform.openai.com/docs/api-reference/threads/getThread
   */
  get_thread_threads__thread_id__get: {
    parameters: {
      path: {
        thread_id: string;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Get Thread
   * @description Retrieves a thread.
   *
   * API Reference - https://platform.openai.com/docs/api-reference/threads/getThread
   */
  get_thread_v1_threads__thread_id__get: {
    parameters: {
      path: {
        thread_id: string;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Get Messages
   * @description Returns a list of messages for a given thread.
   *
   * API Reference - https://platform.openai.com/docs/api-reference/messages/listMessages
   */
  get_messages_threads__thread_id__messages_get: {
    parameters: {
      path: {
        thread_id: string;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Add Messages
   * @description Create a message.
   *
   * API Reference - https://platform.openai.com/docs/api-reference/messages/createMessage
   */
  add_messages_threads__thread_id__messages_post: {
    parameters: {
      path: {
        thread_id: string;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Get Messages
   * @description Returns a list of messages for a given thread.
   *
   * API Reference - https://platform.openai.com/docs/api-reference/messages/listMessages
   */
  get_messages_v1_threads__thread_id__messages_get: {
    parameters: {
      path: {
        thread_id: string;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Add Messages
   * @description Create a message.
   *
   * API Reference - https://platform.openai.com/docs/api-reference/messages/createMessage
   */
  add_messages_v1_threads__thread_id__messages_post: {
    parameters: {
      path: {
        thread_id: string;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Run Thread
   * @description Create a run.
   *
   * API Reference: https://platform.openai.com/docs/api-reference/runs/createRun
   */
  run_thread_threads__thread_id__runs_get: {
    parameters: {
      path: {
        thread_id: string;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Run Thread
   * @description Create a run.
   *
   * API Reference: https://platform.openai.com/docs/api-reference/runs/createRun
   */
  run_thread_v1_threads__thread_id__runs_get: {
    parameters: {
      path: {
        thread_id: string;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Create Batch
   * @description Create large batches of API requests for asynchronous processing.
   * This is the equivalent of POST https://api.openai.com/v1/batch
   * Supports Identical Params as: https://platform.openai.com/docs/api-reference/batch
   *
   * Example Curl
   * ```
   * curl http://localhost:4000/v1/batches         -H "Authorization: Bearer sk-1234"         -H "Content-Type: application/json"         -d '{
   *         "input_file_id": "file-abc123",
   *         "endpoint": "/v1/chat/completions",
   *         "completion_window": "24h"
   * }'
   * ```
   */
  create_batch_batches_post: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * Create Batch
   * @description Create large batches of API requests for asynchronous processing.
   * This is the equivalent of POST https://api.openai.com/v1/batch
   * Supports Identical Params as: https://platform.openai.com/docs/api-reference/batch
   *
   * Example Curl
   * ```
   * curl http://localhost:4000/v1/batches         -H "Authorization: Bearer sk-1234"         -H "Content-Type: application/json"         -d '{
   *         "input_file_id": "file-abc123",
   *         "endpoint": "/v1/chat/completions",
   *         "completion_window": "24h"
   * }'
   * ```
   */
  create_batch_v1_batches_post: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * Retrieve Batch
   * @description Retrieves a batch.
   * This is the equivalent of GET https://api.openai.com/v1/batches/{batch_id}
   * Supports Identical Params as: https://platform.openai.com/docs/api-reference/batch/retrieve
   *
   * Example Curl
   * ```
   * curl http://localhost:4000/v1/batches/batch_abc123     -H "Authorization: Bearer sk-1234"     -H "Content-Type: application/json"
   * ```
   */
  retrieve_batch_batches_batch_id__get: {
    parameters: {
      path: {
        /** @description The ID of the batch to retrieve */
        batch_id: string;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Retrieve Batch
   * @description Retrieves a batch.
   * This is the equivalent of GET https://api.openai.com/v1/batches/{batch_id}
   * Supports Identical Params as: https://platform.openai.com/docs/api-reference/batch/retrieve
   *
   * Example Curl
   * ```
   * curl http://localhost:4000/v1/batches/batch_abc123     -H "Authorization: Bearer sk-1234"     -H "Content-Type: application/json"
   * ```
   */
  retrieve_batch_v1_batches_batch_id__get: {
    parameters: {
      path: {
        /** @description The ID of the batch to retrieve */
        batch_id: string;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Create File
   * @description Upload a file that can be used across - Assistants API, Batch API
   * This is the equivalent of POST https://api.openai.com/v1/files
   *
   * Supports Identical Params as: https://platform.openai.com/docs/api-reference/files/create
   *
   * Example Curl
   * ```
   * curl https://api.openai.com/v1/files         -H "Authorization: Bearer sk-1234"         -F purpose="batch"         -F file="@mydata.jsonl"
   *
   * ```
   */
  create_file_files_post: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * Create File
   * @description Upload a file that can be used across - Assistants API, Batch API
   * This is the equivalent of POST https://api.openai.com/v1/files
   *
   * Supports Identical Params as: https://platform.openai.com/docs/api-reference/files/create
   *
   * Example Curl
   * ```
   * curl https://api.openai.com/v1/files         -H "Authorization: Bearer sk-1234"         -F purpose="batch"         -F file="@mydata.jsonl"
   *
   * ```
   */
  create_file_v1_files_post: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * Moderations
   * @description The moderations endpoint is a tool you can use to check whether content complies with an LLM Providers policies.
   *
   * Quick Start
   * ```
   * curl --location 'http://0.0.0.0:4000/moderations'     --header 'Content-Type: application/json'     --header 'Authorization: Bearer sk-1234'     --data '{"input": "Sample text goes here", "model": "text-moderation-stable"}'
   * ```
   */
  moderations_moderations_post: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * Moderations
   * @description The moderations endpoint is a tool you can use to check whether content complies with an LLM Providers policies.
   *
   * Quick Start
   * ```
   * curl --location 'http://0.0.0.0:4000/moderations'     --header 'Content-Type: application/json'     --header 'Authorization: Bearer sk-1234'     --data '{"input": "Sample text goes here", "model": "text-moderation-stable"}'
   * ```
   */
  moderations_v1_moderations_post: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /** Token Counter */
  token_counter_utils_token_counter_post: {
    requestBody: {
      content: {
        'application/json': components['schemas']['TokenCountRequest'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': components['schemas']['TokenCountResponse'];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Supported Openai Params
   * @description Returns supported openai params for a given litellm model name
   *
   * e.g. `gpt-4` vs `gpt-3.5-turbo`
   *
   * Example curl:
   * ```
   * curl -X GET --location 'http://localhost:4000/utils/supported_openai_params?model=gpt-3.5-turbo-16k'         --header 'Authorization: Bearer sk-1234'
   * ```
   */
  supported_openai_params_utils_supported_openai_params_get: {
    parameters: {
      query: {
        model: string;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Generate Key Fn
   * @description Generate an API key based on the provided data.
   *
   * Docs: https://docs.litellm.ai/docs/proxy/virtual_keys
   *
   * Parameters:
   * - duration: Optional[str] - Specify the length of time the token is valid for. You can set duration as seconds ("30s"), minutes ("30m"), hours ("30h"), days ("30d").
   * - key_alias: Optional[str] - User defined key alias
   * - team_id: Optional[str] - The team id of the key
   * - user_id: Optional[str] - The user id of the key
   * - models: Optional[list] - Model_name's a user is allowed to call. (if empty, key is allowed to call all models)
   * - aliases: Optional[dict] - Any alias mappings, on top of anything in the config.yaml model list. - https://docs.litellm.ai/docs/proxy/virtual_keys#managing-auth---upgradedowngrade-models
   * - config: Optional[dict] - any key-specific configs, overrides config in config.yaml
   * - spend: Optional[int] - Amount spent by key. Default is 0. Will be updated by proxy whenever key is used. https://docs.litellm.ai/docs/proxy/virtual_keys#managing-auth---tracking-spend
   * - send_invite_email: Optional[bool] - Whether to send an invite email to the user_id, with the generate key
   * - max_budget: Optional[float] - Specify max budget for a given key.
   * - max_parallel_requests: Optional[int] - Rate limit a user based on the number of parallel requests. Raises 429 error, if user's parallel requests > x.
   * - metadata: Optional[dict] - Metadata for key, store information for key. Example metadata = {"team": "core-infra", "app": "app2", "email": "<EMAIL>" }
   * - permissions: Optional[dict] - key-specific permissions. Currently just used for turning off pii masking (if connected). Example - {"pii": false}
   * - model_max_budget: Optional[dict] - key-specific model budget in USD. Example - {"text-davinci-002": 0.5, "gpt-3.5-turbo": 0.5}. IF null or {} then no model specific budget.
   *
   * Examples:
   *
   * 1. Allow users to turn on/off pii masking
   *
   * ```bash
   * curl --location 'http://0.0.0.0:8000/key/generate'         --header 'Authorization: Bearer sk-1234'         --header 'Content-Type: application/json'         --data '{
   *         "permissions": {"allow_pii_controls": true}
   * }'
   * ```
   *
   * Returns:
   * - key: (str) The generated api key
   * - expires: (datetime) Datetime object for when key expires.
   * - user_id: (str) Unique user id - used for tracking spend across multiple keys for same user id.
   */
  generate_key_fn_key_generate_post: {
    parameters: {
      header?: {
        Authorization?: string | null;
      };
    };
    requestBody: {
      content: {
        'application/json': components['schemas']['GenerateKeyRequest'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': components['schemas']['GenerateKeyResponse'];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Update Key Fn
   * @description Update an existing key
   */
  update_key_fn_key_update_post: {
    requestBody: {
      content: {
        'application/json': components['schemas']['UpdateKeyRequest'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Delete Key Fn
   * @description Delete a key from the key management system.
   *
   * Parameters::
   * - keys (List[str]): A list of keys or hashed keys to delete. Example {"keys": ["sk-QWrxEynunsNpV1zT48HIrw", "837e17519f44683334df5291321d97b8bf1098cd490e49e215f6fea935aa28be"]}
   *
   * Returns:
   * - deleted_keys (List[str]): A list of deleted keys. Example {"deleted_keys": ["sk-QWrxEynunsNpV1zT48HIrw", "837e17519f44683334df5291321d97b8bf1098cd490e49e215f6fea935aa28be"]}
   *
   *
   * Raises:
   *     HTTPException: If an error occurs during key deletion.
   */
  delete_key_fn_key_delete_post: {
    requestBody: {
      content: {
        'application/json': components['schemas']['KeyRequest'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Info Key Fn V2
   * @description Retrieve information about a list of keys.
   *
   * **New endpoint**. Currently admin only.
   * Parameters:
   *     keys: Optional[list] = body parameter representing the key(s) in the request
   *     user_api_key_dict: UserAPIKeyAuth = Dependency representing the user's API key
   * Returns:
   *     Dict containing the key and its associated information
   *
   * Example Curl:
   * ```
   * curl -X GET "http://0.0.0.0:8000/key/info"     -H "Authorization: Bearer sk-1234"     -d {"keys": ["sk-1", "sk-2", "sk-3"]}
   * ```
   */
  info_key_fn_v2_v2_key_info_post: {
    requestBody?: {
      content: {
        'application/json': components['schemas']['KeyRequest'] | null;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Info Key Fn
   * @description Retrieve information about a key.
   * Parameters:
   *     key: Optional[str] = Query parameter representing the key in the request
   *     user_api_key_dict: UserAPIKeyAuth = Dependency representing the user's API key
   * Returns:
   *     Dict containing the key and its associated information
   *
   * Example Curl:
   * ```
   * curl -X GET "http://0.0.0.0:8000/key/info?key=sk-02Wr4IAlN3NvPXvL5JVvDA" -H "Authorization: Bearer sk-1234"
   * ```
   *
   * Example Curl - if no key is passed, it will use the Key Passed in Authorization Header
   * ```
   * curl -X GET "http://0.0.0.0:8000/key/info" -H "Authorization: Bearer sk-02Wr4IAlN3NvPXvL5JVvDA"
   * ```
   */
  info_key_fn_key_info_get: {
    parameters: {
      query?: {
        /** @description Key in the request parameters */
        key?: string | null;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Spend Key Fn
   * @description View all keys created, ordered by spend
   *
   * Example Request:
   * ```
   * curl -X GET "http://0.0.0.0:8000/spend/keys" -H "Authorization: Bearer sk-1234"
   * ```
   */
  spend_key_fn_spend_keys_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * Spend User Fn
   * @description View all users created, ordered by spend
   *
   * Example Request:
   * ```
   * curl -X GET "http://0.0.0.0:8000/spend/users" -H "Authorization: Bearer sk-1234"
   * ```
   *
   * View User Table row for user_id
   * ```
   * curl -X GET "http://0.0.0.0:8000/spend/users?user_id=1234" -H "Authorization: Bearer sk-1234"
   * ```
   */
  spend_user_fn_spend_users_get: {
    parameters: {
      query?: {
        /** @description Get User Table row for user_id */
        user_id?: string | null;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * View Spend Tags
   * @description LiteLLM Enterprise - View Spend Per Request Tag
   *
   * Example Request:
   * ```
   * curl -X GET "http://0.0.0.0:8000/spend/tags" -H "Authorization: Bearer sk-1234"
   * ```
   *
   * Spend with Start Date and End Date
   * ```
   * curl -X GET "http://0.0.0.0:8000/spend/tags?start_date=2022-01-01&end_date=2022-02-01" -H "Authorization: Bearer sk-1234"
   * ```
   */
  view_spend_tags_spend_tags_get: {
    parameters: {
      query?: {
        /** @description Time from which to start viewing key spend */
        start_date?: string | null;
        /** @description Time till which to view key spend */
        end_date?: string | null;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': components['schemas']['LiteLLM_SpendLogs'][];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Get Global Activity
   * @description Get number of API Requests, total tokens through proxy
   *
   * {
   *     "daily_data": [
   *             const chartdata = [
   *             {
   *             date: 'Jan 22',
   *             api_requests: 10,
   *             total_tokens: 2000
   *             },
   *             {
   *             date: 'Jan 23',
   *             api_requests: 10,
   *             total_tokens: 12
   *             },
   *     ],
   *     "sum_api_requests": 20,
   *     "sum_total_tokens": 2012
   * }
   */
  get_global_activity_global_activity_get: {
    parameters: {
      query?: {
        /** @description Time from which to start viewing spend */
        start_date?: string | null;
        /** @description Time till which to view spend */
        end_date?: string | null;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': components['schemas']['LiteLLM_SpendLogs'][];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Get Global Activity Model
   * @description Get number of API Requests, total tokens through proxy - Grouped by MODEL
   *
   * [
   *     {
   *         "model": "gpt-4",
   *         "daily_data": [
   *                 const chartdata = [
   *                 {
   *                 date: 'Jan 22',
   *                 api_requests: 10,
   *                 total_tokens: 2000
   *                 },
   *                 {
   *                 date: 'Jan 23',
   *                 api_requests: 10,
   *                 total_tokens: 12
   *                 },
   *         ],
   *         "sum_api_requests": 20,
   *         "sum_total_tokens": 2012
   *
   *     },
   *     {
   *         "model": "azure/gpt-4-turbo",
   *         "daily_data": [
   *                 const chartdata = [
   *                 {
   *                 date: 'Jan 22',
   *                 api_requests: 10,
   *                 total_tokens: 2000
   *                 },
   *                 {
   *                 date: 'Jan 23',
   *                 api_requests: 10,
   *                 total_tokens: 12
   *                 },
   *         ],
   *         "sum_api_requests": 20,
   *         "sum_total_tokens": 2012
   *
   *     },
   * ]
   */
  get_global_activity_model_global_activity_model_get: {
    parameters: {
      query?: {
        /** @description Time from which to start viewing spend */
        start_date?: string | null;
        /** @description Time till which to view spend */
        end_date?: string | null;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': components['schemas']['LiteLLM_SpendLogs'][];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Get Global Activity Exceptions Per Deployment
   * @description Get number of 429 errors - Grouped by deployment
   *
   * [
   *     {
   *         "deployment": "https://azure-us-east-1.openai.azure.com/",
   *         "daily_data": [
   *                 const chartdata = [
   *                 {
   *                 date: 'Jan 22',
   *                 num_rate_limit_exceptions: 10
   *                 },
   *                 {
   *                 date: 'Jan 23',
   *                 num_rate_limit_exceptions: 12
   *                 },
   *         ],
   *         "sum_num_rate_limit_exceptions": 20,
   *
   *     },
   *     {
   *         "deployment": "https://azure-us-east-1.openai.azure.com/",
   *         "daily_data": [
   *                 const chartdata = [
   *                 {
   *                 date: 'Jan 22',
   *                 num_rate_limit_exceptions: 10,
   *                 },
   *                 {
   *                 date: 'Jan 23',
   *                 num_rate_limit_exceptions: 12
   *                 },
   *         ],
   *         "sum_num_rate_limit_exceptions": 20,
   *
   *     },
   * ]
   */
  get_global_activity_exceptions_per_deployment_global_activity_exceptions_deployment_get: {
    parameters: {
      query: {
        /** @description Filter by model group */
        model_group: string;
        /** @description Time from which to start viewing spend */
        start_date?: string | null;
        /** @description Time till which to view spend */
        end_date?: string | null;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': components['schemas']['LiteLLM_SpendLogs'][];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Get Global Activity Exceptions
   * @description Get number of API Requests, total tokens through proxy
   *
   * {
   *     "daily_data": [
   *             const chartdata = [
   *             {
   *             date: 'Jan 22',
   *             num_rate_limit_exceptions: 10,
   *             },
   *             {
   *             date: 'Jan 23',
   *             num_rate_limit_exceptions: 10,
   *             },
   *     ],
   *     "sum_api_exceptions": 20,
   * }
   */
  get_global_activity_exceptions_global_activity_exceptions_get: {
    parameters: {
      query: {
        /** @description Filter by model group */
        model_group: string;
        /** @description Time from which to start viewing spend */
        start_date?: string | null;
        /** @description Time till which to view spend */
        end_date?: string | null;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': components['schemas']['LiteLLM_SpendLogs'][];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Calculate Spend
   * @description Accepts all the params of completion_cost.
   *
   * Calculate spend **before** making call:
   *
   * Note: If you see a spend of $0.0 you need to set custom_pricing for your model: https://docs.litellm.ai/docs/proxy/custom_pricing
   *
   * ```
   * curl --location 'http://localhost:4000/spend/calculate'
   * --header 'Authorization: Bearer sk-1234'
   * --header 'Content-Type: application/json'
   * --data '{
   *     "model": "anthropic.claude-v2",
   *     "messages": [{"role": "user", "content": "Hey, how'''s it going?"}]
   * }'
   * ```
   *
   * Calculate spend **after** making call:
   *
   * ```
   * curl --location 'http://localhost:4000/spend/calculate'
   * --header 'Authorization: Bearer sk-1234'
   * --header 'Content-Type: application/json'
   * --data '{
   *     "completion_response": {
   *         "id": "chatcmpl-123",
   *         "object": "chat.completion",
   *         "created": 1677652288,
   *         "model": "gpt-3.5-turbo-0125",
   *         "system_fingerprint": "fp_44709d6fcb",
   *         "choices": [{
   *             "index": 0,
   *             "message": {
   *                 "role": "assistant",
   *                 "content": "Hello there, how may I assist you today?"
   *             },
   *             "logprobs": null,
   *             "finish_reason": "stop"
   *         }]
   *         "usage": {
   *             "prompt_tokens": 9,
   *             "completion_tokens": 12,
   *             "total_tokens": 21
   *         }
   *     }
   * }'
   * ```
   */
  calculate_spend_spend_calculate_post: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * View Spend Logs
   * @description View all spend logs, if request_id is provided, only logs for that request_id will be returned
   *
   * Example Request for all logs
   * ```
   * curl -X GET "http://0.0.0.0:8000/spend/logs" -H "Authorization: Bearer sk-1234"
   * ```
   *
   * Example Request for specific request_id
   * ```
   * curl -X GET "http://0.0.0.0:8000/spend/logs?request_id=chatcmpl-6dcb2540-d3d7-4e49-bb27-291f863f112e" -H "Authorization: Bearer sk-1234"
   * ```
   *
   * Example Request for specific api_key
   * ```
   * curl -X GET "http://0.0.0.0:8000/spend/logs?api_key=sk-Fn8Ej39NkBQmUagFEoUWPQ" -H "Authorization: Bearer sk-1234"
   * ```
   *
   * Example Request for specific user_id
   * ```
   * curl -X GET "http://0.0.0.0:8000/spend/logs?user_id=<EMAIL>" -H "Authorization: Bearer sk-1234"
   * ```
   */
  view_spend_logs_spend_logs_get: {
    parameters: {
      query?: {
        /** @description Get spend logs based on api key */
        api_key?: string | null;
        /** @description Get spend logs based on user_id */
        user_id?: string | null;
        /** @description request_id to get spend logs for specific request_id. If none passed then pass spend logs for all requests */
        request_id?: string | null;
        /** @description Time from which to start viewing key spend */
        start_date?: string | null;
        /** @description Time till which to view key spend */
        end_date?: string | null;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': components['schemas']['LiteLLM_SpendLogs'][];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Global Spend Reset
   * @description ADMIN ONLY / MASTER KEY Only Endpoint
   *
   * Globally reset spend for All API Keys and Teams, maintain LiteLLM_SpendLogs
   *
   * 1. LiteLLM_SpendLogs will maintain the logs on spend, no data gets deleted from there
   * 2. LiteLLM_VerificationTokens spend will be set = 0
   * 3. LiteLLM_TeamTable spend will be set = 0
   */
  global_spend_reset_global_spend_reset_post: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * Global Spend Logs
   * @description [BETA] This is a beta endpoint. It will change.
   *
   * Use this to get global spend (spend per day for last 30d). Admin-only endpoint
   *
   * More efficient implementation of /spend/logs, by creating a view over the spend logs table.
   */
  global_spend_logs_global_spend_logs_get: {
    parameters: {
      query?: {
        /** @description API Key to get global spend (spend per day for last 30d). Admin-only endpoint */
        api_key?: string;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Global Spend
   * @description [BETA] This is a beta endpoint. It will change.
   *
   * View total spend across all proxy keys
   */
  global_spend_global_spend_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * Global Spend Keys
   * @description [BETA] This is a beta endpoint. It will change.
   *
   * Use this to get the top 'n' keys with the highest spend, ordered by spend.
   */
  global_spend_keys_global_spend_keys_get: {
    parameters: {
      query?: {
        /** @description Number of keys to get. Will return Top 'n' keys. */
        limit?: number;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Global Spend Per Team
   * @description [BETA] This is a beta endpoint. It will change.
   *
   * Use this to get daily spend, grouped by `team_id` and `date`
   */
  global_spend_per_team_global_spend_teams_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * Global Spend End Users
   * @description [BETA] This is a beta endpoint. It will change.
   *
   * Use this to get the top 'n' keys with the highest spend, ordered by spend.
   */
  global_spend_end_users_global_spend_end_users_post: {
    requestBody?: {
      content: {
        'application/json': components['schemas']['GlobalEndUsersSpend'] | null;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Global Spend Models
   * @description [BETA] This is a beta endpoint. It will change.
   *
   * Use this to get the top 'n' keys with the highest spend, ordered by spend.
   */
  global_spend_models_global_spend_models_get: {
    parameters: {
      query?: {
        /** @description Number of models to get. Will return Top 'n' models. */
        limit?: number;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /** Global Predict Spend Logs */
  global_predict_spend_logs_global_predict_spend_logs_post: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * New User
   * @description Use this to create a new INTERNAL user with a budget.
   * Internal Users can access LiteLLM Admin UI to make keys, request access to models.
   * This creates a new user and generates a new api key for the new user. The new api key is returned.
   *
   * Returns user id, budget + new key.
   *
   * Parameters:
   * - user_id: Optional[str] - Specify a user id. If not set, a unique id will be generated.
   * - user_alias: Optional[str] - A descriptive name for you to know who this user id refers to.
   * - teams: Optional[list] - specify a list of team id's a user belongs to.
   * - organization_id: Optional[str] - specify the org a user belongs to.
   * - user_email: Optional[str] - Specify a user email.
   * - send_invite_email: Optional[bool] - Specify if an invite email should be sent.
   * - user_role: Optional[str] - Specify a user role - "admin", "app_owner", "app_user"
   * - max_budget: Optional[float] - Specify max budget for a given user.
   * - models: Optional[list] - Model_name's a user is allowed to call. (if empty, key is allowed to call all models)
   * - tpm_limit: Optional[int] - Specify tpm limit for a given user (Tokens per minute)
   * - rpm_limit: Optional[int] - Specify rpm limit for a given user (Requests per minute)
   * - auto_create_key: bool - Default=True. Flag used for returning a key as part of the /user/new response
   *
   * Returns:
   * - key: (str) The generated api key for the user
   * - expires: (datetime) Datetime object for when key expires.
   * - user_id: (str) Unique user id - used for tracking spend across multiple keys for same user id.
   * - max_budget: (float|None) Max budget for given user.
   */
  new_user_user_new_post: {
    requestBody: {
      content: {
        'application/json': components['schemas']['NewUserRequest'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': components['schemas']['NewUserResponse'];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * User Auth
   * @description Allows UI ("https://dashboard.litellm.ai/", or self-hosted - os.getenv("LITELLM_HOSTED_UI")) to request a magic link to be sent to user email, for auth to proxy.
   *
   * Only allows emails from accepted email subdomains.
   *
   * Rate limit: 1 request every 60s.
   *
   * Only works, if you enable 'allow_user_auth' in general settings:
   * e.g.:
   * ```yaml
   * general_settings:
   *     allow_user_auth: true
   * ```
   *
   * Requirements:
   * SMTP server details saved in .env:
   * - os.environ["SMTP_HOST"]
   * - os.environ["SMTP_PORT"]
   * - os.environ["SMTP_USERNAME"]
   * - os.environ["SMTP_PASSWORD"]
   * - os.environ["SMTP_SENDER_EMAIL"]
   */
  user_auth_user_auth_post: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * User Info
   * @description Use this to get user information. (user row + all user key info)
   *
   * Example request
   * ```
   * curl -X GET 'http://localhost:8000/user/info?user_id=krrish7%40berri.ai'     --header 'Authorization: Bearer sk-1234'
   * ```
   */
  user_info_user_info_get: {
    parameters: {
      query?: {
        /** @description User ID in the request parameters */
        user_id?: string | null;
        /** @description set to true to View all users. When using view_all, don't pass user_id */
        view_all?: boolean;
        /** @description Page number for pagination. Only use when view_all is true */
        page?: number | null;
        /** @description Number of items per page. Only use when view_all is true */
        page_size?: number | null;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * User Update
   * @description Example curl
   *
   * ```
   * curl --location 'http://0.0.0.0:4000/user/update'     --header 'Authorization: Bearer sk-1234'     --header 'Content-Type: application/json'     --data '{
   *     "user_id": "test-litellm-user-4",
   *     "user_role": "proxy_admin_viewer"
   * }'
   *
   * See below for all params
   * ```
   */
  user_update_user_update_post: {
    requestBody: {
      content: {
        'application/json': components['schemas']['UpdateUserRequest'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * User Request Model
   * @description Allow a user to create a request to access a model
   */
  user_request_model_user_request_model_post: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * User Get Requests
   * @description Get all "Access" requests made by proxy users, access requests are requests for accessing models
   */
  user_get_requests_user_get_requests_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * Get Users
   * @description [BETA] This could change without notice. Give feedback - https://github.com/BerriAI/litellm/issues
   *
   * Get all users who are a specific `user_role`.
   *
   * Used by the UI to populate the user lists.
   *
   * Currently - admin-only endpoint.
   */
  get_users_user_get_users_get: {
    parameters: {
      query?: {
        /** @description Either 'proxy_admin', 'proxy_viewer', 'app_owner', 'app_user' */
        role?: string;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Block User
   * @description [BETA] Reject calls with this end-user id
   *
   *     (any /chat/completion call with this user={end-user-id} param, will be rejected.)
   *
   *     ```
   *     curl -X POST "http://0.0.0.0:8000/user/block"
   *     -H "Authorization: Bearer sk-1234"
   *     -D '{
   *     "user_ids": [<user_id>, ...]
   *     }'
   *     ```
   */
  block_user_customer_block_post: {
    requestBody: {
      content: {
        'application/json': components['schemas']['BlockUsers'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Unblock User
   * @description [BETA] Unblock calls with this user id
   *
   * Example
   * ```
   * curl -X POST "http://0.0.0.0:8000/user/unblock"
   * -H "Authorization: Bearer sk-1234"
   * -D '{
   * "user_ids": [<user_id>, ...]
   * }'
   * ```
   */
  unblock_user_customer_unblock_post: {
    requestBody: {
      content: {
        'application/json': components['schemas']['BlockUsers'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * New End User
   * @description Allow creating a new Customer
   * NOTE: This used to be called `/end_user/new`, we will still be maintaining compatibility for /end_user/XXX for these endpoints
   *
   * - Allow specifying allowed regions
   * - Allow specifying default model
   *
   * Example curl:
   * ```
   * curl --location 'http://0.0.0.0:4000/customer/new'         --header 'Authorization: Bearer sk-1234'         --header 'Content-Type: application/json'         --data '{
   *         "user_id" : "ishaan-jaff-3",
   *         "allowed_region": "eu",
   *         "budget_id": "free_tier",
   *         "default_model": "azure/gpt-3.5-turbo-eu" <- all calls from this user, use this model?
   *     }'
   *
   *     # return end-user object
   * ```
   */
  new_end_user_customer_new_post: {
    requestBody: {
      content: {
        'application/json': components['schemas']['NewCustomerRequest'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /** End User Info */
  end_user_info_customer_info_get: {
    parameters: {
      query: {
        /** @description End User ID in the request parameters */
        end_user_id: string;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': components['schemas']['LiteLLM_EndUserTable'];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Update End User
   * @description Example curl
   *
   * ```
   * curl --location 'http://0.0.0.0:4000/customer/update'     --header 'Authorization: Bearer sk-1234'     --header 'Content-Type: application/json'     --data '{
   *     "user_id": "test-litellm-user-4",
   *     "budget_id": "paid_tier"
   * }'
   *
   * See below for all params
   * ```
   */
  update_end_user_customer_update_post: {
    requestBody: {
      content: {
        'application/json': components['schemas']['UpdateCustomerRequest'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Delete End User
   * @description Example curl
   *
   * ```
   * curl --location 'http://0.0.0.0:4000/customer/delete'         --header 'Authorization: Bearer sk-1234'         --header 'Content-Type: application/json'         --data '{
   *         "user_ids" :["ishaan-jaff-5"]
   * }'
   *
   * See below for all params
   * ```
   */
  delete_end_user_customer_delete_post: {
    requestBody: {
      content: {
        'application/json': components['schemas']['DeleteCustomerRequest'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * New Team
   * @description Allow users to create a new team. Apply user permissions to their team.
   *
   * [ASK FOR HELP](https://github.com/BerriAI/litellm/issues)
   *
   * Parameters:
   * - team_alias: Optional[str] - User defined team alias
   * - team_id: Optional[str] - The team id of the user. If none passed, we'll generate it.
   * - members_with_roles: List[{"role": "admin" or "user", "user_id": "<user-id>"}] - A list of users and their roles in the team. Get user_id when making a new user via `/user/new`.
   * - metadata: Optional[dict] - Metadata for team, store information for team. Example metadata = {"team": "core-infra", "app": "app2", "email": "<EMAIL>" }
   * - tpm_limit: Optional[int] - The TPM (Tokens Per Minute) limit for this team - all keys with this team_id will have at max this TPM limit
   * - rpm_limit: Optional[int] - The RPM (Requests Per Minute) limit for this team - all keys associated with this team_id will have at max this RPM limit
   * - max_budget: Optional[float] - The maximum budget allocated to the team - all keys for this team_id will have at max this max_budget
   * - models: Optional[list] - A list of models associated with the team - all keys for this team_id will have at most, these models. If empty, assumes all models are allowed.
   * - blocked: bool - Flag indicating if the team is blocked or not - will stop all calls from keys with this team_id.
   *
   * Returns:
   * - team_id: (str) Unique team id - used for tracking spend across multiple keys for same team id.
   *
   * _deprecated_params:
   * - admins: list - A list of user_id's for the admin role
   * - users: list - A list of user_id's for the user role
   *
   * Example Request:
   * ```
   * curl --location 'http://0.0.0.0:4000/team/new'
   * --header 'Authorization: Bearer sk-1234'
   * --header 'Content-Type: application/json'
   * --data '{
   *   "team_alias": "my-new-team_2",
   *   "members_with_roles": [{"role": "admin", "user_id": "user-1234"},
   *     {"role": "user", "user_id": "user-2434"}]
   * }'
   *
   * ```
   */
  new_team_team_new_post: {
    requestBody: {
      content: {
        'application/json': components['schemas']['NewTeamRequest'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': components['schemas']['LiteLLM_TeamTable'];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Update Team
   * @description Use `/team/member_add` AND `/team/member/delete` to add/remove new team members
   *
   * You can now update team budget / rate limits via /team/update
   *
   * Parameters:
   * - team_id: str - The team id of the user. Required param.
   * - team_alias: Optional[str] - User defined team alias
   * - metadata: Optional[dict] - Metadata for team, store information for team. Example metadata = {"team": "core-infra", "app": "app2", "email": "<EMAIL>" }
   * - tpm_limit: Optional[int] - The TPM (Tokens Per Minute) limit for this team - all keys with this team_id will have at max this TPM limit
   * - rpm_limit: Optional[int] - The RPM (Requests Per Minute) limit for this team - all keys associated with this team_id will have at max this RPM limit
   * - max_budget: Optional[float] - The maximum budget allocated to the team - all keys for this team_id will have at max this max_budget
   * - models: Optional[list] - A list of models associated with the team - all keys for this team_id will have at most, these models. If empty, assumes all models are allowed.
   * - blocked: bool - Flag indicating if the team is blocked or not - will stop all calls from keys with this team_id.
   *
   * Example - update team TPM Limit
   *
   * ```
   * curl --location 'http://0.0.0.0:8000/team/update'
   * --header 'Authorization: Bearer sk-1234'
   * --header 'Content-Type: application/json'
   * --data-raw '{
   *     "team_id": "litellm-test-client-id-new",
   *     "tpm_limit": 100
   * }'
   * ```
   */
  update_team_team_update_post: {
    requestBody: {
      content: {
        'application/json': components['schemas']['UpdateTeamRequest'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Team Member Add
   * @description [BETA]
   *
   * Add new members (either via user_email or user_id) to a team
   *
   * If user doesn't exist, new user row will also be added to User Table
   *
   * ```
   *
   * curl -X POST 'http://0.0.0.0:4000/team/member_add'     -H 'Authorization: Bearer sk-1234'     -H 'Content-Type: application/json'     -d '{"team_id": "45e3e396-ee08-4a61-a88e-16b3ce7e0849", "member": {"role": "user", "user_id": "<EMAIL>"}}'
   *
   * ```
   */
  team_member_add_team_member_add_post: {
    requestBody: {
      content: {
        'application/json': components['schemas']['TeamMemberAddRequest'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Team Member Delete
   * @description [BETA]
   *
   * delete members (either via user_email or user_id) from a team
   *
   * If user doesn't exist, an exception will be raised
   * ```
   * curl -X POST 'http://0.0.0.0:8000/team/update'
   * -H 'Authorization: Bearer sk-1234'
   * -H 'Content-Type: application/json'
   * -D '{
   *     "team_id": "45e3e396-ee08-4a61-a88e-16b3ce7e0849",
   *     "user_id": "<EMAIL>"
   * }'
   * ```
   */
  team_member_delete_team_member_delete_post: {
    requestBody: {
      content: {
        'application/json': components['schemas']['TeamMemberDeleteRequest'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Delete Team
   * @description delete team and associated team keys
   *
   * ```
   * curl --location 'http://0.0.0.0:8000/team/delete'
   * --header 'Authorization: Bearer sk-1234'
   * --header 'Content-Type: application/json'
   * --data-raw '{
   *     "team_ids": ["45e3e396-ee08-4a61-a88e-16b3ce7e0849"]
   * }'
   * ```
   */
  delete_team_team_delete_post: {
    requestBody: {
      content: {
        'application/json': components['schemas']['DeleteTeamRequest'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Team Info
   * @description get info on team + related keys
   *
   * ```
   * curl --location 'http://localhost:4000/team/info'     --header 'Authorization: Bearer sk-1234'     --header 'Content-Type: application/json'     --data '{
   *     "teams": ["<team-id>",..]
   * }'
   * ```
   */
  team_info_team_info_get: {
    parameters: {
      query?: {
        /** @description Team ID in the request parameters */
        team_id?: string;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': {
            team_id: string;
            team_info: LiteLLM_TeamTable;
            keys: string[];
            team_memberships: [];
          };
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Block Team
   * @description Blocks all calls from keys with this team id.
   */
  block_team_team_block_post: {
    requestBody: {
      content: {
        'application/json': components['schemas']['BlockTeamRequest'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Unblock Team
   * @description Blocks all calls from keys with this team id.
   */
  unblock_team_team_unblock_post: {
    requestBody: {
      content: {
        'application/json': components['schemas']['BlockTeamRequest'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * List Team
   * @description [Admin-only] List all available teams
   *
   * ```
   * curl --location --request GET 'http://0.0.0.0:4000/team/list'         --header 'Authorization: Bearer sk-1234'
   * ```
   */
  list_team_team_list_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * New Organization
   * @description Allow orgs to own teams
   *
   * Set org level budgets + model access.
   *
   * Only admins can create orgs.
   *
   * # Parameters
   *
   * - `organization_alias`: *str* = The name of the organization.
   * - `models`: *List* = The models the organization has access to.
   * - `budget_id`: *Optional[str]* = The id for a budget (tpm/rpm/max budget) for the organization.
   * ### IF NO BUDGET ID - CREATE ONE WITH THESE PARAMS ###
   * - `max_budget`: *Optional[float]* = Max budget for org
   * - `tpm_limit`: *Optional[int]* = Max tpm limit for org
   * - `rpm_limit`: *Optional[int]* = Max rpm limit for org
   * - `model_max_budget`: *Optional[dict]* = Max budget for a specific model
   * - `budget_duration`: *Optional[str]* = Frequency of reseting org budget
   *
   * Case 1: Create new org **without** a budget_id
   *
   * ```bash
   * curl --location 'http://0.0.0.0:4000/organization/new'
   * --header 'Authorization: Bearer sk-1234'
   * --header 'Content-Type: application/json'
   * --data '{
   *     "organization_alias": "my-secret-org",
   *     "models": ["model1", "model2"],
   *     "max_budget": 100
   * }'
   *
   *
   * ```
   *
   * Case 2: Create new org **with** a budget_id
   *
   * ```bash
   * curl --location 'http://0.0.0.0:4000/organization/new'
   * --header 'Authorization: Bearer sk-1234'
   * --header 'Content-Type: application/json'
   * --data '{
   *     "organization_alias": "my-secret-org",
   *     "models": ["model1", "model2"],
   *     "budget_id": "428eeaa8-f3ac-4e85-a8fb-7dc8d7aa8689"
   * }'
   * ```
   */
  new_organization_organization_new_post: {
    requestBody: {
      content: {
        'application/json': components['schemas']['NewOrganizationRequest'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': components['schemas']['NewOrganizationResponse'];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Update Organization
   * @description [TODO] Not Implemented yet. Let us know if you need this - https://github.com/BerriAI/litellm/issues
   */
  update_organization_organization_update_post: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * Delete Organization
   * @description [TODO] Not Implemented yet. Let us know if you need this - https://github.com/BerriAI/litellm/issues
   */
  delete_organization_organization_delete_post: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * Info Organization
   * @description Get the org specific information
   */
  info_organization_organization_info_post: {
    requestBody: {
      content: {
        'application/json': components['schemas']['OrganizationRequest'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * New Budget
   * @description Create a new budget object. Can apply this to teams, orgs, end-users, keys.
   */
  new_budget_budget_new_post: {
    requestBody: {
      content: {
        'application/json': components['schemas']['BudgetNew'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Info Budget
   * @description Get the budget id specific information
   */
  info_budget_budget_info_post: {
    requestBody: {
      content: {
        'application/json': components['schemas']['BudgetRequest'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Budget Settings
   * @description Get list of configurable params + current value for a budget item + description of each field
   *
   * Used on Admin UI.
   */
  budget_settings_budget_settings_get: {
    parameters: {
      query: {
        budget_id: string;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * List Budget
   * @description List all the created budgets in proxy db. Used on Admin UI.
   */
  list_budget_budget_list_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * Delete Budget
   * @description Delete budget
   */
  delete_budget_budget_delete_post: {
    requestBody: {
      content: {
        'application/json': components['schemas']['BudgetDeleteRequest'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Add New Model
   * @description Allows adding new models to the model list in the config.yaml
   */
  add_new_model_model_new_post: {
    requestBody: {
      content: {
        'application/json': components['schemas']['Deployment'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Update Model
   * @description Edit existing model params
   */
  update_model_model_update_post: {
    requestBody: {
      content: {
        'application/json': components['schemas']['updateDeployment'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Model Info V1
   * @description Provides more info about each model in /models, including config.yaml descriptions (except api key and api base)
   */
  model_info_v1_v1_model_info_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * Model Info V1
   * @description Provides more info about each model in /models, including config.yaml descriptions (except api key and api base)
   */
  model_info_v1_model_info_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * Model Group Info
   * @description Provides more info about each model in /models, including config.yaml descriptions (except api key and api base)
   */
  model_group_info_model_group_info_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * Delete Model
   * @description Allows deleting models in the model list in the config.yaml
   */
  delete_model_model_delete_post: {
    requestBody: {
      content: {
        'application/json': components['schemas']['ModelInfoDelete'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /** Async Queue Request */
  async_queue_request_queue_chat_completions_post: {
    parameters: {
      query?: {
        model?: string | null;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Queue Info
   * @description Help user know the status of an item in the queue
   */
  queue_info_queue_info_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown[];
        };
      };
    };
  };
  /** Retrieve Server Log */
  retrieve_server_log_ollama_logs_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * New Invitation
   * @description Allow admin to create invite links, to onboard new users to Admin UI.
   *
   * ```
   * curl -X POST 'http://localhost:4000/invitation/new'         -H 'Content-Type: application/json'         -D '{
   *         "user_id": "1234" // 👈 id of user in 'LiteLLM_UserTable'
   *     }'
   * ```
   */
  new_invitation_invitation_new_post: {
    requestBody: {
      content: {
        'application/json': components['schemas']['InvitationNew'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': components['schemas']['InvitationModel'];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Invitation Info
   * @description Allow admin to create invite links, to onboard new users to Admin UI.
   *
   * ```
   * curl -X POST 'http://localhost:4000/invitation/new'         -H 'Content-Type: application/json'         -D '{
   *         "user_id": "1234" // 👈 id of user in 'LiteLLM_UserTable'
   *     }'
   * ```
   */
  invitation_info_invitation_info_get: {
    parameters: {
      query: {
        invitation_id: string;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': components['schemas']['InvitationModel'];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Invitation Update
   * @description Update when invitation is accepted
   *
   * ```
   * curl -X POST 'http://localhost:4000/invitation/update'         -H 'Content-Type: application/json'         -D '{
   *         "invitation_id": "1234" // 👈 id of invitation in 'LiteLLM_InvitationTable'
   *         "is_accepted": True // when invitation is accepted
   *     }'
   * ```
   */
  invitation_update_invitation_update_post: {
    requestBody: {
      content: {
        'application/json': components['schemas']['InvitationUpdate'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': components['schemas']['InvitationModel'];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Invitation Delete
   * @description Delete invitation link
   *
   * ```
   * curl -X POST 'http://localhost:4000/invitation/delete'         -H 'Content-Type: application/json'         -D '{
   *         "invitation_id": "1234" // 👈 id of invitation in 'LiteLLM_InvitationTable'
   *     }'
   * ```
   */
  invitation_delete_invitation_delete_post: {
    requestBody: {
      content: {
        'application/json': components['schemas']['InvitationDelete'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': components['schemas']['InvitationModel'];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Update Config
   * @description For Admin UI - allows admin to update config via UI
   *
   * Currently supports modifying General Settings + LiteLLM settings
   */
  update_config_config_update_post: {
    requestBody: {
      content: {
        'application/json': components['schemas']['ConfigYAML'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Update Config General Settings
   * @description Update a specific field in litellm general settings
   */
  update_config_general_settings_config_field_update_post: {
    requestBody: {
      content: {
        'application/json': components['schemas']['ConfigFieldUpdate'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /** Get Config General Settings */
  get_config_general_settings_config_field_info_get: {
    parameters: {
      query: {
        field_name: string;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': components['schemas']['ConfigFieldInfo'];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Get Config List
   * @description List the available fields + current values for a given type of setting (currently just 'general_settings'user_api_key_dict: UserAPIKeyAuth = Depends(user_api_key_auth),)
   */
  get_config_list_config_list_get: {
    parameters: {
      query: {
        config_type: 'general_settings';
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': components['schemas']['ConfigList'][];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Delete Config General Settings
   * @description Delete the db value of this field in litellm general settings. Resets it to it's initial default value on litellm.
   */
  delete_config_general_settings_config_field_delete_post: {
    requestBody: {
      content: {
        'application/json': components['schemas']['ConfigFieldDelete'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Config Yaml Endpoint
   * @description This is a mock endpoint, to show what you can set in config.yaml details in the Swagger UI.
   *
   * Parameters:
   *
   * The config.yaml object has the following attributes:
   * - **model_list**: *Optional[List[ModelParams]]* - A list of supported models on the server, along with model-specific configurations. ModelParams includes "model_name" (name of the model), "litellm_params" (litellm-specific parameters for the model), and "model_info" (additional info about the model such as id, mode, cost per token, etc).
   *
   * - **litellm_settings**: *Optional[dict]*: Settings for the litellm module. You can specify multiple properties like "drop_params", "set_verbose", "api_base", "cache".
   *
   * - **general_settings**: *Optional[ConfigGeneralSettings]*: General settings for the server like "completion_model" (default model for chat completion calls), "use_azure_key_vault" (option to load keys from azure key vault), "master_key" (key required for all calls to proxy), and others.
   *
   * Please, refer to each class's description for a better understanding of the specific attributes within them.
   *
   * Note: This is a mock endpoint primarily meant for demonstration purposes, and does not actually provide or change any configurations.
   */
  config_yaml_endpoint_config_yaml_get: {
    requestBody: {
      content: {
        'application/json': components['schemas']['ConfigYAML'];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Test Endpoint
   * @description [DEPRECATED] use `/health/liveliness` instead.
   *
   * A test endpoint that pings the proxy server to check if it's healthy.
   *
   * Parameters:
   *     request (Request): The incoming request.
   *
   * Returns:
   *     dict: A dictionary containing the route of the request URL.
   */
  test_endpoint_test_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * Health Endpoint
   * @description 🚨 USE `/health/liveliness` to health check the proxy 🚨
   *
   * See more 👉 https://docs.litellm.ai/docs/proxy/health
   *
   *
   * Check the health of all the endpoints in config.yaml
   *
   * To run health checks in the background, add this to config.yaml:
   * ```
   * general_settings:
   *     # ... other settings
   *     background_health_checks: True
   * ```
   * else, the health checks will be run on models when /health is called.
   */
  health_endpoint_health_get: {
    parameters: {
      query?: {
        /** @description Specify the model name (optional) */
        model?: string | null;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          'application/json': components['schemas']['HTTPValidationError'];
        };
      };
    };
  };
  /**
   * Active Callbacks
   * @description Returns a list of active callbacks on litellm.callbacks, litellm.input_callback, litellm.failure_callback, litellm.success_callback
   */
  active_callbacks_active_callbacks_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * Health Readiness
   * @description Unprotected endpoint for checking if worker can receive requests
   */
  health_readiness_health_readiness_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * Health Liveliness
   * @description Unprotected endpoint for checking if worker is alive
   */
  health_liveliness_health_liveliness_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * Cache Ping
   * @description Endpoint for checking if cache can be pinged
   */
  cache_ping_cache_ping_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * Cache Delete
   * @description Endpoint for deleting a key from the cache. All responses from litellm proxy have `x-litellm-cache-key` in the headers
   *
   * Parameters:
   * - **keys**: *Optional[List[str]]* - A list of keys to delete from the cache. Example {"keys": ["key1", "key2"]}
   *
   * ```shell
   * curl -X POST "http://0.0.0.0:4000/cache/delete"     -H "Authorization: Bearer sk-1234"     -d '{"keys": ["key1", "key2"]}'
   * ```
   */
  cache_delete_cache_delete_post: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * Cache Redis Info
   * @description Endpoint for getting /redis/info
   */
  cache_redis_info_cache_redis_info_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * Cache Flushall
   * @description A function to flush all items from the cache. (All items will be deleted from the cache with this)
   * Raises HTTPException if the cache is not initialized or if the cache type does not support flushing.
   * Returns a dictionary with the status of the operation.
   *
   * Usage:
   * ```
   * curl -X POST http://0.0.0.0:4000/cache/flushall -H "Authorization: Bearer sk-1234"
   * ```
   */
  cache_flushall_cache_flushall_post: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /** Home */
  home__get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * Get Routes
   * @description Get a list of available routes in the FastAPI application.
   */
  get_routes_routes_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
  /**
   * Token Generate
   * @description Test endpoint. Admin-only access. Meant for generating admin tokens with specific claims and testing if they work for creating keys, etc.
   */
  token_generate_token_generate_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          'application/json': unknown;
        };
      };
    };
  };
}
