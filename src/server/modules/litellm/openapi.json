{"components": {"schemas": {"BlockTeamRequest": {"properties": {"team_id": {"type": "string", "title": "Team Id"}}, "type": "object", "required": ["team_id"], "title": "BlockTeamRequest"}, "BlockUsers": {"properties": {"user_ids": {"items": {"type": "string"}, "type": "array", "title": "User Ids"}}, "type": "object", "required": ["user_ids"], "title": "BlockUsers"}, "Body_audio_transcriptions_audio_transcriptions_post": {"properties": {"file": {"type": "string", "format": "binary", "title": "File"}}, "type": "object", "required": ["file"], "title": "Body_audio_transcriptions_audio_transcriptions_post"}, "Body_audio_transcriptions_v1_audio_transcriptions_post": {"properties": {"file": {"type": "string", "format": "binary", "title": "File"}}, "type": "object", "required": ["file"], "title": "Body_audio_transcriptions_v1_audio_transcriptions_post"}, "BudgetDeleteRequest": {"properties": {"id": {"type": "string", "title": "Id"}}, "type": "object", "required": ["id"], "title": "BudgetDeleteRequest"}, "BudgetNew": {"properties": {"budget_id": {"type": "string", "title": "Budget Id", "description": "The unique budget id."}, "max_budget": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Max Budget", "description": "Requests will fail if this budget (in USD) is exceeded."}, "soft_budget": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Soft Budget", "description": "Requests will NOT fail if this is exceeded. Will fire alerting though."}, "max_parallel_requests": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON>l Requests", "description": "Max concurrent requests allowed for this budget id."}, "tpm_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Tpm Limit", "description": "Max tokens per minute, allowed for this budget id."}, "rpm_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Rpm Limit", "description": "Max requests per minute, allowed for this budget id."}, "budget_duration": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Budget Duration", "description": "Max duration budget should be set for (e.g. '1hr', '1d', '28d')"}}, "type": "object", "title": "BudgetNew"}, "BudgetRequest": {"properties": {"budgets": {"items": {"type": "string"}, "type": "array", "title": "Budgets"}}, "type": "object", "required": ["budgets"], "title": "BudgetRequest"}, "ConfigFieldDelete": {"properties": {"config_type": {"type": "string", "enum": ["general_settings"], "const": "general_settings", "title": "Config Type"}, "field_name": {"type": "string", "title": "Field Name"}}, "type": "object", "required": ["config_type", "field_name"], "title": "ConfigFieldDelete"}, "ConfigFieldInfo": {"properties": {"field_name": {"type": "string", "title": "Field Name"}, "field_value": {"title": "Field Value"}}, "type": "object", "required": ["field_name", "field_value"], "title": "ConfigFieldInfo"}, "ConfigFieldUpdate": {"properties": {"field_name": {"type": "string", "title": "Field Name"}, "field_value": {"title": "Field Value"}, "config_type": {"type": "string", "enum": ["general_settings"], "const": "general_settings", "title": "Config Type"}}, "type": "object", "required": ["field_name", "field_value", "config_type"], "title": "ConfigFieldUpdate"}, "ConfigGeneralSettings": {"properties": {"completion_model": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Completion Model", "description": "proxy level default model for all chat completion calls"}, "key_management_system": {"anyOf": [{"$ref": "#/components/schemas/KeyManagementSystem"}, {"type": "null"}], "description": "key manager to load keys from / decrypt keys with"}, "use_google_kms": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Use Google Kms", "description": "decrypt keys with google kms"}, "use_azure_key_vault": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Use Azure Key Vault", "description": "load keys from azure key vault"}, "master_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Master Key", "description": "require a key for all calls to proxy"}, "database_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Database Url", "description": "connect to a postgres db - needed for generating temporary keys + tracking spend / key"}, "database_connection_pool_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Database Connection Pool Limit", "description": "default connection pool for prisma client connecting to postgres db", "default": 100}, "database_connection_timeout": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Database Connection Timeout", "description": "default timeout for a connection to the database", "default": 60}, "database_type": {"anyOf": [{"type": "string", "enum": ["dynamo_db"], "const": "dynamo_db"}, {"type": "null"}], "title": "Database Type", "description": "to use dynamodb instead of postgres db"}, "database_args": {"anyOf": [{"$ref": "#/components/schemas/DynamoDBArgs"}, {"type": "null"}], "description": "custom args for instantiating dynamodb client - e.g. billing provision"}, "otel": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Otel", "description": "[BETA] OpenTelemetry support - this might change, use with caution."}, "custom_auth": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Custom Auth", "description": "override user_api_key_auth with your own auth script - https://docs.litellm.ai/docs/proxy/virtual_keys#custom-auth"}, "max_parallel_requests": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON>l Requests", "description": "maximum parallel requests for each api key"}, "global_max_parallel_requests": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Global Max Parallel Requests", "description": "global max parallel requests to allow for a proxy instance."}, "infer_model_from_keys": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Infer Model From Keys", "description": "for `/models` endpoint, infers available model based on environment keys (e.g. OPENAI_API_KEY)"}, "background_health_checks": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Background Health Checks", "description": "run health checks in background"}, "health_check_interval": {"type": "integer", "title": "Health Check Interval", "description": "background health check interval in seconds", "default": 300}, "alerting": {"anyOf": [{"items": {}, "type": "array"}, {"type": "null"}], "title": "Alerting", "description": "List of alerting integrations. Today, just slack - `alerting: ['slack']`"}, "alert_types": {"anyOf": [{"items": {"type": "string", "enum": ["llm_exceptions", "llm_too_slow", "llm_requests_hanging", "budget_alerts", "db_exceptions", "daily_reports", "spend_reports", "cooldown_deployment", "new_model_added", "outage_alerts", "region_outage_alerts"]}, "type": "array"}, {"type": "null"}], "title": "Alert Types", "description": "List of alerting types. By default it is all alerts"}, "alert_to_webhook_url": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON> To Webhook Url", "description": "Mapping of alert type to webhook url. e.g. `alert_to_webhook_url: {'budget_alerts': '*****************************************************************************'}`"}, "alerting_args": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Alerting <PERSON><PERSON>s", "description": "Controllable params for slack alerting - e.g. ttl in cache."}, "alerting_threshold": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Alerting <PERSON><PERSON><PERSON><PERSON>", "description": "sends alerts if requests hang for 5min+"}, "ui_access_mode": {"anyOf": [{"type": "string", "enum": ["admin_only", "all"]}, {"type": "null"}], "title": "Ui Access Mode", "description": "Control access to the Proxy UI", "default": "all"}, "allowed_routes": {"anyOf": [{"items": {}, "type": "array"}, {"type": "null"}], "title": "Allowed Routes", "description": "Proxy API Endpoints you want users to be able to access"}, "enable_public_model_hub": {"type": "boolean", "title": "Enable Public Model Hub", "description": "Public model hub for users to see what models they have access to, supported openai params, etc.", "default": false}}, "type": "object", "title": "ConfigGeneralSettings", "description": "Documents all the fields supported by `general_settings` in config.yaml"}, "ConfigList": {"properties": {"field_name": {"type": "string", "title": "Field Name"}, "field_type": {"type": "string", "title": "Field Type"}, "field_description": {"type": "string", "title": "Field Description"}, "field_value": {"title": "Field Value"}, "stored_in_db": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Stored In Db"}, "field_default_value": {"title": "Field Default Value"}, "premium_field": {"type": "boolean", "title": "Premium Field", "default": false}}, "type": "object", "required": ["field_name", "field_type", "field_description", "field_value", "stored_in_db", "field_default_value"], "title": "ConfigList"}, "ConfigYAML": {"properties": {"environment_variables": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Environment Variables", "description": "Object to pass in additional environment variables via POST request"}, "model_list": {"anyOf": [{"items": {"$ref": "#/components/schemas/ModelParams"}, "type": "array"}, {"type": "null"}], "title": "Model List", "description": "List of supported models on the server, with model-specific configs"}, "litellm_settings": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Litellm <PERSON>s", "description": "litellm Module settings. See __init__.py for all, example litellm.drop_params=True, litellm.set_verbose=True, litellm.api_base, litellm.cache"}, "general_settings": {"anyOf": [{"$ref": "#/components/schemas/ConfigGeneralSettings"}, {"type": "null"}]}, "router_settings": {"anyOf": [{"$ref": "#/components/schemas/UpdateRouterConfig"}, {"type": "null"}], "description": "litellm router object settings. See router.py __init__ for all, example router.num_retries=5, router.timeout=5, router.max_retries=5, router.retry_after=5"}}, "type": "object", "title": "ConfigYAML", "description": "Documents all the fields supported by the config.yaml"}, "DeleteCustomerRequest": {"properties": {"user_ids": {"items": {"type": "string"}, "type": "array", "title": "User Ids"}}, "type": "object", "required": ["user_ids"], "title": "DeleteCustomerRequest", "description": "Delete multiple Customers"}, "DeleteTeamRequest": {"properties": {"team_ids": {"items": {"type": "string"}, "type": "array", "title": "Team Ids"}}, "type": "object", "required": ["team_ids"], "title": "DeleteTeamRequest"}, "Deployment": {"properties": {"model_name": {"type": "string", "title": "Model Name"}, "litellm_params": {"$ref": "#/components/schemas/LiteLLM_Params"}, "model_info": {"$ref": "#/components/schemas/litellm__types__router__ModelInfo"}}, "additionalProperties": true, "type": "object", "required": ["model_name", "litellm_params", "model_info"], "title": "Deployment"}, "DynamoDBArgs": {"properties": {"billing_mode": {"type": "string", "enum": ["PROVISIONED_THROUGHPUT", "PAY_PER_REQUEST"], "title": "Billing Mode"}, "read_capacity_units": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Read Capacity Units"}, "write_capacity_units": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Write Capacity Units"}, "ssl_verify": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Ssl Verify"}, "region_name": {"type": "string", "title": "Region Name"}, "user_table_name": {"type": "string", "title": "User Table Name", "default": "LiteLLM_UserTable"}, "key_table_name": {"type": "string", "title": "Key Table Name", "default": "LiteLLM_VerificationToken"}, "config_table_name": {"type": "string", "title": "Config Table Name", "default": "LiteLLM_Config"}, "spend_table_name": {"type": "string", "title": "Spend Table Name", "default": "LiteLLM_SpendLogs"}, "aws_role_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Aws Role Name"}, "aws_session_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Aws Session Name"}, "aws_web_identity_token": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Aws Web Identity Token"}, "aws_provider_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Aws Provider Id"}, "aws_policy_arns": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Aws Policy Arns"}, "aws_policy": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Aws Policy"}, "aws_duration_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Aws Duration Seconds"}, "assume_role_aws_role_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Assume Role Aws Role Name"}, "assume_role_aws_session_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Assume Role Aws Session Name"}}, "type": "object", "required": ["billing_mode", "region_name"], "title": "DynamoDBArgs"}, "GenerateKeyRequest": {"properties": {"models": {"anyOf": [{"items": {}, "type": "array"}, {"type": "null"}], "title": "Models", "default": []}, "spend": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Spend", "default": 0}, "max_budget": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Max Budget"}, "user_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Id"}, "team_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Team Id"}, "max_parallel_requests": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON>l Requests"}, "metadata": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "default": {}}, "tpm_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Tpm Limit"}, "rpm_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Rpm Limit"}, "budget_duration": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Budget Duration"}, "allowed_cache_controls": {"anyOf": [{"items": {}, "type": "array"}, {"type": "null"}], "title": "Allowed Cache Controls", "default": []}, "soft_budget": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Soft Budget"}, "key_alias": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON> <PERSON>"}, "duration": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Duration"}, "aliases": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Aliases", "default": {}}, "config": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Config", "default": {}}, "permissions": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Permissions", "default": {}}, "model_max_budget": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Model Max Budget", "default": {}}, "send_invite_email": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Send In<PERSON>te <PERSON>"}}, "type": "object", "title": "GenerateKeyRequest"}, "GenerateKeyResponse": {"properties": {"models": {"anyOf": [{"items": {}, "type": "array"}, {"type": "null"}], "title": "Models", "default": []}, "spend": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Spend", "default": 0}, "max_budget": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Max Budget"}, "user_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Id"}, "team_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Team Id"}, "max_parallel_requests": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON>l Requests"}, "metadata": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "default": {}}, "tpm_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Tpm Limit"}, "rpm_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Rpm Limit"}, "budget_duration": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Budget Duration"}, "allowed_cache_controls": {"anyOf": [{"items": {}, "type": "array"}, {"type": "null"}], "title": "Allowed Cache Controls", "default": []}, "soft_budget": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Soft Budget"}, "key_alias": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON> <PERSON>"}, "duration": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Duration"}, "aliases": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Aliases", "default": {}}, "config": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Config", "default": {}}, "permissions": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Permissions", "default": {}}, "model_max_budget": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Model Max Budget", "default": {}}, "send_invite_email": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Send In<PERSON>te <PERSON>"}, "key": {"type": "string", "title": "Key"}, "key_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Key Name"}, "expires": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expires"}, "token_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Token Id"}}, "type": "object", "required": ["key", "expires"], "title": "GenerateKeyResponse"}, "GlobalEndUsersSpend": {"properties": {"api_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Key"}, "startTime": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Starttime"}, "endTime": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Endtime"}}, "type": "object", "title": "GlobalEndUsersSpend"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "InvitationDelete": {"properties": {"invitation_id": {"type": "string", "title": "Invitation Id"}}, "type": "object", "required": ["invitation_id"], "title": "InvitationDelete"}, "InvitationModel": {"properties": {"id": {"type": "string", "title": "Id"}, "user_id": {"type": "string", "title": "User Id"}, "is_accepted": {"type": "boolean", "title": "Is Accepted"}, "accepted_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Accepted At"}, "expires_at": {"type": "string", "format": "date-time", "title": "Expires At"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "created_by": {"type": "string", "title": "Created By"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "updated_by": {"type": "string", "title": "Updated By"}}, "type": "object", "required": ["id", "user_id", "is_accepted", "accepted_at", "expires_at", "created_at", "created_by", "updated_at", "updated_by"], "title": "InvitationModel"}, "InvitationNew": {"properties": {"user_id": {"type": "string", "title": "User Id"}}, "type": "object", "required": ["user_id"], "title": "InvitationNew"}, "InvitationUpdate": {"properties": {"invitation_id": {"type": "string", "title": "Invitation Id"}, "is_accepted": {"type": "boolean", "title": "Is Accepted"}}, "type": "object", "required": ["invitation_id", "is_accepted"], "title": "InvitationUpdate"}, "KeyManagementSystem": {"type": "string", "enum": ["google_kms", "azure_key_vault", "aws_secret_manager", "local"], "title": "KeyManagementSystem"}, "KeyRequest": {"properties": {"keys": {"items": {"type": "string"}, "type": "array", "title": "Keys"}}, "type": "object", "required": ["keys"], "title": "KeyRequest"}, "LiteLLM_BudgetTable": {"properties": {"soft_budget": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Soft Budget"}, "max_budget": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Max Budget"}, "max_parallel_requests": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON>l Requests"}, "tpm_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Tpm Limit"}, "rpm_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Rpm Limit"}, "model_max_budget": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Model Max Budget"}, "budget_duration": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Budget Duration"}}, "type": "object", "title": "LiteLLM_BudgetTable", "description": "Represents user-controllable params for a LiteLLM_BudgetTable record"}, "LiteLLM_EndUserTable": {"properties": {"user_id": {"type": "string", "title": "User Id"}, "blocked": {"type": "boolean", "title": "Blocked"}, "alias": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "spend": {"type": "number", "title": "Spend", "default": 0}, "allowed_model_region": {"anyOf": [{"type": "string", "enum": ["eu"], "const": "eu"}, {"type": "null"}], "title": "Allowed Model Region"}, "default_model": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Default Model"}, "litellm_budget_table": {"anyOf": [{"$ref": "#/components/schemas/LiteLLM_BudgetTable"}, {"type": "null"}]}}, "type": "object", "required": ["user_id", "blocked"], "title": "LiteLLM_EndUserTable"}, "LiteLLM_Params": {"properties": {"custom_llm_provider": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Custom Llm Provider"}, "tpm": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Tpm"}, "rpm": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Rpm"}, "api_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Key"}, "api_base": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Base"}, "api_version": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Version"}, "timeout": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Timeout"}, "stream_timeout": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Stream Timeout"}, "max_retries": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Max Retries"}, "organization": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization"}, "region_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Region Name"}, "vertex_project": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Vertex Project"}, "vertex_location": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Vertex Location"}, "aws_access_key_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Aws Access Key Id"}, "aws_secret_access_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Aws Secret Access Key"}, "aws_region_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Aws Region Name"}, "watsonx_region_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Watsonx Region Name"}, "input_cost_per_token": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Input Cost Per Token"}, "output_cost_per_token": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Output Cost Per <PERSON>"}, "input_cost_per_second": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Input Cost Per Second"}, "output_cost_per_second": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Output Cost Per Second"}, "model": {"type": "string", "title": "Model"}}, "additionalProperties": true, "type": "object", "required": ["model"], "title": "LiteLLM_Params", "description": "LiteLLM Params with 'model' requirement - used for completions"}, "LiteLLM_SpendLogs": {"properties": {"request_id": {"type": "string", "title": "Request Id"}, "api_key": {"type": "string", "title": "Api Key"}, "model": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model", "default": ""}, "api_base": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Base", "default": ""}, "call_type": {"type": "string", "title": "Call Type"}, "spend": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Spend", "default": 0}, "total_tokens": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Total Tokens", "default": 0}, "prompt_tokens": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Prompt Tokens", "default": 0}, "completion_tokens": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Completion Tokens", "default": 0}, "startTime": {"anyOf": [{"type": "string"}, {"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Starttime"}, "endTime": {"anyOf": [{"type": "string"}, {"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Endtime"}, "user": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User", "default": ""}, "metadata": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "default": {}}, "cache_hit": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON>", "default": "False"}, "cache_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "request_tags": {"anyOf": [{"type": "string", "contentMediaType": "application/json", "contentSchema": {}}, {"type": "null"}], "title": "Request Tags"}}, "type": "object", "required": ["request_id", "api_key", "call_type", "startTime", "endTime"], "title": "LiteLLM_SpendLogs"}, "LiteLLM_TeamTable": {"properties": {"team_alias": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Team Alias"}, "team_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Team Id"}, "organization_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization Id"}, "admins": {"items": {}, "type": "array", "title": "Admins", "default": []}, "members": {"items": {}, "type": "array", "title": "Members", "default": []}, "members_with_roles": {"items": {"$ref": "#/components/schemas/Member"}, "type": "array", "title": "Members With Roles", "default": []}, "metadata": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "tpm_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Tpm Limit"}, "rpm_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Rpm Limit"}, "max_budget": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Max Budget"}, "budget_duration": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Budget Duration"}, "models": {"items": {}, "type": "array", "title": "Models", "default": []}, "blocked": {"type": "boolean", "title": "Blocked", "default": false}, "spend": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Spend"}, "max_parallel_requests": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON>l Requests"}, "budget_reset_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Budget Reset At"}, "model_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Model Id"}}, "type": "object", "title": "LiteLLM_TeamTable"}, "Member": {"properties": {"role": {"type": "string", "enum": ["admin", "user"], "title": "Role"}, "user_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Id"}, "user_email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Email"}}, "type": "object", "required": ["role"], "title": "Member"}, "ModelInfoDelete": {"properties": {"id": {"type": "string", "title": "Id"}}, "type": "object", "required": ["id"], "title": "ModelInfoDelete"}, "ModelParams": {"properties": {"model_name": {"type": "string", "title": "Model Name"}, "litellm_params": {"type": "object", "title": "Litellm Params"}, "model_info": {"$ref": "#/components/schemas/litellm__proxy___types__ModelInfo"}}, "type": "object", "required": ["model_name", "litellm_params", "model_info"], "title": "ModelParams"}, "NewCustomerRequest": {"properties": {"user_id": {"type": "string", "title": "User Id"}, "alias": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "blocked": {"type": "boolean", "title": "Blocked", "default": false}, "max_budget": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Max Budget"}, "budget_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Budget Id"}, "allowed_model_region": {"anyOf": [{"type": "string", "enum": ["eu"], "const": "eu"}, {"type": "null"}], "title": "Allowed Model Region"}, "default_model": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Default Model"}}, "type": "object", "required": ["user_id"], "title": "NewCustomerRequest", "description": "Create a new customer, allocate a budget to them"}, "NewOrganizationRequest": {"properties": {"soft_budget": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Soft Budget"}, "max_budget": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Max Budget"}, "max_parallel_requests": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON>l Requests"}, "tpm_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Tpm Limit"}, "rpm_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Rpm Limit"}, "model_max_budget": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Model Max Budget"}, "budget_duration": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Budget Duration"}, "organization_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization Id"}, "organization_alias": {"type": "string", "title": "Organization Alias"}, "models": {"items": {}, "type": "array", "title": "Models", "default": []}, "budget_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Budget Id"}}, "type": "object", "required": ["organization_alias"], "title": "NewOrganizationRequest"}, "NewOrganizationResponse": {"properties": {"organization_id": {"type": "string", "title": "Organization Id"}, "organization_alias": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization Alias"}, "budget_id": {"type": "string", "title": "Budget Id"}, "metadata": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "models": {"items": {"type": "string"}, "type": "array", "title": "Models"}, "created_by": {"type": "string", "title": "Created By"}, "updated_by": {"type": "string", "title": "Updated By"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["organization_id", "budget_id", "models", "created_by", "updated_by", "created_at", "updated_at"], "title": "NewOrganizationResponse"}, "NewTeamRequest": {"properties": {"team_alias": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Team Alias"}, "team_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Team Id"}, "organization_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization Id"}, "admins": {"items": {}, "type": "array", "title": "Admins", "default": []}, "members": {"items": {}, "type": "array", "title": "Members", "default": []}, "members_with_roles": {"items": {"$ref": "#/components/schemas/Member"}, "type": "array", "title": "Members With Roles", "default": []}, "metadata": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "tpm_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Tpm Limit"}, "rpm_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Rpm Limit"}, "max_budget": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Max Budget"}, "budget_duration": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Budget Duration"}, "models": {"items": {}, "type": "array", "title": "Models", "default": []}, "blocked": {"type": "boolean", "title": "Blocked", "default": false}, "model_aliases": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Model Aliases"}}, "type": "object", "title": "NewTeamRequest"}, "NewUserRequest": {"properties": {"models": {"anyOf": [{"items": {}, "type": "array"}, {"type": "null"}], "title": "Models", "default": []}, "spend": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Spend", "default": 0}, "max_budget": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Max Budget"}, "user_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Id"}, "team_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Team Id"}, "max_parallel_requests": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON>l Requests"}, "metadata": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "default": {}}, "tpm_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Tpm Limit"}, "rpm_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Rpm Limit"}, "budget_duration": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Budget Duration"}, "allowed_cache_controls": {"anyOf": [{"items": {}, "type": "array"}, {"type": "null"}], "title": "Allowed Cache Controls", "default": []}, "soft_budget": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Soft Budget"}, "key_alias": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON> <PERSON>"}, "duration": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Duration"}, "aliases": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Aliases", "default": {}}, "config": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Config", "default": {}}, "permissions": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Permissions", "default": {}}, "model_max_budget": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Model Max Budget", "default": {}}, "send_invite_email": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Send In<PERSON>te <PERSON>"}, "user_email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Email"}, "user_role": {"anyOf": [{"type": "string", "enum": ["proxy_admin", "proxy_admin_viewer", "internal_user", "internal_user_viewer", "team", "customer"]}, {"type": "null"}], "title": "User Role"}, "teams": {"anyOf": [{"items": {}, "type": "array"}, {"type": "null"}], "title": "Teams"}, "organization_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization Id"}, "auto_create_key": {"type": "boolean", "title": "Auto Create Key", "default": true}}, "type": "object", "title": "NewUserRequest"}, "NewUserResponse": {"properties": {"models": {"anyOf": [{"items": {}, "type": "array"}, {"type": "null"}], "title": "Models", "default": []}, "spend": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Spend", "default": 0}, "max_budget": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Max Budget"}, "user_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Id"}, "team_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Team Id"}, "max_parallel_requests": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON>l Requests"}, "metadata": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "default": {}}, "tpm_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Tpm Limit"}, "rpm_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Rpm Limit"}, "budget_duration": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Budget Duration"}, "allowed_cache_controls": {"anyOf": [{"items": {}, "type": "array"}, {"type": "null"}], "title": "Allowed Cache Controls", "default": []}, "soft_budget": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Soft Budget"}, "key_alias": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON> <PERSON>"}, "duration": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Duration"}, "aliases": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Aliases", "default": {}}, "config": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Config", "default": {}}, "permissions": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Permissions", "default": {}}, "model_max_budget": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Model Max Budget", "default": {}}, "send_invite_email": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Send In<PERSON>te <PERSON>"}, "key": {"type": "string", "title": "Key"}, "key_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Key Name"}, "expires": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expires"}, "token_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Token Id"}, "user_email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Email"}, "user_role": {"anyOf": [{"type": "string", "enum": ["proxy_admin", "proxy_admin_viewer", "internal_user", "internal_user_viewer", "team", "customer"]}, {"type": "null"}], "title": "User Role"}, "teams": {"anyOf": [{"items": {}, "type": "array"}, {"type": "null"}], "title": "Teams"}, "organization_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization Id"}}, "type": "object", "required": ["key", "expires"], "title": "NewUserResponse"}, "OrganizationRequest": {"properties": {"organizations": {"items": {"type": "string"}, "type": "array", "title": "Organizations"}}, "type": "object", "required": ["organizations"], "title": "OrganizationRequest"}, "TeamMemberAddRequest": {"properties": {"team_id": {"type": "string", "title": "Team Id"}, "member": {"$ref": "#/components/schemas/Member"}, "max_budget_in_team": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Max Budget In Team"}}, "type": "object", "required": ["team_id", "member"], "title": "TeamMemberAddRequest"}, "TeamMemberDeleteRequest": {"properties": {"team_id": {"type": "string", "title": "Team Id"}, "user_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Id"}, "user_email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Email"}}, "type": "object", "required": ["team_id"], "title": "TeamMemberDeleteRequest"}, "TokenCountRequest": {"properties": {"model": {"type": "string", "title": "Model"}, "prompt": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Prompt"}, "messages": {"anyOf": [{"items": {"type": "object"}, "type": "array"}, {"type": "null"}], "title": "Messages"}}, "type": "object", "required": ["model"], "title": "TokenCountRequest"}, "TokenCountResponse": {"properties": {"total_tokens": {"type": "integer", "title": "Total Tokens"}, "request_model": {"type": "string", "title": "Request Model"}, "model_used": {"type": "string", "title": "Model Used"}, "tokenizer_type": {"type": "string", "title": "Tokenizer Type"}}, "type": "object", "required": ["total_tokens", "request_model", "model_used", "tokenizer_type"], "title": "TokenCountResponse"}, "UpdateCustomerRequest": {"properties": {"user_id": {"type": "string", "title": "User Id"}, "alias": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "blocked": {"type": "boolean", "title": "Blocked", "default": false}, "max_budget": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Max Budget"}, "budget_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Budget Id"}, "allowed_model_region": {"anyOf": [{"type": "string", "enum": ["eu"], "const": "eu"}, {"type": "null"}], "title": "Allowed Model Region"}, "default_model": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Default Model"}}, "type": "object", "required": ["user_id"], "title": "UpdateCustomerRequest", "description": "Update a Customer, use this to update customer budgets etc"}, "UpdateKeyRequest": {"properties": {"models": {"anyOf": [{"items": {}, "type": "array"}, {"type": "null"}], "title": "Models", "default": []}, "spend": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Spend"}, "max_budget": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Max Budget"}, "user_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Id"}, "team_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Team Id"}, "max_parallel_requests": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON>l Requests"}, "metadata": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "tpm_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Tpm Limit"}, "rpm_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Rpm Limit"}, "budget_duration": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Budget Duration"}, "allowed_cache_controls": {"anyOf": [{"items": {}, "type": "array"}, {"type": "null"}], "title": "Allowed Cache Controls", "default": []}, "soft_budget": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Soft Budget"}, "key_alias": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON> <PERSON>"}, "duration": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Duration"}, "aliases": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Aliases", "default": {}}, "config": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Config", "default": {}}, "permissions": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Permissions", "default": {}}, "model_max_budget": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Model Max Budget", "default": {}}, "send_invite_email": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Send In<PERSON>te <PERSON>"}, "key": {"type": "string", "title": "Key"}}, "type": "object", "required": ["key"], "title": "UpdateKeyRequest"}, "UpdateRouterConfig": {"properties": {"routing_strategy_args": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Routing Strategy Args"}, "routing_strategy": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Routing Strategy"}, "model_group_retry_policy": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Model Group Retry Policy"}, "allowed_fails": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Allowed Fails"}, "cooldown_time": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Cooldown Time"}, "num_retries": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Num Retries"}, "timeout": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Timeout"}, "max_retries": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Max Retries"}, "retry_after": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Retry After"}, "fallbacks": {"anyOf": [{"items": {"type": "object"}, "type": "array"}, {"type": "null"}], "title": "Fallbacks"}, "context_window_fallbacks": {"anyOf": [{"items": {"type": "object"}, "type": "array"}, {"type": "null"}], "title": "Context Window Fallbacks"}}, "type": "object", "title": "UpdateRouterConfig", "description": "Set of params that you can modify via `router.update_settings()`."}, "UpdateTeamRequest": {"properties": {"team_id": {"type": "string", "title": "Team Id"}, "team_alias": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Team Alias"}, "organization_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization Id"}, "metadata": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "tpm_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Tpm Limit"}, "rpm_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Rpm Limit"}, "max_budget": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Max Budget"}, "models": {"anyOf": [{"items": {}, "type": "array"}, {"type": "null"}], "title": "Models"}, "blocked": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Blocked"}, "budget_duration": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Budget Duration"}}, "type": "object", "required": ["team_id"], "title": "UpdateTeamRequest", "description": "UpdateTeamRequest, used by /team/update when you need to update a team\n\nteam_id: str\nteam_alias: Optional[str] = None\norganization_id: Optional[str] = None\nmetadata: Optional[dict] = None\ntpm_limit: Optional[int] = None\nrpm_limit: Optional[int] = None\nmax_budget: Optional[float] = None\nmodels: Optional[list] = None\nblocked: Optional[bool] = None\nbudget_duration: Optional[str] = None"}, "UpdateUserRequest": {"properties": {"models": {"anyOf": [{"items": {}, "type": "array"}, {"type": "null"}], "title": "Models", "default": []}, "spend": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Spend"}, "max_budget": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Max Budget"}, "user_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Id"}, "team_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Team Id"}, "max_parallel_requests": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON>l Requests"}, "metadata": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "tpm_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Tpm Limit"}, "rpm_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Rpm Limit"}, "budget_duration": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Budget Duration"}, "allowed_cache_controls": {"anyOf": [{"items": {}, "type": "array"}, {"type": "null"}], "title": "Allowed Cache Controls", "default": []}, "soft_budget": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Soft Budget"}, "password": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Password"}, "user_email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Email"}, "user_role": {"anyOf": [{"type": "string", "enum": ["proxy_admin", "proxy_admin_viewer", "internal_user", "internal_user_viewer", "team", "customer"]}, {"type": "null"}], "title": "User Role"}}, "type": "object", "title": "UpdateUserRequest"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "litellm__proxy___types__ModelInfo": {"properties": {"id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Id"}, "mode": {"anyOf": [{"type": "string", "enum": ["embedding", "chat", "completion"]}, {"type": "null"}], "title": "Mode"}, "input_cost_per_token": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Input Cost Per Token", "default": 0}, "output_cost_per_token": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Output Cost Per <PERSON>", "default": 0}, "max_tokens": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON>", "default": 2048}, "base_model": {"anyOf": [{"type": "string", "enum": ["gpt-4-1106-preview", "gpt-4-32k", "gpt-4", "gpt-3.5-turbo-16k", "gpt-3.5-turbo", "text-embedding-ada-002"]}, {"type": "null"}], "title": "Base Model"}}, "additionalProperties": true, "type": "object", "required": ["id", "mode", "base_model"], "title": "ModelInfo"}, "litellm__types__router__ModelInfo": {"properties": {"id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Id"}, "db_model": {"type": "boolean", "title": "Db Model", "default": false}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}, "updated_by": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Updated By"}, "created_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created At"}, "created_by": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Created By"}, "base_model": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Base Model"}}, "additionalProperties": true, "type": "object", "required": ["id"], "title": "ModelInfo"}, "updateDeployment": {"properties": {"model_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model Name"}, "litellm_params": {"anyOf": [{"$ref": "#/components/schemas/updateLiteLLMParams"}, {"type": "null"}]}, "model_info": {"anyOf": [{"$ref": "#/components/schemas/litellm__types__router__ModelInfo"}, {"type": "null"}]}}, "type": "object", "title": "updateDeployment"}, "updateLiteLLMParams": {"properties": {"custom_llm_provider": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Custom Llm Provider"}, "tpm": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Tpm"}, "rpm": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Rpm"}, "api_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Key"}, "api_base": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Base"}, "api_version": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Version"}, "timeout": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Timeout"}, "stream_timeout": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Stream Timeout"}, "max_retries": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Max Retries"}, "organization": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization"}, "region_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Region Name"}, "vertex_project": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Vertex Project"}, "vertex_location": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Vertex Location"}, "aws_access_key_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Aws Access Key Id"}, "aws_secret_access_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Aws Secret Access Key"}, "aws_region_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Aws Region Name"}, "watsonx_region_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Watsonx Region Name"}, "input_cost_per_token": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Input Cost Per Token"}, "output_cost_per_token": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Output Cost Per <PERSON>"}, "input_cost_per_second": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Input Cost Per Second"}, "output_cost_per_second": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Output Cost Per Second"}, "model": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model"}}, "additionalProperties": true, "type": "object", "title": "updateLiteLLMParams"}}, "securitySchemes": {"APIKeyHeader": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Bearer token", "in": "header", "name": "Authorization"}}}, "info": {"title": "LiteLLM API", "description": "Proxy Server to call 100+ LLMs in the OpenAI format. [**Customize Swagger Docs**](https://docs.litellm.ai/docs/proxy/enterprise#swagger-docs---custom-routes--branding)\n\n👉 [```LiteLLM Admin Panel on /ui```](/ui/). <PERSON><PERSON>, Edit Keys with SSO", "version": "1.39.6"}, "openapi": "3.1.0", "paths": {"/sso/key/generate": {"get": {"tags": ["experimental"], "summary": "Google Login", "description": "Create Proxy API Keys using Google Workspace SSO. Requires setting PROXY_BASE_URL in .env\nPROXY_BASE_URL should be the your deployed proxy endpoint, e.g. PROXY_BASE_URL=\"https://litellm-production-7002.up.railway.app/\"\nExample:", "operationId": "google_login_sso_key_generate_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/sso/callback": {"get": {"tags": ["experimental"], "summary": "<PERSON><PERSON>", "description": "Verify login", "operationId": "auth_callback_sso_callback_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/models": {"get": {"tags": ["model management"], "summary": "Model List", "operationId": "model_list_models_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/v1/models": {"get": {"tags": ["model management"], "summary": "Model List", "operationId": "model_list_v1_models_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/openai/deployments/{model}/chat/completions": {"post": {"tags": ["chat/completions"], "summary": "Chat Completion", "operationId": "chat_completion_openai_deployments__model__chat_completions_post", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "model", "in": "path", "required": true, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/engines/{model}/chat/completions": {"post": {"tags": ["chat/completions"], "summary": "Chat Completion", "operationId": "chat_completion_engines__model__chat_completions_post", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "model", "in": "path", "required": true, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/chat/completions": {"post": {"tags": ["chat/completions"], "summary": "Chat Completion", "operationId": "chat_completion_chat_completions_post", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "model", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/chat/completions": {"post": {"tags": ["chat/completions"], "summary": "Chat Completion", "operationId": "chat_completion_v1_chat_completions_post", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "model", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/openai/deployments/{model}/completions": {"post": {"tags": ["completions"], "summary": "Completion", "operationId": "completion_openai_deployments__model__completions_post", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "model", "in": "path", "required": true, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/engines/{model}/completions": {"post": {"tags": ["completions"], "summary": "Completion", "operationId": "completion_engines__model__completions_post", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "model", "in": "path", "required": true, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/completions": {"post": {"tags": ["completions"], "summary": "Completion", "operationId": "completion_completions_post", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "model", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/completions": {"post": {"tags": ["completions"], "summary": "Completion", "operationId": "completion_v1_completions_post", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "model", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/openai/deployments/{model}/embeddings": {"post": {"tags": ["embeddings"], "summary": "Embeddings", "operationId": "embeddings_openai_deployments__model__embeddings_post", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "model", "in": "path", "required": true, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/embeddings": {"post": {"tags": ["embeddings"], "summary": "Embeddings", "operationId": "embeddings_embeddings_post", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "model", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/embeddings": {"post": {"tags": ["embeddings"], "summary": "Embeddings", "operationId": "embeddings_v1_embeddings_post", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "model", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/images/generations": {"post": {"tags": ["images"], "summary": "Image Generation", "operationId": "image_generation_images_generations_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/v1/images/generations": {"post": {"tags": ["images"], "summary": "Image Generation", "operationId": "image_generation_v1_images_generations_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/audio/speech": {"post": {"tags": ["audio"], "summary": "Audio Speech", "description": "Same params as:\n\nhttps://platform.openai.com/docs/api-reference/audio/createSpeech", "operationId": "audio_speech_audio_speech_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/v1/audio/speech": {"post": {"tags": ["audio"], "summary": "Audio Speech", "description": "Same params as:\n\nhttps://platform.openai.com/docs/api-reference/audio/createSpeech", "operationId": "audio_speech_v1_audio_speech_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/audio/transcriptions": {"post": {"tags": ["audio"], "summary": "Audio Transcriptions", "description": "Same params as:\n\nhttps://platform.openai.com/docs/api-reference/audio/createTranscription?lang=curl", "operationId": "audio_transcriptions_audio_transcriptions_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_audio_transcriptions_audio_transcriptions_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/v1/audio/transcriptions": {"post": {"tags": ["audio"], "summary": "Audio Transcriptions", "description": "Same params as:\n\nhttps://platform.openai.com/docs/api-reference/audio/createTranscription?lang=curl", "operationId": "audio_transcriptions_v1_audio_transcriptions_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_audio_transcriptions_v1_audio_transcriptions_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/assistants": {"get": {"tags": ["assistants"], "summary": "Get Assistants", "description": "Returns a list of assistants.\n\nAPI Reference docs - https://platform.openai.com/docs/api-reference/assistants/listAssistants", "operationId": "get_assistants_assistants_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/v1/assistants": {"get": {"tags": ["assistants"], "summary": "Get Assistants", "description": "Returns a list of assistants.\n\nAPI Reference docs - https://platform.openai.com/docs/api-reference/assistants/listAssistants", "operationId": "get_assistants_v1_assistants_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/threads": {"post": {"tags": ["assistants"], "summary": "Create Threads", "description": "Create a thread.\n\nAPI Reference - https://platform.openai.com/docs/api-reference/threads/createThread", "operationId": "create_threads_threads_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/v1/threads": {"post": {"tags": ["assistants"], "summary": "Create Threads", "description": "Create a thread.\n\nAPI Reference - https://platform.openai.com/docs/api-reference/threads/createThread", "operationId": "create_threads_v1_threads_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/threads/{thread_id}": {"get": {"tags": ["assistants"], "summary": "Get Thread", "description": "Retrieves a thread.\n\nAPI Reference - https://platform.openai.com/docs/api-reference/threads/getThread", "operationId": "get_thread_threads__thread_id__get", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "thread_id", "in": "path", "required": true, "schema": {"type": "string", "title": "<PERSON><PERSON><PERSON> Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/threads/{thread_id}": {"get": {"tags": ["assistants"], "summary": "Get Thread", "description": "Retrieves a thread.\n\nAPI Reference - https://platform.openai.com/docs/api-reference/threads/getThread", "operationId": "get_thread_v1_threads__thread_id__get", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "thread_id", "in": "path", "required": true, "schema": {"type": "string", "title": "<PERSON><PERSON><PERSON> Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/threads/{thread_id}/messages": {"post": {"tags": ["assistants"], "summary": "Add Messages", "description": "Create a message.\n\nAPI Reference - https://platform.openai.com/docs/api-reference/messages/createMessage", "operationId": "add_messages_threads__thread_id__messages_post", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "thread_id", "in": "path", "required": true, "schema": {"type": "string", "title": "<PERSON><PERSON><PERSON> Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["assistants"], "summary": "Get Messages", "description": "Returns a list of messages for a given thread.\n\nAPI Reference - https://platform.openai.com/docs/api-reference/messages/listMessages", "operationId": "get_messages_threads__thread_id__messages_get", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "thread_id", "in": "path", "required": true, "schema": {"type": "string", "title": "<PERSON><PERSON><PERSON> Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/threads/{thread_id}/messages": {"post": {"tags": ["assistants"], "summary": "Add Messages", "description": "Create a message.\n\nAPI Reference - https://platform.openai.com/docs/api-reference/messages/createMessage", "operationId": "add_messages_v1_threads__thread_id__messages_post", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "thread_id", "in": "path", "required": true, "schema": {"type": "string", "title": "<PERSON><PERSON><PERSON> Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["assistants"], "summary": "Get Messages", "description": "Returns a list of messages for a given thread.\n\nAPI Reference - https://platform.openai.com/docs/api-reference/messages/listMessages", "operationId": "get_messages_v1_threads__thread_id__messages_get", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "thread_id", "in": "path", "required": true, "schema": {"type": "string", "title": "<PERSON><PERSON><PERSON> Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/threads/{thread_id}/runs": {"get": {"tags": ["assistants"], "summary": "<PERSON> Thread", "description": "Create a run.\n\nAPI Reference: https://platform.openai.com/docs/api-reference/runs/createRun", "operationId": "run_thread_threads__thread_id__runs_get", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "thread_id", "in": "path", "required": true, "schema": {"type": "string", "title": "<PERSON><PERSON><PERSON> Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/threads/{thread_id}/runs": {"get": {"tags": ["assistants"], "summary": "<PERSON> Thread", "description": "Create a run.\n\nAPI Reference: https://platform.openai.com/docs/api-reference/runs/createRun", "operationId": "run_thread_v1_threads__thread_id__runs_get", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "thread_id", "in": "path", "required": true, "schema": {"type": "string", "title": "<PERSON><PERSON><PERSON> Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/batches": {"post": {"tags": ["batch"], "summary": "Create Batch", "description": "Create large batches of API requests for asynchronous processing.\nThis is the equivalent of POST https://api.openai.com/v1/batch\nSupports Identical Params as: https://platform.openai.com/docs/api-reference/batch\n\nExample Curl\n```\ncurl http://localhost:4000/v1/batches         -H \"Authorization: Bearer sk-1234\"         -H \"Content-Type: application/json\"         -d '{\n        \"input_file_id\": \"file-abc123\",\n        \"endpoint\": \"/v1/chat/completions\",\n        \"completion_window\": \"24h\"\n}'\n```", "operationId": "create_batch_batches_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/v1/batches": {"post": {"tags": ["batch"], "summary": "Create Batch", "description": "Create large batches of API requests for asynchronous processing.\nThis is the equivalent of POST https://api.openai.com/v1/batch\nSupports Identical Params as: https://platform.openai.com/docs/api-reference/batch\n\nExample Curl\n```\ncurl http://localhost:4000/v1/batches         -H \"Authorization: Bearer sk-1234\"         -H \"Content-Type: application/json\"         -d '{\n        \"input_file_id\": \"file-abc123\",\n        \"endpoint\": \"/v1/chat/completions\",\n        \"completion_window\": \"24h\"\n}'\n```", "operationId": "create_batch_v1_batches_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/batches{batch_id}": {"get": {"tags": ["batch"], "summary": "Retri<PERSON>", "description": "Retrieves a batch.\nThis is the equivalent of GET https://api.openai.com/v1/batches/{batch_id}\nSupports Identical Params as: https://platform.openai.com/docs/api-reference/batch/retrieve\n\nExample Curl\n```\ncurl http://localhost:4000/v1/batches/batch_abc123     -H \"Authorization: Bearer sk-1234\"     -H \"Content-Type: application/json\" \n```", "operationId": "retrieve_batch_batches_batch_id__get", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "batch_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Batch ID to retrieve", "description": "The ID of the batch to retrieve"}, "description": "The ID of the batch to retrieve"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/batches{batch_id}": {"get": {"tags": ["batch"], "summary": "Retri<PERSON>", "description": "Retrieves a batch.\nThis is the equivalent of GET https://api.openai.com/v1/batches/{batch_id}\nSupports Identical Params as: https://platform.openai.com/docs/api-reference/batch/retrieve\n\nExample Curl\n```\ncurl http://localhost:4000/v1/batches/batch_abc123     -H \"Authorization: Bearer sk-1234\"     -H \"Content-Type: application/json\" \n```", "operationId": "retrieve_batch_v1_batches_batch_id__get", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "batch_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Batch ID to retrieve", "description": "The ID of the batch to retrieve"}, "description": "The ID of the batch to retrieve"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/files": {"post": {"tags": ["files"], "summary": "Create File", "description": "Upload a file that can be used across - Assistants API, Batch API \nThis is the equivalent of POST https://api.openai.com/v1/files\n\nSupports Identical Params as: https://platform.openai.com/docs/api-reference/files/create\n\nExample Curl\n```\ncurl https://api.openai.com/v1/files         -H \"Authorization: Bearer sk-1234\"         -F purpose=\"batch\"         -F file=\"@mydata.jsonl\"\n\n```", "operationId": "create_file_files_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/v1/files": {"post": {"tags": ["files"], "summary": "Create File", "description": "Upload a file that can be used across - Assistants API, Batch API \nThis is the equivalent of POST https://api.openai.com/v1/files\n\nSupports Identical Params as: https://platform.openai.com/docs/api-reference/files/create\n\nExample Curl\n```\ncurl https://api.openai.com/v1/files         -H \"Authorization: Bearer sk-1234\"         -F purpose=\"batch\"         -F file=\"@mydata.jsonl\"\n\n```", "operationId": "create_file_v1_files_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/moderations": {"post": {"tags": ["moderations"], "summary": "Moderations", "description": "The moderations endpoint is a tool you can use to check whether content complies with an LLM Providers policies.\n\nQuick Start\n```\ncurl --location 'http://0.0.0.0:4000/moderations'     --header 'Content-Type: application/json'     --header 'Authorization: Bearer sk-1234'     --data '{\"input\": \"Sample text goes here\", \"model\": \"text-moderation-stable\"}'\n```", "operationId": "moderations_moderations_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/v1/moderations": {"post": {"tags": ["moderations"], "summary": "Moderations", "description": "The moderations endpoint is a tool you can use to check whether content complies with an LLM Providers policies.\n\nQuick Start\n```\ncurl --location 'http://0.0.0.0:4000/moderations'     --header 'Content-Type: application/json'     --header 'Authorization: Bearer sk-1234'     --data '{\"input\": \"Sample text goes here\", \"model\": \"text-moderation-stable\"}'\n```", "operationId": "moderations_v1_moderations_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/utils/token_counter": {"post": {"tags": ["llm utils"], "summary": "Token Counter", "operationId": "token_counter_utils_token_counter_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenCountRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenCountResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/utils/supported_openai_params": {"get": {"tags": ["llm utils"], "summary": "Supported Openai Params", "description": "Returns supported openai params for a given litellm model name \n\ne.g. `gpt-4` vs `gpt-3.5-turbo` \n\nExample curl: \n```\ncurl -X GET --location 'http://localhost:4000/utils/supported_openai_params?model=gpt-3.5-turbo-16k'         --header 'Authorization: Bearer sk-1234'\n```", "operationId": "supported_openai_params_utils_supported_openai_params_get", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "model", "in": "query", "required": true, "schema": {"type": "string", "title": "Model"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/key/generate": {"post": {"tags": ["key management"], "summary": "Generate Key Fn", "description": "Generate an API key based on the provided data.\n\nDocs: https://docs.litellm.ai/docs/proxy/virtual_keys\n\nParameters:\n- duration: Optional[str] - Specify the length of time the token is valid for. You can set duration as seconds (\"30s\"), minutes (\"30m\"), hours (\"30h\"), days (\"30d\").\n- key_alias: Optional[str] - User defined key alias\n- team_id: Optional[str] - The team id of the key\n- user_id: Optional[str] - The user id of the key\n- models: Optional[list] - Model_name's a user is allowed to call. (if empty, key is allowed to call all models)\n- aliases: Optional[dict] - Any alias mappings, on top of anything in the config.yaml model list. - https://docs.litellm.ai/docs/proxy/virtual_keys#managing-auth---upgradedowngrade-models\n- config: Optional[dict] - any key-specific configs, overrides config in config.yaml\n- spend: Optional[int] - Amount spent by key. Default is 0. Will be updated by proxy whenever key is used. https://docs.litellm.ai/docs/proxy/virtual_keys#managing-auth---tracking-spend\n- send_invite_email: Optional[bool] - Whether to send an invite email to the user_id, with the generate key\n- max_budget: Optional[float] - Specify max budget for a given key.\n- max_parallel_requests: Optional[int] - Rate limit a user based on the number of parallel requests. Raises 429 error, if user's parallel requests > x.\n- metadata: Optional[dict] - Metadata for key, store information for key. Example metadata = {\"team\": \"core-infra\", \"app\": \"app2\", \"email\": \"<EMAIL>\" }\n- permissions: Optional[dict] - key-specific permissions. Currently just used for turning off pii masking (if connected). Example - {\"pii\": false}\n- model_max_budget: Optional[dict] - key-specific model budget in USD. Example - {\"text-davinci-002\": 0.5, \"gpt-3.5-turbo\": 0.5}. IF null or {} then no model specific budget.\n\nExamples:\n\n1. Allow users to turn on/off pii masking\n\n```bash\ncurl --location 'http://0.0.0.0:8000/key/generate'         --header 'Authorization: Bearer sk-1234'         --header 'Content-Type: application/json'         --data '{\n        \"permissions\": {\"allow_pii_controls\": true}\n}'\n```\n\nReturns:\n- key: (str) The generated api key\n- expires: (datetime) Datetime object for when key expires.\n- user_id: (str) Unique user id - used for tracking spend across multiple keys for same user id.", "operationId": "generate_key_fn_key_generate_post", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "Authorization", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateKeyRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateKeyResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/key/update": {"post": {"tags": ["key management"], "summary": "Update Key Fn", "description": "Update an existing key", "operationId": "update_key_fn_key_update_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateKeyRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/key/delete": {"post": {"tags": ["key management"], "summary": "Delete Key Fn", "description": "Delete a key from the key management system.\n\nParameters::\n- keys (List[str]): A list of keys or hashed keys to delete. Example {\"keys\": [\"sk-QWrxEynunsNpV1zT48HIrw\", \"837e17519f44683334df5291321d97b8bf1098cd490e49e215f6fea935aa28be\"]}\n\nReturns:\n- deleted_keys (List[str]): A list of deleted keys. Example {\"deleted_keys\": [\"sk-QWrxEynunsNpV1zT48HIrw\", \"837e17519f44683334df5291321d97b8bf1098cd490e49e215f6fea935aa28be\"]}\n\n\nRaises:\n    HTTPException: If an error occurs during key deletion.", "operationId": "delete_key_fn_key_delete_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/KeyRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/v2/key/info": {"post": {"tags": ["key management"], "summary": "Info Key Fn V2", "description": "Retrieve information about a list of keys.\n\n**New endpoint**. Currently admin only.\nParameters:\n    keys: Optional[list] = body parameter representing the key(s) in the request\n    user_api_key_dict: UserAPIKeyAuth = Dependency representing the user's API key\nReturns:\n    Dict containing the key and its associated information\n\nExample Curl:\n```\ncurl -X GET \"http://0.0.0.0:8000/key/info\"     -H \"Authorization: Bearer sk-1234\"     -d {\"keys\": [\"sk-1\", \"sk-2\", \"sk-3\"]}\n```", "operationId": "info_key_fn_v2_v2_key_info_post", "requestBody": {"content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/KeyRequest"}, {"type": "null"}], "title": "Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/key/info": {"get": {"tags": ["key management"], "summary": "Info Key Fn", "description": "Retrieve information about a key.\nParameters:\n    key: Optional[str] = Query parameter representing the key in the request\n    user_api_key_dict: UserAPIKeyAuth = Dependency representing the user's API key\nReturns:\n    Dict containing the key and its associated information\n\nExample Curl:\n```\ncurl -X GET \"http://0.0.0.0:8000/key/info?key=sk-02Wr4IAlN3NvPXvL5JVvDA\" -H \"Authorization: Bearer sk-1234\"\n```\n\nExample Curl - if no key is passed, it will use the Key Passed in Authorization Header\n```\ncurl -X GET \"http://0.0.0.0:8000/key/info\" -H \"Authorization: Bearer sk-02Wr4IAlN3NvPXvL5JVvDA\"\n```", "operationId": "info_key_fn_key_info_get", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "key", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Key in the request parameters", "title": "Key"}, "description": "Key in the request parameters"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/spend/keys": {"get": {"tags": ["Budget & Spend Tracking"], "summary": "Spend Key Fn", "description": "View all keys created, ordered by spend\n\nExample Request:\n```\ncurl -X GET \"http://0.0.0.0:8000/spend/keys\" -H \"Authorization: Bearer sk-1234\"\n```", "operationId": "spend_key_fn_spend_keys_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/spend/users": {"get": {"tags": ["Budget & Spend Tracking"], "summary": "Spend User Fn", "description": "View all users created, ordered by spend\n\nExample Request:\n```\ncurl -X GET \"http://0.0.0.0:8000/spend/users\" -H \"Authorization: Bearer sk-1234\"\n```\n\nView User Table row for user_id\n```\ncurl -X GET \"http://0.0.0.0:8000/spend/users?user_id=1234\" -H \"Authorization: Bearer sk-1234\"\n```", "operationId": "spend_user_fn_spend_users_get", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Get User Table row for user_id", "title": "User Id"}, "description": "Get User Table row for user_id"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/spend/tags": {"get": {"tags": ["Budget & Spend Tracking"], "summary": "View Spend Tags", "description": "LiteLLM Enterprise - View Spend Per Request Tag\n\nExample Request:\n```\ncurl -X GET \"http://0.0.0.0:8000/spend/tags\" -H \"Authorization: Bearer sk-1234\"\n```\n\nSpend with Start Date and End Date\n```\ncurl -X GET \"http://0.0.0.0:8000/spend/tags?start_date=2022-01-01&end_date=2022-02-01\" -H \"Authorization: Bearer sk-1234\"\n```", "operationId": "view_spend_tags_spend_tags_get", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Time from which to start viewing key spend", "title": "Start Date"}, "description": "Time from which to start viewing key spend"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Time till which to view key spend", "title": "End Date"}, "description": "Time till which to view key spend"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LiteLLM_SpendLogs"}, "title": "Response 200 View Spend Tags Spend Tags Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/global/activity": {"get": {"tags": ["Budget & Spend Tracking"], "summary": "Get Global Activity", "description": "Get number of API Requests, total tokens through proxy\n\n{\n    \"daily_data\": [\n            const chartdata = [\n            {\n            date: 'Jan 22',\n            api_requests: 10,\n            total_tokens: 2000\n            },\n            {\n            date: 'Jan 23',\n            api_requests: 10,\n            total_tokens: 12\n            },\n    ],\n    \"sum_api_requests\": 20,\n    \"sum_total_tokens\": 2012\n}", "operationId": "get_global_activity_global_activity_get", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Time from which to start viewing spend", "title": "Start Date"}, "description": "Time from which to start viewing spend"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Time till which to view spend", "title": "End Date"}, "description": "Time till which to view spend"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LiteLLM_SpendLogs"}, "title": "Response 200 Get Global Activity Global Activity Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/global/activity/model": {"get": {"tags": ["Budget & Spend Tracking"], "summary": "Get Global Activity Model", "description": "Get number of API Requests, total tokens through proxy - Grouped by MODEL\n\n[\n    {\n        \"model\": \"gpt-4\",\n        \"daily_data\": [\n                const chartdata = [\n                {\n                date: 'Jan 22',\n                api_requests: 10,\n                total_tokens: 2000\n                },\n                {\n                date: 'Jan 23',\n                api_requests: 10,\n                total_tokens: 12\n                },\n        ],\n        \"sum_api_requests\": 20,\n        \"sum_total_tokens\": 2012\n\n    },\n    {\n        \"model\": \"azure/gpt-4-turbo\",\n        \"daily_data\": [\n                const chartdata = [\n                {\n                date: 'Jan 22',\n                api_requests: 10,\n                total_tokens: 2000\n                },\n                {\n                date: 'Jan 23',\n                api_requests: 10,\n                total_tokens: 12\n                },\n        ],\n        \"sum_api_requests\": 20,\n        \"sum_total_tokens\": 2012\n\n    },\n]", "operationId": "get_global_activity_model_global_activity_model_get", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Time from which to start viewing spend", "title": "Start Date"}, "description": "Time from which to start viewing spend"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Time till which to view spend", "title": "End Date"}, "description": "Time till which to view spend"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LiteLLM_SpendLogs"}, "title": "Response 200 Get Global Activity Model Global Activity Model Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/global/activity/exceptions/deployment": {"get": {"tags": ["Budget & Spend Tracking"], "summary": "Get Global Activity Exceptions Per Deployment", "description": "Get number of 429 errors - Grouped by deployment\n\n[\n    {\n        \"deployment\": \"https://azure-us-east-1.openai.azure.com/\",\n        \"daily_data\": [\n                const chartdata = [\n                {\n                date: 'Jan 22',\n                num_rate_limit_exceptions: 10\n                },\n                {\n                date: 'Jan 23',\n                num_rate_limit_exceptions: 12\n                },\n        ],\n        \"sum_num_rate_limit_exceptions\": 20,\n\n    },\n    {\n        \"deployment\": \"https://azure-us-east-1.openai.azure.com/\",\n        \"daily_data\": [\n                const chartdata = [\n                {\n                date: 'Jan 22',\n                num_rate_limit_exceptions: 10,\n                },\n                {\n                date: 'Jan 23',\n                num_rate_limit_exceptions: 12\n                },\n        ],\n        \"sum_num_rate_limit_exceptions\": 20,\n\n    },\n]", "operationId": "get_global_activity_exceptions_per_deployment_global_activity_exceptions_deployment_get", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "model_group", "in": "query", "required": true, "schema": {"type": "string", "description": "Filter by model group", "title": "Model Group"}, "description": "Filter by model group"}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Time from which to start viewing spend", "title": "Start Date"}, "description": "Time from which to start viewing spend"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Time till which to view spend", "title": "End Date"}, "description": "Time till which to view spend"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LiteLLM_SpendLogs"}, "title": "Response 200 Get Global Activity Exceptions Per Deployment Global Activity Exceptions Deployment Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/global/activity/exceptions": {"get": {"tags": ["Budget & Spend Tracking"], "summary": "Get Global Activity Exceptions", "description": "Get number of API Requests, total tokens through proxy\n\n{\n    \"daily_data\": [\n            const chartdata = [\n            {\n            date: 'Jan 22',\n            num_rate_limit_exceptions: 10,\n            },\n            {\n            date: 'Jan 23',\n            num_rate_limit_exceptions: 10,\n            },\n    ],\n    \"sum_api_exceptions\": 20,\n}", "operationId": "get_global_activity_exceptions_global_activity_exceptions_get", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "model_group", "in": "query", "required": true, "schema": {"type": "string", "description": "Filter by model group", "title": "Model Group"}, "description": "Filter by model group"}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Time from which to start viewing spend", "title": "Start Date"}, "description": "Time from which to start viewing spend"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Time till which to view spend", "title": "End Date"}, "description": "Time till which to view spend"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LiteLLM_SpendLogs"}, "title": "Response 200 Get Global Activity Exceptions Global Activity Exceptions Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/spend/calculate": {"post": {"tags": ["Budget & Spend Tracking"], "summary": "Calculate Spend", "description": "Accepts all the params of completion_cost.\n\nCalculate spend **before** making call:\n\nNote: If you see a spend of $0.0 you need to set custom_pricing for your model: https://docs.litellm.ai/docs/proxy/custom_pricing\n\n```\ncurl --location 'http://localhost:4000/spend/calculate'\n--header 'Authorization: Bearer sk-1234'\n--header 'Content-Type: application/json'\n--data '{\n    \"model\": \"anthropic.claude-v2\",\n    \"messages\": [{\"role\": \"user\", \"content\": \"Hey, how'''s it going?\"}]\n}'\n```\n\nCalculate spend **after** making call:\n\n```\ncurl --location 'http://localhost:4000/spend/calculate'\n--header 'Authorization: Bearer sk-1234'\n--header 'Content-Type: application/json'\n--data '{\n    \"completion_response\": {\n        \"id\": \"chatcmpl-123\",\n        \"object\": \"chat.completion\",\n        \"created\": 1677652288,\n        \"model\": \"gpt-3.5-turbo-0125\",\n        \"system_fingerprint\": \"fp_44709d6fcb\",\n        \"choices\": [{\n            \"index\": 0,\n            \"message\": {\n                \"role\": \"assistant\",\n                \"content\": \"Hello there, how may I assist you today?\"\n            },\n            \"logprobs\": null,\n            \"finish_reason\": \"stop\"\n        }]\n        \"usage\": {\n            \"prompt_tokens\": 9,\n            \"completion_tokens\": 12,\n            \"total_tokens\": 21\n        }\n    }\n}'\n```", "operationId": "calculate_spend_spend_calculate_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}, "cost": {"description": "The calculated cost", "example": 0, "type": "float"}}}, "security": [{"APIKeyHeader": []}]}}, "/spend/logs": {"get": {"tags": ["Budget & Spend Tracking"], "summary": "View Spend Logs", "description": "View all spend logs, if request_id is provided, only logs for that request_id will be returned\n\nExample Request for all logs\n```\ncurl -X GET \"http://0.0.0.0:8000/spend/logs\" -H \"Authorization: Bearer sk-1234\"\n```\n\nExample Request for specific request_id\n```\ncurl -X GET \"http://0.0.0.0:8000/spend/logs?request_id=chatcmpl-6dcb2540-d3d7-4e49-bb27-291f863f112e\" -H \"Authorization: Bearer sk-1234\"\n```\n\nExample Request for specific api_key\n```\ncurl -X GET \"http://0.0.0.0:8000/spend/logs?api_key=sk-Fn8Ej39NkBQmUagFEoUWPQ\" -H \"Authorization: Bearer sk-1234\"\n```\n\nExample Request for specific user_id\n```\ncurl -X GET \"http://0.0.0.0:8000/spend/logs?user_id=<EMAIL>\" -H \"Authorization: Bearer sk-1234\"\n```", "operationId": "view_spend_logs_spend_logs_get", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "api_key", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Get spend logs based on api key", "title": "Api Key"}, "description": "Get spend logs based on api key"}, {"name": "user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Get spend logs based on user_id", "title": "User Id"}, "description": "Get spend logs based on user_id"}, {"name": "request_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "request_id to get spend logs for specific request_id. If none passed then pass spend logs for all requests", "title": "Request Id"}, "description": "request_id to get spend logs for specific request_id. If none passed then pass spend logs for all requests"}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Time from which to start viewing key spend", "title": "Start Date"}, "description": "Time from which to start viewing key spend"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Time till which to view key spend", "title": "End Date"}, "description": "Time till which to view key spend"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LiteLLM_SpendLogs"}, "title": "Response 200 View Spend Logs Spend Logs Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/global/spend/reset": {"post": {"tags": ["Budget & Spend Tracking"], "summary": "Global Spend Reset", "description": "ADMIN ONLY / MASTER KEY Only Endpoint\n\nGlobally reset spend for All API Keys and Teams, maintain LiteLLM_SpendLogs\n\n1. LiteLLM_SpendLogs will maintain the logs on spend, no data gets deleted from there\n2. LiteLLM_VerificationTokens spend will be set = 0\n3. LiteLLM_TeamTable spend will be set = 0", "operationId": "global_spend_reset_global_spend_reset_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/global/spend/logs": {"get": {"tags": ["Budget & Spend Tracking"], "summary": "Global Spend Logs", "description": "[BETA] This is a beta endpoint. It will change.\n\nUse this to get global spend (spend per day for last 30d). Admin-only endpoint\n\nMore efficient implementation of /spend/logs, by creating a view over the spend logs table.", "operationId": "global_spend_logs_global_spend_logs_get", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "api_key", "in": "query", "required": false, "schema": {"type": "string", "description": "API Key to get global spend (spend per day for last 30d). Admin-only endpoint", "title": "Api Key"}, "description": "API Key to get global spend (spend per day for last 30d). Admin-only endpoint"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/global/spend": {"get": {"tags": ["Budget & Spend Tracking"], "summary": "Global Spend", "description": "[BETA] This is a beta endpoint. It will change.\n\nView total spend across all proxy keys", "operationId": "global_spend_global_spend_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/global/spend/keys": {"get": {"tags": ["Budget & Spend Tracking"], "summary": "Global Spend Keys", "description": "[BETA] This is a beta endpoint. It will change.\n\nUse this to get the top 'n' keys with the highest spend, ordered by spend.", "operationId": "global_spend_keys_global_spend_keys_get", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "description": "Number of keys to get. Will return Top 'n' keys.", "title": "Limit"}, "description": "Number of keys to get. Will return Top 'n' keys."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/global/spend/teams": {"get": {"tags": ["Budget & Spend Tracking"], "summary": "Global Spend Per Team", "description": "[BETA] This is a beta endpoint. It will change.\n\nUse this to get daily spend, grouped by `team_id` and `date`", "operationId": "global_spend_per_team_global_spend_teams_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/global/spend/end_users": {"post": {"tags": ["Budget & Spend Tracking"], "summary": "Global Spend End Users", "description": "[BETA] This is a beta endpoint. It will change.\n\nUse this to get the top 'n' keys with the highest spend, ordered by spend.", "operationId": "global_spend_end_users_global_spend_end_users_post", "requestBody": {"content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/GlobalEndUsersSpend"}, {"type": "null"}], "title": "Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/global/spend/models": {"get": {"tags": ["Budget & Spend Tracking"], "summary": "Global Spend Models", "description": "[BETA] This is a beta endpoint. It will change.\n\nUse this to get the top 'n' keys with the highest spend, ordered by spend.", "operationId": "global_spend_models_global_spend_models_get", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "description": "Number of models to get. Will return Top 'n' models.", "title": "Limit"}, "description": "Number of models to get. Will return Top 'n' models."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/global/predict/spend/logs": {"post": {"tags": ["Budget & Spend Tracking"], "summary": "Global Predict Spend Logs", "operationId": "global_predict_spend_logs_global_predict_spend_logs_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/user/new": {"post": {"tags": ["Internal User management"], "summary": "New User", "description": "Use this to create a new INTERNAL user with a budget.\nInternal Users can access LiteLLM Admin UI to make keys, request access to models.\nThis creates a new user and generates a new api key for the new user. The new api key is returned.\n\nReturns user id, budget + new key.\n\nParameters:\n- user_id: Optional[str] - Specify a user id. If not set, a unique id will be generated.\n- user_alias: Optional[str] - A descriptive name for you to know who this user id refers to.\n- teams: Optional[list] - specify a list of team id's a user belongs to.\n- organization_id: Optional[str] - specify the org a user belongs to.\n- user_email: Optional[str] - Specify a user email.\n- send_invite_email: Optional[bool] - Specify if an invite email should be sent.\n- user_role: Optional[str] - Specify a user role - \"admin\", \"app_owner\", \"app_user\"\n- max_budget: Optional[float] - Specify max budget for a given user.\n- models: Optional[list] - Model_name's a user is allowed to call. (if empty, key is allowed to call all models)\n- tpm_limit: Optional[int] - Specify tpm limit for a given user (Tokens per minute)\n- rpm_limit: Optional[int] - Specify rpm limit for a given user (Requests per minute)\n- auto_create_key: bool - Default=True. Flag used for returning a key as part of the /user/new response\n\nReturns:\n- key: (str) The generated api key for the user\n- expires: (datetime) Datetime object for when key expires.\n- user_id: (str) Unique user id - used for tracking spend across multiple keys for same user id.\n- max_budget: (float|None) Max budget for given user.", "operationId": "new_user_user_new_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUserRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUserResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/user/auth": {"post": {"tags": ["Internal User management"], "summary": "User <PERSON>", "description": "Allows UI (\"https://dashboard.litellm.ai/\", or self-hosted - os.getenv(\"LITELLM_HOSTED_UI\")) to request a magic link to be sent to user email, for auth to proxy.\n\nOnly allows emails from accepted email subdomains.\n\nRate limit: 1 request every 60s.\n\nOnly works, if you enable 'allow_user_auth' in general settings:\ne.g.:\n```yaml\ngeneral_settings:\n    allow_user_auth: true\n```\n\nRequirements:\nSMTP server details saved in .env:\n- os.environ[\"SMTP_HOST\"]\n- os.environ[\"SMTP_PORT\"]\n- os.environ[\"SMTP_USERNAME\"]\n- os.environ[\"SMTP_PASSWORD\"]\n- os.environ[\"SMTP_SENDER_EMAIL\"]", "operationId": "user_auth_user_auth_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/user/info": {"get": {"tags": ["Internal User management"], "summary": "User Info", "description": "Use this to get user information. (user row + all user key info)\n\nExample request\n```\ncurl -X GET 'http://localhost:8000/user/info?user_id=krrish7%40berri.ai'     --header 'Authorization: Bearer sk-1234'\n```", "operationId": "user_info_user_info_get", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "User ID in the request parameters", "title": "User Id"}, "description": "User ID in the request parameters"}, {"name": "view_all", "in": "query", "required": false, "schema": {"type": "boolean", "description": "set to true to View all users. When using view_all, don't pass user_id", "default": false, "title": "View All"}, "description": "set to true to View all users. When using view_all, don't pass user_id"}, {"name": "page", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "Page number for pagination. Only use when view_all is true", "default": 0, "title": "Page"}, "description": "Page number for pagination. Only use when view_all is true"}, {"name": "page_size", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "Number of items per page. Only use when view_all is true", "default": 25, "title": "<PERSON>"}, "description": "Number of items per page. Only use when view_all is true"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user/update": {"post": {"tags": ["Internal User management"], "summary": "User Update", "description": "Example curl \n\n```\ncurl --location 'http://0.0.0.0:4000/user/update'     --header 'Authorization: Bearer sk-1234'     --header 'Content-Type: application/json'     --data '{\n    \"user_id\": \"test-litellm-user-4\",\n    \"user_role\": \"proxy_admin_viewer\"\n}'\n\nSee below for all params \n```", "operationId": "user_update_user_update_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/user/request_model": {"post": {"tags": ["Internal User management"], "summary": "User Request Model", "description": "Allow a user to create a request to access a model", "operationId": "user_request_model_user_request_model_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/user/get_requests": {"get": {"tags": ["Internal User management"], "summary": "User Get Requests", "description": "Get all \"Access\" requests made by proxy users, access requests are requests for accessing models", "operationId": "user_get_requests_user_get_requests_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/user/get_users": {"get": {"tags": ["Internal User management"], "summary": "Get Users", "description": "[BETA] This could change without notice. Give feedback - https://github.com/BerriAI/litellm/issues\n\nGet all users who are a specific `user_role`.\n\nUsed by the UI to populate the user lists.\n\nCurrently - admin-only endpoint.", "operationId": "get_users_user_get_users_get", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "role", "in": "query", "required": false, "schema": {"type": "string", "description": "Either 'proxy_admin', 'proxy_viewer', 'app_owner', 'app_user'", "title": "Role"}, "description": "Either 'proxy_admin', 'proxy_viewer', 'app_owner', 'app_user'"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/customer/block": {"post": {"tags": ["Customer Management"], "summary": "Block User", "description": "[BETA] Reject calls with this end-user id\n\n    (any /chat/completion call with this user={end-user-id} param, will be rejected.)\n\n    ```\n    curl -X POST \"http://0.0.0.0:8000/user/block\"\n    -H \"Authorization: Bearer sk-1234\"\n    -D '{\n    \"user_ids\": [<user_id>, ...]\n    }'\n    ```", "operationId": "block_user_customer_block_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlockUsers"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/customer/unblock": {"post": {"tags": ["Customer Management"], "summary": "Unblock User", "description": "[BETA] Unblock calls with this user id\n\nExample\n```\ncurl -X POST \"http://0.0.0.0:8000/user/unblock\"\n-H \"Authorization: Bearer sk-1234\"\n-D '{\n\"user_ids\": [<user_id>, ...]\n}'\n```", "operationId": "unblock_user_customer_unblock_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlockUsers"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/customer/new": {"post": {"tags": ["Customer Management"], "summary": "New End User", "description": "Allow creating a new Customer \nNOTE: This used to be called `/end_user/new`, we will still be maintaining compatibility for /end_user/XXX for these endpoints\n\n- Allow specifying allowed regions \n- Allow specifying default model\n\nExample curl:\n```\ncurl --location 'http://0.0.0.0:4000/customer/new'         --header 'Authorization: Bearer sk-1234'         --header 'Content-Type: application/json'         --data '{\n        \"user_id\" : \"ishaan-jaff-3\",\n        \"allowed_region\": \"eu\",\n        \"budget_id\": \"free_tier\",\n        \"default_model\": \"azure/gpt-3.5-turbo-eu\" <- all calls from this user, use this model? \n    }'\n\n    # return end-user object\n```", "operationId": "new_end_user_customer_new_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewCustomerRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/customer/info": {"get": {"tags": ["Customer Management"], "summary": "End User Info", "operationId": "end_user_info_customer_info_get", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "end_user_id", "in": "query", "required": true, "schema": {"type": "string", "description": "End User ID in the request parameters", "title": "End User Id"}, "description": "End User ID in the request parameters"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LiteLLM_EndUserTable"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/customer/update": {"post": {"tags": ["Customer Management"], "summary": "Update End User", "description": "Example curl \n\n```\ncurl --location 'http://0.0.0.0:4000/customer/update'     --header 'Authorization: Bearer sk-1234'     --header 'Content-Type: application/json'     --data '{\n    \"user_id\": \"test-litellm-user-4\",\n    \"budget_id\": \"paid_tier\"\n}'\n\nSee below for all params \n```", "operationId": "update_end_user_customer_update_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCustomerRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/customer/delete": {"post": {"tags": ["Customer Management"], "summary": "Delete End User", "description": "Example curl \n\n```\ncurl --location 'http://0.0.0.0:4000/customer/delete'         --header 'Authorization: Bearer sk-1234'         --header 'Content-Type: application/json'         --data '{\n        \"user_ids\" :[\"ishaan-jaff-5\"]\n}'\n\nSee below for all params \n```", "operationId": "delete_end_user_customer_delete_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteCustomerRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/team/new": {"post": {"tags": ["team management"], "summary": "New Team", "description": "Allow users to create a new team. Apply user permissions to their team.\n\n[ASK FOR HELP](https://github.com/BerriAI/litellm/issues)\n\nParameters:\n- team_alias: Optional[str] - User defined team alias\n- team_id: Optional[str] - The team id of the user. If none passed, we'll generate it.\n- members_with_roles: List[{\"role\": \"admin\" or \"user\", \"user_id\": \"<user-id>\"}] - A list of users and their roles in the team. Get user_id when making a new user via `/user/new`.\n- metadata: Optional[dict] - Metadata for team, store information for team. Example metadata = {\"team\": \"core-infra\", \"app\": \"app2\", \"email\": \"<EMAIL>\" }\n- tpm_limit: Optional[int] - The TPM (Tokens Per Minute) limit for this team - all keys with this team_id will have at max this TPM limit\n- rpm_limit: Optional[int] - The RPM (Requests Per Minute) limit for this team - all keys associated with this team_id will have at max this RPM limit\n- max_budget: Optional[float] - The maximum budget allocated to the team - all keys for this team_id will have at max this max_budget\n- models: Optional[list] - A list of models associated with the team - all keys for this team_id will have at most, these models. If empty, assumes all models are allowed.\n- blocked: bool - Flag indicating if the team is blocked or not - will stop all calls from keys with this team_id.\n\nReturns:\n- team_id: (str) Unique team id - used for tracking spend across multiple keys for same team id.\n\n_deprecated_params:\n- admins: list - A list of user_id's for the admin role\n- users: list - A list of user_id's for the user role\n\nExample Request:\n```\ncurl --location 'http://0.0.0.0:4000/team/new' \n--header 'Authorization: Bearer sk-1234' \n--header 'Content-Type: application/json' \n--data '{\n  \"team_alias\": \"my-new-team_2\",\n  \"members_with_roles\": [{\"role\": \"admin\", \"user_id\": \"user-1234\"},\n    {\"role\": \"user\", \"user_id\": \"user-2434\"}]\n}'\n\n```", "operationId": "new_team_team_new_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewTeamRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LiteLLM_TeamTable"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/team/update": {"post": {"tags": ["team management"], "summary": "Update Team", "description": "Use `/team/member_add` AND `/team/member/delete` to add/remove new team members  \n\nYou can now update team budget / rate limits via /team/update\n\nParameters:\n- team_id: str - The team id of the user. Required param.\n- team_alias: Optional[str] - User defined team alias\n- metadata: Optional[dict] - Metadata for team, store information for team. Example metadata = {\"team\": \"core-infra\", \"app\": \"app2\", \"email\": \"<EMAIL>\" }\n- tpm_limit: Optional[int] - The TPM (Tokens Per Minute) limit for this team - all keys with this team_id will have at max this TPM limit\n- rpm_limit: Optional[int] - The RPM (Requests Per Minute) limit for this team - all keys associated with this team_id will have at max this RPM limit\n- max_budget: Optional[float] - The maximum budget allocated to the team - all keys for this team_id will have at max this max_budget\n- models: Optional[list] - A list of models associated with the team - all keys for this team_id will have at most, these models. If empty, assumes all models are allowed.\n- blocked: bool - Flag indicating if the team is blocked or not - will stop all calls from keys with this team_id.\n\nExample - update team TPM Limit\n\n```\ncurl --location 'http://0.0.0.0:8000/team/update' \n--header 'Authorization: Bearer sk-1234' \n--header 'Content-Type: application/json' \n--data-raw '{\n    \"team_id\": \"litellm-test-client-id-new\",\n    \"tpm_limit\": 100\n}'\n```", "operationId": "update_team_team_update_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateTeamRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/team/member_add": {"post": {"tags": ["team management"], "summary": "Team Member Add", "description": "[BETA]\n\nAdd new members (either via user_email or user_id) to a team\n\nIf user doesn't exist, new user row will also be added to User Table\n\n```\n\ncurl -X POST 'http://0.0.0.0:4000/team/member_add'     -H 'Authorization: Bearer sk-1234'     -H 'Content-Type: application/json'     -d '{\"team_id\": \"45e3e396-ee08-4a61-a88e-16b3ce7e0849\", \"member\": {\"role\": \"user\", \"user_id\": \"<EMAIL>\"}}'\n\n```", "operationId": "team_member_add_team_member_add_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TeamMemberAddRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/team/member_delete": {"post": {"tags": ["team management"], "summary": "Team Member Delete", "description": "[BETA]\n\ndelete members (either via user_email or user_id) from a team\n\nIf user doesn't exist, an exception will be raised\n```\ncurl -X POST 'http://0.0.0.0:8000/team/update' \n-H 'Authorization: Bearer sk-1234' \n-H 'Content-Type: application/json' \n-D '{\n    \"team_id\": \"45e3e396-ee08-4a61-a88e-16b3ce7e0849\",\n    \"user_id\": \"<EMAIL>\"\n}'\n```", "operationId": "team_member_delete_team_member_delete_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TeamMemberDeleteRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/team/delete": {"post": {"tags": ["team management"], "summary": "Delete Team", "description": "delete team and associated team keys\n\n```\ncurl --location 'http://0.0.0.0:8000/team/delete' \n--header 'Authorization: Bearer sk-1234' \n--header 'Content-Type: application/json' \n--data-raw '{\n    \"team_ids\": [\"45e3e396-ee08-4a61-a88e-16b3ce7e0849\"]\n}'\n```", "operationId": "delete_team_team_delete_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteTeamRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/team/info": {"get": {"tags": ["team management"], "summary": "Team Info", "description": "get info on team + related keys\n\n```\ncurl --location 'http://localhost:4000/team/info'     --header 'Authorization: Bearer sk-1234'     --header 'Content-Type: application/json'     --data '{\n    \"teams\": [\"<team-id>\",..]\n}'\n```", "operationId": "team_info_team_info_get", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "team_id", "in": "query", "required": false, "schema": {"type": "string", "description": "Team ID in the request parameters", "title": "Team Id"}, "description": "Team ID in the request parameters"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/team/block": {"post": {"tags": ["team management"], "summary": "Block Team", "description": "Blocks all calls from keys with this team id.", "operationId": "block_team_team_block_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlockTeamRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/team/unblock": {"post": {"tags": ["team management"], "summary": "Unblock Team", "description": "Blocks all calls from keys with this team id.", "operationId": "unblock_team_team_unblock_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlockTeamRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/team/list": {"get": {"tags": ["team management"], "summary": "List Team", "description": "[Admin-only] List all available teams\n\n```\ncurl --location --request GET 'http://0.0.0.0:4000/team/list'         --header 'Authorization: Bearer sk-1234'\n```", "operationId": "list_team_team_list_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/organization/new": {"post": {"tags": ["organization management"], "summary": "New Organization", "description": "Allow orgs to own teams\n\nSet org level budgets + model access.\n\nOnly admins can create orgs.\n\n# Parameters\n\n- `organization_alias`: *str* = The name of the organization.\n- `models`: *List* = The models the organization has access to.\n- `budget_id`: *Optional[str]* = The id for a budget (tpm/rpm/max budget) for the organization.\n### IF NO BUDGET ID - CREATE ONE WITH THESE PARAMS ###\n- `max_budget`: *Optional[float]* = Max budget for org\n- `tpm_limit`: *Optional[int]* = Max tpm limit for org\n- `rpm_limit`: *Optional[int]* = Max rpm limit for org\n- `model_max_budget`: *Optional[dict]* = Max budget for a specific model\n- `budget_duration`: *Optional[str]* = Frequency of reseting org budget\n\nCase 1: Create new org **without** a budget_id\n\n```bash\ncurl --location 'http://0.0.0.0:4000/organization/new' \n--header 'Authorization: Bearer sk-1234' \n--header 'Content-Type: application/json' \n--data '{\n    \"organization_alias\": \"my-secret-org\",\n    \"models\": [\"model1\", \"model2\"],\n    \"max_budget\": 100\n}'\n\n\n```\n\nCase 2: Create new org **with** a budget_id\n\n```bash\ncurl --location 'http://0.0.0.0:4000/organization/new' \n--header 'Authorization: Bearer sk-1234' \n--header 'Content-Type: application/json' \n--data '{\n    \"organization_alias\": \"my-secret-org\",\n    \"models\": [\"model1\", \"model2\"],\n    \"budget_id\": \"428eeaa8-f3ac-4e85-a8fb-7dc8d7aa8689\"\n}'\n```", "operationId": "new_organization_organization_new_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewOrganizationRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewOrganizationResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/organization/update": {"post": {"tags": ["organization management"], "summary": "Update Organization", "description": "[TODO] Not Implemented yet. Let us know if you need this - https://github.com/BerriAI/litellm/issues", "operationId": "update_organization_organization_update_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/organization/delete": {"post": {"tags": ["organization management"], "summary": "Delete Organization", "description": "[TODO] Not Implemented yet. Let us know if you need this - https://github.com/BerriAI/litellm/issues", "operationId": "delete_organization_organization_delete_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/organization/info": {"post": {"tags": ["organization management"], "summary": "Info Organization", "description": "Get the org specific information", "operationId": "info_organization_organization_info_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/budget/new": {"post": {"tags": ["budget management"], "summary": "New Budget", "description": "Create a new budget object. Can apply this to teams, orgs, end-users, keys.", "operationId": "new_budget_budget_new_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BudgetNew"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/budget/info": {"post": {"tags": ["budget management"], "summary": "Info Budget", "description": "Get the budget id specific information", "operationId": "info_budget_budget_info_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BudgetRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/budget/settings": {"get": {"tags": ["budget management"], "summary": "Budget Settings", "description": "Get list of configurable params + current value for a budget item + description of each field\n\nUsed on Admin UI.", "operationId": "budget_settings_budget_settings_get", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "budget_id", "in": "query", "required": true, "schema": {"type": "string", "title": "Budget Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/budget/list": {"get": {"tags": ["budget management"], "summary": "List Budget", "description": "List all the created budgets in proxy db. Used on Admin UI.", "operationId": "list_budget_budget_list_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/budget/delete": {"post": {"tags": ["budget management"], "summary": "Delete Budget", "description": "Delete budget", "operationId": "delete_budget_budget_delete_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BudgetDeleteRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/model/new": {"post": {"tags": ["model management"], "summary": "Add New Model", "description": "Allows adding new models to the model list in the config.yaml", "operationId": "add_new_model_model_new_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Deployment"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/model/update": {"post": {"tags": ["model management"], "summary": "Update Model", "description": "Edit existing model params", "operationId": "update_model_model_update_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/updateDeployment"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/v1/model/info": {"get": {"tags": ["model management"], "summary": "Model Info V1", "description": "Provides more info about each model in /models, including config.yaml descriptions (except api key and api base)", "operationId": "model_info_v1_v1_model_info_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/model/info": {"get": {"tags": ["model management"], "summary": "Model Info V1", "description": "Provides more info about each model in /models, including config.yaml descriptions (except api key and api base)", "operationId": "model_info_v1_model_info_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/model_group/info": {"get": {"tags": ["model management"], "summary": "Model Group Info", "description": "Provides more info about each model in /models, including config.yaml descriptions (except api key and api base)", "operationId": "model_group_info_model_group_info_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/model/delete": {"post": {"tags": ["model management"], "summary": "Delete Model", "description": "Allows deleting models in the model list in the config.yaml", "operationId": "delete_model_model_delete_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ModelInfoDelete"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/queue/chat/completions": {"post": {"tags": ["experimental"], "summary": "Async Queue Request", "operationId": "async_queue_request_queue_chat_completions_post", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "model", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/queue/info": {"get": {"tags": ["experimental"], "summary": "Queue Info", "description": "Help user know the status of an item in the queue", "operationId": "queue_info_queue_info_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {}, "type": "array", "title": "Response Queue Info Queue Info Get"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/ollama_logs": {"get": {"tags": ["experimental"], "summary": "Retrieve Server Log", "operationId": "retrieve_server_log_ollama_logs_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/invitation/new": {"post": {"tags": ["Invite <PERSON>s"], "summary": "New Invitation", "description": "Allow admin to create invite links, to onboard new users to Admin UI.\n\n```\ncurl -X POST 'http://localhost:4000/invitation/new'         -H 'Content-Type: application/json'         -D '{\n        \"user_id\": \"1234\" // 👈 id of user in 'LiteLLM_UserTable'\n    }'\n```", "operationId": "new_invitation_invitation_new_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvitationNew"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvitationModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/invitation/info": {"get": {"tags": ["Invite <PERSON>s"], "summary": "Invitation Info", "description": "Allow admin to create invite links, to onboard new users to Admin UI.\n\n```\ncurl -X POST 'http://localhost:4000/invitation/new'         -H 'Content-Type: application/json'         -D '{\n        \"user_id\": \"1234\" // 👈 id of user in 'LiteLLM_UserTable'\n    }'\n```", "operationId": "invitation_info_invitation_info_get", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "invitation_id", "in": "query", "required": true, "schema": {"type": "string", "title": "Invitation Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvitationModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/invitation/update": {"post": {"tags": ["Invite <PERSON>s"], "summary": "Invitation Update", "description": "Update when invitation is accepted\n\n```\ncurl -X POST 'http://localhost:4000/invitation/update'         -H 'Content-Type: application/json'         -D '{\n        \"invitation_id\": \"1234\" // 👈 id of invitation in 'LiteLLM_InvitationTable'\n        \"is_accepted\": True // when invitation is accepted\n    }'\n```", "operationId": "invitation_update_invitation_update_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvitationUpdate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvitationModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/invitation/delete": {"post": {"tags": ["Invite <PERSON>s"], "summary": "Invitation Delete", "description": "Delete invitation link\n\n```\ncurl -X POST 'http://localhost:4000/invitation/delete'         -H 'Content-Type: application/json'         -D '{\n        \"invitation_id\": \"1234\" // 👈 id of invitation in 'LiteLLM_InvitationTable'\n    }'\n```", "operationId": "invitation_delete_invitation_delete_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvitationDelete"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvitationModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/config/update": {"post": {"tags": ["config.yaml"], "summary": "Update Config", "description": "For Admin UI - allows admin to update config via UI\n\nCurrently supports modifying General Settings + LiteLLM settings", "operationId": "update_config_config_update_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfigYAML"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/config/field/update": {"post": {"tags": ["config.yaml"], "summary": "Update Config General Settings", "description": "Update a specific field in litellm general settings", "operationId": "update_config_general_settings_config_field_update_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfigFieldUpdate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/config/field/info": {"get": {"tags": ["config.yaml"], "summary": "Get Config General Settings", "operationId": "get_config_general_settings_config_field_info_get", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "field_name", "in": "query", "required": true, "schema": {"type": "string", "title": "Field Name"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfigFieldInfo"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/config/list": {"get": {"tags": ["config.yaml"], "summary": "Get Config List", "description": "List the available fields + current values for a given type of setting (currently just 'general_settings'user_api_key_dict: UserAPIKeyAuth = Depends(user_api_key_auth),)", "operationId": "get_config_list_config_list_get", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "config_type", "in": "query", "required": true, "schema": {"enum": ["general_settings"], "const": "general_settings", "type": "string", "title": "Config Type"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConfigList"}, "title": "Response Get Config List Config List Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/config/field/delete": {"post": {"tags": ["config.yaml"], "summary": "Delete Config General Settings", "description": "Delete the db value of this field in litellm general settings. Resets it to it's initial default value on litellm.", "operationId": "delete_config_general_settings_config_field_delete_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfigFieldDelete"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/config/yaml": {"get": {"tags": ["config.yaml"], "summary": "Config Yaml Endpoint", "description": "This is a mock endpoint, to show what you can set in config.yaml details in the Swagger UI.\n\nParameters:\n\nThe config.yaml object has the following attributes:\n- **model_list**: *Optional[List[ModelParams]]* - A list of supported models on the server, along with model-specific configurations. ModelParams includes \"model_name\" (name of the model), \"litellm_params\" (litellm-specific parameters for the model), and \"model_info\" (additional info about the model such as id, mode, cost per token, etc).\n\n- **litellm_settings**: *Optional[dict]*: Settings for the litellm module. You can specify multiple properties like \"drop_params\", \"set_verbose\", \"api_base\", \"cache\".\n\n- **general_settings**: *Optional[ConfigGeneralSettings]*: General settings for the server like \"completion_model\" (default model for chat completion calls), \"use_azure_key_vault\" (option to load keys from azure key vault), \"master_key\" (key required for all calls to proxy), and others.\n\nPlease, refer to each class's description for a better understanding of the specific attributes within them.\n\nNote: This is a mock endpoint primarily meant for demonstration purposes, and does not actually provide or change any configurations.", "operationId": "config_yaml_endpoint_config_yaml_get", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfigYAML"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/test": {"get": {"tags": ["health"], "summary": "Test Endpoint", "description": "[DEPRECATED] use `/health/liveliness` instead.\n\nA test endpoint that pings the proxy server to check if it's healthy.\n\nParameters:\n    request (Request): The incoming request.\n\nReturns:\n    dict: A dictionary containing the route of the request URL.", "operationId": "test_endpoint_test_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/health": {"get": {"tags": ["health"], "summary": "Health Endpoint", "description": "🚨 USE `/health/liveliness` to health check the proxy 🚨\n\nSee more 👉 https://docs.litellm.ai/docs/proxy/health\n\n\nCheck the health of all the endpoints in config.yaml\n\nTo run health checks in the background, add this to config.yaml:\n```\ngeneral_settings:\n    # ... other settings\n    background_health_checks: True\n```\nelse, the health checks will be run on models when /health is called.", "operationId": "health_endpoint_health_get", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "model", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Specify the model name (optional)", "title": "Model"}, "description": "Specify the model name (optional)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/active/callbacks": {"get": {"tags": ["health"], "summary": "Active Callbacks", "description": "Returns a list of active callbacks on litellm.callbacks, litellm.input_callback, litellm.failure_callback, litellm.success_callback", "operationId": "active_callbacks_active_callbacks_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/health/readiness": {"get": {"tags": ["health"], "summary": "Health Readiness", "description": "Unprotected endpoint for checking if worker can receive requests", "operationId": "health_readiness_health_readiness_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/health/liveliness": {"get": {"tags": ["health"], "summary": "Health Liveliness", "description": "Unprotected endpoint for checking if worker is alive", "operationId": "health_liveliness_health_liveliness_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/cache/ping": {"get": {"tags": ["caching"], "summary": "<PERSON><PERSON>", "description": "Endpoint for checking if cache can be pinged", "operationId": "cache_ping_cache_ping_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/cache/delete": {"post": {"tags": ["caching"], "summary": "<PERSON><PERSON>", "description": "Endpoint for deleting a key from the cache. All responses from litellm proxy have `x-litellm-cache-key` in the headers\n\nParameters:\n- **keys**: *Optional[List[str]]* - A list of keys to delete from the cache. Example {\"keys\": [\"key1\", \"key2\"]}\n\n```shell\ncurl -X POST \"http://0.0.0.0:4000/cache/delete\"     -H \"Authorization: Bearer sk-1234\"     -d '{\"keys\": [\"key1\", \"key2\"]}'\n```", "operationId": "cache_delete_cache_delete_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/cache/redis/info": {"get": {"tags": ["caching"], "summary": "<PERSON><PERSON>", "description": "Endpoint for getting /redis/info", "operationId": "cache_redis_info_cache_redis_info_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/cache/flushall": {"post": {"tags": ["caching"], "summary": "<PERSON><PERSON>", "description": "A function to flush all items from the cache. (All items will be deleted from the cache with this)\nRaises HTTPException if the cache is not initialized or if the cache type does not support flushing.\nReturns a dictionary with the status of the operation.\n\nUsage:\n```\ncurl -X POST http://0.0.0.0:4000/cache/flushall -H \"Authorization: Bearer sk-1234\"\n```", "operationId": "cache_flushall_cache_flushall_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/": {"get": {"summary": "Home", "operationId": "home__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/routes": {"get": {"summary": "Get Routes", "description": "Get a list of available routes in the FastAPI application.", "operationId": "get_routes_routes_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}, "/token/generate": {"get": {"summary": "Token Generate", "description": "Test endpoint. Admin-only access. Meant for generating admin tokens with specific claims and testing if they work for creating keys, etc.", "operationId": "token_generate_token_generate_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"APIKeyHeader": []}]}}}}