// src/scripts/migration/provider-model-migrator.ts
import { eq } from 'drizzle-orm';
import { uniq } from 'lodash';
import pMap from 'p-map';

import { lobechatDB } from '@/database/instance/lobechatDB';
import { userSettings } from '@/database/instance/lobechatDB/schemas';
import { aiModels, aiProviders } from '@/database/instance/lobechatDB/schemas/aiInfra';
import { KeyVaultsGateKeeper } from '@/server/modules/KeyVaultsEncrypt';

// src/scripts/migration/types.ts
export interface MigrationConfig {
  batchSize?: number;
  validateOnly?: boolean;
}

export interface MigrationResult {
  errors: Error[];
  modelsCount: number;
  providersCount: number;
  success: boolean;
  warnings: string[];
}

export class AiInfraMigrator {
  private readonly config: MigrationConfig;
  private readonly results: MigrationResult;
  private keyVaultsSecret: string;

  constructor(keyVaultsSecret: string, config: MigrationConfig = {}) {
    this.keyVaultsSecret = keyVaultsSecret;
    this.config = {
      batchSize: 100,
      validateOnly: false,
      ...config,
    };

    this.results = {
      errors: [],
      modelsCount: 0,
      providersCount: 0,
      success: false,
      warnings: [],
    };
  }

  async migrate(userIds: string[]): Promise<MigrationResult> {
    try {
      // 2. 批量处理用户数据
      for (const batch of this.getBatches(userIds)) {
        await this.processBatch(batch);
      }

      this.results.success = true;

      return this.results;
    } catch (error) {
      console.error('Migration failed:', error);
      this.results.errors.push(error as Error);
      return this.results;
    }
  }

  private async processBatch(users: string[]): Promise<void> {
    await pMap(
      users,
      async (userId) => {
        console.log(`Migrating user: ${userId}...`);
        console.time(`Migrating cost(${userId})`);
        await lobechatDB.transaction(async (trx) => {
          try {
            // 1. 读取用户设置
            const settings = await trx.query.userSettings.findFirst({
              where: eq(userSettings.id, userId),
            });

            // 2. 转换 Provider 数据
            const providers = await this.migrateProviders(userId, settings, trx);
            this.results.providersCount += providers.length;

            // 3. 转换 Model 数据
            const models = await this.migrateModels(userId, settings, providers, trx);
            this.results.modelsCount += models.length;
          } catch (error) {
            console.error(`Failed to migrate user ${userId}:`, error);
          }
        });
        console.timeEnd(`Migrating cost(${userId})`);
      },
      { concurrency: 10 },
    );
  }

  private async migrateProviders(userId: string, settings: any, trx: any): Promise<any[]> {
    const providers: any[] = [];
    const keyVaults = await KeyVaultsGateKeeper.getUserKeyVaults(
      this.keyVaultsSecret,
      settings.keyVaults,
      userId,
    );

    const languageModel = settings.languageModel || {};

    const keys = uniq(Object.keys(languageModel).concat(Object.keys(keyVaults)));

    for (const providerId of keys) {
      const provider = await this.convertToProvider({
        config: languageModel[providerId] || {},
        id: providerId,
        keyVaults: keyVaults[providerId],
        userId,
      });

      providers.push(provider);
    }

    await trx.insert(aiProviders).values(providers).onConflictDoNothing();
    return providers;
  }

  private async migrateModels(
    userId: string,
    settings: any,
    providers: any[],
    trx: any,
  ): Promise<any[]> {
    const models: any[] = [];
    const languageModel = settings.languageModel || {};

    for (const provider of providers) {
      const providerConfig = languageModel[provider.id];
      if (!providerConfig) continue;

      // 处理自定义模型
      const customModels = providerConfig.customModelCards || [];
      for (const modelCard of customModels) {
        const model = this.convertToModel({
          enabled: (providerConfig.enabledModels || []).includes(modelCard.id),
          modelCard,
          providerId: provider.id,
          source: 'custom',
          userId,
        });

        models.push(model);
      }

      // 处理远程模型
      const remoteModels = providerConfig.remoteModelCards || [];
      for (const modelCard of remoteModels) {
        const model = this.convertToModel({
          enabled: (providerConfig.enabledModels || []).includes(modelCard.id),
          modelCard,
          providerId: provider.id,
          source: 'remote',
          userId,
        });

        if (models.every((m) => m.id !== model.id)) {
          models.push(model);
        }
      }

      // 处理已开启模型
      const enabledModels = (providerConfig.enabledModels as string[]) || [];

      for (const id of enabledModels) {
        const model = this.convertToModel({
          enabled: true,
          modelCard: { id },
          providerId: provider.id,
          source: 'builtin',
          userId,
        });

        if (models.every((m) => m.id !== model.id)) {
          models.push(model);
        }
      }
    }

    // 确保不会出现 model id 超级长的情况
    // Cloud 迁移测试下来存在若干奇葩 case，比如填错导致：
    // model_id = gpt-3.5-turbo-0125,gpt-3.5-plus,gpt-3.5-turbo-16k,gpt-4-all,dall-e-3,gpt-4-gizmo-g-2fkFE8rbu,cosmic-dream,gpt-4-gizmo-g-FdMHL1sN,consensus,gpt-4-gizmo-g-bo0FiWLY7,claude-3-opus-20240229,glm-4
    // 还有拿 aki key 当 model id 的
    const filterModels = models.filter((model) => model.id.length < 140);

    if (filterModels.length > 0) {
      await trx.insert(aiModels).values(filterModels).onConflictDoNothing();
    }

    return filterModels;
  }

  private async convertToProvider(data: any): Promise<any> {
    const keyVaultsGateKeeper = await KeyVaultsGateKeeper.initWithEnvKey();

    return {
      enabled: data.config.enabled,
      fetchOnClient: data.config.fetchOnClient,
      id: data.id,
      keyVaults: await keyVaultsGateKeeper.encrypt(data.keyVaults),
      source: 'builtin',
      userId: data.userId,
    };
  }

  private convertToModel(data: any): any {
    const { modelCard, userId, providerId, source, enabled } = data;
    return {
      abilities: {
        files: modelCard.files,
        functionCall: modelCard.functionCall,
        vision: modelCard.vision,
      },
      config: {
        deploymentName: modelCard.deploymentName,
      },
      contextWindowTokens: modelCard.contextWindowTokens || modelCard.tokens,
      createdAt: new Date(),
      description: modelCard.description,
      displayName: modelCard.displayName,
      enabled,
      id: modelCard.id,
      parameters: {},
      pricing: modelCard.pricing || {},
      providerId,
      releasedAt: modelCard.releasedAt,
      source,
      type: 'chat',
      userId,
    };
  }

  // Utility methods...
  private getBatches<T>(items: T[]): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < items.length; i += this.config.batchSize!) {
      batches.push(items.slice(i, i + this.config.batchSize!));
    }
    return batches;
  }
}
