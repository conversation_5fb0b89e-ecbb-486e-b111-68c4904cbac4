import qs from 'query-string';
import urlJoin from 'url-join';

import { vercelEnv } from '@/envs/vercel';

class VercelClient {
  private headers = {
    'Authorization': `Bearer ${vercelEnv.VERCEL_ACCESS_TOKEN}`,
    'Content-Type': 'application/json',
  };

  private genUrl = (url: string) => {
    return qs.stringifyUrl({
      query: {
        teamId: vercelEnv.VERCEL_TEAM_ID,
      },
      url: urlJoin('https://api.vercel.com/v1', url as string),
    });
  };

  GET = async (url: string) =>
    fetch(this.genUrl(url), {
      headers: this.headers,
    });

  PATCH = async (url: string, body?: any) =>
    fetch(this.genUrl(url), {
      body: JSON.stringify(body),
      headers: this.headers,
      method: 'PATCH',
    });

  POST = async (url: string, body?: any) =>
    fetch(this.genUrl(url), {
      body: JSON.stringify(body),
      headers: this.headers,
      method: 'POST',
    });

  DELETE = async (url: string, body?: any) =>
    fetch(this.genUrl(url), {
      body: JSON.stringify(body),
      headers: this.headers,
      method: 'DELETE',
    });
}

export const vercelClient = new VercelClient();
