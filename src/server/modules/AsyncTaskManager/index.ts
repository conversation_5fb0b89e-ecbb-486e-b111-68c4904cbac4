import taskList from '@/server/tasks';

// 任务处理器类型定义
type TaskHandler = (payload: any) => Promise<any>;

/**
 * 任务管理器
 *
 * @description 维护一个任务注册表，并根据任务名称执行相应的任务
 */
class TaskManager {
  private tasks: Map<string, TaskHandler>;

  constructor() {
    this.tasks = new Map();
    this.registerTasks();
  }

  /**
   * 注册所有可用任务
   */
  private registerTasks() {
    taskList.forEach((item) => {
      this.tasks.set(item.id, item.runner);
    });
  }

  /**
   * 执行一个任务
   * @param taskName - 任务名称
   * @param payload - 任务载荷
   * @returns {Promise<any>}
   */
  async run(taskName: string, payload: any): Promise<any> {
    const handler = this.tasks.get(taskName);

    if (!handler) {
      throw new Error(`Task "${taskName}" not found.`);
    }

    console.log(`Executing task: ${taskName}`);
    return handler(payload);
  }
}

export const taskManager = new TaskManager();
