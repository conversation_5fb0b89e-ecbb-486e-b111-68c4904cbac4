import { z } from 'zod';

import { CommonQuerySchema } from '@/types/query';

export interface AllowlistItem {
  createdAt: number;
  id: string;
  identifier: string;
  instanceId?: string;
  updatedAt: number;
  user?: {
    avatar: string;
    id: string;
    username: string;
  };
}

export const AllowlistQuerySchema = z
  .object({
    emailOrUsernameOrUserId: z.string().optional(),
    range: z.tuple([z.string(), z.string()]).optional(),
  })
  .merge(CommonQuerySchema);

export type AllowlistQuery = z.infer<typeof AllowlistQuerySchema>;

export interface BlocklistItem {
  createdAt: number;
  id: string;
  identifier: string;
  instanceId?: string;
  updatedAt: number;
  user?: {
    avatar: string;
    id: string;
    username: string;
  };
}

export const BlocklistQuerySchema = z
  .object({
    emailOrUsernameOrUserId: z.string().optional(),
    range: z.tuple([z.string(), z.string()]).optional(),
  })
  .merge(CommonQuerySchema);

export type BlocklistQuery = z.infer<typeof BlocklistQuerySchema>;

export enum RestrictionType {
  Allowlist = 'allowlist',
  Blocklist = 'blocklist',
}
