import { z } from 'zod';

import { CommonQuerySchema } from '@/types/query';

export type MessageRoleType = 'user' | 'system' | 'assistant' | 'tool';

export interface ModelReasoning {
  content?: string;
  duration?: number;
}

export interface UserMessage {
  content: string | null;
  createdAt: Date;
  id: string;
  role: string;
}

export interface MessageItem {
  // agentId: string | null;
  // clientId: string | null;
  content: string | null;
  createdAt: Date;
  // error: any | null;
  // favorite: boolean | null;
  id: string;
  model: string | null;
  // observationId: string | null;
  // parentId: string | null;
  provider: string | null;
  // quotaId: string | null;
  // reasoning: ModelReasoning | null;
  role: MessageRoleType;
  // sessionId: string | null;
  // threadId: string | null;
  // jsonb type
  // tools: any | null;
  // topicId: string | null;
  // jsonb type
  // traceId: string | null;
  updatedAt: Date;
  userId: string;
}

export const MessageQuerySchema = z
  .object({
    content: z.string().optional(),
    model: z.string().optional(),
    provider: z.string().optional(),
    range: z.tuple([z.string(), z.string()]).optional(),
    role: z.enum(['user', 'system', 'assistant', 'tool']).optional(),
    topicId: z.string().optional(),
    userId: z.string().optional(),
  })
  .merge(CommonQuerySchema);

export type MessageQuery = z.infer<typeof MessageQuerySchema>;
