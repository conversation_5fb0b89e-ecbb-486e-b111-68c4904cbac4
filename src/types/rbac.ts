import { z } from 'zod';

import type { RoleItem } from '@/database/instance/lobechatDB/schemas/rbac';
import type { RoleQuotaItem } from '@/types/quota';

// ==================== 角色相关类型 ====================

// 角色列表项（包含统计信息）
export interface RoleWithStats extends RoleItem {
  permissionCount: number;
  quota: RoleQuotaItem | null;
  userCount: number;
}

// 角色创建输入
export interface CreateRoleInput {
  description?: string;
  displayName: string;
  isActive?: boolean;
  name: string;
  quota?: {
    fileMb?: number | null;
    tokenBudget?: number | null;
    vectorCount?: number | null;
  };
}

// 角色更新输入
export interface UpdateRoleInput {
  description?: string;
  displayName?: string;
  name?: string;
  quota?: {
    fileMb?: number | null;
    tokenBudget?: number | null;
    vectorCount?: number | null;
  };
  roleId: number;
}

// ==================== 用户角色管理类型 ====================

// 角色用户信息
export interface RoleUserInfo {
  createdAt: Date;
  email: string | null;
  expiresAt: Date | null;
  id: string;
  isOnboarded: boolean | null;
  username: string | null;
}

// 添加用户到角色的输入
export interface AddRoleUsersInput {
  expiresAt?: Date;
  roleId: number;
  userIds: string[];
}

// 从角色中移除用户的输入
export interface RemoveRoleUsersInput {
  roleId: number;
  userIds: string[];
}

// ==================== 角色权限模型管理类型 ====================

// 角色模型权限输入
export interface RoleModelsInput {
  models: string[];
  roleId: number;
}

// 角色删除输入
export interface DeleteRoleInput {
  roleId: number;
}

// 角色状态切换输入
export interface ToggleRoleActiveInput {
  roleId: number;
}

// 角色ID查询输入
export interface RoleIdInput {
  roleId: number;
}

// ==================== API 响应类型 ====================

// 通用API响应
export interface ApiResponse<T = any> {
  data?: T;
  message?: string;
  success: boolean;
}

// 角色API响应
export type RoleResponse = ApiResponse<RoleItem>;
export type RoleListResponse = ApiResponse<RoleWithStats[]>;
export type RoleUsersResponse = ApiResponse<RoleUserInfo[]>;
export type RoleModelsResponse = ApiResponse<string[]>;
export type RoleUserApiKeysResponse = ApiResponse<RoleUserVirtualKeyResponse[]>;

// ==================== 通用工具类型 ====================

// 分页参数
export interface PaginationParams {
  page?: number;
  pageSize?: number;
}

// 排序参数
export interface SortParams {
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 查询参数
export interface QueryParams extends PaginationParams, SortParams {
  isActive?: boolean;
  search?: string;
}

// 批量操作结果
export interface BatchOperationResult {
  failureCount: number;
  failures?: Array<{
    error: string;
    id: string;
  }>;
  success: boolean;
  successCount: number;
}

// ==================== Zod Schema验证 ====================

// 角色配额Schema
const RoleQuotaSchema = z
  .object({
    fileMb: z.number().int().min(0).nullable().optional(),
    tokenBudget: z.number().min(0).nullable().optional(),
    vectorCount: z.number().int().min(0).nullable().optional(),
  })
  .optional();

// 创建角色Schema
export const CreateRoleSchema = z.object({
  description: z.string().optional(),
  displayName: z.string().min(1, 'Display name is required'),
  isActive: z.boolean().default(true),
  name: z.string().min(1, 'Role name is required'),
  quota: RoleQuotaSchema,
});

// 更新角色Schema
export const UpdateRoleSchema = z.object({
  description: z.string().optional(),
  displayName: z.string().min(1, 'Display name is required').optional(),
  name: z.string().min(1, 'Role name is required').optional(),
  quota: RoleQuotaSchema,
  roleId: z.number(),
});

// 删除角色Schema
export const DeleteRoleSchema = z.object({
  roleId: z.number(),
});

// 角色状态切换Schema
export const ToggleRoleActiveSchema = z.object({
  roleId: z.number(),
});

// 角色ID查询Schema
export const RoleIdSchema = z.object({
  roleId: z.number(),
});

// 添加用户到角色Schema
export const AddRoleUsersSchema = z.object({
  expiresAt: z.date().optional(),
  roleId: z.number(),
  userIds: z.array(z.string()),
});

// 从角色中移除用户Schema
export const RemoveRoleUsersSchema = z.object({
  roleId: z.number(),
  userIds: z.array(z.string()),
});

// 角色模型权限Schema
export const RoleModelsSchema = z.object({
  models: z.array(z.string()),
  roleId: z.number(),
});

// ==================== 工具类型 ====================

// 基于Partial的更新类型工具
export type PartialUpdateFields<T> = Partial<Omit<T, 'id' | 'createdAt' | 'updatedAt'>>;

// 角色配额类型
export interface RoleQuota {
  fileMb?: number | null;
  tokenBudget?: number | null;
  vectorCount?: number | null;
}

// ==================== API Key 相关类型 ====================

// API Key 信息（解密后的对象）
export interface VirtualKeyInfo extends Record<string, string> {
  virtualKey: string; // Virtual Key 值
  virtualKeyId: string; // Virtual Key ID
}

// 用户 API Key 详情（包含解密后的信息）
export interface UserVirtualKeyDetails {
  roleId: string;
  userId: string;
  virtualKeyInfo: VirtualKeyInfo;
}

// 创建用户 API Key 的输入数据
export interface CreateUserVirtualKeyData {
  roleId: string;
  userId: string;
  virtualKeyInfo: VirtualKeyInfo;
}

// 批量创建用户 API Key 的输入数据
export type BatchCreateUserVirtualKeyData = CreateUserVirtualKeyData;

// 角色用户 API Key 响应
export interface RoleUserVirtualKeyResponse {
  roleId: string;
  userId: string;
  virtualKeyInfo: VirtualKeyInfo;
}
