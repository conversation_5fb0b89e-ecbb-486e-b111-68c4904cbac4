import { z } from 'zod';

/**
 * 角色配额信息项
 */
export interface RoleQuotaItem {
  accessedAt: Date;
  createdAt: Date;
  fileMb: number | null;
  id: number;
  roleId: string;
  tokenBudget: number | null;
  updatedAt: Date;
  vectorCount: number | null;
}

/**
 * 更新角色配额数据
 */
export interface UpdateRoleQuotaData {
  fileMb?: number | null;
  tokenBudget?: number | null;
  vectorCount?: number | null;
}

/**
 * 创建角色配额数据
 */
export interface CreateRoleQuotaData {
  fileMb?: number | null;
  roleId: string;
  tokenBudget?: number | null;
  vectorCount?: number | null;
}

/**
 * 更新角色配额的验证 schema
 */
export const UpdateRoleQuotaSchema = z.object({
  fileMb: z.number().int().min(0).nullable().optional(),
  tokenBudget: z.number().min(0).nullable().optional(),
  vectorCount: z.number().int().min(0).nullable().optional(),
});

/**
 * 创建角色配额的验证 schema
 */
export const CreateRoleQuotaSchema = z.object({
  fileMb: z.number().int().min(0).nullable().optional(),
  roleId: z.string().min(1),
  tokenBudget: z.number().min(0).nullable().optional(),
  vectorCount: z.number().int().min(0).nullable().optional(),
});

/**
 * 角色配额查询参数
 */
export const RoleQuotaQuerySchema = z.object({
  roleId: z.string().min(1),
});

export type UpdateRoleQuotaInput = z.infer<typeof UpdateRoleQuotaSchema>;
export type CreateRoleQuotaInput = z.infer<typeof CreateRoleQuotaSchema>;
export type RoleQuotaQueryInput = z.infer<typeof RoleQuotaQuerySchema>;
