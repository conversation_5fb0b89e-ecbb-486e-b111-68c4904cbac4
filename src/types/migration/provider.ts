import { z } from 'zod';

import { CommonQuerySchema } from '@/types/query';

export interface ProviderMigration {
  afterProviders: string[];
  avatar?: string;
  beforeProviders: string[];
  email: string;
  id: string;
  status: 'success' | 'error' | 'waiting';
  username: string;
}
export interface ModelStatistics {
  customModels: number;
  enabledModels: number;
  remoteModels: number;
}

export interface ProviderMigrationDetail {
  after?: {
    enabled: boolean | null;
    fetchOnClient: boolean | null;
    keyVaults: Record<string, string>;
  };
  afterModels: ModelStatistics;
  before: {
    enabled: boolean;
    fetchOnClient: boolean;
    keyVaults: Record<string, string>;
  };
  beforeModels: ModelStatistics;
  id: string;
}

export interface ModelMigrationDetail {
  after: any;
  before: any;
  id: string;
}

export const ProviderMigrationSchema = z
  .object({
    emailOrUsernameOrUserId: z.string().optional(),
    id: z.string().optional(),
    status: z.enum(['success', 'error']).optional(),
  })
  .merge(CommonQuerySchema);

export type ProviderMigrationQuery = z.infer<typeof ProviderMigrationSchema>;
