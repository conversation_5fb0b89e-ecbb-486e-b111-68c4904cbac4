import { z } from 'zod';

export interface LiteLLMParams {
  api_base: string;
  api_version: string;
  model: string;
  rpm: number;
  tpm: number;
  weight: number;
}

export interface ModelInfo {
  cache_creation_input_token_cost: number | null;
  cache_read_input_token_cost: number;
  db_model: boolean;
  id: string;
  input_cost_per_audio_token: number | null;
  input_cost_per_character: number | null;
  input_cost_per_query: number | null;
  input_cost_per_second: number | null;
  input_cost_per_token: number;
  input_cost_per_token_above_128k_tokens: number | null;
  key: string;
  litellm_provider: string;
  max_input_tokens: number;
  max_output_tokens: number;
  max_tokens: number;
  mode: string;
  output_cost_per_audio_token: number | null;
  output_cost_per_character: number | null;
  output_cost_per_character_above_128k_tokens: number | null;
  output_cost_per_second: number | null;
  output_cost_per_token: number;
  output_cost_per_token_above_128k_tokens: number | null;
  output_vector_size: number | null;
  supported_openai_params: string[];
  supports_assistant_prefill: boolean;
  supports_audio_input: boolean;
  supports_audio_output: boolean;
  supports_function_calling: boolean;
  supports_prompt_caching: boolean;
  supports_response_schema: boolean;
  supports_system_messages: boolean | null;
  supports_vision: boolean;
}

export interface ModelInfoItem {
  litellm_params: LiteLLMParams;
  model_info: ModelInfo;
  model_name: string;
}

export interface ModelItem {
  channels: ModelInfoItem[];
  created: string;
  id: string;
  object: string;
  owned_by: string;
}

export interface ChannelItem {
  api_base: string;
  models: ModelInfoItem[];
  name: string;
}

export interface LiteLLMKeyUpdateParams {
  budget_duration?: '1mo' | '1d';
  max_budget?: number;
  metadata?: any;
  models?: string[];
  rpm_limit?: number;
  spend?: number;
  tpm_limit?: number;
}

export const LiteLLMKeyUpdateParamsSchema = z.object({
  budgetDuration: z.enum(['1mo', '1d']).optional(),
  budgetResetAt: z.string().optional(),
  expires: z.string().optional(),
  maxBudget: z.number().optional(),
  metadata: z.any().optional(),
  models: z.array(z.string()).optional(),
  rpm_limit: z.number().optional(),
  spend: z.number().optional(),
  tpm_limit: z.number().optional(),
});

// ==================== Credential Management Types ====================

export interface CredentialInfo {
  credential_info: {
    description?: string;
  };
  credential_name: string;
  credential_values: {
    api_base?: string;
    api_key?: string;
    api_version?: string;
  };
}

export interface CreateCredentialParams {
  credentialName: string;
  credentialValues: {
    api_base?: string | null;
    api_key: string;
    api_version?: string;
  };
  description?: string;
}

export interface UpdateCredentialParams {
  credentialName: string;
  credentialValues: {
    api_base?: string | null;
    api_key?: string;
    api_version?: string;
  };
  description?: string;
}

export interface CreateCredentialResponse {
  credential_name: string;
}

// ==================== Model Management Types ====================

export interface CreateModelParams {
  [key: string]: unknown;
  litellm_params: {
    [key: string]: any;
    custom_llm_provider?: string;
    input_cost_per_token?: number | null;
    litellm_credential_name?: string;
    model: string;
    output_cost_per_token?: number | null;
    rpm?: number | null;
    tpm?: number | null;
    weight?: number | null;
  };
  model_info: {
    [key: string]: any;
    id?: string | null;
    input_cost_per_token?: number | null;
    litellm_provider: string;
    max_tokens?: number | null;
    mode?: ('embedding' | 'chat' | 'completion') | null;
    output_cost_per_token?: number | null;
  };
  model_name: string;
}

export interface CreateModelResponse {
  model_id: string;
  model_name: string;
}

export type UpdateModelParams = CreateModelParams;
