import { z } from 'zod';

import { CommonQuerySchema } from '@/types/query';

// 知识库信息接口
export interface KnowledgeBaseInfo {
  id: string;
  name: string;
}

// 文件列表项接口（扩展版）
export interface FileItem {
  accessedAt: Date | null;
  chunkCount: number;
  clientId: string | null;
  createdAt: Date;
  fileHash: string | null;
  fileType: string;
  id: string;
  // 关联数据
  knowledgeBases: KnowledgeBaseInfo[];
  name: string;
  size: number;
  updatedAt: Date;
  url: string;
  userId: string; // 向量化分块数量
}

// 文件查询参数接口
export const FileQuerySchema = z
  .object({
    fileType: z.string().optional(),
    name: z.string().optional(),
    range: z.tuple([z.string(), z.string()]).optional(),
    userId: z.string().optional(),
  })
  .merge(CommonQuerySchema);

export type FileQuery = z.infer<typeof FileQuerySchema>;
