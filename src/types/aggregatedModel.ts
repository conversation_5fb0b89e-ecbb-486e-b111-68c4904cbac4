import { z } from 'zod';

import type {
  aggregatedModel,
  aggregatedModelLitellmModel,
} from '@/database/instance/adminDB/schemas/aggregatedModel';
import type { CreateModelParams } from '@/types/litellm';

import type { AdminModelSelectItem } from './adminInfra';

// ==================== 基础类型定义 ====================

// 聚合模型能力类型
export interface ModelAbilities {
  [key: string]: any;
  functionCall?: boolean;
  imageOutput?: boolean;
  reasoning?: boolean;
  search?: boolean;
  vision?: boolean; // 允许其他属性以保持兼容性
}

// 聚合模型定价类型
export interface ModelPricing {
  [key: string]: any;
  input?: number;
  output?: number; // 允许其他属性以保持兼容性
}

// 聚合模型类型枚举
export type ModelType = 'chat' | 'embedding' | 'image' | 'audio';

// 从数据库schema推导的基础类型
export type AggregatedModelItem = typeof aggregatedModel.$inferSelect & {
  abilities: ModelAbilities;
  associatedModelsCount?: number;
  pricing: ModelPricing; // 关联模型数量
};

export type NewAggregatedModel = typeof aggregatedModel.$inferInsert & {
  pricing: ModelPricing;
};

export type AggregatedModelLitellmItem = typeof aggregatedModelLitellmModel.$inferSelect & {
  litellmParams?: Partial<CreateModelParams['litellm_params']>;
};
export type NewAggregatedModelLitellm = typeof aggregatedModelLitellmModel.$inferInsert & {
  litellmParams?: Partial<CreateModelParams['litellm_params']>;
};

// ==================== 扩展类型定义 ====================

// 包含关联模型的聚合模型
export interface AggregatedModelWithAssociationModels extends AggregatedModelItem {
  models: AdminModelSelectItem[];
}

// ==================== 输入输出类型 ====================

// 创建聚合模型数据（基于数据库insert类型）
export interface CreateAggregatedModelData
  extends Omit<NewAggregatedModel, 'createdAt' | 'updatedAt'> {
  abilities?: ModelAbilities;
  pricing: ModelPricing;
  type?: ModelType;
}

// 更新聚合模型数据
export interface UpdateAggregatedModelData
  extends Partial<Omit<NewAggregatedModel, 'id' | 'createdAt' | 'updatedAt'>> {
  abilities?: ModelAbilities;
  pricing?: ModelPricing;
  type?: ModelType;
}

// 创建聚合模型与LiteLLM模型关联数据
export type CreateAggregatedModelLitellmData = NewAggregatedModelLitellm;

// ==================== 查询参数类型 ====================

// 聚合模型查询参数
export interface AggregatedModelQuery {
  enabled?: boolean;
  limit?: number;
  offset?: number;
  type?: ModelType;
}

// 关联查询参数
export interface AggregatedModelAssociationQuery {
  aggregatedModelId?: string;
  infraModelId?: string;
  infraProviderId?: string;
  litellmModelId?: string;
}

// 模型状态切换参数
export interface ToggleModelStatusInput {
  enabled: boolean;
  id: string;
}

// ==================== API响应类型 ====================

// 通用API响应
export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  success: boolean;
}

// 聚合模型API响应类型
export type AggregatedModelResponse = ApiResponse<AggregatedModelItem>;
export type AggregatedModelListResponse = ApiResponse<AggregatedModelItem[]>;
export type AggregatedModelWithAssociationResponse =
  ApiResponse<AggregatedModelWithAssociationModels>;
export type AggregatedModelLitellmResponse = ApiResponse<AggregatedModelLitellmItem>;
export type BooleanResponse = ApiResponse<boolean>;

// ==================== Zod Schema验证 ====================

// 模型能力Schema
const ModelAbilitiesSchema = z
  .object({
    functionCall: z.boolean().optional(),
    imageOutput: z.boolean().optional(),
    reasoning: z.boolean().optional(),
    search: z.boolean().optional(),
    vision: z.boolean().optional(),
  })
  .optional();

// 模型定价Schema
const ModelPricingSchema = z.object({
  input: z.number().nonnegative().optional(),
  output: z.number().nonnegative().optional(),
});

// 模型类型Schema
const ModelTypeSchema = z.enum(['chat', 'embedding', 'image', 'audio']).default('chat');

// LiteLLM 参数Schema
export const LiteLLMParamsSchema = z
  .object({
    custom_llm_provider: z.string().optional(),
    input_cost_per_token: z.number().nullable().optional(),
    litellm_credential_name: z.string().optional(),
    model: z.string(),
    output_cost_per_token: z.number().nullable().optional(),
    rpm: z.number().nullable().optional(),
    tpm: z.number().nullable().optional(),
    weight: z.number().nullable().optional(),
  })
  .catchall(z.any());

// 创建聚合模型数据验证Schema
export const CreateAggregatedModelSchema = z.object({
  abilities: ModelAbilitiesSchema,
  contextWindowTokens: z.number().int().positive().optional(),
  description: z.string().optional(),
  displayName: z.string().min(1).max(200),
  enabled: z.boolean().default(true),
  fallbackModelId: z.string().optional(),
  id: z.string().min(1).max(150),
  logo: z.string().optional(),
  pricing: ModelPricingSchema,
  type: ModelTypeSchema,
});

// 更新聚合模型数据验证Schema
export const UpdateAggregatedModelSchema = z.object({
  abilities: ModelAbilitiesSchema,
  contextWindowTokens: z.number().int().positive().optional(),
  description: z.string().optional(),
  displayName: z.string().min(1).max(200).optional(),
  enabled: z.boolean().optional(),
  fallbackModelId: z.string().optional(),
  logo: z.string().optional(),
  pricing: ModelPricingSchema.optional(),
  type: ModelTypeSchema.optional(),
});

// 创建关联数据验证Schema
export const CreateAggregatedModelLitellmSchema = z.object({
  aggregatedModelId: z.string().min(1),
  infraModelId: z.string().min(1),
  infraProviderId: z.string().min(1),
  litellmParams: LiteLLMParamsSchema.partial().optional().default({}),
});

// 查询参数Schema
export const AggregatedModelQuerySchema = z
  .object({
    enabled: z.boolean().optional(),
    limit: z.number().int().positive().optional(),
    offset: z.number().int().nonnegative().optional(),
    type: ModelTypeSchema.optional(),
  })
  .optional();

// 模型状态切换Schema
export const ToggleModelStatusSchema = z.object({
  enabled: z.boolean(),
  id: z.string().min(1),
});

// ==================== 工具类型 ====================

// 基于Partial的更新类型工具
export type PartialUpdateFields<T> = Partial<Omit<T, 'id' | 'createdAt' | 'updatedAt'>>;

// 分页参数
export interface PaginationParams {
  limit?: number;
  offset?: number;
}

// 排序参数
export interface SortParams {
  sortBy?: keyof AggregatedModelItem;
  sortOrder?: 'asc' | 'desc';
}

// 扩展查询参数
export interface ExtendedAggregatedModelQuery
  extends AggregatedModelQuery,
    PaginationParams,
    SortParams {
  search?: string;
}
