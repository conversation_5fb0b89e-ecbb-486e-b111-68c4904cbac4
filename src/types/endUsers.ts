import type { User } from '@clerk/nextjs/server';
import { z } from 'zod';

import { CommonQuerySchema } from '@/types/query';
import { Plans } from '@/types/subscription';

export interface EndUserCredit {
  freeExpire?: string | null;
  freePercent: number;
  freeUsed?: number | null;
  subscriptionExpire?: string | null;
  subscriptionPercent: number;
  subscriptionUsed?: number | null;
}

export interface EndUser {
  avatar: string | null;
  clerk?: User;
  createdAt: Date;
  credits: EndUserCredit;
  email: string | null;
  freeCredit: number;
  hasRisk?: boolean;
  id: string;
  isOnboarded: boolean | null;
  messageCount?: number | null;
  plan: Plans;
  subscriptionCredit: number;
  username: string | null;
}

export enum EndUserType {
  Active = 'active',
  All = 'all',
  Banned = 'banned',
  Bug = 'bug',
  Inactive = 'inactive',
  Paid = 'paid',
  Risk = 'risk',
}

export const UserQuerySchema = z
  .object({
    emailOrUsernameOrUserId: z.string().optional(),
    id: z.string().optional(),
    plan: z.nativeEnum(Plans).optional(),
    range: z.tuple([z.string(), z.string()]).optional(),
    userType: z.nativeEnum(EndUserType).optional(),
  })
  .merge(CommonQuerySchema);

export type UserQuery = z.infer<typeof UserQuerySchema>;
