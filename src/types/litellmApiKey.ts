import { z } from 'zod';

/**
 * LiteLLM API Key 信息项
 */
export interface LiteLLMApiKeyItem {
  roleId: string;
  userId: string;
  virtualKey: string;
  virtualKeyId: string;
}

/**
 * 创建 LiteLLM API Key 数据
 */
export interface CreateLiteLLMApiKeyData {
  roleId: string;
  userId: string;
  virtualKey: string;
  virtualKeyId: string;
}

/**
 * 更新 LiteLLM API Key 数据
 */
export interface UpdateLiteLLMApiKeyData {
  virtualKey?: string;
  virtualKeyId?: string;
}

/**
 * 创建 LiteLLM API Key 的验证 schema
 */
export const CreateLiteLLMApiKeySchema = z.object({
  roleId: z.string().min(1, '角色ID不能为空'),
  userId: z.string().min(1, '用户ID不能为空'),
  virtualKey: z.string().min(1, 'Virtual Key 不能为空'),
  virtualKeyId: z.string().min(1, 'Virtual Key ID 不能为空'),
});

/**
 * 更新 LiteLLM API Key 的验证 schema
 */
export const UpdateLiteLLMApiKeySchema = z.object({
  virtualKey: z.string().min(1, 'Virtual Key 不能为空').optional(),
  virtualKeyId: z.string().min(1, 'Virtual Key ID 不能为空').optional(),
});

/**
 * 查询 LiteLLM API Key 参数
 */
export interface LiteLLMApiKeyQueryData {
  roleId?: string;
  userId?: string;
}

/**
 * 查询 LiteLLM API Key 的验证 schema
 */
export const LiteLLMApiKeyQuerySchema = z.object({
  roleId: z.string().min(1, '角色ID不能为空').optional(),
  userId: z.string().min(1, '用户ID不能为空').optional(),
});

export type CreateLiteLLMApiKeyInput = z.infer<typeof CreateLiteLLMApiKeySchema>;
export type UpdateLiteLLMApiKeyInput = z.infer<typeof UpdateLiteLLMApiKeySchema>;
export type LiteLLMApiKeyQueryInput = z.infer<typeof LiteLLMApiKeyQuerySchema>;
