import { LICENSE_STATUS } from '@/const/license';

/**
 * 许可证状态类型
 */
export type LicenseStatus = (typeof LICENSE_STATUS)[keyof typeof LICENSE_STATUS];

/**
 * 许可证验证响应接口（来自 API）
 */
export interface LicenseValidationResponse {
  data: {
    /** 许可证剩余天数 */
    expiresIn?: number;
    /** 是否有效 */
    isValid: boolean;
    /** 状态消息 */
    message: string;
    /** 许可证状态 */
    status: LicenseStatus;
  };
  /** 请求是否成功 */
  success: boolean;
  /** 响应时间戳 */
  timestamp: string;
}

/**
 * 许可证验证结果接口（内部使用）
 */
export interface LicenseValidationResult {
  /** 许可证剩余天数 */
  expiresIn?: number;
  /** 是否有效 */
  isValid: boolean;
  /** 状态消息 */
  message: string;
  /** 许可证状态 */
  status: LicenseStatus;
}

/**
 * 许可证中包含的功能列表
 */
export interface LicenseFeature {
  /** 授权功能 */
  grantFeatures: string[];
  /** 许可证 ID */
  licenseId: string;
  /** 撤销功能 */
  revokeFeatures: string[];
  /** 版本 ID */
  versionId: string;
}

/**
 * 许可证配置接口
 */
export interface LicenseConfig {
  /** Console 服务地址 */
  consoleUrl: string;
  /** 许可证密钥 */
  license: string;
}

/**
 * 许可证验证错误接口
 */
export interface LicenseValidationError {
  /** 错误代码 */
  code: string;
  /** 错误消息 */
  message: string;
  /** 错误状态 */
  status: LicenseStatus;
}
