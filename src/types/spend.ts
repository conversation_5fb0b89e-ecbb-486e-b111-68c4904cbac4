import { z } from 'zod';

import { CommonQuerySchema } from '@/types/query';

export const SpendQuerySchema = z
  .object({
    emailOrUsernameOrUserId: z.string().optional(),
    model: z.string().optional(),
    range: z.tuple([z.string(), z.string()]).optional(),
    type: z.string().optional(),
  })
  .merge(CommonQuerySchema);

export type SpendQuery = z.infer<typeof SpendQuerySchema>;

export interface SpendItem {
  /**
   *  Time To First Token (TTFT)
   */
  TTFT: number;
  completionStartTime: Date;
  completionTokens: number;
  duration: number;
  endTime: Date;
  id: string;
  ip: string;
  latency: number;
  model: string;
  promptTokens: number;
  spend: number;
  startTime: Date;
  totalTokens: number;
  type: string;
  user: {
    avatar: string;
    email: string;
    id: string;
    name?: string;
  };
}
