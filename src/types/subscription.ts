import { z } from 'zod';

import { SubscriptionStatus } from '@/const/subscription';
import { CommonQuerySchema } from '@/types/query';

export enum BillingMode {
  Payment = 'payment',
  Subscription = 'subscription',
}

export enum Status {
  ActiveNotCancelling,
  ActiveFutureCancellation,
  Cancelled,
}

export enum Plans {
  Free = 'free',
  Hobby = 'hobby',
  Premium = 'premium',
  Starter = 'starter',
  Ultimate = 'ultimate',
}

export enum Recurring {
  Monthly = 'monthly',
  Yearly = 'yearly',
}

export interface SubscriptionPlan {
  id: Plans;
  limit: {
    credit: number;
    embeddingStorage: number;
    fileStorage: number;
  };
  price: {
    monthly: number;
    yearly: number;
  };
}

export interface CreditUsage {
  expiredAt: Date | null;
  limit: number;
  resetAt: Date | null;
  spend: number;
}
export interface DowngradeBilling {
  plan: Plans | null;
  pricing: number;
  recurring: Recurring | null;
}

export interface CurrentSubscription {
  billingCycleEnd?: number | null;
  billingCycleStart?: number | null;
  cancelAt?: number | null;
  cancelAtPeriodEnd: boolean;
  downgradeBilling?: DowngradeBilling | null;
  /**
   * 是否存在降级订阅的情况
   */
  hasDowngradeBilling: boolean;
  id: string;
  plan: Plans | null;
  pricing: number;
  recurring: Recurring | null;
  stripeId?: string | null;
  usage: {
    free: CreditUsage;
    package?: CreditUsage;
    subscription?: CreditUsage;
  };
}

export interface SubscriptionDetail {
  billingCycleEnd?: number;
  billingCycleStart?: number;
  billingPaidAt?: number;
  cancelAt?: number;
  downgradeBilling?: DowngradeBilling | null;
  id: string;
  plan: Plans;
  pricing: number;
  recurring: Recurring;
  status: SubscriptionStatus;
}

export interface InvoiceItem {
  amount: number;
  createdAt: Date;
  currency: string;
  dueAt?: Date;
  id: string;
  number: string;
  paidAt?: Date;
  status: string;
  total: number;
  url: string;
}

export const OrderQuerySchema = z
  .object({
    emailOrUsernameOrUserId: z.string().optional(),
    mode: z.nativeEnum(BillingMode).optional(),
    plan: z.nativeEnum(Plans).optional(),
    range: z.tuple([z.string(), z.string()]).optional(),
    recurring: z.nativeEnum(Recurring).optional(),
    status: z.string().optional(),
  })
  .merge(CommonQuerySchema);

export type OrderQuery = z.infer<typeof OrderQuerySchema>;

export interface OrderItem {
  billingCycleEnd: number;
  billingCycleStart: number;
  billingPaidAt: number;
  cancelAt?: number;
  cancelAtPeriodEnd: boolean;
  createdAt: Date;
  currency: string;
  id: string;
  mode: BillingMode;
  plan: Plans;
  pricing: number;
  quantity: number;
  recurring: Recurring;
  status: Status;
  stripeId: string;
  updatedAt: Date;
  user: {
    avatar: string;
    email: string;
    id: string;
    name?: string;
  };
}

export const BudgetQuerySchema = z
  .object({
    emailOrUsernameOrUserId: z.string().optional(),
    id: z.string().optional(),
    key: z.string().optional(),
    range: z.tuple([z.string(), z.string()]).optional(),
    token: z.string().optional(),
  })
  .merge(CommonQuerySchema);

export type BudgetQuery = z.infer<typeof BudgetQuerySchema>;

export interface BudgetItem {
  budgetDuration?: string;
  budgetResetAt?: string;
  createdAt: string;
  expires: string;
  id: string;
  key: string;
  maxBudget: number;
  metadata: {
    [key: string]: any;
    plan?: Plans;
    tags?: string[];
  };
  models: string[];
  rpmLimit: string;
  spend: number;
  token: string;
  tpmLimit: number;
  updatedAt: string;
  user?: {
    avatar: string;
    email: string;
    id: string;
    name?: string;
  };
}
