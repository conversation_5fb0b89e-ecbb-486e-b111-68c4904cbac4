import { z } from 'zod';

export const CommonQuerySchema = z.object({
  current: z.number().optional(),
  pageSize: z.number().optional(),
});

export type CommonQuery = z.infer<typeof CommonQuerySchema>;
/**
 * 传入的结构例如:
 * {
 *  credit: 'ascend',
 *  spend: 'descend'
 * }
 */

export enum SortEnum {
  Asc = 'ascend',
  Des = 'descend',
}

export const SortQuerySchema = z
  .record(z.union([z.literal(SortEnum.Asc), z.literal(SortEnum.Des)]))
  .optional();

export type SortQuery = Record<string, SortEnum>;
