import { z } from 'zod';

import {
  AdminModelSelectItem,
  NewAdminModelItem,
  NewAdminProviderItem,
  adminProviders,
} from '@/database/instance/adminDB/schemas';

// ==================== 数据库类型定义 ====================

/**
 * 基础的 Provider 数据库类型
 */
export type BaseAdminProviderSelectItem = typeof adminProviders.$inferSelect;

/**
 * 业务逻辑相关的 AdminProviderSelectItem 类型
 * 包含解密后的 keyVaults 和完整的 settings 类型定义
 */
export type AdminProviderSelectItem = Omit<
  BaseAdminProviderSelectItem,
  'settings' | 'keyVaults'
> & {
  keyVaults: {
    apiKey?: string;
    baseURL?: string;
  };
  litellmCredential: string;
  modelCount?: number;
  settings: {
    apiKey?: string;
    baseURL?: string;
    responseAnimation?:
      | string
      | {
          speed: number;
          text: string;
        };
    sdkType?: string;
    showModelFetcher?: boolean;
    supportResponsesApi?: boolean;
  };
};

// ==================== AI Provider 相关类型 ====================

/**
 * 创建 AI Provider 数据
 */
export type CreateAiProviderData = Omit<NewAdminProviderItem, 'keyVaults'> & {
  keyVaults: {
    apiKey?: string;
    baseURL?: string;
  };
};

/**
 * 更新 AI Provider 数据
 */
export type UpdateAiProviderData = Partial<CreateAiProviderData>;

/**
 * AI Provider 详情（包含模型列表）
 */
export interface AiProviderWithModels extends AdminProviderSelectItem {
  models: AdminModelSelectItem[];
}

/**
 * 创建 AI Model 数据
 */
export type CreateAiModelData = NewAdminModelItem;

/**
 * 更新 AI Model 数据
 */
export type UpdateAiModelData = Partial<NewAdminModelItem>;

// ==================== 验证 Schema ====================

export const AiProviderSettingsSchema = z.object({
  sdkType: z.string().optional(),
  showModelFetcher: z.boolean().default(false),
  supportResponsesApi: z.boolean().default(false),
});

/**
 * 创建 AI Provider 数据验证 Schema
 */
export const CreateAiProviderSchema = z.object({
  checkModel: z.string().optional(),
  description: z.string().optional(),
  enabled: z.boolean().default(true),
  fetchOnClient: z.boolean().default(false),
  id: z.string(),
  keyVaults: z.object({
    apiKey: z.string(),
    baseURL: z.string().optional(),
  }),
  litellmCredential: z.string().optional(),
  logo: z.string().optional(),
  name: z.string().min(1).max(100).optional(),
  settings: AiProviderSettingsSchema,
  sort: z.number().int().optional(),
  source: z.enum(['builtin', 'custom']).default('custom'),
});

/**
 * 更新 AI Provider 数据验证 Schema
 */
export const UpdateAiProviderSchema = z.object({
  checkModel: z.string().optional(),
  description: z.string().optional().nullable(),
  enabled: z.boolean().optional().nullable(),
  fetchOnClient: z.boolean().optional(),
  keyVaults: z.object({
    apiKey: z.string().optional(),
    baseURL: z.string().optional(),
  }),
  litellmCredential: z.string(),
  logo: z.string().optional(),
  name: z.string().min(1).max(100).optional(),
  settings: AiProviderSettingsSchema,
  sort: z.number().int().optional(),
  source: z.enum(['builtin', 'custom']).optional(),
});

/**
 * 创建 AI Model 数据验证 Schema
 */
export const CreateAiModelSchema = z.object({
  abilities: z.any().default({}),
  config: z.any().optional(),
  contextWindowTokens: z.number().int().positive().optional(),
  description: z.string().optional(),
  displayName: z.string().min(1).max(200).optional(),
  enabled: z.boolean().default(true),
  id: z.string().min(1).max(150),
  organization: z.string().max(100).optional(),
  parameters: z.any().default({}),
  pricing: z.any().optional(),
  providerId: z.string().min(1).max(64),
  releasedAt: z.string().max(10).optional(),
  sort: z.number().int().optional(),
  source: z.enum(['remote', 'custom', 'builtin']).default('custom'),
  type: z.string().max(20).default('chat'),
});

/**
 * 更新 AI Model 数据验证 Schema
 */
export const UpdateAiModelSchema = z.object({
  abilities: z.any().optional(),
  config: z.any().optional(),
  contextWindowTokens: z.number().int().positive().optional(),
  description: z.string().optional(),
  displayName: z.string().min(1).max(200).optional(),
  enabled: z.boolean().optional(),
  organization: z.string().max(100).optional(),
  parameters: z.any().optional(),
  pricing: z.any().optional(),
  releasedAt: z.string().max(10).optional(),
  sort: z.number().int().optional(),
  source: z.enum(['remote', 'custom', 'builtin']).optional(),
  type: z.string().max(20).optional(),
});

/**
 * AI Provider 查询参数验证 Schema
 */
export const AiProviderQuerySchema = z.object({
  enabled: z.boolean().optional(),
  limit: z.number().int().positive().optional(),
  offset: z.number().int().min(0).optional(),
  source: z.enum(['builtin', 'custom']).optional(),
});

/**
 * AI Model 查询参数验证 Schema
 */
export const AiModelQuerySchema = z.object({
  enabled: z.boolean().optional(),
  limit: z.number().int().positive().optional(),
  offset: z.number().int().min(0).optional(),
  providerId: z.string().optional(),
  source: z.enum(['remote', 'custom', 'builtin']).optional(),
  type: z.string().optional(),
});

/**
 * 切换模型启用状态验证 Schema
 */
export const ToggleAiModelEnableSchema = z.object({
  enabled: z.boolean(),
  id: z.string(),
  providerId: z.string(),
  source: z.enum(['builtin', 'custom', 'remote']).optional(),
});

export type ToggleAiModelEnableParams = z.infer<typeof ToggleAiModelEnableSchema>;

// ==================== API 相关类型 ====================

/**
 * 统一的响应结构类型
 */
export type ApiResponse<T> = {
  data?: T;
  error?: string;
  success: boolean;
};

/**
 * 更新渠道信息的入参类型
 */
export const UpdateInfraProviderInputSchema = z.object({
  data: UpdateAiProviderSchema,
  providerId: z.string(),
});

export type UpdateInfraProviderInput = z.infer<typeof UpdateInfraProviderInputSchema>;

/**
 * 切换渠道状态的入参类型
 */
export const ToggleInfraProviderStatusInputSchema = z.object({
  enabled: z.boolean(),
  providerId: z.string(),
});

export type ToggleInfraProviderStatusInput = z.infer<typeof ToggleInfraProviderStatusInputSchema>;

/**
 * 更新模型信息的入参类型
 */
export const UpdateInfraModelInputSchema = z.object({
  data: UpdateAiModelSchema,
  modelId: z.string(),
  providerId: z.string(),
});

export type UpdateInfraModelInput = z.infer<typeof UpdateInfraModelInputSchema>;

/**
 * 删除模型的入参类型
 */
export const DeleteInfraModelInputSchema = z.object({
  modelId: z.string(),
  providerId: z.string(),
});

export type DeleteInfraModelInput = z.infer<typeof DeleteInfraModelInputSchema>;

/**
 * 切换模型状态的入参类型
 */
export const ToggleInfraModelStatusInputSchema = z.object({
  enabled: z.boolean(),
  modelId: z.string(),
  providerId: z.string(),
});

export type ToggleInfraModelStatusInput = z.infer<typeof ToggleInfraModelStatusInputSchema>;

/**
 * 清空渠道模型的返回数据类型
 */
export type ClearProviderModelsResult = {
  deletedCount: number;
  success: boolean;
};

/**
 * 渠道使用统计查询的入参类型
 */
export const GetProviderUsageStatsInputSchema = z.object({
  endDate: z.string().optional(),
  providerId: z.string(),
  range: z.tuple([z.string(), z.string()]).optional(),
  startDate: z.string().optional(),
});

export type GetProviderUsageStatsInput = z.infer<typeof GetProviderUsageStatsInputSchema>;

/**
 * 模型使用统计数据类型
 */
export type ModelUsageStats = {
  calls: number;
  inputTokens: number;
  model: string;
  modelId: string;
  outputTokens: number;
  spend: number;
  totalTokens: number;
};

/**
 * 渠道使用统计返回数据类型
 */
export type ProviderUsageStatsResult = {
  modelStats: ModelUsageStats[];
  totalCalls: number;
  totalInputTokens: number;
  totalOutputTokens: number;
  totalSpend: number;
  totalTokens: number;
};

// ==================== 导出数据库类型 ====================

export type AggregatedModelLitellmItem = AdminModelSelectItem & {
  litellmModelId: string;
};

export type {
  AdminModelSelectItem,
  NewAdminModelItem,
  NewAdminProviderItem,
} from '@/database/instance/adminDB/schemas';
