import { AiFullModelCard, LobeDefaultAiModelListItem } from '@/types/aiModel';

import { default as anthropic } from './anthropic';
import { default as azure } from './azure';
import { default as bedrock } from './bedrock';
import { default as gemini } from './gemini';
import { default as openai } from './openai';
import { default as vertexai } from './vertexai';

type ModelsMap = Record<string, AiFullModelCard[]>;

const buildDefaultModelList = (map: ModelsMap): LobeDefaultAiModelListItem[] => {
  let models: LobeDefaultAiModelListItem[] = [];

  Object.entries(map).forEach(([provider, providerModels]) => {
    const newModels = providerModels.map((model) => ({
      ...model,
      abilities: model.abilities ?? {},
      enabled: model.enabled || false,
      providerId: provider,
      source: 'builtin',
    }));
    models = models.concat(newModels);
  });

  return models;
};

export const LOBE_DEFAULT_MODEL_LIST = buildDefaultModelList({
  anthropic,
  azure,
  bedrock,
  gemini,
  openai,
  vertexai,
});

export { default as anthropic } from './anthropic';
export { default as azure } from './azure';
export { default as bedrock } from './bedrock';
export { default as gemini } from './gemini';
export { default as openai } from './openai';
export { default as vertexai } from './vertexai';
