import url<PERSON>oin from 'url-join';
import { z } from 'zod';

import { BRANDING_LOGO_URL, BRANDING_NAME, ORG_NAME } from '@/const/branding';
import { CLERK_URL, OFFICIAL_URL } from '@/const/url';

export const ServerConfigSchema = z.object({
  BASE_URL: z.string().url(),
  BRANDING_LOGO_URL: z.string().url(),
  BRANDING_NAME: z.string(),
  CHAT_CLERK_APPLICATION_ID: z.string(),
  CHAT_CLERK_INSTANCE_ID: z.string(),
  CHAT_ENABLE_STRIPE: z.boolean(),
  ORG_NAME: z.string(),
});

export type IServerConfig = z.infer<typeof ServerConfigSchema>;

export const DEFAULT_SERVER_CONFIG: IServerConfig = {
  BASE_URL: OFFICIAL_URL,
  BRANDING_LOGO_URL: BRANDING_LOGO_URL,
  BRANDING_NAME: BRANDING_NAME,
  CHAT_CLERK_APPLICATION_ID: '',
  CHAT_CLERK_INSTANCE_ID: '',
  CHAT_ENABLE_STRIPE: false,
  ORG_NAME: ORG_NAME,
};

export const mapServerConfigToState = (config: IServerConfig) => {
  return {
    baseUrl: config.BASE_URL,
    brandingLogoUrl: config.BRANDING_LOGO_URL,
    brandingName: config.BRANDING_NAME,
    clerkUrl: urlJoin(
      CLERK_URL,
      'apps',
      config?.CHAT_CLERK_APPLICATION_ID ?? '',
      'instances',
      config?.CHAT_CLERK_INSTANCE_ID ?? '',
    ),
    enableClerk:
      Boolean(config.CHAT_CLERK_APPLICATION_ID && config.CHAT_CLERK_INSTANCE_ID) || false,
    enableStripe: config.CHAT_ENABLE_STRIPE || false,
    isCustomBranding: config.BRANDING_NAME !== 'LobeChat',
    isCustomORG: config.ORG_NAME !== 'LobeHub',
    orgName: config.ORG_NAME || ORG_NAME,
  };
};
