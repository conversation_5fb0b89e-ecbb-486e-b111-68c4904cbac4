import { createEnv } from '@t3-oss/env-nextjs';
import { z } from 'zod';

import { clerkEnv } from '@/envs/clerk';
import { stripeEnv } from '@/envs/stripe';
import { merge } from '@/utils/merge';

import { DEFAULT_SERVER_CONFIG, mapServerConfigToState } from './schema';

const env = createEnv({
  runtimeEnv: {
    BASE_URL: process.env.BASE_URL,
    BRANDING_LOGO_URL: process.env.BRANDING_LOGO_URL,
    BRANDING_NAME: process.env.BRANDING_NAME,
    ORG_NAME: process.env.ORG_NAME,
  },

  server: {
    BASE_URL: z.string().url().optional(),
    BRANDING_LOGO_URL: z.string().url().optional(),
    BRANDING_NAME: z.string().optional(),
    ORG_NAME: z.string().optional(),
  },
});

export const getServerConfig = () => {
  const { CHAT_CLERK_APPLICATION_ID, CHAT_CLERK_INSTANCE_ID } = clerkEnv;
  const { CHAT_ENABLE_STRIPE } = stripeEnv;

  return merge(DEFAULT_SERVER_CONFIG, {
    CHAT_CLERK_APPLICATION_ID,
    CHAT_CLERK_INSTANCE_ID,
    CHAT_ENABLE_STRIPE,
    ...env,
  });
};

export const serverConfig = () => {
  const serverConfig = getServerConfig();
  return mapServerConfigToState(serverConfig);
};

export * from './schema';
