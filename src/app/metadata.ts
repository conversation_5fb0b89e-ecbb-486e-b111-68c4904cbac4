import { t } from 'i18next';
import { Metadata } from 'next';

import { serverConfig } from '@/config/serverConfig';
import { OG_URL } from '@/const/url';

export const generateMetadata = async (): Promise<Metadata> => {
  const { brandingName, brandingLogoUrl, orgName, baseUrl, isCustomBranding, isCustomORG } =
    serverConfig();
  return {
    alternates: {
      canonical: baseUrl,
    },
    appleWebApp: {
      statusBarStyle: 'black-translucent',
      title: brandingName,
    },
    icons: isCustomBranding
      ? brandingLogoUrl
      : {
          apple: '/apple-touch-icon.png?v=1',
          icon: '/favicon.ico?v=1',
          shortcut: '/favicon-32x32.ico?v=1',
        },
    metadataBase: new URL(baseUrl),
    openGraph: {
      images: [
        {
          alt: t('admin.title', { appName: brandingName }),
          height: 640,
          url: isCustomORG ? OG_URL : brandingLogoUrl,
          width: 1200,
        },
      ],
      locale: 'en-US',
      siteName: brandingName,
      title: brandingName,
      type: 'website',
      url: baseUrl,
    },
    title: {
      default: t('admin.title', { appName: brandingName }),
      template: `%s · ${brandingName}`,
    },
    twitter: {
      card: 'summary_large_image',
      images: isCustomORG ? [OG_URL] : [brandingLogoUrl],
      site: isCustomORG ? `@${orgName}` : '@lobehub',
      title: t('admin.title', { appName: brandingName }),
    },
  };
};
