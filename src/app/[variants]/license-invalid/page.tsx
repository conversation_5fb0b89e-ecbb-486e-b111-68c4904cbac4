'use client';

import { <PERSON><PERSON>, Card, Result, Typography } from 'antd';
import { AlertCircle, RefreshCw } from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import { LICENSE_STATUS } from '@/const/license';

const { Paragraph, Text } = Typography;

const handleRetry = () => {
  // 重新刷新页面，重新进行 license 验证
  location.href = '/';
};

const handleContactSupport = () => {
  // 可以根据实际情况修改支持联系方式
  window.open('mailto:<EMAIL>', '_blank');
};

export default function LicenseInvalidPage() {
  const searchParams = useSearchParams();
  const [licenseInfo, setLicenseInfo] = useState({
    expiresIn: undefined as number | undefined,
    message: '',
    status: '',
  });

  useEffect(() => {
    // 从 URL 参数中获取 license 验证失败的详细信息
    const status = searchParams.get('status') || 'invalid';
    const message = searchParams.get('message') || '许可证无效';
    const expiresIn = searchParams.get('expiresIn');

    setLicenseInfo({
      expiresIn: expiresIn ? parseInt(expiresIn, 10) : undefined,
      message,
      status,
    });
  }, [searchParams]);

  const getStatusIcon = () => {
    switch (licenseInfo.status) {
      case LICENSE_STATUS.EXPIRED: {
        return <AlertCircle style={{ color: '#ff4d4f' }} />;
      }
      case LICENSE_STATUS.DISABLED: {
        return <AlertCircle style={{ color: '#ff4d4f' }} />;
      }
      case LICENSE_STATUS.PENDING: {
        return <AlertCircle style={{ color: '#faad14' }} />;
      }
      default: {
        return <AlertCircle style={{ color: '#ff4d4f' }} />;
      }
    }
  };

  const getStatusDescription = () => {
    switch (licenseInfo.status) {
      case LICENSE_STATUS.EXPIRED: {
        return '您的许可证已过期，请联系管理员续费或更新许可证。';
      }
      case LICENSE_STATUS.DISABLED: {
        return '您的许可证已被禁用，请联系管理员获取帮助。';
      }
      case LICENSE_STATUS.PENDING: {
        return '您的许可证尚未生效，请等待生效时间或联系管理员。';
      }
      case 'error': {
        return '许可证验证过程中发生错误，请稍后重试或联系技术支持。';
      }
      default: {
        return '许可证验证失败，请检查许可证配置或联系管理员。';
      }
    }
  };

  return (
    <div
      style={{
        alignItems: 'center',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        height: '100vh',
        justifyContent: 'center',
        padding: '20px',
        width: '100vw',
      }}
    >
      <Card
        style={{
          borderRadius: '12px',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
          maxWidth: 600,
          width: '100%',
        }}
      >
        <Result
          extra={[
            <Button icon={<RefreshCw size={16} />} key="retry" onClick={handleRetry} type="primary">
              重试验证
            </Button>,
            <Button key="contact" onClick={handleContactSupport}>
              联系支持
            </Button>,
          ]}
          icon={getStatusIcon()}
          subTitle={getStatusDescription()}
          title="许可证验证失败"
        >
          <div style={{ marginTop: 24 }}>
            <Paragraph>
              <Text strong>错误信息：</Text>
              <Text type="danger">{licenseInfo.message}</Text>
            </Paragraph>

            <Paragraph style={{ color: '#666', fontSize: '14px', marginTop: 16 }}>
              请联系系统管理员或者技术支持确认许可证状态
            </Paragraph>
          </div>
        </Result>
      </Card>
    </div>
  );
}
