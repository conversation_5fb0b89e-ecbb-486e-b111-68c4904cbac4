'use client';

import { Divider, Skeleton } from 'antd';
import { useTheme } from 'antd-style';
import { memo } from 'react';
import { Flexbox } from 'react-layout-kit';

const Loading = memo<{ mobile?: boolean }>(({ mobile }) => {
  const theme = useTheme();
  return (
    <Flexbox
      align={'center'}
      gap={16}
      justify={'center'}
      paddingBlock={32}
      paddingInline={mobile ? 24 : 40}
      style={
        mobile
          ? {
              minHeight: '100%',
            }
          : {
              background: theme.colorBgContainer,
              borderRadius: theme.borderRadiusLG,
              boxShadow: `0 0 0 1px ${theme.colorBorderSecondary}`,
            }
      }
      width={mobile ? '100%' : 'min(400px, 100vw - 48px)'}
    >
      <Flexbox align={'center'} width={'100%'}>
        <Skeleton
          active
          paragraph={{
            rows: 1,
            style: {
              alignItems: 'center',
              display: 'flex',
              flexDirection: 'column',
            },
            width: '80%',
          }}
          title={false}
        />
        <Skeleton
          active
          paragraph={{
            rows: 1,
            style: {
              alignItems: 'center',
              display: 'flex',
              flexDirection: 'column',
            },
            width: '50%',
          }}
          title={false}
        />
      </Flexbox>

      <Flexbox align={'center'} gap={8} width={'100%'}>
        <Skeleton.Button active block />
      </Flexbox>
      <Divider style={{ marginBlock: 0 }} />
      <Flexbox align={'center'} gap={8} width={'100%'}>
        <Skeleton active paragraph={{ rows: 1 }} title={false} />
        <Skeleton.Button active block />
        <Skeleton.Button active block />
      </Flexbox>
      <Skeleton
        active
        paragraph={{
          rows: 1,
          style: {
            alignItems: 'center',
            display: 'flex',
            flexDirection: 'column',
          },
          width: '50%',
        }}
        title={false}
      />
    </Flexbox>
  );
});

export default Loading;
