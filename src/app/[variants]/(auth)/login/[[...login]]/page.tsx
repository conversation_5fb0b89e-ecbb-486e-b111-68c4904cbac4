import { ClerkLoaded, ClerkLoading } from '@clerk/nextjs';

import { DynamicLayoutProps } from '@/types/next';
import { RouteVariants } from '@/utils/server/routeVariants';

import Client from './Client';
import ClientLoading from './ClientLoading';

const Page = async (props: DynamicLayoutProps) => {
  const isMobile = await RouteVariants.getIsMobile(props);

  return (
    <>
      <ClerkLoading>
        <ClientLoading mobile={isMobile} />
      </ClerkLoading>
      <ClerkLoaded>
        <Client mobile={isMobile} />
      </ClerkLoaded>
    </>
  );
};

Page.displayName = 'Login';

export default Page;
