import { AuroraBackground } from '@lobehub/ui/awesome';
import { PropsWithChildren } from 'react';
import { Center } from 'react-layout-kit';

import { DynamicLayoutProps } from '@/types/next';
import { RouteVariants } from '@/utils/server/routeVariants';

import Background from './features/Background';

const Layout = async ({ children, ...props }: PropsWithChildren<DynamicLayoutProps>) => {
  const isMobile = await RouteVariants.getIsMobile(props);

  if (isMobile) return children;

  return (
    <AuroraBackground height={'100%'} padding={32} style={{ position: 'relative' }} width={'100%'}>
      <Background>
        <Center gap={120} height={'100%'} horizontal style={{ zIndex: 100 }} width={'100%'}>
          {children}
        </Center>
      </Background>
    </AuroraBackground>
  );
};

Layout.displayName = 'AuthLayout';

export default Layout;
