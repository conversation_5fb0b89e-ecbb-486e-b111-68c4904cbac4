'use client';

import { useTheme } from 'antd-style';
import Link from 'next/link';
import { PropsWithChildren, memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Center, Flexbox } from 'react-layout-kit';

import { ProductLogo } from '@/components/Branding';
import { DISCORD, EMAIL_SUPPORT } from '@/const/url';
import LangButton from '@/features/User/UserPanel/LangButton';
import ThemeButton from '@/features/User/UserPanel/ThemeButton';

const COPYRIGHT = `© ${new Date().getFullYear()} LobeHub, LLC`;

const Background = memo<PropsWithChildren>(({ children }) => {
  const theme = useTheme();
  const { t } = useTranslation('subscription');
  return (
    <>
      <Flexbox align={'center'} horizontal justify={'space-between'} width={'100%'}>
        <Flexbox align={'center'} horizontal>
          <ProductLogo extra={'Admin'} size={36} type={'combine'} />
        </Flexbox>
        <Flexbox gap={8} horizontal>
          <LangButton placement={'bottomRight'} />
          <ThemeButton placement={'bottomRight'} />
        </Flexbox>
      </Flexbox>
      <Center height={'100%'} width={'100%'}>
        {children}
      </Center>
      <Flexbox
        align={'center'}
        gap={8}
        horizontal
        style={{ color: theme.colorTextDescription }}
        width={'100%'}
      >
        <div>{COPYRIGHT}</div>
        <span>·</span>
        <Link href={DISCORD} style={{ color: theme.colorTextSecondary }} target={'_blank'}>
          {t('qa.support.community')}
        </Link>
        <span>·</span>
        <Link
          href={`mailto:${EMAIL_SUPPORT}`}
          style={{ color: theme.colorTextSecondary }}
          target={'_blank'}
        >
          {t('qa.support.email')}
        </Link>
      </Flexbox>
    </>
  );
});

export default Background;
