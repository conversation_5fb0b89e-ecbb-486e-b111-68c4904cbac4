import { ThemeAppearance } from 'antd-style';
import { ResolvingViewport } from 'next';
import { NuqsAdapter } from 'nuqs/adapters/next/app';
import { ReactNode } from 'react';
import { isRtlLang } from 'rtl-detect';

import NProgress from '@/components/NProgress';
import { DEFAULT_LANG } from '@/const/locale';
import AuthProvider from '@/layouts/AuthProvider';
import GlobalProvider from '@/layouts/GlobalProvider';
import { DynamicLayoutProps } from '@/types/next';
import { RouteVariants } from '@/utils/server/routeVariants';

interface RootLayoutProps extends DynamicLayoutProps {
  children: ReactNode;
}

const RootLayout = async ({ children, params }: RootLayoutProps) => {
  const { variants } = await params;

  const { locale, isMobile, theme } = RouteVariants.deserializeVariants(variants);

  const direction = isRtlLang(locale) ? 'rtl' : 'ltr';
  return (
    <html dir={direction} lang={locale} suppressHydrationWarning>
      <body>
        <NuqsAdapter>
          <GlobalProvider appearance={theme} isMobile={isMobile} locale={locale}>
            <NProgress />
            <AuthProvider>{children}</AuthProvider>
          </GlobalProvider>
        </NuqsAdapter>
      </body>
    </html>
  );
};

export { generateMetadata } from '../metadata';

export const generateViewport = async (props: DynamicLayoutProps): ResolvingViewport => {
  const isMobile = await RouteVariants.getIsMobile(props);

  const dynamicScale = isMobile ? { maximumScale: 1, userScalable: false } : {};

  return {
    ...dynamicScale,
    initialScale: 1,
    minimumScale: 1,
    themeColor: [
      { color: '#f8f8f8', media: '(prefers-color-scheme: light)' },
      { color: '#000', media: '(prefers-color-scheme: dark)' },
    ],
    viewportFit: 'cover',
    width: 'device-width',
  };
};

RootLayout.displayName = 'RootLayout';

export default RootLayout;

// add static params to make most pages static
export const generateStaticParams = () => {
  if (process.env.NODE_ENV !== 'production') return [];

  const themes: ThemeAppearance[] = ['dark', 'light'];
  const mobileOptions = [true, false];
  // only static for several page, other go to dynamic
  const staticLocales = [DEFAULT_LANG, 'zh-CN'];

  const variants: { variants: string }[] = [];

  for (const locale of staticLocales) {
    for (const theme of themes) {
      for (const isMobile of mobileOptions) {
        variants.push({
          variants: RouteVariants.serializeVariants({ isMobile, locale, theme }),
        });
      }
    }
  }

  return variants;
};
