'use client';

import { ProColumns, ProTableProps } from '@ant-design/pro-components';
import { SiStripe } from '@icons-pack/react-simple-icons';
import { ActionIcon, CopyButton, Icon, Modal, Tag, Tooltip } from '@lobehub/ui';
import { Popover, Progress } from 'antd';
import { useTheme } from 'antd-style';
import { Clock3Icon, MaximizeIcon } from 'lucide-react';
import Link from 'next/link';
import { parseAsInteger, useQueryState } from 'nuqs';
import { memo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';
import urlJoin from 'url-join';

import ModalContent from '@/app/[variants]/(main)/users/[slug]/features/Subscription/ModalContent';
import { calcProgress } from '@/app/[variants]/(main)/users/[slug]/features/Subscription/calcProgress';
import { useDateRangePresets } from '@/components/DateRangePicker';
import Table from '@/components/Table';
import UserInTable from '@/components/UserInTable';
import { STRIPE_URL } from '@/const/url';
import PlanIcon from '@/features/PlanIcon';
import { trpcClient } from '@/libs/trpc/client';
import { serverConfigSelectors, useServerConfigStore } from '@/store/serverConfig';
import { SortQuery } from '@/types/query';
import { BillingMode, OrderItem, Plans, Recurring } from '@/types/subscription';
import { formatDate, formatDateWithTime, formatPricingWithCurrency } from '@/utils/format';

const request: ProTableProps<any, any>['request'] = async (params, sorts) =>
  trpcClient.subscript.getOrders.query({
    params,
    sorts: sorts as SortQuery,
  });

const OrderTable = memo(() => {
  const theme = useTheme();
  const { t } = useTranslation(['user', 'subscription']);
  const [open, setOpen] = useState(false);
  const [item, setItem] = useState<any>(null);
  const { enableStripe } = useServerConfigStore(serverConfigSelectors.config);
  const defaultPresets = useDateRangePresets() || [];

  const [currentPage, setCurrentPage] = useQueryState(
    'current',
    parseAsInteger.withDefault(1).withOptions({ clearOnDefault: true }),
  );
  const [pageSize, setPageSize] = useQueryState(
    'pageSize',
    parseAsInteger.withDefault(10).withOptions({ clearOnDefault: true }),
  );

  const columns: ProColumns<OrderItem>[] = [
    {
      dataIndex: 'range',
      fieldProps: {
        presets: defaultPresets,
      },
      hideInTable: true,
      key: 'range',
      title: t('table.columns.createdAt'),
      valueType: 'dateRange',
    },
    {
      dataIndex: 'emailOrUsernameOrUserId',
      hideInTable: true,
      key: 'emailOrUsernameOrUserId',
      title: t('table.columns.emailOrUsernameOrUserId'),
    },
    {
      dataIndex: 'user',
      hideInSearch: true,
      key: 'user',
      render: (_, entity) => {
        const { avatar, email, name, id } = entity.user || {};
        return <UserInTable avatar={avatar || id} email={email} id={id} username={name || id} />;
      },
      title: t('table.columns.username'),
      width: 250,
    },
    {
      dataIndex: 'status',
      key: 'status',
      render: (_, entity) => {
        const isActive = entity?.status === 0;
        return (
          <Flexbox gap={8} horizontal>
            {isActive ? (
              <Tag
                color={'success'}
                style={{
                  margin: 0,
                }}
              >
                {t('subscription.status.active')}
              </Tag>
            ) : (
              <Tag
                style={{
                  background: theme.colorFillTertiary,
                  color: theme.colorTextSecondary,
                  margin: 0,
                }}
              >
                {t('subscription.status.inactive')}
              </Tag>
            )}
            {isActive && entity?.cancelAt && (
              <Tag
                icon={<Icon icon={Clock3Icon} />}
                style={{
                  background: theme.colorFillTertiary,
                  color: theme.colorTextSecondary,
                  margin: 0,
                }}
              >
                {t('subscription.cancelAt')}: {formatDate(entity?.cancelAt, 1000)}
              </Tag>
            )}
          </Flexbox>
        );
      },
      title: t('subscription.status.title'),
      valueEnum: {
        0: t('subscription.status.active'),
        1: t('subscription.status.inactive'),
      },
      valueType: 'select',
      width: 200,
    },
    {
      dataIndex: 'plan',
      key: 'plan',
      render: (_, entity) => {
        const isActive = entity?.status === 0;
        const percent = isActive
          ? calcProgress(entity.billingCycleStart, entity.billingCycleEnd)
          : 0;
        return (
          <Flexbox align={'center'} gap={12} horizontal>
            <Tooltip
              title={[
                formatDate(entity.billingCycleStart, 1000),
                formatDate(entity.billingCycleEnd, 1000),
              ].join(' ~ ')}
            >
              {isActive ? (
                <Progress
                  percent={percent}
                  showInfo={false}
                  size={24}
                  strokeWidth={16}
                  type="circle"
                />
              ) : (
                <div
                  style={{
                    border: `2px dashed ${theme.colorFillSecondary}`,
                    borderRadius: 24,
                    height: 24,
                    width: 24,
                  }}
                />
              )}
            </Tooltip>
            <PlanIcon plan={entity.plan} size={24} type={'combine'} />
          </Flexbox>
        );
      },
      title: t('subscription.plan'),
      width: 200,
      /* eslint-disable sort-keys-fix/sort-keys-fix  */
      valueEnum: {
        [Plans.Hobby]: {
          text: <PlanIcon plan={Plans.Hobby} size={20} type={'combine'} />,
        },
        [Plans.Starter]: {
          text: <PlanIcon plan={Plans.Starter} size={20} type={'combine'} />,
        },
        [Plans.Premium]: {
          text: <PlanIcon plan={Plans.Premium} size={20} type={'combine'} />,
        },
        [Plans.Ultimate]: {
          text: <PlanIcon plan={Plans.Ultimate} size={20} type={'combine'} />,
        },
      },
      /* eslint-enable */
    },
    {
      dataIndex: 'mode',
      key: 'mode',
      render: (_, entity) => (
        <Tag>
          {entity.mode === BillingMode.Subscription
            ? t('plans.subscribe', { ns: 'subscription' })
            : t('plans.navs.payonce', { ns: 'subscription' })}
        </Tag>
      ),
      title: t('subscription.mode'),
      valueEnum: {
        [BillingMode.Subscription]: {
          text: t('plans.subscribe', { ns: 'subscription' }),
        },
        [BillingMode.Payment]: {
          text: t('plans.navs.payonce', { ns: 'subscription' }),
        },
      },
      width: 150,
    },
    {
      dataIndex: 'recurring',
      key: 'recurring',
      render: (_, entity) => (
        <Tag>
          {entity.mode === BillingMode.Subscription
            ? entity.recurring === Recurring.Monthly
              ? t('plans.navs.monthly', { ns: 'subscription' })
              : t('plans.navs.yearly', { ns: 'subscription' })
            : entity.recurring === Recurring.Monthly
              ? t('recurring.month', { ns: 'subscription', quantity: entity.quantity })
              : t('recurring.month', { ns: 'subscription', quantity: 12 })}
        </Tag>
      ),
      title: t('subscription.recurring'),
      valueEnum: {
        [Recurring.Monthly]: {
          text: t('plans.navs.monthly', { ns: 'subscription' }),
        },
        [Recurring.Yearly]: {
          text: t('plans.navs.yearly', { ns: 'subscription' }),
        },
      },
      width: 150,
    },
    {
      dataIndex: 'pricing',
      hideInSearch: true,
      key: 'pricing',
      render: (_, entity) => (
        <Flexbox align={'baseline'} gap={8} horizontal>
          <span
            style={{
              color: theme.colorTextDescription,
            }}
          >
            {entity.currency.toUpperCase()}
          </span>
          <span
            style={{
              fontWeight: 500,
            }}
          >
            {formatPricingWithCurrency(entity.pricing, entity.currency)}
          </span>
        </Flexbox>
      ),
      sorter: true,
      title: t('subscription.pricing'),
      width: 150,
    },
    {
      dataIndex: 'billingPaidAt',
      hideInSearch: true,
      key: 'billingPaidAt',
      render: (_, entity) => formatDateWithTime(entity.billingPaidAt, 1000),
      sorter: true,
      title: t('subscription.billingPaidAt'),
      valueType: 'dateTime',
      width: 180,
    },
    {
      dataIndex: 'createdAt',
      hideInSearch: true,
      key: 'createdAt',
      sorter: true,
      title: t('table.columns.createdAt'),
      valueType: 'dateTime',
      width: 180,
    },

    {
      dataIndex: 'billingCycleEnd',
      hideInSearch: true,
      key: 'billingCycleEnd',
      render: (_, entity) =>
        entity.mode === BillingMode.Payment || entity.status !== 0 ? (
          <span style={{ color: theme.colorTextDescription }}>
            {t('subscription.noNextBillingDate')}
          </span>
        ) : (
          formatDateWithTime(entity.billingCycleEnd, 1000)
        ),
      title: t('subscription.nextBillingDate'),
      valueType: 'dateTime',
      width: 180,
    },
    {
      align: 'right',
      dataIndex: 'id',
      fixed: 'right',
      hideInSearch: true,
      key: 'detail',
      render: (_, entity) => (
        <Flexbox gap={8} horizontal justify={'flex-end'}>
          {enableStripe ? (
            <Link href={urlJoin(STRIPE_URL, 'subscriptions', entity.id)} target={'_blank'}>
              <ActionIcon
                active
                fill={theme.colorText}
                icon={SiStripe}
                size={'small'}
                title={t('actions.stripSubscriptions')}
              />
            </Link>
          ) : (
            <Popover
              arrow={false}
              content={
                <span
                  style={{
                    fontFamily: theme.fontFamilyCode,
                  }}
                >
                  {entity.id}
                </span>
              }
              placement={'topRight'}
              trigger={['hover']}
            >
              <CopyButton active content={entity.id} size={'small'} />
            </Popover>
          )}
          <ActionIcon
            active
            icon={MaximizeIcon}
            onClick={() => {
              setItem(entity);
              setOpen(true);
            }}
            size={'small'}
            title={t('subscription.detail')}
          />
        </Flexbox>
      ),
      title: '',
      width: 56,
    },
  ];

  return (
    <>
      <Table<OrderItem>
        columns={columns}
        headerTitle={t('subscription.title')}
        pagination={{
          current: currentPage,
          onChange: (page) => {
            setCurrentPage(page);
          },
          onShowSizeChange: (current, size) => {
            setCurrentPage(current);
            setPageSize(size);
          },
          pageSize,
        }}
        request={request}
        rowKey={'id'}
      />
      <Modal onCancel={() => setOpen(false)} open={open} title={t('subscription.title')}>
        {item && <ModalContent data={item} />}
      </Modal>
    </>
  );
});

export default OrderTable;
