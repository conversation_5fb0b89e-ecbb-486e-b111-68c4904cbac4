'use client';

import { ProColumns, ProTableProps } from '@ant-design/pro-components';
import { ActionIcon, CopyButton, Icon, Modal, Tag } from '@lobehub/ui';
import { Popover, Progress, Typography } from 'antd';
import { useTheme } from 'antd-style';
import dayjs from 'dayjs';
import { Clock3Icon, MaximizeIcon } from 'lucide-react';
import { parseAsInteger, useQueryState } from 'nuqs';
import { memo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import { useDateRangePresets } from '@/components/DateRangePicker';
import Table from '@/components/Table';
import UserInTable from '@/components/UserInTable';
import PlanIcon from '@/features/PlanIcon';
import { trpcClient } from '@/libs/trpc/client';
import { SortQuery } from '@/types/query';
import { BudgetItem } from '@/types/subscription';
import { formatDate, formatIntergerNumber } from '@/utils/format';
import { today } from '@/utils/time';

import ModalContent from './ModalContent';

const request: ProTableProps<any, any>['request'] = async (params, sorts) =>
  trpcClient.subscript.getBudgets.query({
    params,
    sorts: sorts as SortQuery,
  });

const BudgetTable = memo(() => {
  const theme = useTheme();
  const { t } = useTranslation(['user', 'subscription']);
  const [open, setOpen] = useState(false);
  const [item, setItem] = useState<any>(null);
  const defaultPresets = useDateRangePresets() || [];

  const [currentPage, setCurrentPage] = useQueryState(
    'current',
    parseAsInteger.withDefault(1).withOptions({ clearOnDefault: true }),
  );
  const [pageSize, setPageSize] = useQueryState(
    'pageSize',
    parseAsInteger.withDefault(10).withOptions({ clearOnDefault: true }),
  );

  const columns: ProColumns<BudgetItem>[] = [
    {
      dataIndex: 'user',
      hideInSearch: true,
      key: 'user',
      render: (_, entity) => {
        if (!entity.user?.id) return '--';
        const { avatar, email, name, id } = entity.user || {};
        return <UserInTable avatar={avatar || id} email={email} id={id} username={name || id} />;
      },
      title: t('table.columns.username'),
      width: 250,
    },
    {
      dataIndex: 'range',
      fieldProps: {
        presets: defaultPresets,
      },
      hideInTable: true,
      key: 'range',
      title: t('table.columns.createdAt'),
      valueType: 'dateRange',
    },
    {
      dataIndex: 'emailOrUsernameOrUserId',
      hideInTable: true,
      key: 'emailOrUsernameOrUserId',
      title: t('table.columns.emailOrUsernameOrUserId'),
    },

    {
      dataIndex: 'id',
      hideInTable: true,
      key: 'id',
      title: 'ID',
    },

    {
      dataIndex: 'token',
      hideInTable: true,
      key: 'token',
      title: 'Token',
    },
    {
      dataIndex: 'spend',
      hideInSearch: true,
      key: 'spend',
      render: (_, { spend, expires, budgetResetAt, maxBudget }) => {
        const isExpired = dayjs(expires).isBefore(today());
        const isOver = spend >= (maxBudget || 0);
        return (
          <Flexbox align={'center'} gap={8} horizontal>
            <Progress
              percent={(spend / (maxBudget || 1)) * 100}
              showInfo={false}
              size={24}
              strokeColor={isOver ? theme.colorWarning : theme.colorInfo}
              strokeWidth={16}
              type="circle"
            />
            <div>{formatIntergerNumber((spend / (maxBudget || 1)) * 100)}%</div>
            {!isExpired && (
              <Tag
                color={'success'}
                style={{
                  margin: 0,
                }}
              >
                {t('budget.status.active')}
              </Tag>
            )}
            {isOver && (
              <Tag
                color={'warning'}
                style={{
                  margin: 0,
                }}
              >
                {t('budget.status.over')}
              </Tag>
            )}
            {isExpired && (
              <Tag
                style={{
                  background: theme.colorFillTertiary,
                  color: theme.colorTextSecondary,
                  margin: 0,
                }}
              >
                {t('budget.status.expired')}
              </Tag>
            )}
            {!isExpired && budgetResetAt && (
              <Tag
                icon={<Icon icon={Clock3Icon} />}
                style={{
                  background: theme.colorFillTertiary,
                  color: theme.colorTextSecondary,
                  margin: 0,
                }}
              >
                {t('budget.budgetResetAt')}: {formatDate(budgetResetAt)}
              </Tag>
            )}
          </Flexbox>
        );
      },
      title: t('budget.spend'),
      width: 300,
    },

    {
      dataIndex: 'maxBudget',
      hideInSearch: true,
      key: 'maxBudget',
      render: (_, { maxBudget, spend }) => (
        <div>
          <span style={{ fontWeight: 'bold' }}>{formatIntergerNumber(spend * 1_000_000)}</span>
          <Typography.Text type={'secondary'}>
            {['', '/', formatIntergerNumber(maxBudget * 1_000_000)].join(' ')}
          </Typography.Text>
        </div>
      ),
      title: t('budget.used'),
      width: 150,
    },
    {
      dataIndex: 'budgetDuration',
      hideInSearch: true,
      key: 'budgetDuration',
      render: (_, { budgetDuration }) => (
        <Tag>{budgetDuration ? budgetDuration.toUpperCase() : t('budget.oneTime')}</Tag>
      ),
      title: t('budget.budgetDuration'),
      width: 100,
    },
    {
      dataIndex: 'metadata',
      hideInSearch: true,
      key: 'metadata',
      render: (_, { metadata: { plan, tags } = {} }) => (
        <Flexbox align={'center'} gap={8} horizontal justify={'flex-start'}>
          {plan && <PlanIcon plan={plan} type={'tag'} />}
          {tags &&
            tags?.map((tag: string) => (
              <Tag key={tag} style={{ margin: 0 }}>
                {tag}
              </Tag>
            ))}
        </Flexbox>
      ),
      title: t('budget.metadata'),
      width: 120,
    },
    {
      dataIndex: 'key',
      render: (text) => (
        <Flexbox gap={4} horizontal>
          <span style={{ color: theme.colorTextSecondary, fontFamily: theme.fontFamilyCode }}>
            {text}
          </span>
          <CopyButton content={text as string} size={'small'} />
        </Flexbox>
      ),
      title: t('budget.keyAlias'),
    },
    {
      dataIndex: 'createdAt',
      hideInSearch: true,
      key: 'createdAt',
      sorter: true,
      title: t('budget.createdAt'),
      valueType: 'dateTime',
      width: 200,
    },
    {
      dataIndex: 'expires',
      hideInSearch: true,
      key: 'expires',
      sorter: true,
      title: t('budget.expires'),
      valueType: 'dateTime',
      width: 200,
    },

    {
      align: 'right',
      fixed: 'right',
      hideInSearch: true,
      key: 'tools',
      render: (_, entity) => (
        <Flexbox gap={8} horizontal justify={'flex-end'}>
          <Popover
            arrow={false}
            content={
              <span
                style={{
                  fontFamily: theme.fontFamilyCode,
                }}
              >
                {entity.token}
              </span>
            }
            placement={'topRight'}
            trigger={['hover']}
          >
            <CopyButton active content={entity.token} size={'small'} />
          </Popover>
          <ActionIcon
            active
            icon={MaximizeIcon}
            onClick={() => {
              setItem(entity);
              setOpen(true);
            }}
            size={'small'}
            title={t('budget.detail')}
          />
        </Flexbox>
      ),
      width: 56,
    },
  ];

  return (
    <>
      <Table<BudgetItem>
        columns={columns}
        headerTitle={t('budget.title')}
        pagination={{
          current: currentPage,
          onChange: (page) => {
            setCurrentPage(page);
          },
          onShowSizeChange: (current, size) => {
            setCurrentPage(current);
            setPageSize(size);
          },
          pageSize,
        }}
        request={request}
        rowKey={'token'}
      />
      <Modal
        onCancel={() => setOpen(false)}
        onOk={() => setOpen(false)}
        open={open}
        title={t('budget.title')}
      >
        {item && <ModalContent data={item} />}
      </Modal>
    </>
  );
});

export default BudgetTable;
