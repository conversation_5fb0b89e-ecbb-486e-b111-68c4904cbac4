import { ModelTag } from '@lobehub/icons';
import { Tag } from '@lobehub/ui';
import { Progress } from 'antd';
import { useTheme } from 'antd-style';
import dayjs from 'dayjs';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import CreatedTime from '@/components/CreatedTime';
import Descriptions from '@/components/Descriptions';
import PlanIcon from '@/features/PlanIcon';
import { BudgetItem } from '@/types/subscription';
import { formatDateWithTime, formatIntergerNumber } from '@/utils/format';
import { today } from '@/utils/time';
import { withNoSSR } from '@/utils/withNoSSR';

interface ModalContentProps {
  data: BudgetItem;
}

const ModalContent = memo<ModalContentProps>(({ data }) => {
  const { t } = useTranslation('user');
  const theme = useTheme();
  const isExpired = dayjs(data.expires).isBefore(today());
  const isOver = data.spend >= data.maxBudget;

  return (
    <Flexbox gap={16} key={data.id}>
      <Descriptions
        bordered
        items={[
          {
            copyable: true,
            key: 'key',
            label: 'KEY',
            style: {
              fontFamily: theme.fontFamilyCode,
            },
            value: data.key,
          },
          {
            copyable: true,
            key: 'token',
            label: 'TOKEN',
            style: {
              fontFamily: theme.fontFamilyCode,
            },
            value: data.token,
          },
          {
            key: 'spend',
            label: t('budget.spend'),
            value: (
              <Flexbox align={'center'} gap={10} horizontal>
                <Progress
                  percent={(data.spend / data.maxBudget) * 100}
                  showInfo={false}
                  size={24}
                  strokeColor={isOver ? theme.colorWarning : theme.colorInfo}
                  strokeWidth={16}
                  type="circle"
                />
                <div>{formatIntergerNumber((data.spend / data.maxBudget) * 100)}%</div>
              </Flexbox>
            ),
          },
          {
            key: 'maxBudget',
            label: t('budget.used'),
            value: (
              <div>
                <span style={{ fontWeight: 'bold' }}>
                  {formatIntergerNumber(data.spend * 1_000_000)}
                </span>
                {['', '/', formatIntergerNumber(data.maxBudget * 1_000_000)].join(' ')}
              </div>
            ),
          },
          {
            key: 'status',
            label: t('budget.status.title'),
            value: (
              <Flexbox gap={8} horizontal>
                {!isExpired && (
                  <Tag
                    color={'success'}
                    style={{
                      margin: 0,
                    }}
                  >
                    {t('budget.status.active')}
                  </Tag>
                )}
                {isOver && (
                  <Tag
                    color={'warning'}
                    style={{
                      margin: 0,
                    }}
                  >
                    {t('budget.status.over')}
                  </Tag>
                )}
                {isExpired && (
                  <Tag
                    style={{
                      background: theme.colorFillTertiary,
                      color: theme.colorTextSecondary,
                      margin: 0,
                    }}
                  >
                    {t('budget.status.expired')}
                  </Tag>
                )}
              </Flexbox>
            ),
          },
        ]}
      />
      <Descriptions
        bordered
        items={[
          {
            key: 'budgetDuration',
            label: t('budget.budgetDuration'),
            value: data?.budgetDuration?.toUpperCase() || t('budget.oneTime'),
          },
          {
            key: 'createdAt',
            label: t('budget.createdAt'),
            value: formatDateWithTime(data?.createdAt),
          },
          {
            key: 'budgetResetAt',
            label: t('budget.budgetResetAt'),
            value: formatDateWithTime(data?.budgetResetAt),
          },
          {
            key: 'expires',
            label: t('budget.expires'),
            value: formatDateWithTime(data?.expires),
          },
        ]}
      />
      <Descriptions
        bordered
        items={[
          {
            key: 'metadata',
            label: t('budget.metadata'),
            value: (
              <Flexbox align={'center'} gap={8} horizontal justify={'flex-start'}>
                {data?.metadata?.plan && <PlanIcon plan={data.metadata.plan} type={'tag'} />}
                {data?.metadata?.tags &&
                  data.metadata.tags?.map((tag: string) => (
                    <Tag key={tag} style={{ margin: 0 }}>
                      {tag}
                    </Tag>
                  ))}
              </Flexbox>
            ),
          },
          {
            key: 'rpm/tpm',
            label: 'RPM / TPM',
            value: `${data?.rpmLimit || '--'} / ${data?.tpmLimit || '--'}`,
          },
          {
            key: 'models',
            label: t('budget.models'),
            value: (
              <Flexbox align={'center'} gap={8} horizontal justify={'flex-start'}>
                {data?.models.length > 0
                  ? data.models.map((model) => (
                      <ModelTag key={model} model={model} style={{ margin: 0 }} />
                    ))
                  : '--'}
              </Flexbox>
            ),
          },
        ]}
      />
      <CreatedTime
        createdAt={formatDateWithTime(data?.createdAt)}
        updatedAt={formatDateWithTime(data?.updatedAt)}
      />
    </Flexbox>
  );
});

export default withNoSSR(ModalContent);
