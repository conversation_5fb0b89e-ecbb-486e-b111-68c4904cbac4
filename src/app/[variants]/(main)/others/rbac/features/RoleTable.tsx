'use client';

import { ProColumns, ProTable } from '@ant-design/pro-components';
import { Icon } from '@lobehub/ui';
import { Button, Dropdown, MenuProps, Modal, Row, Spin, Typography, message } from 'antd';
import { createGlobalStyle, createStyles, useTheme } from 'antd-style';
import dayjs from 'dayjs';
import {
  Ban,
  CircleCheckBig,
  Copy,
  Edit,
  Ellipsis,
  Eye,
  Info,
  Lock,
  Plus,
  Trash,
} from 'lucide-react';
import { memo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { trpcQuery } from '@/libs/trpc/client';

import ConfigDrawer from './ConfigDrawer';
import RoleModal from './RoleModal';
import UserDrawer from './UserDrawer';

const RoleTable = memo(() => {
  const { t } = useTranslation('rbac');
  const [newOpen, setNewOpen] = useState<{ open: boolean; role?: any }>({ open: false });
  const [configOpen, setConfigOpen] = useState<{
    open: boolean;
    roleId?: string;
    roleName?: string;
  }>({ open: false });
  const [userOpen, setUserOpen] = useState<{ open: boolean; role?: any }>({ open: false });
  const { Text } = Typography;
  const theme = useTheme();
  const Global = createGlobalStyle`
  .ant-pro-card .ant-pro-card-body {
   padding-inline: 0;
  }
`;
  const useStyles = createStyles(({ css }) => {
    return {
      activeState: css`
        cursor: pointer;

        position: relative;

        width: 50px;
        height: 24px;
        padding-block: 0;
        padding-inline: 16px 6px;
        border-radius: 4px;

        font-size: 14px;
        line-height: 24px;
        color: ${theme.colorSuccess};

        background: rgba(0, 211, 91, 10%);

        &::after {
          content: '';

          position: absolute;
          inset-block-start: 9px;
          inset-inline-start: 5px;

          width: 6px;
          height: 6px;
          border-radius: 100%;

          background: ${theme.colorSuccess};
        }
      `,
      defaultState: css`
        cursor: pointer;

        position: relative;

        width: 50px;
        height: 24px;
        padding-block: 0;
        padding-inline: 16px 6px;
        border-radius: 4px;

        font-size: 14px;
        line-height: 24px;
        color: ${theme.colorText};

        background: ${theme.colorBgContainerDisabled};

        &::after {
          content: '';

          position: absolute;
          inset-block-start: 9px;
          inset-inline-start: 5px;

          width: 6px;
          height: 6px;
          border-radius: 100%;

          background: ${theme.colorText};
        }
      `,
      optBtn: css`
        height: 28px;
        padding-block: 0;
        padding-inline: 8px;
        line-height: 28px;
      `,
      table: css``,
    };
  });
  const { styles } = useStyles();
  const [copyLoading, setCopyLoading] = useState<number>();
  const createRoleMutation = trpcQuery.rbac.createRole.useMutation();
  const { data: roles, refetch, isLoading, isFetching } = trpcQuery.rbac.getAllRoles.useQuery();
  type RoleType = NonNullable<typeof roles>[number];
  const deleteRoleMutation = trpcQuery.rbac.deleteRole.useMutation({
    onError: (error: any) => {
      message.error(t('table.action.deleteFailed') + ':' + error.message);
    },
    onSuccess: () => {
      message.success(t('table.action.deleteSuccess'));
      refetch();
    },
  });

  const toggleRoleMutation = trpcQuery.rbac.toggleRoleActive.useMutation({
    onError: (error: any) => {
      message.error(t('table.action.switchFailed') + ':' + error.message);
    },
    onSuccess: () => {
      message.success(t('table.action.switchSuccess'));
      refetch();
    },
  });
  // 切换角色状态
  const handleToggleActive = (roleId: number) => {
    toggleRoleMutation.mutate({ roleId });
  };
  // 用户管理
  const handleManageUsers = (role: RoleType) => {
    setUserOpen({ open: true, role: role });
  };
  // 复制角色名称
  const handleCopy = async (record: RoleType) => {
    if (copyLoading) return message.error(t('table.action.waitCopy'));
    try {
      setCopyLoading(record.id);
      const roleData = {
        description: record.description || '',
        displayName: record.displayName + `-${t('table.action.copy')}`,
        // 使用真实的配额数据，如果还在加载则使用默认值
        isActive: false,
        name: record.name + `-${t('table.action.copy')}`,
        quota: record?.quota
          ? {
              fileMb: record?.quota?.fileMb ?? 0,
              tokenBudget: record?.quota?.tokenBudget ?? 0,
              vectorCount: record?.quota?.vectorCount ?? 0,
            }
          : undefined,
      };

      await createRoleMutation.mutateAsync({
        ...roleData,
      });
      refetch();
      message.success(t('table.action.copySuccess'));
    } catch (e: any) {
      message.error(t('table.action.copyFailed') + ':' + e.message);
    } finally {
      setCopyLoading(undefined);
    }
  };
  // 下拉操作项
  const getItems = (record: RoleType) => {
    const items: MenuProps['items'] = [
      {
        disabled: copyLoading === record.id,
        key: 'copy',
        label: (
          <div>
            <Icon icon={Copy} size={{ size: 14 }} style={{ marginRight: 8 }} />
            {t('table.action.copy')}
          </div>
        ),
        onClick: () => {
          handleCopy(record);
        },
      },
      {
        // @ts-ignore
        hidden: !!record?.isActive,
        key: 'edit',
        label: (
          <div>
            <Icon icon={Edit} size={{ size: 14 }} style={{ marginRight: 8 }} />
            {t('table.action.edit')}
          </div>
        ),
        onClick: () => {
          setNewOpen({ open: true, role: record });
        },
      },
      {
        // @ts-ignore
        hidden: !!record?.isActive,
        key: 'open',
        label: (
          <div>
            <Icon icon={CircleCheckBig} size={{ size: 14 }} style={{ marginRight: 8 }} />
            {t('table.action.open')}
          </div>
        ),
        onClick: () => {
          Modal.confirm({
            content: t('table.action.openConfirm'),
            icon: (
              <Icon
                icon={Info}
                size={{ size: 18 }}
                style={{ color: theme.colorText, marginTop: 2 }}
              />
            ),
            okText: t('table.action.confirm'),
            onOk: () => {
              handleToggleActive(record.id);
            },
            width: 300,
          });
        },
      },
      {
        // @ts-ignore
        hidden: !record?.isActive,

        key: 'close',
        label: (
          <div>
            <Icon icon={Ban} size={{ size: 14 }} style={{ marginRight: 8 }} />
            {t('table.action.close')}
          </div>
        ),
        onClick: () => {
          Modal.confirm({
            content: t('table.action.closeConfirm'),
            icon: (
              <Icon
                icon={Info}
                size={{ size: 18 }}
                style={{ color: theme.colorText, marginTop: 2 }}
              />
            ),
            okButtonProps: {
              danger: true,
            },
            okText: t('table.action.close'),
            onOk: () => {
              handleToggleActive(record.id);
            },
            width: 300,
          });
        },
      },
      {
        // @ts-ignore
        hidden: !!record?.isActive || record.userCount > 0,

        key: 'delete',
        label: (
          <div style={{ color: theme.colorError }}>
            <Icon icon={Trash} size={{ size: 14 }} style={{ marginRight: 8 }} />
            {t('table.action.delete')}
          </div>
        ),
        onClick: () => {
          Modal.confirm({
            content: t('table.action.deleteConfirm'),
            icon: (
              <Icon
                icon={Info}
                size={{ size: 18 }}
                style={{ color: theme.colorText, marginTop: 2 }}
              />
            ),
            okButtonProps: {
              danger: true,
            },
            okText: t('table.action.delete'),
            onOk: () => {
              deleteRoleMutation.mutate({
                roleId: record.id,
              });
            },
            width: 300,
          });
        },
      },
    ];
    return { items };
  };
  const columns: ProColumns<RoleType>[] = [
    {
      dataIndex: 'displayName',
      ellipsis: true,
      key: 'name',
      render: (txt: any, record: RoleType) => {
        return (
          <div style={{ width: '100%' }}>
            <Text style={{ lineHeight: '22px', margin: 0 }}>{txt}</Text>
            {record.description && (
              <>
                <br />
                <Text
                  ellipsis
                  style={{ fontSize: 12, lineHeight: '20px', wordWrap: 'break-word' }}
                  type={'secondary'}
                >
                  {record.description}
                </Text>
              </>
            )}
          </div>
        );
      },
      title: t('table.colums.name'),
      width: 200,
    },
    {
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (_: any, record: RoleType) =>
        record.createdAt ? dayjs(record.createdAt).format('YYYY-MM-DD') : '-',
      sorter: (a: RoleType, b: RoleType) => {
        const aTime = a.createdAt ? new Date(a.createdAt).getTime() : 0;
        const bTime = b.createdAt ? new Date(b.createdAt).getTime() : 0;
        return aTime - bTime;
      },
      title: t('table.colums.createTime'),
      width: 140,
    },
    {
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      render: (_: any, record: RoleType) =>
        record.updatedAt ? dayjs(record.updatedAt).format('YYYY-MM-DD HH:mm:ss') : '-',
      sorter: (a: RoleType, b: RoleType) => {
        const aTime = a.updatedAt ? new Date(a.updatedAt).getTime() : 0;
        const bTime = b.updatedAt ? new Date(b.updatedAt).getTime() : 0;
        return aTime - bTime;
      },
      title: t('table.colums.updateTime'),
      width: 180,
    },
    {
      dataIndex: 'quota',
      key: 'quota',
      render: (quota: any) => (
        <>
          {quota.tokenBudget && (
            <Row align={'middle'} style={{ minWidth: 140 }}>
              <Text style={{ lineHeight: '22px', margin: 0 }}>{quota.tokenBudget}$</Text>
              <Text style={{ fontSize: 12, lineHeight: '20px', marginLeft: 8 }} type={'secondary'}>
                Tokens
              </Text>
            </Row>
          )}

          {quota.fileMb && (
            <Row align={'middle'}>
              <Text style={{ lineHeight: '22px', margin: 0 }}>{quota.fileMb}MB</Text>
              <Text style={{ fontSize: 12, lineHeight: '20px', marginLeft: 8 }} type={'secondary'}>
                文件空间
              </Text>
            </Row>
          )}
          {quota.vectorCount && (
            <Row align={'middle'}>
              <Text style={{ lineHeight: '22px', margin: 0 }}>
                {quota.vectorCount}
                {t('table.values.unitGe')}
              </Text>
              <Text style={{ fontSize: 12, lineHeight: '20px', marginLeft: 8 }} type={'secondary'}>
                {t('table.values.vectorSpace')}
              </Text>
            </Row>
          )}
        </>
      ),
      title: t('table.colums.quota'),
      width: 140,
    },
    {
      dataIndex: 'permissionCount',
      key: 'permissionCount',
      render: (_: any, record: RoleType) => (
        <Row align={'middle'} style={{ minWidth: 80 }} wrap={false}>
          <Text style={{ lineHeight: '22px', margin: 0 }}>
            {/* todo 用户数量不匹配 */}
            {record.userCount}/{' '}
            {record.permissionCount === 0 ? t('table.values.unlimited') : record.permissionCount}
          </Text>
          {(record.permissionCount === 0 || record.permissionCount > record.userCount) && (
            <Icon
              icon={Eye}
              onClick={() => {
                handleManageUsers(record);
              }}
              size={{ size: 14 }}
              style={{ cursor: 'pointer', marginLeft: 4 }}
            />
          )}
        </Row>
      ),
      title: t('table.colums.userCount'),
      width: 80,
    },
    {
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive: any) =>
        isActive ? (
          <div className={styles.activeState}>{t('table.values.active')}</div>
        ) : (
          <div className={styles.defaultState}>{t('table.values.inactive')}</div>
        ),
      title: t('table.colums.status'),
      width: 80,
    },
    {
      dataIndex: 'option',
      key: 'option',
      render: (_: any, record: RoleType) => (
        <Row align={'middle'} justify={'end'} wrap={false}>
          {!record.isActive && (
            <Button
              className={styles.optBtn}
              onClick={() =>
                setConfigOpen({
                  open: true,
                  // @ts-ignore 忽略id类型
                  roleId: record.id,
                  roleName: record?.displayName || record?.name,
                })
              }
              style={{ marginRight: 8 }}
            >
              <Icon icon={Lock} size={{ size: 14 }} />
              {t('table.action.config')}
            </Button>
          )}

          <Dropdown menu={getItems(record)} placement="bottomLeft">
            <Button className={styles.optBtn}>
              <Icon icon={Ellipsis} size={{ size: 14 }} />
            </Button>
          </Dropdown>
        </Row>
      ),
      title: t('table.colums.option'),
      width: 130,
    },
  ];

  return (
    <>
      <Row justify={'end'} style={{ marginTop: -60 }}>
        <Button onClick={() => setNewOpen({ open: true })} type={'primary'}>
          <Icon icon={Plus} size={{ size: 14 }} />
          {t('table.action.new')}
        </Button>
      </Row>
      <Global />
      {/* todo 默认刷新没有触发如何绑定 */}
      <Spin spinning={isLoading || isFetching}>
        <ProTable
          className={styles.table}
          columns={columns as any}
          dataSource={roles}
          pagination={{
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} ${t('table.action.sum')} ${total} ${t('table.action.total')}`,
          }}
          rowKey={(record) => record.id}
          search={false}
          tableStyle={{
            paddingInline: 0,
          }}
          toolbar={{
            title: <>{t('table.title')}</>,
          }}
        />
      </Spin>
      <RoleModal
        onConfig={(id: number, roleName: string) => {
          // @ts-ignore 忽略id类型
          setConfigOpen({ open: true, roleId: id, roleName });
        }}
        onOk={() => {
          refetch();
        }}
        open={newOpen.open}
        role={newOpen.role}
        setOpen={(open) => setNewOpen({ open })}
      />
      <ConfigDrawer
        open={configOpen.open}
        // @ts-ignore 忽略id类型
        roleId={configOpen.roleId}
        roleName={configOpen.roleName}
        setOpen={(open) => setConfigOpen({ open })}
      />
      <UserDrawer
        open={userOpen.open}
        role={userOpen.role}
        setOpen={(open) => setUserOpen({ open })}
      />
    </>
  );
});

export default RoleTable;
