'use client';

import {
  Button,
  Col,
  Form,
  Input,
  InputNumber,
  Modal,
  Radio,
  Row,
  Space,
  Spin,
  message,
} from 'antd';
import { createStyles, useTheme } from 'antd-style';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { trpcQuery } from '@/libs/trpc/client';

interface props {
  onConfig?: (id: number, roleName: string) => void;
  onOk?: () => void;
  open: boolean;
  role?: any;
  setOpen: (open: boolean) => void;
}

const RoleModal = ({ open, setOpen, role, onOk, onConfig }: props) => {
  const { t } = useTranslation('rbac');
  const [loading, setLoading] = useState(false);
  const theme = useTheme();
  const useStyles = createStyles(({ css }) => {
    return {
      formHead: css`
        position: relative;

        height: 22px;
        margin-block-end: 16px;
        padding-inline-start: 10px;

        font-size: 14px;
        font-weight: 500;
        line-height: 22px;
        color: ${theme.colorTextHeading};

        &::before {
          content: '';

          position: absolute;
          inset-block-start: 6px;
          inset-inline-start: 0;

          width: 2px;
          height: 10px;
          border-radius: 20px;

          background: ${theme.colorTextHeading};
        }
      `,
    };
  });
  const { styles } = useStyles();
  const isNew = !role;
  const [form] = Form.useForm();
  const createRoleMutation = trpcQuery.rbac.createRole.useMutation();
  const updateRoleMutation = trpcQuery.rbac.updateRole.useMutation();

  // 仅创建
  const handleSubmit = async (isConfig = false) => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      const { fileMb, tokenBudget, vectorCount, ...roleInfo } = values;
      const roleData = {
        ...roleInfo,
        limit: undefined,
      };
      let submitRole;
      // 额度无限为null
      const quota =
        values.limit === 'limit'
          ? {
              fileMb,
              tokenBudget,
              vectorCount,
            }
          : undefined;
      // 组织提交数据以匹配rbac路由的接口格式
      if (isNew) {
        // 创建角色（包含配额）
        const res = await createRoleMutation.mutateAsync({
          ...roleData,
          quota,
        });
        submitRole = res.data;
      } else {
        // 更新角色（包含配额）
        const res = await updateRoleMutation.mutateAsync({
          roleId: role.id,
          ...roleData,
          quota,
        });
        submitRole = res.data;
      }
      setOpen(false);
      onOk?.();
      if (isConfig) {
        onConfig?.(submitRole.id, submitRole?.displayName || submitRole?.name);
      }
    } catch (e: any) {
      message.error(t('createRole.action.saveFailed') + ':' + e.message);
    } finally {
      setLoading(false);
    }
  };
  // 权限配置
  const handleConfig = async () => {
    await handleSubmit(true);
  };
  // 获取角色配额信息
  const { data: roleQuotas } = trpcQuery.rbac.getRoleQuotas.useQuery(
    { roleId: role?.id || 0 },
    { enabled: open && !!role },
  );

  useEffect(() => {
    if (open) {
      form.resetFields();
      if (isNew) {
        // 新建
        form.setFieldsValue({
          fileMb: 0,
          isActive: true,
          limit: 'noLimit',
          tokenBudget: 0,
          vectorCount: 0,
        });
      } else {
        if (role && roleQuotas) {
          form.setFieldsValue({
            description: role.description || '',
            displayName: role.displayName,
            // 使用真实的配额数据，如果还在加载则使用默认值
            fileMb: roleQuotas?.fileMb ?? 0,
            isActive: role.isActive,
            limit: roleQuotas ? 'limit' : 'noLimit',
            name: role.name,
            tokenBudget: roleQuotas?.tokenBudget ?? 0,
            vectorCount: roleQuotas?.vectorCount ?? 0,
          });
        }
      }
    }
  }, [open, roleQuotas]);

  return (
    <Modal
      footer={null}
      onCancel={() => setOpen(false)}
      open={open}
      title={isNew ? t('createRole.title') : t('createRole.editTitle')}
      width={606}
    >
      <Spin spinning={loading}>
        <Form
          form={form}
          initialValues={{
            all: 'limit',
            isActive: true,
          }}
          layout="vertical"
        >
          <Row className={styles.formHead} style={{ marginTop: 16 }}>
            {t('createRole.form.base')}
          </Row>
          {/* todo 角色名称和显示名称 */}
          <Form.Item
            label={t('createRole.form.name')}
            name="name"
            rules={[{ message: t('createRole.rules.nameRequired'), required: true }]}
          >
            <Input placeholder={t('createRole.rules.nameRequired')} />
          </Form.Item>
          <Form.Item
            label={'显示名称'}
            name="displayName"
            rules={[{ message: '显示名称不能为空', required: true }]}
          >
            <Input placeholder={'请输入显示名称'} />
          </Form.Item>
          <Form.Item label={t('createRole.form.description')} name="description">
            <Input.TextArea placeholder={t('createRole.rules.descriptionRequired')} rows={3} />
          </Form.Item>
          <Form.Item label={t('createRole.form.enabled')} name="isActive">
            <Radio.Group>
              <Radio value={true}>{t('createRole.form.yes')}</Radio>
              <Radio value={false}>{t('createRole.form.no')}</Radio>
            </Radio.Group>
          </Form.Item>
          <Row className={styles.formHead}>{t('createRole.form.quotaMonth')}</Row>
          {/* todo 不限额字段以及值 */}
          <Form.Item label="" name="limit">
            <Radio.Group>
              <Radio value={'noLimit'}>{t('createRole.form.unlimited')}</Radio>
              <Radio value={'limit'}>{t('createRole.form.limited')}</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item dependencies={['limit']} noStyle>
            {() => {
              const limit = form.getFieldValue('limit');
              return limit === 'limit' ? (
                <Row gutter={[32, 16]}>
                  <Col>
                    <Form.Item
                      label={t('createRole.form.tokenQuota')}
                      name="tokenBudget"
                      rules={[
                        { message: t('createRole.rules.quotaRequired'), required: true },
                        { message: 'Token配额不能小于0', min: 0, type: 'number' },
                      ]}
                    >
                      <InputNumber style={{ width: 160 }} suffix={'$'} />
                    </Form.Item>
                  </Col>
                  <Col>
                    <Form.Item
                      label={t('createRole.form.fileQuota')}
                      name="fileMb"
                      rules={[{ message: t('createRole.rules.quotaRequired'), required: true }]}
                    >
                      <InputNumber min={0} style={{ width: 160 }} suffix="MB" />
                    </Form.Item>
                  </Col>
                  <Col>
                    <Form.Item
                      label={t('createRole.form.vectorQuota')}
                      name="vectorCount"
                      rules={[{ message: t('createRole.rules.quotaRequired'), required: true }]}
                    >
                      <InputNumber min={0} style={{ width: 160 }} suffix="个" />
                    </Form.Item>
                  </Col>
                </Row>
              ) : null;
            }}
          </Form.Item>
        </Form>
        <Space style={{ justifyContent: 'end', marginTop: 24, width: '100%' }}>
          <Button onClick={() => setOpen(false)}>{t('createRole.action.cancel')}</Button>
          {isNew ? (
            <>
              <Button onClick={() => handleSubmit()}>{t('createRole.action.create')}</Button>
              <Button onClick={handleConfig} type="primary">
                {t('createRole.action.config')}
              </Button>
            </>
          ) : (
            <Button onClick={() => handleSubmit()} type="primary">
              {t('createRole.action.save')}
            </Button>
          )}
        </Space>
      </Spin>
    </Modal>
  );
};

export default RoleModal;
