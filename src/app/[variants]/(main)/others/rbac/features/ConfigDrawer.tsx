'use client';

import { Icon } from '@lobehub/ui';
import {
  Avatar,
  Drawer,
  Input,
  List,
  Row,
  Segmented,
  Spin,
  Switch,
  Tag,
  Typography,
  message,
} from 'antd';
import { createGlobalStyle, useTheme } from 'antd-style';
import { Atom, Calendar, Earth, Eye, Image, Search } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { trpcQuery } from '@/libs/trpc/client';
import { AggregatedModelItem } from '@/types/aggregatedModel';

interface props {
  open: boolean;
  roleId?: number;
  roleName?: string;
  setOpen: (open: boolean) => void;
}
const SegmentedGlobal = createGlobalStyle`
  .ant-segmented-item{
    width:25%;
  }
`;
const SearchGlobal = createGlobalStyle`
  .ant-input-group-addon{
    display: none!important;
  }
`;

const ConfigDrawer = ({ open, setOpen, roleId = 0, roleName = '' }: props) => {
  const { t } = useTranslation('rbac');
  const { Text } = Typography;
  const [tab, setTab] = useState(t('configDrawer.tabs.model'));
  const theme = useTheme();
  const { data: models, isLoading: isLoadingModels } =
    trpcQuery.aggregatedModel.getAllModels.useQuery();
  const [searchValue, setSearchValue] = useState('');
  const {
    data: roleModels,
    refetch: refetchModels,
    isLoading,
  } = trpcQuery.rbac.getRoleModels.useQuery(
    {
      roleId: roleId,
    },
    { enabled: !!(open && roleId) },
  );

  // 添加角色模型
  const addModelsMutation = trpcQuery.rbac.addRoleModels.useMutation({
    onError: (error: any) => {
      message.error(t('configDrawer.action.addModelFailed') + ':' + error.message);
    },
    onSuccess: () => {
      message.success(t('configDrawer.action.addModelSuccess'));
      refetchModels();
    },
  });

  // 移除角色模型
  const removeModelsMutation = trpcQuery.rbac.removeRoleModels.useMutation({
    onError: (error: any) => {
      message.error(t('configDrawer.action.removeModelFailed') + ':' + error.message);
    },
    onSuccess: () => {
      message.success(t('configDrawer.action.removeModelSuccess'));
      refetchModels();
    },
  });

  // 处理列表数据
  const handleListData = () => {
    let enabled: AggregatedModelItem[] = [],
      disabled: AggregatedModelItem[] = [];

    if (models?.data && roleModels?.data) {
      models.data.forEach((item) => {
        if (roleModels?.data && roleModels?.data.includes(item.id)) {
          enabled.push(item);
        } else {
          disabled.push(item);
        }
      });
    }
    if (searchValue) {
      enabled = enabled.filter((item) => item.displayName?.includes(searchValue));
      disabled = disabled.filter((item) => item.displayName?.includes(searchValue));
    }

    return [enabled, disabled];
  };
  // 启用模型
  const addModel = async (modelId: string) => {
    try {
      if (!roleId) {
        message.error(t('configDrawer.action.roleIdInvalid'));
        return;
      }
      await addModelsMutation.mutateAsync({
        models: [modelId],
        roleId: roleId,
      });
    } catch {
      // 错误处理已在mutation中处理
    }
  };
  // 禁用模型
  const removeModel = async (modelId: string) => {
    try {
      if (!roleId) {
        message.error(t('configDrawer.action.roleIdInvalid'));
        return;
      }
      await removeModelsMutation.mutateAsync({
        models: [modelId],
        roleId: roleId,
      });
    } catch {
      // 错误处理已在mutation中处理
    }
  };

  useEffect(() => {
    if (open) {
      setSearchValue('');
    }
  }, [open]);

  return (
    <Drawer
      onClose={() => {
        setOpen(false);
      }}
      open={open}
      placement="right"
      title={`${t('configDrawer.title')}-${roleName}`}
      width={860}
    >
      <SegmentedGlobal />
      <Segmented
        onChange={setTab}
        options={[
          t('configDrawer.tabs.model'),
          t('configDrawer.tabs.assistant'),
          t('configDrawer.tabs.plugin'),
          t('configDrawer.tabs.knowledge'),
        ]}
        style={{ marginBottom: 16, width: '100%' }}
        value={tab}
      />
      <Spin
        spinning={
          isLoadingModels ||
          isLoading ||
          addModelsMutation.isPending ||
          removeModelsMutation.isPending
        }
      >
        <SearchGlobal />
        <Input.Search
          enterButton={false}
          onSearch={(value) => {
            setSearchValue(value);
          }}
          placeholder={t('configDrawer.searchPlaceholder')}
          prefix={
            <Icon
              icon={Search}
              size={{ size: 16 }}
              style={{ color: theme.colorTextPlaceholder, marginRight: 8 }}
            />
          }
          size="large"
        />
        <div
          style={{
            height: 'calc(100% - 120px)',
            marginTop: 16,
            overflowX: 'hidden',
            overflowY: 'auto',
          }}
        >
          <Text style={{ color: theme.colorTextDescription, fontSize: '14px', lineHeight: '22px' }}>
            {t('configDrawer.enabled')}
          </Text>
          <List
            dataSource={handleListData()[0]}
            renderItem={(item) => (
              <List.Item
                key={item.id}
                style={{ display: 'grid', gridTemplateColumns: '1fr 260px' }}
              >
                <List.Item.Meta
                  avatar={<Avatar>{item?.displayName?.charAt(0) || 'M'}</Avatar>}
                  description={item?.description}
                  title={item?.displayName}
                />
                <Row
                  align={'middle'}
                  gutter={8}
                  justify={'end'}
                  style={{ width: 260 }}
                  wrap={false}
                >
                  {item?.abilities && (item.abilities as any).vision && (
                    <Tag>
                      <Icon icon={Eye} size={{ size: 14 }} />
                    </Tag>
                  )}
                  {item?.abilities && (item.abilities as any).functionCall && (
                    <Tag>
                      <Icon icon={Calendar} size={{ size: 14 }} />
                    </Tag>
                  )}
                  {item?.abilities && (item.abilities as any).imageGeneration && (
                    <Tag>
                      <Icon icon={Image} size={{ size: 14 }} />
                    </Tag>
                  )}

                  {item?.abilities && (item.abilities as any).reasoning && (
                    <Tag>
                      <Icon icon={Atom} size={{ size: 14 }} />
                    </Tag>
                  )}
                  {item?.abilities && (item.abilities as any).search && (
                    <Tag>
                      <Icon icon={Earth} size={{ size: 14 }} />
                    </Tag>
                  )}

                  <Tag>{Math.floor((item?.contextWindowTokens || 0) / 1000)}K</Tag>
                  <Switch
                    checked={true}
                    onChange={() => {
                      removeModel(item.id);
                    }}
                  />
                </Row>
              </List.Item>
            )}
            style={{ width: '100%' }}
          />
          <Text style={{ color: theme.colorTextDescription, fontSize: '14px', lineHeight: '22px' }}>
            {t('configDrawer.disabled')}
          </Text>
          <List
            dataSource={handleListData()[1]}
            renderItem={(item) => (
              <List.Item
                key={item.id}
                style={{ display: 'grid', gridTemplateColumns: '1fr 260px' }}
              >
                <List.Item.Meta
                  avatar={<Avatar>{item?.displayName?.charAt(0) || 'M'}</Avatar>}
                  description={item?.description}
                  title={item?.displayName}
                />
                <Row
                  align={'middle'}
                  gutter={8}
                  justify={'end'}
                  style={{ width: 260 }}
                  wrap={false}
                >
                  {item?.abilities && (item.abilities as any).vision && (
                    <Tag>
                      <Icon icon={Eye} size={{ size: 14 }} />
                    </Tag>
                  )}
                  {item?.abilities && (item.abilities as any).functionCall && (
                    <Tag>
                      <Icon icon={Calendar} size={{ size: 14 }} />
                    </Tag>
                  )}
                  {item?.abilities && (item.abilities as any).imageGeneration && (
                    <Tag>
                      <Icon icon={Image} size={{ size: 14 }} />
                    </Tag>
                  )}

                  {item?.abilities && (item.abilities as any).reasoning && (
                    <Tag>
                      <Icon icon={Atom} size={{ size: 14 }} />
                    </Tag>
                  )}
                  {item?.abilities && (item.abilities as any).search && (
                    <Tag>
                      <Icon icon={Earth} size={{ size: 14 }} />
                    </Tag>
                  )}

                  <Tag>{Math.floor((item?.contextWindowTokens || 0) / 1000)}K</Tag>
                  <Switch
                    checked={false}
                    onChange={() => {
                      addModel(item.id);
                    }}
                  />
                </Row>
              </List.Item>
            )}
          />
        </div>
      </Spin>
    </Drawer>
  );
};

export default ConfigDrawer;
