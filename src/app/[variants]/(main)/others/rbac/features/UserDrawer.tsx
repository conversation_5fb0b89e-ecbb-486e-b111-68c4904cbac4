'use client';

import { ModalForm, ProFormDigit } from '@ant-design/pro-components';
import { Icon } from '@lobehub/ui';
import {
  Avatar,
  Button,
  Drawer,
  Form,
  Input,
  Modal,
  Row,
  Space,
  Table,
  Typography,
  message,
} from 'antd';
import { createGlobalStyle, useTheme } from 'antd-style';
import { CircleMinus, Plus, Search, Settings } from 'lucide-react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { trpcQuery } from '@/libs/trpc/client';
import { EndUser } from '@/types/endUsers';

import UserModal from './UserModal';

interface props {
  open: boolean;
  role?: any;
  setOpen: (open: boolean) => void;
}
const SearchGlobal = createGlobalStyle`
  .ant-input-group-addon{
    display: none!important;
  }
`;

const UserDrawer = ({ open, setOpen, role }: props) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const [userOpen, setUserOpen] = useState(false);
  const { t } = useTranslation('rbac');
  const { Text } = Typography;
  const theme = useTheme();
  const [form] = Form.useForm<{ limit: number }>();

  const {
    data: roleUsers,
    refetch: refetchUsers,
    isLoading,
  } = trpcQuery.rbac.getRoleUsers.useQuery(
    {
      roleId: role?.id,
    },
    { enabled: !!role && open },
  );
  // 从角色移除用户
  const removeUsersMutation = trpcQuery.rbac.removeRoleUsers.useMutation({
    onError: (error: any) => {
      message.error(t('userDrawer.actions.removeFailed') + ':' + error.message);
    },
    onSuccess: () => {
      message.success(t('userDrawer.actions.removeSuccess'));
      refetchUsers();
    },
  });

  const handleRemoveUser = async (userIds: string[]) => {
    if (!role.id) return;
    try {
      await removeUsersMutation.mutateAsync({
        roleId: role.id,
        userIds: userIds,
      });
      if (selectedRowKeys.length)
        setSelectedRowKeys(selectedRowKeys.filter((id) => !userIds.includes(id)));
    } catch {
      // 错误处理已在mutation中处理
    }
  };
  // 全部移除
  const handleRemoveAllUser = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning(t('userDrawer.actions.removeSelect'));
      return;
    }
    Modal.confirm({
      onOk: async () => {
        handleRemoveUser(selectedRowKeys || []);
      },
      title: t('userDrawer.actions.removeConfirm'),
    });
  };

  const columns = [
    {
      dataIndex: 'username',
      render: (text: string, row: EndUser) => (
        <Row align={'middle'} wrap={false}>
          <Avatar icon={text?.charAt(0) || 'U'} size={32} style={{ marginRight: 8 }} />
          <div>
            <Text>{text}</Text>
            <Text style={{ display: 'block', fontSize: 12 }} type={'secondary'}>
              {row.id}
            </Text>
          </div>
        </Row>
      ),
      title: t('userDrawer.columns.name'),
    },
    {
      dataIndex: 'email',
      title: t('userDrawer.columns.email'),
    },
    {
      dataIndex: 'options',
      // 修复lint：为第一个参数指定类型
      render: (_: any, record: EndUser) => (
        <Button onClick={() => handleRemoveUser([record.id])}>
          <Icon icon={CircleMinus} size={{ size: 14 }} />
          {t('userDrawer.actions.remove')}
        </Button>
      ),
      title: t('userDrawer.columns.action'),
      width: 100,
    },
  ];

  return (
    <Drawer onClose={() => setOpen(false)} open={open} title={role?.displayName} width={860}>
      <Row align={'middle'} justify={'space-between'}>
        <SearchGlobal />
        <Input.Search
          enterButton={false}
          onSearch={(value) => {
            setSearchValue(value);
          }}
          placeholder={t('userDrawer.searchPlaceholder')}
          prefix={
            <Icon
              icon={Search}
              size={{ size: 16 }}
              style={{ color: theme.colorTextPlaceholder, marginRight: 8 }}
            />
          }
          style={{ width: 264 }}
        />
        <Space>
          <Button disabled={selectedRowKeys.length === 0} onClick={handleRemoveAllUser}>
            <Icon icon={CircleMinus} size={{ size: 14 }} />
            {t('userDrawer.actions.allRemove')}
          </Button>

          <ModalForm<{
            limit: number;
          }>
            autoFocusFirstInput
            form={form}
            layout="horizontal"
            modalProps={{
              destroyOnHidden: true,
            }}
            onFinish={async (values) => {
              console.log(values);
              // todo 修改数量限制
              message.success(t('userDrawer.actions.submitSuccess'));
              return true;
            }}
            title={t('userDrawer.actions.limitTitle')}
            trigger={
              <Button disabled={true}>
                <Icon icon={Settings} size={{ size: 14 }} />
                {t('userDrawer.actions.limit')}
              </Button>
            }
            width={400}
          >
            <Row style={{ marginTop: 24 }}>
              <ProFormDigit
                label={t('userDrawer.actions.limitTitle')}
                name="limit"
                placeholder={t('userDrawer.actions.limitPlaceholder')}
                width="md"
              />
            </Row>
          </ModalForm>

          <Button
            onClick={() => {
              setUserOpen(true);
            }}
            type="primary"
          >
            <Icon icon={Plus} size={{ size: 14 }} />
            {t('userDrawer.actions.addUser')}
          </Button>
        </Space>
      </Row>
      <Table
        columns={columns}
        dataSource={
          searchValue
            ? (roleUsers?.filter((item) => item?.username?.includes(searchValue)) as any[]) || []
            : (roleUsers as any[]) || []
        }
        loading={isLoading || removeUsersMutation.isPending}
        pagination={false}
        rowKey={(record) => record.id}
        rowSelection={{
          onChange: (selectedRowKeys) => setSelectedRowKeys(selectedRowKeys as string[]),
          selectedRowKeys: selectedRowKeys,
          type: 'checkbox',
        }}
        scroll={{ y: document.documentElement.clientHeight - 216 }} //todo 更优的自适应处理
        style={{ marginTop: 16 }}
      />
      <UserModal
        onOk={() => {
          refetchUsers();
        }}
        open={userOpen}
        roleId={role?.id}
        setOpen={setUserOpen}
        userIds={(roleUsers || []).map((item) => item.id)}
      />
    </Drawer>
  );
};

export default UserDrawer;
