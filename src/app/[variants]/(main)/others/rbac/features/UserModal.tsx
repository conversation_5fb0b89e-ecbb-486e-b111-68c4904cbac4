import { Icon } from '@lobehub/ui';
import {
  Avatar,
  Button,
  Checkbox,
  Col,
  Empty,
  Input,
  Modal,
  Row,
  Spin,
  Typography,
  message,
} from 'antd';
import { createGlobalStyle, useTheme } from 'antd-style';
import { Search } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { trpcQuery } from '@/libs/trpc/client';

interface props {
  onOk: () => void;
  open: boolean;
  roleId?: number;
  setOpen: (open: boolean) => void;
  userIds?: string[];
}
const SearchGlobal = createGlobalStyle`
  .ant-input-group-addon{
    display: none!important;
  }
`;
const UserModal = ({ open, setOpen, roleId, userIds, onOk }: props) => {
  const { t } = useTranslation('rbac');
  const { Text } = Typography;
  const theme = useTheme();
  const [selectedUserIds, setSelectedUserIds] = useState<string[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const {
    data: userList,
    isLoading,
    refetch,
  } = trpcQuery.user.getUsers.useQuery(
    {
      params: {
        emailOrUsernameOrUserId: searchValue,
      },
    },
    {
      enabled: false,
    },
  );

  const addUsersMutation = trpcQuery.rbac.addRoleUsers.useMutation({
    onError: (error: any) => {
      message.error(t('userModal.action.addUserFailed') + ':' + error.message);
    },
    onSuccess: () => {
      message.success(t('userModal.action.addUserSuccess'));
      setOpen(false);
      setSelectedUserIds([]);
      setSearchValue('');
      onOk();
    },
  });

  const handleAddUsers = async () => {
    await addUsersMutation.mutate({
      roleId: roleId as number,
      userIds: selectedUserIds,
    });
  };

  const getCanSelectedUser = () => {
    if (!searchValue) return [];
    return userList?.data?.filter((item) => !userIds?.includes(item.id));
  };

  useEffect(() => {
    if (open) {
      refetch();
    }
  }, [searchValue]);

  return (
    <Modal
      closable={false}
      footer={null}
      maskClosable={true}
      onCancel={() => setOpen(false)}
      open={open}
      style={{ left: '100%', marginLeft: -336, top: 140 }}
      title={false}
      width={312}
    >
      <Spin spinning={addUsersMutation.isPending}>
        <SearchGlobal />
        <Input.Search
          enterButton={false}
          onSearch={(value) => {
            setSearchValue(value);
          }}
          placeholder={t('userModal.searchPlaceholder')}
          prefix={
            <Icon
              icon={Search}
              size={{ size: 16 }}
              style={{ color: theme.colorTextPlaceholder, marginRight: 8 }}
            />
          }
          style={{ width: 264 }}
        />
        {/* todo最大高度滚动 */}
        <Spin spinning={isLoading}>
          <div style={{ marginBottom: 12, marginTop: 12 }}>
            {!getCanSelectedUser()?.length && <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
            {getCanSelectedUser()?.map((item) => (
              <Row
                align={'middle'}
                key={`${item.id}-userModal`}
                style={{ marginBottom: 12 }}
                wrap={false}
              >
                {/* @ts-ignore */}
                <Avatar icon={item?.name?.charAt(0) || 'U'} size={32} style={{ marginRight: 8 }} />
                <Col flex={'188px'}>
                  {/* @ts-ignore */}
                  <Text>{item?.name}</Text>
                  <Text
                    ellipsis
                    style={{ display: 'block', fontSize: 12, lineHeight: '20px' }}
                    type={'secondary'}
                  >
                    {item?.email}
                  </Text>
                </Col>
                <Col style={{ paddingLeft: 12 }}>
                  <Checkbox
                    checked={selectedUserIds.includes(item.id)}
                    onChange={(e) => {
                      setSelectedUserIds(
                        e.target.checked
                          ? [...selectedUserIds, item.id]
                          : selectedUserIds.filter((id) => id !== item.id),
                      );
                    }}
                  />
                </Col>
              </Row>
            ))}
          </div>
        </Spin>
        <div>
          <Button disabled={!selectedUserIds.length} onClick={handleAddUsers} type={'primary'}>
            {t('userModal.add')}
          </Button>
        </div>
      </Spin>
    </Modal>
  );
};

export default UserModal;
