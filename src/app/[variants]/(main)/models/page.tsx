'use client';

import { Icon } from '@lobehub/ui';
import { But<PERSON>, Col, Row, message } from 'antd';
import { Plus } from 'lucide-react';
import { useEffect, useState } from 'react';

import { trpcClient, trpcQuery } from '@/libs/trpc/client';
import { AggregatedModelItem } from '@/types/aggregatedModel';

import AddModelDrawer from './features/AddModelDrawer';
import ModelsDetail from './features/ModelsDetail';
import ModelsMenu from './features/ModelsMenu';

const ModelsPage = () => {
  const [newOpen, setNewOpen] = useState<{ data?: AggregatedModelItem; open: boolean }>({
    open: false,
  });
  const [activeModels, setActiveModels] = useState<any>();
  const {
    data,
    isFetching: isLoading,
    refetch,
  } = trpcQuery.aggregatedModel.getAllModels.useQuery();

  // 新建按钮点击事件
  const handleNew = () => setNewOpen({ open: true });
  const handleEdit = () => {
    if (!activeModels) return;
    setNewOpen({ data: activeModels, open: true });
  };
  const handleDelete = async () => {
    if (!activeModels?.id) {
      message.error('未选中要删除的模型');
      return;
    }
    try {
      await trpcClient.aggregatedModel.deleteModel.mutate(activeModels.id);
      message.success('模型删除成功');

      // 刷新模型列表数据
      refetch();

      // 删除当前选中的渠道，清空选中状态
      setActiveModels(undefined);
    } catch (error: any) {
      message.error(`删除失败: ${error.message}`);
    }
  };
  const handleToggle = async (enabled: boolean) => {
    try {
      // @ts-ignore todo 参数调整
      await trpcClient.aggregatedModel.toggleModelStatus.mutate({
        enabled,
        id: activeModels?.id,
        // updatedBy: 'admin',
      });
      message.success(`模型${enabled ? '启用' : '禁用'}成功`);

      refetch();
    } catch (error: any) {
      message.error(`${enabled ? '启用' : '禁用'}失败: ${error.message}`);
    }
  };

  useEffect(() => {
    // 同步当前所选模型为最新的数据
    if (activeModels && Array.isArray(data?.data)) {
      const find = data.data.find((item) => item.id === activeModels.id);
      if (find) {
        setActiveModels({ ...find } as any);
      }
    }
  }, [data]);
  return (
    <>
      <Row justify={'end'} style={{ marginTop: -60 }}>
        <Button onClick={handleNew} type={'primary'}>
          <Icon icon={Plus} size={{ size: 14 }} />
          添加模型
        </Button>
      </Row>
      <Row style={{ minHeight: 'calc(100vh -  242px)' }} wrap={false}>
        <Col flex="320px">
          <ModelsMenu
            activeModels={activeModels}
            data={(data?.data as AggregatedModelItem[]) || []}
            isLoading={isLoading}
            setActiveModels={setActiveModels}
          />
        </Col>
        <Col flex="auto">
          <ModelsDetail
            activeModels={activeModels}
            handleDelete={handleDelete}
            handleEdit={handleEdit}
            handleToggle={handleToggle}
          />
        </Col>
      </Row>
      <AddModelDrawer
        data={newOpen.data}
        fallbackOptions={data?.data?.map((item) => ({
          label: item.displayName || item.id || '-',
          value: item.id,
        }))}
        onClose={() => setNewOpen({ open: false })}
        onOk={() => {
          refetch();
        }}
        open={newOpen.open}
      />
    </>
  );
};

ModelsPage.displayName = 'ModelsPage';

export default ModelsPage;
