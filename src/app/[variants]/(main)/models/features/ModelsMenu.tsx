'use client';

import { Icon } from '@lobehub/ui';
import { Avatar, Col, Input, Row, Skeleton, Typography } from 'antd';
import { createGlobalStyle, createStyles, useTheme } from 'antd-style';
import { Ban, Check, Search } from 'lucide-react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { AggregatedModelItem } from '@/types/aggregatedModel';

const SearchGlobal = createGlobalStyle`
  .ant-input-group-addon{
    display: none!important;
  }
`;

const ModelsMenu = ({
  activeModels,
  setActiveModels,
  data,
  isLoading,
}: {
  activeModels?: AggregatedModelItem;
  data: AggregatedModelItem[];
  isLoading: boolean;
  setActiveModels: (models: AggregatedModelItem) => void;
}) => {
  const { t } = useTranslation('channel');
  const { Text } = Typography;
  const theme = useTheme();
  const useStyles = createStyles(({ css }) => {
    return {
      activeState: css`
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        position: relative;

        width: 16px;
        height: 16px;
        padding-block: 0;
        padding-inline: 2px;
        border-radius: 4px;

        font-size: 14px;
        line-height: 24px;
        color: ${theme.colorSuccess};

        background: ${theme.colorSuccessBg};
      `,
      channelActiveItem: css`
        height: 80px;
        padding-block: 16px;
        padding-inline: 12px;
        border-radius: 6px;
        cursor: pointer;

        color: ${theme.colorText};

        background: ${theme.colorBgContainer};

        .ant-typography {
          color: #000;
        }
      `,
      channelItem: css`
        height: 80px;
        padding-block: 16px;
        padding-inline: 12px;
        border-radius: 6px;
        cursor: pointer;
      `,
      defaultState: css`
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        position: relative;

        width: 16px;
        height: 16px;
        padding-block: 0;
        padding-inline: 2px;
        border-radius: 4px;

        font-size: 14px;
        line-height: 24px;
        color: ${theme.colorText};

        background: ${theme.colorTextPlaceholder};
      `,
      errorState: css`
        cursor: pointer;

        position: relative;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 16px;
        height: 16px;
        padding-block: 0;
        padding-inline: 2px;
        border-radius: 4px;

        font-size: 14px;
        line-height: 24px;
        color: ${theme.colorError};

        background: ${theme.colorErrorBg};
      `,
    };
  });
  const { styles } = useStyles();
  const [searchValue, setSearchValue] = useState('');

  return (
    <div
      style={{
        backgroundColor: theme.gray2A,
        borderBottomLeftRadius: 8,
        borderTopLeftRadius: 8,
        height: '100%',
        padding: '16px 12px',
      }}
    >
      <Skeleton active loading={isLoading}>
        <SearchGlobal />
        <Input.Search
          enterButton={false}
          onSearch={(value) => {
            setSearchValue(value);
          }}
          placeholder={t('menu.searchPlaceholder')}
          prefix={
            <Icon
              icon={Search}
              size={{ size: 16 }}
              style={{ color: theme.colorTextPlaceholder, marginRight: 8 }}
            />
          }
          style={{ width: '100%' }}
        />
        <div style={{ minHeight: 'calc(100% - 24px)', overflowY: 'auto', padding: '12px 0' }}>
          {data
            ?.filter((item: AggregatedModelItem) => item?.displayName?.includes(searchValue))
            ?.map((item: AggregatedModelItem) => (
              <div
                className={
                  activeModels?.id === item.id ? styles.channelActiveItem : styles.channelItem
                }
                key={`${item.id}-channelMenu`}
                onClick={() => {
                  setActiveModels(item);
                }}
              >
                <Row
                  align="middle"
                  justify={'space-between'}
                  style={{ cursor: 'pointer' }}
                  wrap={false}
                >
                  <Col flex={'44px'}>
                    <Avatar icon={item?.logo || item?.displayName?.charAt(0)} />
                  </Col>
                  <Col flex={'auto'}>
                    <div>
                      <Text
                        ellipsis
                        style={{
                          color: theme.colorText,
                          fontSize: 16,
                          lineHeight: '24px',
                          marginRight: 6,
                          maxWidth: 200,
                        }}
                      >
                        {item?.displayName}
                      </Text>
                      {item.enabled ? (
                        <div className={styles.activeState}>
                          <Icon icon={Check} size={{ size: 12 }} />
                        </div>
                      ) : (
                        <div className={styles.defaultState}>
                          <Icon icon={Ban} size={{ size: 12 }} />
                        </div>
                      )}
                    </div>
                    <div>
                      <Text
                        ellipsis
                        style={{
                          color: theme.colorTextDescription,
                          fontSize: 14,
                          lineHeight: '22px',
                          marginTop: 2,
                        }}
                      >
                        {item?.id}
                      </Text>
                    </div>
                  </Col>
                </Row>
                <Row
                  align={'middle'}
                  style={{
                    color: theme.colorTextSecondary,
                    fontSize: 14,
                    lineHeight: '22px',
                    marginTop: 8,
                  }}
                />
              </div>
            ))}
        </div>
      </Skeleton>
    </div>
  );
};

export default ModelsMenu;
