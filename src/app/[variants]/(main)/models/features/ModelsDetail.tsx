'use client';

import { Icon } from '@lobehub/ui';
import {
  Button,
  Descriptions,
  DescriptionsProps,
  Dropdown,
  Empty,
  MenuProps,
  Modal,
  Row,
  Skeleton,
  Space,
  Typography,
} from 'antd';
import { createStyles, useTheme } from 'antd-style';
import dayjs from 'dayjs';
import { Ban, CircleCheckBig, Ellipsis, Info, Pencil, Trash } from 'lucide-react';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import { trpcQuery } from '@/libs/trpc/client';
import { AggregatedModelItem } from '@/types/aggregatedModel';

import ChannelList from './ChannelList';

const ModelsDetail = ({
  activeModels,
  handleEdit,
  handleDelete,
  handleToggle,
}: {
  activeModels?: AggregatedModelItem;
  handleDelete: () => void;
  handleEdit: () => void;
  handleToggle: (enabled: boolean) => void;
}) => {
  const { Text } = Typography;
  const theme = useTheme();
  const useStyles = createStyles(({ css }) => {
    return {
      channelBtn: css`
        height: 28px;
        padding-block: 0;
        padding-inline: 8px;

        font-size: 14px;
        line-height: 28px;
      `,
    };
  });
  const { styles } = useStyles();
  const { t } = useTranslation('models');
  const {
    data,
    isFetching: isProviderLoading,
    refetch,
  } = trpcQuery.aggregatedModel.getModelInfo.useQuery(activeModels?.id ?? '', {
    enabled: false,
  });

  const detail = data?.data || undefined;
  const items: MenuProps['items'] = [
    {
      // @ts-ignore
      hidden: !!detail?.enabled,

      key: 'open',
      label: (
        <div>
          <Icon icon={CircleCheckBig} size={{ size: 14 }} style={{ marginRight: 8 }} />
          {t('detail.open')}
        </div>
      ),
      onClick: () => {
        Modal.confirm({
          content: t('detail.disableConfirm'),
          icon: (
            <Icon icon={Info} size={{ size: 18 }} style={{ color: theme.gray, marginTop: 2 }} />
          ),
          okText: t('detail.confirm'),
          onOk: async () => {
            await handleToggle(true);
            refetch();
          },
          width: 300,
        });
      },
    },
    {
      // @ts-ignore
      hidden: !detail?.enabled,

      key: 'close',
      label: (
        <div style={{ color: theme.colorError }}>
          <Icon icon={Ban} size={{ size: 14 }} style={{ marginRight: 8 }} />
          {t('detail.disable')}
        </div>
      ),
      onClick: () => {
        Modal.confirm({
          content: t('detail.disableConfirm'),
          icon: (
            <Icon icon={Info} size={{ size: 18 }} style={{ color: theme.gray, marginTop: 2 }} />
          ),
          okButtonProps: {
            danger: true,
          },
          okText: t('detail.disable'),
          onOk: async () => {
            await handleToggle(false);
            refetch();
          },
          width: 300,
        });
      },
    },
    {
      key: 'delete',
      label: (
        <div style={{ color: theme.colorError }}>
          <Icon icon={Trash} size={{ size: 14 }} style={{ marginRight: 8 }} />
          {t('detail.delete')}
        </div>
      ),
      onClick: () => {
        Modal.confirm({
          content: t('detail.deleteConfirm'),
          icon: (
            <Icon icon={Info} size={{ size: 18 }} style={{ color: theme.gray, marginTop: 2 }} />
          ),
          okButtonProps: {
            danger: true,
          },
          okText: t('detail.delete'),
          onOk: handleDelete,
          width: 300,
        });
      },
    },
  ];
  useEffect(() => {
    if (activeModels?.id) {
      refetch();
    }
  }, [activeModels]);

  return (
    <div
      style={{
        backgroundColor: theme.grayBg,
        borderBottomRightRadius: 8,
        borderTopRightRadius: 8,
        display: 'flex',
        minHeight: '100%',
        padding: 24,
      }}
    >
      <Skeleton active loading={isProviderLoading}>
        {/* 基本信息 */}
        {data ? (
          <div style={{ minHeight: '100%', width: '100%' }}>
            <Row align="middle" justify="space-between" style={{ marginBottom: 8 }} wrap={false}>
              <Text ellipsis style={{ fontSize: 18, fontWeight: 500 }}>
                {detail?.displayName}
              </Text>
              <Space>
                <Button className={styles.channelBtn} onClick={handleEdit}>
                  <Icon icon={Pencil} size={{ size: 14 }} />
                  {t('detail.edit')}
                </Button>
                <Dropdown menu={{ items }} placement="bottomLeft">
                  <Button className={styles.channelBtn}>
                    <Icon icon={Ellipsis} size={{ size: 14 }} />
                  </Button>
                </Dropdown>
              </Space>
            </Row>
            <Descriptions
              column={2}
              items={
                [
                  {
                    children: <Text>{detail?.id}</Text>,
                    key: 'id',
                    label: t('detail.modelId'),
                    span: 2,
                  },
                  {
                    children: <Text>{detail?.description || '-'}</Text>,
                    key: 'description',
                    label: t('detail.modelDescription'),
                    span: 2,
                  },
                  {
                    children: <Text>${detail?.pricing?.input || '-'}</Text>,
                    key: 'pricinginput',
                    label: t('detail.inputPrice'),
                    span: 1,
                  },
                  {
                    children: <Text>${detail?.pricing?.output || '-'}</Text>,
                    key: 'pricingoutput',
                    label: t('detail.outputPrice'),
                    span: 1,
                  },
                  {
                    children: (
                      <Text>{dayjs(detail?.createdAt).format('YYYY-MM-DD HH:mm:ss') || '-'}</Text>
                    ),
                    key: 'createdAt',
                    label: t('detail.createdAt'),
                    span: 1,
                  },
                  {
                    children: <Text>{detail?.fallbackModelId || '-'}</Text>,
                    key: 'fallbackModelId',
                    label: t('detail.fallbackModel'),
                    span: 1,
                  },
                ] as DescriptionsProps['items']
              }
              size={'small'}
              style={{ marginBottom: 8 }}
            />
            <ChannelList
              data={detail?.models || []}
              loadModelDetail={refetch}
              loading={isProviderLoading}
              modelDetail={detail || undefined}
            />
          </div>
        ) : (
          <div
            style={{
              alignItems: 'center',
              display: 'flex',
              justifyContent: 'center',
              width: '100%',
            }}
          >
            <Empty description={t('detail.empty')} image={Empty.PRESENTED_IMAGE_SIMPLE} />
          </div>
        )}
      </Skeleton>
    </div>
  );
};

export default ModelsDetail;
