import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Col,
  Drawer,
  Form,
  Input,
  InputNumber,
  Row,
  Select,
  Slider,
  Switch,
  Typography,
  message,
} from 'antd';
import { useTheme } from 'antd-style';
import { memo, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { trpcClient } from '@/libs/trpc/client';

// 上下文窗口预设值
const CONTEXT_WINDOW_MARKS = {
  0: '0',
  128: '128K',
  200: '200K',
  256: <>&nbsp;</>,
  32: '32K',
  64: '64K',
};

const AddModelDrawer = memo<{
  data?: any;
  fallbackOptions?: { label: string; value: string }[];
  onClose: () => void;
  onOk: () => void;
  open: boolean;
}>(({ open, onClose, data, onOk, fallbackOptions = [] }) => {
  const { t } = useTranslation('channel');
  const [form] = Form.useForm();
  const [contextWindowTokens, setMaxContextWindow] = useState<number>(0);
  const theme = useTheme();
  const [loading, setLoading] = useState(false);
  const [fallbackEnabled, setFallbackEnabled] = useState(false);
  const handleClose = () => {
    onClose();
  };

  const abilityList = [
    {
      desc: t('abilityList.functionCallDesc'),
      key: ['abilities', 'functionCall'],
      title: t('abilityList.functionCall'),
    },
    {
      desc: t('abilityList.visionDesc'),
      key: ['abilities', 'vision'],
      title: t('abilityList.vision'),
    },
    // {
    //   desc: '此配置将启用模型的深度思考能力，具体效果完全取决于模型本身。请测试该模型是否具备可用的深度思考能力。',
    //   key: ['abilities', 'cache'],
    //   title: '支持上下文缓存',
    // },
    {
      desc: t('abilityList.reasoningDesc'),
      key: ['abilities', 'reasoning'],
      title: t('abilityList.reasoning'),
    },
    {
      desc: t('abilityList.searchDesc'),
      key: ['abilities', 'search'],
      title: t('abilityList.search'),
    },
    {
      desc: t('abilityList.imageGenerationDesc'),
      key: ['abilities', 'imageGeneration'],
      title: t('abilityList.imageGeneration'),
    },
  ];
  // 模型类型选项
  const MODEL_TYPES = [
    { label: t('types.chat'), value: 'chat' },
    { label: t('types.image'), value: 'image' },
    { label: t('types.embedding'), value: 'embedding' },
    { label: t('types.rerank'), value: 'rerank' },
    { label: t('types.text2video'), value: 'text2video' },
    { label: t('types.stt'), value: 'stt' },
    { label: t('types.tts'), value: 'tts' },
    { label: t('types.realtime'), value: 'realtime' },
  ];
  const handleMaxContextWindowChange = (value: number) => {
    setMaxContextWindow(value);
    form.setFieldsValue({ contextWindowTokens: value });
  };
  const isNew = !data;

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      const submitData = {
        abilities: {
          functionCall: values['abilities']?.functionCall || false,
          imageGeneration: values['abilities']?.imageGeneration || false,
          imageOutput: values['abilities']?.imageOutput || false,
          reasoning: values['abilities']?.reasoning || false,
          search: values['abilities']?.search || false,
          vision: values['abilities']?.vision || false,
        },
        contextWindowTokens: values.contextWindowTokens * 1000 || undefined,
        description: values.description,
        displayName: values.displayName,
        enabled: values.enabled,
        fallbackModelId: fallbackEnabled ? values?.fallbackModelId : undefined,
        id: values.id,
        parameters: {},
        pricing: {
          input: values.inputPrice,
          output: values.outputPrice,
        },
        source: 'custom' as const,
        type: values.type,
      };

      if (isNew) {
        const result = await trpcClient.aggregatedModel.createModel.mutate(submitData);
        if (result.success) {
          message.success(t('addModel.addSuccess'));
          form.resetFields();
          handleClose();
          onOk();
        } else {
          message.error(result.error || t('addModel.addFailed'));
          return;
        }
      } else {
        const result = await trpcClient.aggregatedModel.updateModel.mutate({
          data: submitData,
          id: data!.id,
        });
        if (result.success) {
          message.success(t('addModel.addSuccess'));
          form.resetFields();
          handleClose();
          onOk();
        } else {
          message.error(result.error || t('addModel.editFailed'));
          return;
        }
      }
    } catch {
      message.error(`${isNew ? t('addModel.addFailed') : t('addModel.editFailed')}`);
    } finally {
      setLoading(false);
    }
  };
  // 初始化表单数据
  useEffect(() => {
    if (!open) return;
    if (!isNew) {
      // 编辑模式：回显数据
      form.setFieldsValue({
        abilities: { ...data?.abilities },
        contextWindowTokens: Math.floor(data?.contextWindowTokens) / 1000 || 0,
        description: data?.description || '',
        displayName: data?.displayName || '',
        enabled: data?.enabled ?? true,
        fallbackModelId: data?.fallbackModelId,
        id: data?.id,
        inputPrice: data?.pricing?.input || 0,
        outputPrice: data?.pricing?.output || 0,
        type: data?.type || 'chat',
      });
      setFallbackEnabled(!!data?.fallbackModelId);
      setMaxContextWindow(Math.floor(data?.contextWindowTokens) / 1000 || 0);
    } else {
      // 创建模式：设置默认值
      form.setFieldsValue({
        abilities: {},
        contextWindowTokens: 0,
        enabled: true,
        inputPrice: 0,
        outputPrice: 0,
        type: 'chat',
      });
      setMaxContextWindow(0);
    }
  }, [open, data]);

  return (
    <Drawer
      footer={
        <Row gutter={8} justify={'end'} style={{ padding: '16px 24px' }}>
          <Button style={{ marginRight: 8 }} onClick={handleClose}>
            {t('addModel.cancel')}
          </Button>
          <Button loading={loading} onClick={handleSubmit} type="primary">
            {t('addModel.confirm')}
          </Button>
        </Row>
      }
      onClose={handleClose}
      open={open}
      title={isNew ? t('addModel.add') : t('addModel.edit')}
      width={860}
    >
      <Form form={form} layout="vertical">
        <Form.Item
          extra={t('addModel.modelIdDesc')}
          label={t('addModel.modelId')}
          name="id"
          rules={[
            { message: t('addModel.modelIdRequired'), required: true },
            { max: 150, message: t('addModel.modelIdMax') },
          ]}
        >
          <Input placeholder={t('addModel.modelIdPlaceholder')} style={{ width: 440 }} />
        </Form.Item>
        <Form.Item
          label={t('addModel.modelType')}
          name="type"
          rules={[{ message: t('addModel.modelTypeRequired'), required: true }]}
        >
          <Select
            options={MODEL_TYPES}
            placeholder={t('addModel.modelTypePlaceholder')}
            style={{ width: 216 }}
          />
        </Form.Item>
        <Form.Item
          label={t('addModel.modelDisplayName')}
          name="displayName"
          rules={[
            { message: t('addModel.modelDisplayNameRequired'), required: true },
            { max: 200, message: t('addModel.modelDisplayNameMax') },
          ]}
        >
          <Input placeholder={t('addModel.modelDisplayNamePlaceholder')} style={{ width: 440 }} />
        </Form.Item>
        <Form.Item label={t('addModel.modelDescription')} name="description">
          <Input.TextArea
            maxLength={500}
            placeholder={t('addModel.modelDescriptionPlaceholder')}
            rows={3}
            showCount
          />
        </Form.Item>
        <Row gutter={24} style={{ width: 456 }}>
          <Col span={12}>
            <Form.Item
              label={t('addModel.inputPrice')}
              layout="vertical"
              name="inputPrice"
              rules={[{ message: t('addModel.inputPricePlaceholder'), required: true }]}
            >
              <InputNumber
                addonAfter={t('addModel.inputPriceUnit')}
                min={0}
                placeholder={t('addModel.inputPricePlaceholder')}
                style={{ width: 216 }}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label={t('addModel.outputPrice')}
              layout="vertical"
              name="outputPrice"
              rules={[{ message: t('addModel.outputPriceRequired'), required: true }]}
            >
              <InputNumber
                addonAfter={t('addModel.outputPriceUnit')}
                min={0}
                placeholder={t('addModel.outputPricePlaceholder')}
                style={{ width: 216 }}
              />
            </Form.Item>
          </Col>
        </Row>
        <Row align="middle" gutter={12} style={{ width: 456 }}>
          <Col flex="auto">
            <Form.Item
              extra={t('addModel.contextWindowTokensDesc')}
              label={t('addModel.contextWindowTokens')}
              layout="vertical"
              name="contextWindowTokens"
              style={{ marginBottom: 0 }}
            >
              <Slider
                marks={CONTEXT_WINDOW_MARKS}
                max={256}
                min={0}
                onChange={handleMaxContextWindowChange}
                step={null}
                style={{ width: 320 }}
              />
            </Form.Item>
          </Col>
          <Col>
            {/* todo 修改的时候是否直接格式化成功对应的大小 */}
            <InputNumber
              max={256}
              min={0}
              onChange={(v) => {
                handleMaxContextWindowChange(Number(v));
              }}
              precision={3}
              style={{ width: 80 }}
              value={contextWindowTokens}
            />
          </Col>
        </Row>
        <div style={{ fontWeight: 500, marginBottom: 16 }}>{t('addModel.abilityList')}</div>
        {abilityList.map((item) => (
          <Row
            gutter={12}
            key={item.key.join('.')}
            style={{
              backgroundColor: theme.colorBgContainer,
              marginBottom: 8,
              padding: '16px 12px',
            }}
            wrap={false}
          >
            <Col flex="48px" style={{ paddingTop: 4 }}>
              <Avatar icon={'G'} shape="square" size={32} />
            </Col>
            <Col flex="auto">
              <div style={{ fontSize: 14, lineHeight: '22px', marginBottom: 4 }}>{item.title}</div>
              <Typography.Text style={{ fontSize: 12, lineHeight: '20px' }} type="secondary">
                {item.desc}
              </Typography.Text>
            </Col>
            <Col flex="58px" style={{ alignSelf: 'center' }}>
              <Form.Item name={item.key} style={{ margin: 0 }} valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>
          </Row>
        ))}
        <div style={{ marginTop: 16 }}>
          <Form.Item label={t('addModel.enableFallbackModel')}>
            <Switch checked={fallbackEnabled} onChange={setFallbackEnabled} />
          </Form.Item>
          {fallbackEnabled && (
            <Form.Item
              label={t('addModel.fallbackModel')}
              name="fallbackModelId"
              rules={[{ message: t('addModel.fallbackModelRequired'), required: true }]}
              style={{ marginBottom: 8 }}
            >
              <Select
                allowClear
                options={fallbackOptions.filter((item) => item.value !== data?.id)}
                placeholder={t('addModel.fallbackModelPlaceholder')}
                style={{ width: 216 }}
              />
            </Form.Item>
          )}
        </div>
      </Form>
    </Drawer>
  );
});

export default AddModelDrawer;
