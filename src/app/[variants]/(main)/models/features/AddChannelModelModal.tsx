'use client';

import { Form, InputNumber, Modal, Select, message } from 'antd';
import { memo, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { trpcClient } from '@/libs/trpc/client';
import { AdminModelSelectItem, AdminProviderSelectItem } from '@/types/adminInfra';

interface EditChannelModelData {
  aggregatedModelId?: string;
  infraModelId: string;
  infraProviderId: string;
  litellmModelId: string;
  weight?: number | null;
}

interface AddChannelModelModalProps {
  aggregatedModelId?: string;
  data?: EditChannelModelData;
  onCancel: () => void;
  onSuccess: () => void;
  open: boolean;
}

const AddChannelModelModal = memo<AddChannelModelModalProps>(
  ({ open, aggregatedModelId, data, onCancel, onSuccess }) => {
    const { t } = useTranslation('models');
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [providers, setProviders] = useState<AdminProviderSelectItem[]>([]);
    const [models, setModels] = useState<AdminModelSelectItem[]>([]);
    const [selectedProvider, setSelectedProvider] = useState<string>();
    const isNew = !data;
    const handleCancel = () => {
      form.resetFields();
      setSelectedProvider(undefined);
      onCancel();
    };
    // 加载渠道列表
    useEffect(() => {
      const loadProviders = async () => {
        try {
          const result = await trpcClient.adminInfra.getAllInfraProviders.query();
          if (result.success) {
            setProviders(result.data || []);
          }
        } catch (error: any) {
          message.error(t('addModel.loadChannelListFailedMessage', { error: error.message }));
        }
      };

      if (open) {
        loadProviders();
      }
    }, [open]);

    // 编辑模式数据回显
    useEffect(() => {
      if (!open) return;
      if (isNew) {
        form.resetFields();
        setSelectedProvider(undefined);
      } else {
        form.setFieldsValue({
          infraModelId: data.infraModelId,
          infraProviderId: data.infraProviderId,
          weight: data.weight,
        });
        setSelectedProvider(data.infraProviderId);
      }
    }, [data, open]);

    // 当选择渠道时加载对应的模型列表
    useEffect(() => {
      const loadModels = async () => {
        if (!selectedProvider) {
          setModels([]);
          return;
        }

        try {
          const result =
            await trpcClient.adminInfra.getInfraModelsByProviderId.query(selectedProvider);
          if (result.success) {
            setModels(result.data || []);
          }
        } catch (error: any) {
          message.error(t('addModel.loadModelListFailedMessage', { error: error.message }));
        }
      };

      loadModels();
    }, [selectedProvider]);

    const handleProviderChange = (value: string) => {
      setSelectedProvider(value);
      form.setFieldsValue({ infraModelId: undefined }); // 清空模型选择
    };

    const handleSubmit = async () => {
      if (!aggregatedModelId) return;
      try {
        const values = await form.validateFields();
        setLoading(true);

        if (!isNew && data) {
          // 编辑模式：更新关联模型的 LiteLLM 参数
          const result = await trpcClient.aggregatedModel.updateInfraModelRelation.mutate({
            litellmModelId: data.litellmModelId,
            litellmParams: {
              weight: values.weight,
            },
          });

          if (result.success) {
            message.success(t('addModel.updateModelSuccess'));
            form.resetFields();
            setSelectedProvider(undefined);
            onSuccess();
            handleCancel();
          } else {
            message.error(result.error || t('addModel.updateModelFailed'));
          }
        } else {
          // 添加模式：创建新的关联
          const result = await trpcClient.aggregatedModel.addInfraModelRelation.mutate({
            aggregatedModelId,
            infraModelId: values.infraModelId,
            infraProviderId: values.infraProviderId,
            litellmParams: {
              weight: values.weight,
            },
          });

          if (result.success) {
            message.success(t('addModel.addModelSuccess'));
            form.resetFields();
            setSelectedProvider(undefined);
            onSuccess();
            handleCancel();
          } else {
            message.error(result.error || t('addModel.addModelFailed'));
          }
        }
      } catch {
        message.error(`${!isNew ? t('addModel.updateModelFailed') : t('addModel.addModelFailed')}`);
      } finally {
        setLoading(false);
      }
    };

    return (
      <Modal
        confirmLoading={loading}
        onCancel={handleCancel}
        onOk={handleSubmit}
        open={open}
        title={!isNew ? t('addModel.editModelTitle') : t('addModel.addModelTitle')}
        width={500}
      >
        <Form form={form} layout="vertical" preserve={false}>
          <Form.Item
            label={t('addModel.selectChannel')}
            name="infraProviderId"
            rules={[{ message: t('addModel.selectChannelRequired'), required: true }]}
          >
            <Select
              disabled={!isNew}
              filterOption={(input, option) =>
                (option?.children as any)?.toLowerCase().includes(input.toLowerCase())
              }
              onChange={handleProviderChange}
              placeholder={t('addModel.selectChannelPlaceholder')}
              showSearch
            >
              {providers.map((provider) => (
                <Select.Option key={provider.id} value={provider.id}>
                  {provider.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label={t('addModel.selectModel')}
            name="infraModelId"
            rules={[{ message: t('addModel.selectModelRequired'), required: true }]}
          >
            <Select
              disabled={!isNew || !selectedProvider}
              filterOption={(input, option) =>
                (option?.children as any)?.toLowerCase().includes(input.toLowerCase())
              }
              placeholder={t('addModel.selectChannelPlaceholder')}
              showSearch
            >
              {models.map((model) => (
                <Select.Option key={model.id} value={model.id}>
                  {model.displayName || model.id}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label={t('addModel.weight')}
            name="weight"
            rules={[{ message: t('addModel.weightRequired'), required: true }]}
            tooltip={t('addModel.weightTooltip')}
          >
            <InputNumber
              max={100}
              min={0}
              placeholder={t('addModel.weightPlaceholder')}
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Form>
      </Modal>
    );
  },
);

export default AddChannelModelModal;
