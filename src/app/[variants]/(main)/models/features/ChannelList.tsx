'use client';

import { Icon } from '@lobehub/ui';
import { Button, Card, Row, Space, Table, Tag, Tooltip, message } from 'antd';
import { useTheme } from 'antd-style';
import type { ColumnsType } from 'antd/es/table';
import { Pencil, Plus, Trash2 } from 'lucide-react';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { trpcClient } from '@/libs/trpc/client';
import { AdminModelSelectItem } from '@/types/adminInfra';
import { AggregatedModelWithAssociationModels } from '@/types/aggregatedModel';

import AddChannelModelModal from './AddChannelModelModal';

// 定义渠道数据类型
export type ChannelItem = AdminModelSelectItem & {
  color?: string;
};

export interface ChannelListProps {
  data: AdminModelSelectItem[];
  loadModelDetail: () => void;
  loading?: boolean;
  modelDetail?: AggregatedModelWithAssociationModels;
}
// 根据 theme 生成 20 种固定颜色的数组（可根据主题色自定义，这里以常见色为例）
export const CHANNEL_COLORS: string[] = [
  '#1890ff', // 蓝色
  '#52c41a', // 绿色
  '#faad14', // 金色
  '#f5222d', // 红色
  '#722ed1', // 紫色
  '#13c2c2', // 青色
  '#eb2f96', // 粉色
  '#fa541c', // 橙色
  '#2f54eb', // 深蓝
  '#a0d911', // 黄绿色
  '#b37feb', // 淡紫
  '#36cfc9', // 浅青
  '#ffec3d', // 黄色
  '#ff7a45', // 珊瑚橙
  '#597ef7', // 浅蓝
  '#73d13d', // 浅绿
  '#ffc53d', // 浅金
  '#ff4d4f', // 浅红
  '#9254de', // 浅紫
  '#5cdbd3', // 浅青绿
];

const ChannelList: React.FC<ChannelListProps> = ({
  data,
  modelDetail,
  loading,
  loadModelDetail,
}) => {
  const theme = useTheme();
  const { t } = useTranslation('models');
  const getColorData = (data: ChannelItem[]) => {
    return data.map((item) => {
      return { color: CHANNEL_COLORS[data.indexOf(item)], ...item };
    });
  };
  const [newOpen, setNewOpen] = useState<{ data?: any; open: boolean }>({
    data: null,
    open: false,
  });

  // 编辑关联模型
  const handleEditAssociation = async (record: AdminModelSelectItem) => {
    if (!modelDetail) return;

    try {
      // 获取关联模型的详细信息
      const result = await trpcClient.aggregatedModel.getInfraModelRelationDetail.query({
        aggregatedModelId: modelDetail.id,
        infraModelId: record.id,
        infraProviderId: record.providerId,
      });

      if (result.success && result.data) {
        setNewOpen({
          data: {
            aggregatedModelId: result.data.aggregatedModelId,
            infraModelId: result.data.infraModelId,
            infraProviderId: result.data.infraProviderId,
            litellmModelId: result.data.litellmModelId,
            weight: result.data.litellmParams?.weight,
          },
          open: true,
        });
      } else {
        message.error(result.error || t('table.getAssociationInfoFailed'));
      }
    } catch (error: any) {
      message.error(`${t('table.getAssociationInfoFailed')}: ${error.message}`);
    }
  };

  // 删除关联模型
  const handleDeleteAssociation = async (record: AdminModelSelectItem) => {
    if (!modelDetail) return;

    try {
      const result = await trpcClient.aggregatedModel.deleteInfraModelRelation.mutate({
        aggregatedModelId: modelDetail.id,
        infraModelId: record.id,
        infraProviderId: record.providerId,
      });

      if (result.success) {
        message.success(t('table.deleteAssociationSuccess'));
        loadModelDetail(); // 重新加载数据
      } else {
        message.error(result.error || t('table.deleteAssociationFailed'));
      }
    } catch (error: any) {
      message.error(`${t('table.deleteAssociationFailed')}: ${error.message}`);
    }
  };

  const columns: ColumnsType<ChannelItem> = [
    {
      dataIndex: 'displayName',
      key: 'displayName',
      render: (text, record) => {
        return (
          <Space>
            <span
              style={{
                background: record?.color,
                borderRadius: '50%',
                display: 'inline-block',
                height: 10,
                width: 10,
              }}
            />
            <span>{text}</span>
          </Space>
        );
      },
      title: t('table.channel'),
    },
    {
      dataIndex: 'id',
      key: 'id',
      render: (text) => <Tag>{text}</Tag>,
      title: t('table.model'),
    },
    {
      width: 120,
      dataIndex: 'pricing',
      key: 'pricing',
      render: (text) => `$${text?.input} / $${text?.output}`,
      sorter: (a, b) => {
        //@ts-ignore
        const aPrice = parseFloat((a?.pricing?.input as string) || '0');
        //@ts-ignore
        const bPrice = parseFloat((b?.pricing?.input as string) || '0');
        return aPrice - bPrice;
      },
      title: t('table.price'),
    },
    {
      width: 100,
      dataIndex: 'weight',
      key: 'weight',
      //@ts-ignore
      sorter: (a, b) => a?.weight - b?.weight,
      title: t('table.weight'),
    },
    {
      width: 120,
      dataIndex: 'balance',
      key: 'balance',
      //@ts-ignore
      sorter: (a, b) => parseFloat(a?.balance) - parseFloat(b?.balance),
      title: t('table.loadBalance'),
    },
    {
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title={t('table.edit')}>
            <Button
              icon={<Icon icon={Pencil} size={{ size: 16 }} />}
              onClick={() => handleEditAssociation(record)}
              size="small"
            />
          </Tooltip>
          <Tooltip title={t('table.delete')}>
            <Button
              danger
              icon={<Icon icon={Trash2} size={{ size: 16 }} />}
              onClick={() => handleDeleteAssociation(record)}
              size="small"
            />
          </Tooltip>
        </Space>
      ),
      title: '',
      width: 80,
    },
  ];

  return (
    <Card
      extra={
        <Button onClick={() => setNewOpen({ open: true })} type="primary">
          <Icon icon={Plus} /> {t('table.addChannel')}
        </Button>
      }
      title={t('table.channelList')}
    >
      <Row
        align="middle"
        style={{
          background: theme.colorBgContainer,
          borderRadius: 20,
          marginBottom: 16,
          overflow: 'hidden',
        }}
        wrap={false}
      >
        {getColorData(data).map((item) => (
          <div
            key={item.color}
            //@ts-ignore
            style={{ background: item.color, height: 8, width: item?.balance ?? 0 }}
          />
        ))}
      </Row>
      <Table
        columns={columns}
        dataSource={getColorData(data)}
        loading={loading}
        locale={{ emptyText: t('table.noAssociationChannelModel') }}
        pagination={false}
        rowKey="id"
      />
      <AddChannelModelModal
        aggregatedModelId={modelDetail?.id}
        data={newOpen.data}
        onCancel={() => setNewOpen({ open: false })}
        onSuccess={loadModelDetail}
        open={newOpen.open}
      />
    </Card>
  );
};

export default ChannelList;
