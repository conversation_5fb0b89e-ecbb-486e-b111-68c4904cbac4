'use client';

import { Divider, Form, Input, InputNumber, Modal, Switch, message } from 'antd';
import { useEffect } from 'react';

import type { RoleItem } from '@/database/instance/lobechatDB/schemas/rbac';
import { trpcQuery } from '@/libs/trpc/client';

interface RoleConfigModalProps {
  loading: boolean;
  onCancel: () => void;
  onSubmit: (values: any) => void;
  open: boolean;
  role: RoleItem | null;
}

interface RoleFormData {
  description?: string;
  displayName: string;
  fileMb: number | null;
  isActive: boolean;
  name: string;
  // 配额相关
  tokenBudget: number | null;
  vectorCount: number | null;
}

export default function RoleConfigModal({
  open,
  role,
  onCancel,
  onSubmit,
  loading,
}: RoleConfigModalProps) {
  const [form] = Form.useForm<RoleFormData>();

  // 获取角色配额信息
  const { data: roleQuotas } = trpcQuery.rbac.getRoleQuotas.useQuery(
    { roleId: role?.id || 0 },
    { enabled: open && !!role },
  );

  useEffect(() => {
    if (open) {
      if (role) {
        // 编辑模式 - 设置基本信息和真实配额值
        form.setFieldsValue({
          description: role.description || '',
          displayName: role.displayName,
          // 使用真实的配额数据，如果还在加载则使用默认值
          fileMb: roleQuotas?.fileMb ?? 100,

          isActive: role.isActive,

          name: role.name,
          tokenBudget: roleQuotas?.tokenBudget ?? 1000,
          vectorCount: roleQuotas?.vectorCount ?? 10_000,
        });
      } else {
        // 新建模式
        form.resetFields();
        form.setFieldsValue({
          fileMb: 100,
          isActive: true,
          tokenBudget: 1000,
          vectorCount: 10_000,
        });
      }
    }
  }, [open, role, roleQuotas, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      // 提取配额信息
      const { fileMb, tokenBudget, vectorCount, ...roleInfo } = values;

      // 组织提交数据以匹配rbac路由的接口格式
      const submitData = {
        ...roleInfo,
        quota: {
          fileMb,
          tokenBudget,
          vectorCount,
        },
      };

      onSubmit(submitData);
    } catch {
      message.error('请检查表单填写是否正确');
    }
  };

  return (
    <Modal
      cancelText="取消"
      confirmLoading={loading}
      maskClosable={false}
      okText="确定"
      onCancel={onCancel}
      onOk={handleSubmit}
      open={open}
      title={role ? '编辑角色' : '新建角色'}
      width={600}
    >
      <Form autoComplete="off" form={form} layout="vertical">
        {/* 基本信息部分 */}
        <Form.Item
          label="角色名称"
          name="name"
          rules={[
            { message: '请输入角色名称', required: true },
            { message: '角色名称至少2个字符', min: 2 },
            { max: 50, message: '角色名称最多50个字符' },
          ]}
        >
          <Input placeholder="请输入角色名称（英文标识）" />
        </Form.Item>

        <Form.Item
          label="显示名称"
          name="displayName"
          rules={[
            { message: '请输入显示名称', required: true },
            { message: '显示名称至少2个字符', min: 2 },
            { max: 100, message: '显示名称最多100个字符' },
          ]}
        >
          <Input placeholder="请输入显示名称（中文名称）" />
        </Form.Item>

        <Form.Item
          label="角色描述"
          name="description"
          rules={[{ max: 500, message: '描述最多500个字符' }]}
        >
          <Input.TextArea maxLength={500} placeholder="请输入角色描述（可选）" rows={3} showCount />
        </Form.Item>

        <Form.Item label="启用状态" name="isActive" valuePropName="checked">
          <Switch checkedChildren="启用" unCheckedChildren="禁用" />
        </Form.Item>

        {/* 配额设置部分 */}
        <Divider orientation="left">配额设置</Divider>

        <Form.Item
          extra="用户可使用的Token数量限制"
          label="Token 配额"
          name="tokenBudget"
          rules={[
            { message: '请输入Token配额', required: true },
            { message: 'Token配额不能小于0', min: 0, type: 'number' },
          ]}
        >
          <InputNumber
            formatter={(value) => `${value}`.replaceAll(/\B(?=(\d{3})+(?!\d))/g, ',')}
            min={0}
            parser={(value) => Number(value!.replaceAll(/\$\s?|(,*)/g, '')) as any}
            placeholder="请输入Token配额"
            step={100}
            style={{ width: '100%' }}
          />
        </Form.Item>

        <Form.Item
          extra="用户可使用的文件存储空间限制"
          label="文件存储配额 (MB)"
          name="fileMb"
          rules={[
            { message: '请输入文件存储配额', required: true },
            { message: '文件存储配额不能小于0', min: 0, type: 'number' },
          ]}
        >
          <InputNumber
            addonAfter="MB"
            min={0}
            placeholder="请输入文件存储配额"
            step={10}
            style={{ width: '100%' }}
          />
        </Form.Item>

        <Form.Item
          extra="用户可创建的向量数量限制"
          label="向量数量配额"
          name="vectorCount"
          rules={[
            { message: '请输入向量数量配额', required: true },
            { message: '向量数量配额不能小于0', min: 0, type: 'number' },
          ]}
        >
          <InputNumber
            formatter={(value) => `${value}`.replaceAll(/\B(?=(\d{3})+(?!\d))/g, ',')}
            min={0}
            parser={(value) => Number(value!.replaceAll(/\$\s?|(,*)/g, '')) as any}
            placeholder="请输入向量数量配额"
            step={1000}
            style={{ width: '100%' }}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
}
