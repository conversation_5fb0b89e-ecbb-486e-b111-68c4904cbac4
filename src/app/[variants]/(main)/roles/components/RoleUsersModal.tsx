'use client';

import { Button, Form, Input, Modal, Space, Table, Tag, message } from 'antd';
import { useEffect, useState } from 'react';

import { trpcQuery } from '@/libs/trpc/client';

interface RoleUsersModalProps {
  onCancel: () => void;
  open: boolean;
  roleId: number | null;
  roleName: string;
}

export default function RoleUsersModal({ open, onCancel, roleId, roleName }: RoleUsersModalProps) {
  const [form] = Form.useForm();
  const [newUserIds, setNewUserIds] = useState<string>('');

  // 获取角色用户列表
  const {
    data: roleUsers,
    refetch: refetchUsers,
    isLoading,
  } = trpcQuery.user.getUsers.useQuery({});

  // 添加用户到角色
  const addUsersMutation = trpcQuery.rbac.addRoleUsers.useMutation({
    onError: (error: any) => {
      message.error(`添加用户失败: ${error.message}`);
    },
    onSuccess: () => {
      message.success('用户添加成功');
      setNewUserIds('');
      form.resetFields();
      refetchUsers();
    },
  });

  // 从角色移除用户
  const removeUsersMutation = trpcQuery.rbac.removeRoleUsers.useMutation({
    onError: (error: any) => {
      message.error(`移除用户失败: ${error.message}`);
    },
    onSuccess: () => {
      message.success('用户移除成功');
      refetchUsers();
    },
  });

  useEffect(() => {
    if (open) {
      form.resetFields();
      setNewUserIds('');
    }
  }, [open, form]);

  const handleAddUsers = async () => {
    if (!roleId || !newUserIds.trim()) {
      message.warning('请输入用户ID');
      return;
    }

    const userIds = newUserIds
      .split('\n')
      .map((id) => id.trim())
      .filter((id) => id.length > 0);

    if (userIds.length === 0) {
      message.warning('请输入有效的用户ID');
      return;
    }

    try {
      await addUsersMutation.mutateAsync({
        roleId,
        userIds,
      });
    } catch {
      // 错误处理已在mutation中处理
    }
  };

  const handleRemoveUser = async (userId: string) => {
    if (!roleId) return;

    try {
      await removeUsersMutation.mutateAsync({
        roleId,
        userIds: [userId],
      });
    } catch {
      // 错误处理已在mutation中处理
    }
  };

  const columns = [
    {
      dataIndex: 'username',
      key: 'username',
      title: '用户名',
    },
    {
      dataIndex: 'id',
      key: 'id',
      title: '用户ID',
    },
    {
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => new Date(date).toLocaleString(),
      title: '添加时间',
    },
    {
      dataIndex: 'expiresAt',
      key: 'expiresAt',
      render: (date: string | null) => (date ? new Date(date).toLocaleString() : '无过期时间'),
      title: '过期时间',
    },
    {
      key: 'actions',
      render: (_: any, record: any) => (
        <Button
          danger
          loading={removeUsersMutation.isPending}
          onClick={() => handleRemoveUser(record.id)}
          size="small"
          type="link"
        >
          移除
        </Button>
      ),
      title: '操作',
    },
  ];

  return (
    <Modal
      cancelText="关闭"
      footer={null}
      onCancel={onCancel}
      open={open}
      title={`管理角色用户 - ${roleName}`}
      width={800}
    >
      <div style={{ marginBottom: 16 }}>
        <h4>当前角色用户列表</h4>
        <Table
          columns={columns}
          dataSource={roleUsers?.data || []}
          loading={isLoading}
          pagination={{ pageSize: 5 }}
          rowKey="id"
          size="small"
        />
      </div>

      <div>
        <h4>添加新用户</h4>
        <Form form={form} layout="vertical">
          <Form.Item extra="每行输入一个用户ID，支持批量添加" label="用户ID列表" name="userIds">
            <Input.TextArea
              onChange={(e) => setNewUserIds(e.target.value)}
              placeholder={`请输入用户ID，例如：\nuser123\nuser456\nuser789`}
              rows={4}
              value={newUserIds}
            />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button loading={addUsersMutation.isPending} onClick={handleAddUsers} type="primary">
                添加用户
              </Button>
              <Tag color="blue">当前用户数量: {roleUsers?.data?.length || 0}</Tag>
            </Space>
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
}
