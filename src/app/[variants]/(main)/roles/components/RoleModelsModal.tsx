'use client';

import { Button, Input, Modal, Space, Tag, message } from 'antd';
import { useEffect, useState } from 'react';

import { trpcQuery } from '@/libs/trpc/client';

interface RoleModelsModalProps {
  onCancel: () => void;
  open: boolean;
  roleId: number | null;
  roleName: string;
}

export default function RoleModelsModal({
  open,
  onCancel,
  roleId,
  roleName,
}: RoleModelsModalProps) {
  const [modelInput, setModelInput] = useState<string>('');

  // 获取角色模型列表
  const {
    data: roleModels,
    refetch: refetchModels,
    isLoading,
  } = trpcQuery.rbac.getRoleModels.useQuery(
    { roleId: roleId! },
    { enabled: open && roleId !== null },
  );

  // 添加角色模型
  const addModelsMutation = trpcQuery.rbac.addRoleModels.useMutation({
    onError: (error: any) => {
      message.error(`添加模型失败: ${error.message}`);
    },
    onSuccess: () => {
      message.success('模型添加成功');
      refetchModels();
      setModelInput('');
    },
  });

  // 移除角色模型
  const removeModelsMutation = trpcQuery.rbac.removeRoleModels.useMutation({
    onError: (error: any) => {
      message.error(`删除模型失败: ${error.message}`);
    },
    onSuccess: () => {
      message.success('模型删除成功');
      refetchModels();
      setModelInput('');
    },
  });

  useEffect(() => {
    if (open) {
      // 清空输入框
      setModelInput('');
    }
  }, [open]);

  const handleAddModel = async () => {
    const modelName = modelInput.trim();

    if (!modelName) {
      message.warning('请输入模型名称');
      return;
    }

    if (!roleId) {
      message.error('角色ID无效');
      return;
    }

    // 类型转换和检查模型是否已存在
    const currentModels = (roleModels?.data as string[]) || [];
    if (currentModels.includes(modelName)) {
      message.warning('模型已存在');
      return;
    }

    // 添加模型
    try {
      await addModelsMutation.mutateAsync({
        models: [modelName],
        roleId,
      });
    } catch {
      // 错误处理已在mutation中处理
    }
  };

  const handleRemoveModel = async () => {
    const modelName = modelInput.trim();

    if (!modelName) {
      message.warning('请输入要删除的模型名称');
      return;
    }

    if (!roleId) {
      message.error('角色ID无效');
      return;
    }

    // 类型转换和检查模型是否存在
    const currentModels = (roleModels?.data as string[]) || [];
    if (!currentModels.includes(modelName)) {
      message.warning('模型不存在');
      return;
    }

    // 移除模型
    try {
      await removeModelsMutation.mutateAsync({
        models: [modelName],
        roleId,
      });
    } catch {
      // 错误处理已在mutation中处理
    }
  };

  const currentModelsCount = roleModels?.data?.length || 0;
  const modelsList = (roleModels?.data as string[]) || [];

  return (
    <Modal
      cancelText="关闭"
      footer={null}
      onCancel={onCancel}
      open={open}
      title={`管理角色模型 - ${roleName}`}
      width={700}
    >
      <div style={{ marginBottom: 24 }}>
        <h4>当前可用模型列表</h4>
        <div style={{ marginBottom: 12 }}>
          <Tag color="blue">当前模型数量: {currentModelsCount}</Tag>
          {isLoading && <Tag color="orange">加载中...</Tag>}
        </div>
        {modelsList.length > 0 ? (
          <div
            style={{
              background: '#f5f5f5',
              borderRadius: '6px',
              maxHeight: '200px',
              overflowY: 'auto',
              padding: '12px',
            }}
          >
            <Space wrap>
              {modelsList.map((model, index) => (
                <Tag color="default" key={index}>
                  {model}
                </Tag>
              ))}
            </Space>
          </div>
        ) : (
          <div
            style={{
              background: '#f5f5f5',
              borderRadius: '6px',
              color: '#999',
              padding: '12px',
              textAlign: 'center',
            }}
          >
            暂无可用模型
          </div>
        )}
      </div>

      <div>
        <h4>模型操作</h4>
        <Space.Compact style={{ marginBottom: 16, width: '100%' }}>
          <Input
            onChange={(e) => setModelInput(e.target.value)}
            onPressEnter={handleAddModel}
            placeholder="请输入模型名称，例如：gpt-4, claude-3-sonnet"
            value={modelInput}
          />
          <Button loading={addModelsMutation.isPending} onClick={handleAddModel} type="primary">
            添加模型
          </Button>
          <Button danger loading={removeModelsMutation.isPending} onClick={handleRemoveModel}>
            删除模型
          </Button>
        </Space.Compact>

        <div style={{ color: '#666', fontSize: '12px' }}>
          提示：输入模型名称后，点击&quot;添加模型&quot;将其加入列表，点击&quot;删除模型&quot;将其从列表中移除
        </div>
      </div>
    </Modal>
  );
}
