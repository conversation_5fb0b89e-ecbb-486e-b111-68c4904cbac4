'use client';

import { But<PERSON>, Mo<PERSON>, Space, Switch, Table, message } from 'antd';
import { useState } from 'react';

import type { RoleItem } from '@/database/instance/lobechatDB/schemas/rbac';
import { trpcQuery } from '@/libs/trpc/client';

import RoleConfigModal from './components/RoleConfigModal';
import RoleModelsModal from './components/RoleModelsModal';
import RoleUsersModal from './components/RoleUsersModal';

export default function RolesPage() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingRole, setEditingRole] = useState<RoleItem | null>(null);
  const [isUsersModalOpen, setIsUsersModalOpen] = useState(false);
  const [isModelsModalOpen, setIsModelsModalOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState<RoleItem | null>(null);

  const { data: roles, refetch, isLoading } = trpcQuery.rbac.getAllRoles.useQuery();

  const createRoleMutation = trpcQuery.rbac.createRole.useMutation();
  const updateRoleMutation = trpcQuery.rbac.updateRole.useMutation();

  const deleteRoleMutation = trpcQuery.rbac.deleteRole.useMutation({
    onError: (error: any) => {
      message.error(`删除失败: ${error.message}`);
    },
    onSuccess: () => {
      message.success('角色删除成功');
      refetch();
    },
  });

  const toggleRoleMutation = trpcQuery.rbac.toggleRoleActive.useMutation({
    onError: (error: any) => {
      message.error(`切换失败: ${error.message}`);
    },
    onSuccess: () => {
      message.success('角色状态切换成功');
      refetch();
    },
  });

  const handleCreate = () => {
    setEditingRole(null);
    setIsModalOpen(true);
  };

  const handleEdit = (role: RoleItem) => {
    setEditingRole(role);
    setIsModalOpen(true);
  };

  const handleDelete = (roleId: number) => {
    Modal.confirm({
      content: '确定要删除这个角色吗？此操作不可撤销。',
      onOk: () => deleteRoleMutation.mutate({ roleId }),
      title: '确认删除',
    });
  };

  const handleToggleActive = (roleId: number) => {
    toggleRoleMutation.mutate({ roleId });
  };

  const handleManageUsers = (role: RoleItem) => {
    setSelectedRole(role);
    setIsUsersModalOpen(true);
  };

  const handleManageModels = (role: RoleItem) => {
    setSelectedRole(role);
    setIsModelsModalOpen(true);
  };

  const handleSubmit = async (values: any) => {
    try {
      const { quota, ...roleData } = values;

      if (editingRole) {
        // 更新角色（包含配额）
        await updateRoleMutation.mutateAsync({
          roleId: editingRole.id,
          ...roleData,
          quota,
        });
      } else {
        // 创建角色（包含配额）
        await createRoleMutation.mutateAsync({
          ...roleData,
          quota,
        });
      }

      message.success(editingRole ? '角色更新成功' : '角色创建成功');
      refetch();
      setIsModalOpen(false);
      setEditingRole(null);
    } catch (error: any) {
      message.error(`操作失败: ${error.message}`);
    }
  };

  const columns = [
    {
      dataIndex: 'id',
      key: 'id',
      title: 'ID',
      width: 80,
    },
    {
      dataIndex: 'name',
      key: 'name',
      title: '角色名称',
    },
    {
      dataIndex: 'displayName',
      key: 'displayName',
      title: '显示名称',
    },
    {
      dataIndex: 'description',
      ellipsis: true,
      key: 'description',
      title: '描述',
    },
    {
      dataIndex: 'isSystem',
      key: 'isSystem',
      render: (isSystem: boolean) => <Switch checked={isSystem} disabled size="small" />,
      title: '系统角色',
    },
    {
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive: boolean, record: RoleItem) => (
        <Switch
          checked={isActive}
          loading={toggleRoleMutation.isPending}
          onChange={() => handleToggleActive(record.id)}
          size="small"
        />
      ),
      title: '状态',
    },
    {
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => new Date(date).toLocaleString(),
      title: '创建时间',
    },
    {
      key: 'actions',
      render: (_: any, record: RoleItem) => (
        <Space>
          <Button onClick={() => handleEdit(record)} size="small" type="link">
            编辑
          </Button>
          <Button onClick={() => handleManageUsers(record)} size="small" type="link">
            用户管理
          </Button>
          <Button onClick={() => handleManageModels(record)} size="small" type="link">
            模型管理
          </Button>
          {!record.isSystem && (
            <Button
              danger
              loading={deleteRoleMutation.isPending}
              onClick={() => handleDelete(record.id)}
              size="small"
              type="link"
            >
              删除
            </Button>
          )}
        </Space>
      ),
      title: '操作',
    },
  ];

  return (
    <div style={{ padding: 24 }}>
      <div
        style={{
          alignItems: 'center',
          display: 'flex',
          justifyContent: 'space-between',
          marginBottom: 16,
        }}
      >
        <h1>角色管理</h1>
        <Button onClick={handleCreate} type="primary">
          新建角色
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={roles}
        loading={isLoading}
        pagination={{ pageSize: 10 }}
        rowKey="id"
      />

      <RoleConfigModal
        loading={createRoleMutation.isPending || updateRoleMutation.isPending}
        onCancel={() => {
          setIsModalOpen(false);
          setEditingRole(null);
        }}
        onSubmit={handleSubmit}
        open={isModalOpen}
        role={editingRole}
      />

      <RoleUsersModal
        onCancel={() => {
          setIsUsersModalOpen(false);
          setSelectedRole(null);
        }}
        open={isUsersModalOpen}
        roleId={selectedRole?.id || null}
        roleName={selectedRole?.displayName || ''}
      />

      <RoleModelsModal
        onCancel={() => {
          setIsModelsModalOpen(false);
          setSelectedRole(null);
        }}
        open={isModelsModalOpen}
        roleId={selectedRole?.id || null}
        roleName={selectedRole?.displayName || ''}
      />
    </div>
  );
}
