import { ActionType, ProColumns } from '@ant-design/pro-components';
import { Button } from '@lobehub/ui';
import { App } from 'antd';
import { parseAsInteger, useQueryState } from 'nuqs';
import { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import ProTable from '@/components/Table';
import UserInTable from '@/components/UserInTable';
import { trpcClient } from '@/libs/trpc/client';
import { ProviderMigration } from '@/types/migration';

import ProviderDetail from './ProviderDetail';

interface MigrationTableProps {
  keyVaultsSecret: string;
}
const MigrationTable = ({ keyVaultsSecret }: MigrationTableProps) => {
  const { t } = useTranslation('user');
  const [isMigrating, setIsMigrating] = useState<boolean | undefined>(undefined);
  const [clearing, setIsClearing] = useState<boolean | undefined>(undefined);
  const [tableLoading, setTableLoading] = useState<boolean | undefined>(undefined);
  const { message, modal } = App.useApp();
  const [total, setTotal] = useState<number>(0);
  const [successCount, setSuccessCount] = useState<number>(0);
  const [currentPage, setCurrentPage] = useQueryState(
    'current',
    parseAsInteger.withDefault(1).withOptions({ clearOnDefault: true }),
  );
  const [pageSize, setPageSize] = useQueryState(
    'pageSize',
    parseAsInteger.withDefault(20).withOptions({ clearOnDefault: true }),
  );

  const columns: ProColumns<ProviderMigration>[] = [
    {
      dataIndex: 'emailOrUsernameOrUserId',
      hideInTable: true,
      key: 'emailOrUsernameOrUserId',
      title: t('table.columns.emailOrUsernameOrUserId'),
    },
    {
      dataIndex: 'username',
      hideInSearch: true,
      key: 'username',
      render: (_, entity) => {
        const { avatar, email, username, id } = entity;
        return (
          <UserInTable
            avatar={avatar}
            email={email}
            id={id}
            navToDetail={false}
            username={username}
          />
        );
      },
      title: t('table.columns.username'),
      width: 250,
    },
    {
      dataIndex: 'status',
      hideInSearch: true,
      title: '迁移状态',
      valueEnum: {
        error: {
          status: 'error',
          text: '错误',
        },
        success: {
          status: 'success',
          text: '成功',
        },
        waiting: {
          status: 'default',
          text: '待开始',
        },
      },
      width: 120,
    },
    {
      dataIndex: 'beforeProviders',
      hideInSearch: true,
      render: (_, record) => {
        const hasLength = record.beforeProviders.length > 0;
        return hasLength ? (
          <>
            {record.beforeProviders.length}： {record.beforeProviders.join(' / ')}
          </>
        ) : (
          '未解析出服务商，请检查你的 KEY_VAULTS_SECRET 是否正确'
        );
      },
      title: '旧版服务商配置',
      width: 400,
    },
    {
      dataIndex: 'afterProviders',
      hideInSearch: true,
      render: (_, record) => {
        const hasLength = record.afterProviders.length > 0;
        return hasLength ? (
          <>
            {record.afterProviders.length}： {record.afterProviders.join(' / ')}
          </>
        ) : (
          '0'
        );
      },
      title: '新版服务商配置',
      width: 400,
    },
  ];

  const actionRef = useRef<ActionType>(null);

  const runMigration = async (userIds?: string[]) => {
    setIsMigrating(true);
    await trpcClient.migration.runMigration.mutate({ keyVaultsSecret, userIds });
    await actionRef.current?.reload();
    setIsMigrating(false);
  };

  return (
    <ProTable
      actionRef={actionRef}
      columns={columns}
      expandable={{
        expandedRowRender: (record) => (
          <ProviderDetail keyVaultsSecret={keyVaultsSecret} userId={record.id} />
        ),
      }}
      loading={isMigrating || tableLoading}
      onLoadingChange={(loading) => setTableLoading(loading as boolean)}
      pagination={{
        current: currentPage,
        onChange: (page) => {
          setCurrentPage(page);
        },
        onShowSizeChange: (current, size) => {
          setCurrentPage(current);
          setPageSize(size);
        },
        pageSize,
      }}
      request={async (params) => {
        const result = await trpcClient.migration.getMigrationProviders.query({
          keyVaultsSecret,
          params,
        });

        setTotal(result.total);
        setSuccessCount(result.data?.filter((item) => item.status === 'success').length);

        return result;
      }}
      rowKey={'id'}
      rowSelection={{}}
      tableAlertOptionRender={({ selectedRows, onCleanSelected }) => {
        return (
          <Flexbox gap={16} horizontal>
            <Button
              color={'default'}
              disabled={isMigrating}
              loading={clearing}
              onClick={async () => {
                const userIds = selectedRows
                  .filter((row) => row.status === 'success')
                  .map((row) => row.id);

                if (userIds.length > 0) {
                  setIsClearing(true);
                  await trpcClient.migration.cleanMigratedUsersConfig.mutate({ userIds });
                  await actionRef.current?.reload();
                  setIsClearing(false);
                } else {
                  message.info('暂无可清理用户');
                }

                onCleanSelected();
              }}
            >
              移除已成功用户旧版配置
            </Button>
            <Button
              color={'default'}
              loading={isMigrating}
              onClick={async () => {
                const userIds = selectedRows
                  .filter((row) => row.status !== 'success')
                  .map((row) => row.id);

                await runMigration(userIds);
                onCleanSelected();
              }}
              variant={'solid'}
            >
              迁移选中用户
            </Button>
          </Flexbox>
        );
      }}
      toolbar={{
        actions: [
          <Button
            color={'default'}
            disabled={tableLoading}
            key={'migration'}
            loading={isMigrating}
            onClick={async () => {
              const restUsers = total - successCount;

              // 实测两次：
              // 459 迁移耗时 15s , 约 32.6ms/人
              // 349 迁移耗时 11.77s , 约 33.7ms /人
              const ratio = 0.035;

              modal.confirm({
                content: `确定迁移所有用户配置？${restUsers} 位用户约耗时 ${restUsers * ratio} 秒`,
                onOk: async () => {
                  runMigration();
                },
                title: '迁移全量用户配置',
              });
            }}
          >
            迁移全量用户配置
          </Button>,
        ],
        title: tableLoading
          ? '获取迁移用户中...'
          : [`需迁移用户 ${total}`, `待迁移 ${total - successCount}`].filter(Boolean).join(' · '),
      }}
    />
  );
};

export default MigrationTable;
