import { Statistic } from 'antd';
import { createStyles } from 'antd-style';
import { Flexbox } from 'react-layout-kit';

import { ModelStatistics } from '@/types/migration';

const useStyles = createStyles(({ token, css }) => ({
  item: css`
    .ant-statistic-content-value-int {
      color: ${token.colorTextTertiary};
    }
  `,
}));

const ModelStats = (props: ModelStatistics) => {
  const { styles, cx } = useStyles();
  return (
    <Flexbox gap={64} horizontal>
      <Statistic
        className={cx(props.enabledModels === 0 && styles.item)}
        title="启用模型"
        value={props.enabledModels}
      />
      <Statistic
        className={cx(props.remoteModels === 0 && styles.item)}
        title="远程模型"
        value={props.remoteModels}
      />
      <Statistic
        className={cx(props.customModels === 0 && styles.item)}
        title="自定义模型"
        value={props.customModels}
      />
    </Flexbox>
  );
};

export default ModelStats;
