import { Button, InputPassword } from '@lobehub/ui';
import { Typography } from 'antd';
import { createStyles } from 'antd-style';
import { Lock, Shield } from 'lucide-react';
import { useState } from 'react';
import { Center, Flexbox } from 'react-layout-kit';

const useStyles = createStyles(({ token, css }) => ({
  content: css`
    width: 100%;
    max-width: 400px;
    padding: 32px;
    border-radius: ${token.borderRadiusLG}px;

    text-align: center;

    background: ${token.colorBgContainer};
  `,
  icon: css`
    width: 64px;
    height: 64px;
    margin-block-end: 24px;
    color: ${token.colorPrimary};
  `,
  inputWrapper: css`
    display: flex;
    align-items: center;
    margin-block-end: 24px;

    .anticon {
      color: ${token.colorTextSecondary};
    }
  `,
  title: css`
    margin-block-end: 24px;
    font-size: 24px;
    font-weight: bold;
    color: ${token.colorTextHeading};
  `,
}));

interface EncryptionPageProps {
  key: string;
  setKey: (key: string) => void;
}
const EncryptionPage = ({ setKey, key }: EncryptionPageProps) => {
  const { styles } = useStyles();

  const [value, setValue] = useState(key);

  const handleSubmit = () => {
    setKey(value);
  };

  return (
    <Center>
      <div className={styles.content}>
        <Shield className={styles.icon} />
        <h1 className={styles.title}>输入秘钥开始迁移</h1>
        <div>
          请输入
          <span style={{ marginInline: 4 }}>
            <code>KEY_VAULTS_SECRET</code>
          </span>
          ，用于迁移用户自定义的 API Key、Proxy URL 等敏感数据。
        </div>

        <Flexbox gap={16} style={{ marginTop: 24 }}>
          <InputPassword
            onChange={(e) => setValue(e.target.value)}
            onPressEnter={handleSubmit}
            placeholder="请输入 KEY_VAULTS_SECRET"
            value={value}
          />
          <Button block icon={Lock} onClick={handleSubmit} type="primary">
            准备迁移
          </Button>
        </Flexbox>
        <div style={{ marginTop: 16 }}>
          <Typography.Text type={'secondary'}>
            该字段仅用于加密解密用户数据，不会被系统存储
          </Typography.Text>
        </div>
      </div>
    </Center>
  );
};

export default EncryptionPage;
