import { ProColumns } from '@ant-design/pro-components';
import { ActionIcon, Highlighter } from '@lobehub/ui';
import { createStyles } from 'antd-style';
import { ArrowUpRight } from 'lucide-react';
import { useState } from 'react';
import { Flexbox } from 'react-layout-kit';

import ProTable from '@/components/Table';
import { trpcClient } from '@/libs/trpc/client';
import { ProviderMigrationDetail } from '@/types/migration';

import ModelDetail from './ModelDetail';
import ModelStats from './ModelStats';

const useStyles = createStyles(({ css }) => ({
  compare: css`
    vertical-align: top;
  `,
}));

interface ProviderDetailProps {
  keyVaultsSecret: string;
  userId: string;
}

const ProviderDetail = ({ keyVaultsSecret, userId }: ProviderDetailProps) => {
  const { styles } = useStyles();

  const [modelModalVisible, setOpen] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState<string>('');

  const providerColumns: ProColumns<ProviderMigrationDetail>[] = [
    {
      dataIndex: 'id',
      render: (data, record) => (
        <Flexbox align={'center'} gap={4} horizontal width={100}>
          {data}
          <ActionIcon
            icon={ArrowUpRight}
            onClick={() => {
              setSelectedProvider(record.id);
              setOpen(true);
            }}
            size={'small'}
            title={'查看详情'}
          />
        </Flexbox>
      ),
      title: '服务商',
      width: 80,
    },
    {
      className: styles.compare,
      dataIndex: 'before',
      render: (data, record) => (
        <Flexbox gap={12}>
          <ModelStats {...record.beforeModels} />
          <Highlighter language={'json'} style={{ height: '100%' }} wrap>
            {JSON.stringify(data, null, 2)}
          </Highlighter>
        </Flexbox>
      ),

      title: '迁移前',
      width: 400,
    },
    {
      className: styles.compare,
      dataIndex: 'after',
      render: (data, record) =>
        !record.after ? (
          <Highlighter language={'json'}>undefined</Highlighter>
        ) : (
          <Flexbox gap={12}>
            <ModelStats {...record.afterModels} />
            <Highlighter language={'json'}>{JSON.stringify(data, null, 2)}</Highlighter>
          </Flexbox>
        ),
      title: '迁移后',
      width: 400,
    },
  ];

  return (
    <>
      <ProTable
        columns={providerColumns}
        pagination={false}
        request={async () => {
          return await trpcClient.migration.getUserConfigAiInfra.query({
            keyVaultsSecret,
            userId,
          });
        }}
        rowKey={'id'}
        search={false}
        size={'small'}
        toolbar={{ settings: undefined }}
      />
      {selectedProvider && (
        <ModelDetail
          open={modelModalVisible}
          providerId={selectedProvider}
          setOpen={(open) => {
            setOpen(open);

            if (!open) {
              setSelectedProvider('');
            }
          }}
          userId={userId}
        />
      )}
    </>
  );
};

export default ProviderDetail;
