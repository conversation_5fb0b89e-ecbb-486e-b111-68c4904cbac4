import { ProColumns } from '@ant-design/pro-components';
import { Highlighter } from '@lobehub/ui';
import { Modal } from 'antd';
import { Flexbox } from 'react-layout-kit';

import ProTable from '@/components/Table';
import { trpcClient } from '@/libs/trpc/client';
import { ModelMigrationDetail } from '@/types/migration';

interface ModelDetailProps {
  open: boolean;
  providerId: string;
  setOpen: (open: boolean) => void;
  userId: string;
}

const ModelDetail = ({ providerId, open, setOpen, userId }: ModelDetailProps) => {
  const columns: ProColumns<ModelMigrationDetail>[] = [
    {
      dataIndex: 'id',
      title: '模型',
      width: 200,
    },
    {
      dataIndex: 'before',
      render: (data) => (
        <Flexbox>
          <Highlighter language={'json'} style={{ height: '100%' }} wrap>
            {JSON.stringify(data, null, 2)}
          </Highlighter>
        </Flexbox>
      ),

      title: '迁移前',
      width: 500,
    },
    {
      dataIndex: 'after',
      render: (data) => (
        <Flexbox>
          <Highlighter language={'json'} style={{ height: '100%' }} wrap>
            {JSON.stringify(data, null, 2)}
          </Highlighter>
        </Flexbox>
      ),

      title: '迁移后',
      width: 500,
    },
  ];

  return (
    <Modal
      footer={null}
      onCancel={() => setOpen(false)}
      open={open}
      title={`${providerId} Models 详情`}
      width={'90vw'}
    >
      <ProTable
        columns={columns}
        pagination={false}
        request={async () => {
          return await trpcClient.migration.getModelsDetail.query({
            providerId,
            userId,
          });
        }}
        rowKey={'id'}
        search={false}
        toolbar={{ settings: undefined }}
      />
    </Modal>
  );
};

export default ModelDetail;
