import { checkVercelEnv } from '@/envs/vercel';

import EnvError from './features/EnvError';
import SendTestMessage from './features/SendTestMessage';
import Settings from './features/Settings';

const Page = () => {
  const isVercelEnvValid = checkVercelEnv();

  if (!isVercelEnvValid) {
    return <EnvError />;
  }

  return (
    <>
      <SendTestMessage />
      <Settings />
    </>
  );
};

Page.displayName = 'ConsoleWebhook';

export default Page;
