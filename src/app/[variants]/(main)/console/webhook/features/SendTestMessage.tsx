'use client';

import { SiDiscord } from '@icons-pack/react-simple-icons';
import { Button } from '@lobehub/ui';
import { App, Card } from 'antd';
import { useTheme } from 'antd-style';
import { memo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

const SendTestMessage = memo(() => {
  const { message } = App.useApp();
  const { t } = useTranslation('console');
  const theme = useTheme();
  const [loading, setLoading] = useState(false);
  return (
    <Card bordered={false} style={{ background: theme.colorFillTertiary }}>
      <Flexbox gap={16} horizontal justify={'space-between'}>
        <Flexbox align={'center'} gap={16} horizontal>
          <SiDiscord size={24} />
          <div>{t('webhooks.discord')}</div>
        </Flexbox>
        <Button
          loading={loading}
          onClick={async () => {
            setLoading(true);
            await fetch('/api/webhooks/discord/test');
            message.success(t('webhooks.sendSuccess'));
            setLoading(false);
          }}
          type={'primary'}
        >
          {t('webhooks.sendTestMessage')}
        </Button>
      </Flexbox>
    </Card>
  );
});

export default SendTestMessage;
