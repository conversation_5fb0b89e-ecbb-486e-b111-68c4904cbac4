'use client';

import { Alert, Tag } from '@lobehub/ui';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

const EnvError = memo(() => {
  const { t } = useTranslation('console');
  const tag = ['EDGE_CONFIG', 'VERCEL_ACCESS_TOKEN'].map((key) => <Tag key={key}>{key}</Tag>);
  return (
    <Alert
      description={
        <Flexbox gap={4} horizontal>
          {tag}
        </Flexbox>
      }
      message={t('webhooks.errors.invalidEnv')}
      type={'warning'}
    />
  );
});

export default EnvError;
