'use client';

import { Button, Form, type FormProps, Input, Select } from '@lobehub/ui';
import { App, Skeleton, Switch, TimePicker } from 'antd';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { memo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { DEFAULT_WEBHOOK_SETTINGS } from '@/const/webhook';
import { trpcClient, trpcQuery } from '@/libs/trpc/client';
import { localeOptions } from '@/locales/resources';
import { merge } from '@/utils/merge';

dayjs.extend(utc);
const timeFormat = 'HH:mm';

const Settings = memo(() => {
  const { message } = App.useApp();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const { t } = useTranslation('console');
  const { data, isLoading } = trpcQuery.webhooks.getConfig.useQuery();

  const items: FormProps['items'] = [
    {
      children: <Switch />,
      label: t('webhooks.form.enabled'),
      minWidth: undefined,
      name: 'enabled',
      valuePropName: 'checked',
    },
    {
      children: <Input placeholder={'https://discord.com/api/webhooks/xxx'} />,
      label: 'Discord Webhook URL',
      name: 'webhookUrl',
      required: true,
    },
    {
      children: (
        <Select
          options={localeOptions.map((item) => ({
            label: item.label,
            value: item.value,
          }))}
        />
      ),
      label: t('webhooks.form.lang'),
      name: 'lang',
    },
    {
      children: <TimePicker format={timeFormat} minuteStep={30} style={{ width: '100%' }} />,
      getValueFromEvent: (date) => date?.utc().format(timeFormat),
      getValueProps: (value) => ({
        value: value ? dayjs.utc(value, timeFormat).local() : undefined,
      }),
      label: t('webhooks.form.time'),
      name: 'time',
    },
  ];

  if (isLoading) return <Skeleton active title={false} />;

  return (
    <Form
      footer={
        <>
          <Button
            htmlType="button"
            onClick={() => {
              form.resetFields();
              message.success(t('webhooks.restSuccess'));
            }}
          >
            {t('webhooks.reset')}
          </Button>
          <Button htmlType="submit" loading={loading} type="primary">
            {t('webhooks.submit')}
          </Button>
        </>
      }
      initialValues={merge(DEFAULT_WEBHOOK_SETTINGS, data)}
      itemMinWidth={'max(30%,240px)'}
      items={items}
      itemsType={'flat'}
      onFinish={async (v) => {
        setLoading(true);
        console.log(v);
        const result = await trpcClient.webhooks.updateConfig.query(v);
        if (result) message.success(t('webhooks.submitSuccess'));
        setLoading(false);
      }}
      variant={'borderless'}
    />
  );
});

export default Settings;
