'use client';

import { I<PERSON>, Tooltip } from '@lobehub/ui';
import { useResponsive, useTheme } from 'antd-style';
import { InfoIcon } from 'lucide-react';
import { parseAsInteger, useQueryState } from 'nuqs';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import { trpcQuery } from '@/libs/trpc/client';
import { EndUserType } from '@/types/endUsers';

import TabCard from '../../../features/TabCard';

const types = ['all', 'paid', 'active', 'inactive', 'risk', 'bug', 'banned'] as EndUserType[];

interface UserTabsProps {
  activeKey: EndUserType;
  setActiveKey: (key: EndUserType) => void;
}

const UserTabs = memo<UserTabsProps>(({ activeKey, setActiveKey }) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [_, setCurrentPage] = useQueryState(
    'current',
    parseAsInteger.withDefault(1).withOptions({ clearOnDefault: true }),
  );
  const { data, isLoading } = trpcQuery.user.getUserTypes.useQuery();
  const { mobile } = useResponsive();
  const { t } = useTranslation('user');
  const theme = useTheme();

  const info: {
    [key in EndUserType]?: string;
  } = {
    [EndUserType.All]: undefined,
    [EndUserType.Paid]: t('tabs.info.paid'),
    [EndUserType.Active]: t('tabs.info.active'),
    [EndUserType.Inactive]: t('tabs.info.inactive'),
    [EndUserType.Risk]: t('tabs.info.risk'),
    [EndUserType.Bug]: t('tabs.info.bug'),
    [EndUserType.Banned]: t('tabs.info.banned'),
  };

  return (
    <Flexbox
      gap={12}
      horizontal
      style={{
        overflowX: 'auto',
        overflowY: 'hidden',
        paddingBottom: 4,
        paddingInline: mobile ? 16 : 0,
      }}
      width={'100%'}
    >
      {types.map((key) => (
        <TabCard
          active={activeKey === key}
          count={data?.[key]}
          extra={
            info[key] && (
              <Tooltip title={info[key]}>
                <Icon
                  color={activeKey === key ? theme.colorTextDescription : theme.colorFill}
                  icon={InfoIcon}
                />
              </Tooltip>
            )
          }
          key={key}
          label={t(`tabs.type.${key}`)}
          loading={isLoading}
          onClick={() => {
            setActiveKey(key);
            setCurrentPage(1);
          }}
        />
      ))}
    </Flexbox>
  );
});

export default UserTabs;
