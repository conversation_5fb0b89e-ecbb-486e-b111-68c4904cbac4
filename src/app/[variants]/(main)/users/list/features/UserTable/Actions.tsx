import { Si<PERSON><PERSON><PERSON> } from '@icons-pack/react-simple-icons';
import { ActionIcon, CopyButton, Dropdown, Icon } from '@lobehub/ui';
import { Popover } from 'antd';
import { useTheme } from 'antd-style';
import { BanIcon, CircleDashedIcon, LockIcon, MoreHorizontalIcon, UnlockIcon } from 'lucide-react';
import Link from 'next/link';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';
import urlJoin from 'url-join';

import { useUserActions } from '@/app/[variants]/(main)/users/features/useUserActions';
import { serverConfigSelectors, useServerConfigStore } from '@/store/serverConfig';
import { EndUser } from '@/types/endUsers';

const Actions = memo<{ entity: EndUser; refetch: () => void }>(({ entity, refetch }) => {
  const theme = useTheme();
  const { enableClerk, clerkUrl } = useServerConfigStore(serverConfigSelectors.config);
  const { t } = useTranslation('user');
  const { lockUser, banUser, isLoaidng } = useUserActions({ onFinish: refetch });

  const { id, clerk } = entity;

  return (
    <Flexbox gap={8} horizontal justify={'flex-end'}>
      {enableClerk ? (
        <Link href={urlJoin(clerkUrl, 'users', entity.id)} target={'_blank'}>
          <ActionIcon
            active
            fill={theme.colorText}
            icon={SiClerk}
            size={'small'}
            title={t('actions.clerk')}
          />
        </Link>
      ) : (
        <Popover
          arrow={false}
          content={
            <span
              style={{
                fontFamily: theme.fontFamilyCode,
              }}
            >
              {entity.id}
            </span>
          }
          placement={'topRight'}
          trigger={['hover']}
        >
          <CopyButton active content={entity.id} size={'small'} />
        </Popover>
      )}
      <Dropdown
        menu={{
          items: [
            {
              danger: true,
              icon: <Icon icon={clerk?.locked ? UnlockIcon : LockIcon} />,
              key: 'lock',
              label: clerk?.locked ? t('actions.unlock') : t('actions.lock'),
              onClick: () => lockUser(id, clerk?.locked),
            },
            {
              danger: true,
              icon: <Icon icon={clerk?.locked ? CircleDashedIcon : BanIcon} />,
              key: 'ban',
              label: clerk?.banned ? t('actions.unban') : t('actions.ban'),
              onClick: () => banUser(id, clerk?.banned),
            },
          ],
        }}
        trigger={['click']}
      >
        <ActionIcon active icon={MoreHorizontalIcon} loading={isLoaidng} size={'small'} />
      </Dropdown>
    </Flexbox>
  );
});

export default Actions;
