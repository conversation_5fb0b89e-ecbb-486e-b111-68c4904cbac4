import dayjs from 'dayjs';
import { memo } from 'react';
import { Flexbox } from 'react-layout-kit';

import { EndUserCredit } from '@/types/endUsers';
import { Plans } from '@/types/subscription';
import { today } from '@/utils/time';

import CreditProgress from './CreditProgress';

interface CreditProgressProps extends EndUserCredit {
  plan: Plans;
}

const CreditOverview = memo<CreditProgressProps>(
  ({
    plan,
    freeExpire,
    subscriptionExpire,
    subscriptionUsed,
    freeUsed,
    freePercent,
    subscriptionPercent,
  }) => {
    const isFreeExpired = dayjs(freeExpire).isBefore(today());
    if (plan === Plans.Free) {
      return (
        <CreditProgress
          creditPercent={freePercent}
          disabled={isFreeExpired}
          expiredAt={freeExpire}
          spend={freeUsed}
        />
      );
    }

    return (
      <Flexbox gap={4}>
        {!isFreeExpired && (
          <CreditProgress creditPercent={freePercent} expiredAt={freeExpire} spend={freeUsed} />
        )}
        {plan !== Plans.Hobby && (
          <CreditProgress
            creditPercent={subscriptionPercent}
            expiredAt={subscriptionExpire}
            spend={subscriptionUsed}
          />
        )}
      </Flexbox>
    );
  },
);

export default CreditOverview;
