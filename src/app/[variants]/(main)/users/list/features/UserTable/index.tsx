'use client';

import { ActionType, ProColumns, ProTableProps } from '@ant-design/pro-components';
import { Icon, Tag, Tooltip } from '@lobehub/ui';
import { BanIcon, LockIcon, SirenIcon } from 'lucide-react';
import { parseAsInteger, useQueryState } from 'nuqs';
import { memo, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import { useDateRangePresets } from '@/components/DateRangePicker/useDateRangePresets';
import Table from '@/components/Table';
import UserInTable from '@/components/UserInTable';
import PlanIcon from '@/features/PlanIcon';
import { trpcClient } from '@/libs/trpc/client';
import { featureFlagSelectors } from '@/store/featureFlag/selectors';
import { useFeatureFlagStore } from '@/store/featureFlag/store';
import { serverConfigSelectors, useServerConfigStore } from '@/store/serverConfig';
import { EndUser, EndUserType } from '@/types/endUsers';
import { SortQuery } from '@/types/query';
import { Plans } from '@/types/subscription';

import Actions from './Actions';
import ActiveIndex from './ActiveIndex';
import CreditOverview from './CreditOverview';

const request: ProTableProps<any, any>['request'] = async (params, sorts) =>
  trpcClient.user.getUsers.query({ params, sorts: sorts as SortQuery });

const UserTable = memo<{
  activeKey: EndUserType;
}>(({ activeKey }) => {
  const { enableClerk } = useServerConfigStore(serverConfigSelectors.config);
  const subscriptionEnabled = useFeatureFlagStore(featureFlagSelectors.subscriptionEnabled);

  const { t } = useTranslation('user');
  const defaultPresets = useDateRangePresets() || [];

  const [currentPage, setCurrentPage] = useQueryState(
    'current',
    parseAsInteger.withDefault(1).withOptions({ clearOnDefault: true }),
  );
  const [pageSize, setPageSize] = useQueryState(
    'pageSize',
    parseAsInteger.withDefault(10).withOptions({ clearOnDefault: true }),
  );

  const tableRef = useRef<ActionType>(null);

  const refetch = () => tableRef.current?.reload();

  useEffect(() => {
    refetch();
  }, [activeKey]);

  const columns: ProColumns<EndUser>[] = [
    {
      dataIndex: 'emailOrUsernameOrUserId',
      hideInTable: true,
      key: 'emailOrUsernameOrUserId',
      title: t('table.columns.emailOrUsernameOrUserId'),
    },
    {
      dataIndex: 'range',
      fieldProps: {
        presets: defaultPresets,
      },
      hideInTable: true,
      key: 'range',
      title: t('table.columns.registerAt'),
      valueType: 'dateRange',
    },
    {
      dataIndex: 'username',
      hideInSearch: true,
      key: 'username',
      render: (_, entity) => {
        const { avatar, email, username, id } = entity;
        return (
          <UserInTable
            avatar={avatar}
            email={email}
            extra={
              <Flexbox
                horizontal
                style={{
                  zoom: 0.8,
                }}
              >
                {entity.hasRisk && (
                  <Tooltip title={t('table.user.risk')}>
                    <Tag color={'error'} icon={<Icon icon={SirenIcon} />} />
                  </Tooltip>
                )}
                {entity?.clerk?.locked && (
                  <Tooltip title={t('status.locked')}>
                    <Tag color={'warning'} icon={<Icon icon={LockIcon} />} style={{ margin: 0 }} />
                  </Tooltip>
                )}
                {entity?.clerk?.banned && (
                  <Tooltip title={t('status.banned')}>
                    <Tag color={'error'} icon={<Icon icon={BanIcon} />} style={{ margin: 0 }} />
                  </Tooltip>
                )}
              </Flexbox>
            }
            id={id}
            username={username}
          />
        );
      },
      title: t('table.columns.username'),
      width: 250,
    },
    {
      dataIndex: 'bizData',
      hideInSearch: true,
      key: 'bizData',
      render: (_, entity) => <ActiveIndex {...entity} />,
      title: t('table.columns.bizData'),
      width: 150,
    },
    ...(subscriptionEnabled
      ? ([
          {
            dataIndex: 'plan',
            key: 'plan',
            render: (_, entity) => {
              const { plan } = entity;
              return <PlanIcon plan={plan} size={24} type={'combine'} />;
            },
            title: t('table.columns.plan'),
            width: 150,

            /* eslint-disable sort-keys-fix/sort-keys-fix  */
            valueEnum: {
              [Plans.Free]: {
                text: <PlanIcon plan={Plans.Free} size={20} type={'combine'} />,
              },
              [Plans.Hobby]: {
                text: <PlanIcon plan={Plans.Hobby} size={20} type={'combine'} />,
              },
              [Plans.Starter]: {
                text: <PlanIcon plan={Plans.Starter} size={20} type={'combine'} />,
              },
              [Plans.Premium]: {
                text: <PlanIcon plan={Plans.Premium} size={20} type={'combine'} />,
              },
              [Plans.Ultimate]: {
                text: <PlanIcon plan={Plans.Ultimate} size={20} type={'combine'} />,
              },
            },
            /* eslint-enable */
          },
          {
            dataIndex: 'creditUsage',
            hideInSearch: true,
            key: 'creditUsage',
            render: (_, entity) => <CreditOverview {...entity.credits} plan={entity.plan} />,
            title: t('table.columns.creditUsage'),
            width: 240,
          },
        ] as ProColumns<EndUser>[])
      : []),
    {
      align: 'right',
      dataIndex: 'createdAt',
      hideInSearch: true,
      key: 'createdAt',
      sorter: true,
      title: t('table.columns.registerAt'),
      valueType: 'dateTime',
      width: 150,
    },
    {
      align: 'right',
      dataIndex: ['clerk', 'lastActiveAt'],
      hideInSearch: true,
      hideInTable: !enableClerk,
      key: 'lastActiveAt',
      title: t('table.columns.lastActiveAt'),
      valueType: 'dateTime',
      width: 150,
    },
    {
      align: 'right',
      dataIndex: 'id',
      fixed: 'right',
      hideInSearch: true,
      key: 'detail',
      render: (_, entity) => <Actions entity={entity} refetch={refetch} />,
      title: '',
      width: 56,
    },
  ];

  return (
    <Table<EndUser>
      actionRef={tableRef}
      columns={columns}
      headerTitle={t(`tabs.type.${activeKey as EndUserType}`)}
      key={'id'}
      pagination={{
        current: currentPage,
        onChange: (page) => {
          setCurrentPage(page);
        },
        onShowSizeChange: (current, size) => {
          setCurrentPage(current);
          setPageSize(size);
        },
        pageSize,
      }}
      request={(params, sort, filter) => request({ ...params, userType: activeKey }, sort, filter)}
      rowKey={'id'}
    />
  );
});

export default UserTable;
