import { Icon, Tag, Tooltip } from '@lobehub/ui';
import { CheckIcon, MessageSquareIcon, MessageSquareOffIcon, XIcon } from 'lucide-react';
import { memo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import { EndUser } from '@/types/endUsers';

import MessageModal from '../../../features/MessageModal';

type ActiveIndexProps = EndUser;

const ActiveIndex = memo<ActiveIndexProps>(({ isOnboarded, id, messageCount }) => {
  const { t } = useTranslation('user');
  const [open, setOpen] = useState(false);

  return (
    <Flexbox gap={4} horizontal>
      <Tooltip title={t('table.user.isOnboard')}>
        <Tag
          color={isOnboarded ? 'success' : 'default'}
          icon={<Icon icon={isOnboarded ? CheckIcon : XIcon} />}
        />
      </Tooltip>
      <Tooltip title={t('table.user.messages')}>
        <Tag
          color={messageCount !== 0 ? 'success' : 'default'}
          icon={<Icon icon={messageCount !== 0 ? MessageSquareIcon : MessageSquareOffIcon} />}
          onClick={() => {
            setOpen(true);
          }}
          style={{ cursor: 'pointer' }}
        >
          {messageCount || ''}
        </Tag>
      </Tooltip>
      {messageCount !== 0 && (
        <MessageModal
          key={`${id}_${open}`}
          onClose={() => {
            setOpen(false);
          }}
          open={open}
          userId={id}
        />
      )}
    </Flexbox>
  );
});

export default ActiveIndex;
