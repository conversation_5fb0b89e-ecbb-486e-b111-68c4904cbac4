import { Tooltip } from '@lobehub/ui';
import { Progress, Typography } from 'antd';
import { useTheme } from 'antd-style';
import dayjs from 'dayjs';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';

interface CreditProgressProps {
  creditPercent: number;
  disabled?: boolean;
  expiredAt?: string | null;
  spend?: number | null;
}
const format = (num: number) => new Intl.NumberFormat('en-US').format(num);

const CreditProgress = memo<CreditProgressProps>(
  ({ creditPercent, disabled, spend, expiredAt }) => {
    const { t } = useTranslation('user');
    const theme = useTheme();
    if (typeof spend !== 'number') return <Typography.Text type={'secondary'}>-</Typography.Text>;

    return (
      <Tooltip
        title={[
          t('table.credit.used', { used: format(spend) }),
          t('table.credit.expiredAt', { expiredAt: dayjs(expiredAt).format('YYYY-MM-DD') }),
        ].join(' · ')}
      >
        <Progress
          format={(number) => (disabled ? t('table.credit.expired') : `${number?.toFixed(1)}%`)}
          percent={creditPercent}
          size="small"
          strokeColor={disabled ? theme.colorTextDisabled : undefined}
          style={{
            cursor: 'not-allowed',
            opacity: disabled ? 0.5 : 1,
          }}
        />
      </Tooltip>
    );
  },
);

export default CreditProgress;
