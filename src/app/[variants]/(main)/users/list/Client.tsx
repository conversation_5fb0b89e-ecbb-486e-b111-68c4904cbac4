'use client';

import { useResponsive } from 'antd-style';
import { useQueryState } from 'nuqs';
import { memo } from 'react';
import { Flexbox } from 'react-layout-kit';

import { EndUserType } from '@/types/endUsers';

import UserTable from './features/UserTable';
import UserTabs from './features/UserTabs';

const Client = memo(() => {
  const [activeKey, setActiveKey] = useQueryState('userType', {
    clearOnDefault: true,
    defaultValue: EndUserType.All,
  });
  const { mobile } = useResponsive();
  return (
    <Flexbox gap={mobile ? 16 : 40}>
      <UserTabs activeKey={activeKey as EndUserType} setActiveKey={setActiveKey} />
      <UserTable activeKey={activeKey as EndUserType} />
    </Flexbox>
  );
});

export default Client;
