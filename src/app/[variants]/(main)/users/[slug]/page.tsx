import { Flexbox } from 'react-layout-kit';

import { PageProps } from '@/types/next';
import { RouteVariants } from '@/utils/server/routeVariants';

import Profile from './features/Profile';

type Props = PageProps<{ slug: string; variants: string }>;

const Page = async (props: Props) => {
  const params = await props.params;
  const isMobile = await RouteVariants.getIsMobile(props);

  return (
    <Flexbox gap={64} horizontal width={'100%'} wrap={'wrap'}>
      <Profile isMobile={isMobile} userId={params.slug} />
    </Flexbox>
  );
};

Page.displayName = 'UsersProfile';

export default Page;

export const dynamic = 'force-static';
