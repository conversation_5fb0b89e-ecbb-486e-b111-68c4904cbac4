'use client';

import { Drawer, Empty, Flex, Pagination, Space } from 'antd';
import { parseAsInteger, useQueryState } from 'nuqs';
import { memo, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import type { TopicListItem } from '@/database/models/topic';
import { trpcClient } from '@/libs/trpc/client';

import { TopicListSkeleton } from './SkeletonLoading';
import TopicCard from './TopicCard';

interface MessageTopicDrawerProps {
  onClose: () => void;
  open: boolean;
  userId: string;
}

const MessageTopicDrawer = memo<MessageTopicDrawerProps>(({ open, onClose, userId }) => {
  const { t } = useTranslation('user');

  // 话题列表状态
  const [current, setCurrent] = useQueryState(
    'current',
    parseAsInteger.withDefault(1).withOptions({ clearOnDefault: true }),
  );
  const [topics, setTopics] = useState<TopicListItem[]>([]);
  const [topicsLoading, setTopicsLoading] = useState(false);
  const [topicsTotal, setTopicsTotal] = useState(0);

  // 加载话题列表
  const loadTopics = async () => {
    setTopicsLoading(true);

    try {
      const result = await trpcClient.user.getUserTopics.query({
        params: {
          current,
          pageSize: 5,
          userId,
        },
      });

      setTopics(result?.data || []);
      setTopicsTotal(result?.total || 0);
    } catch (error) {
      console.error('Failed to load topics:', error);
    } finally {
      setTopicsLoading(false);
    }
  };

  // 当抽屉打开时自动加载话题列表
  useEffect(() => {
    if (open && userId) {
      loadTopics();
    }
  }, [open, userId, current]);

  return (
    <Drawer
      onClose={onClose}
      open={open}
      placement="right"
      size="large"
      title={t('drawer.topic.title')}
      width={1200}
    >
      {topicsLoading ? (
        <TopicListSkeleton />
      ) : topics.length === 0 ? (
        <Empty description={t('drawer.topic.empty')} image={Empty.PRESENTED_IMAGE_SIMPLE} />
      ) : (
        <Flex gap={16} vertical>
          <Space direction="vertical" size="middle" style={{ width: '100%' }}>
            {topics.map((topic) => (
              <TopicCard key={topic.id} topic={topic} />
            ))}
          </Space>

          <Pagination
            current={current}
            hideOnSinglePage
            onChange={(page) => setCurrent(page)}
            pageSize={5}
            showQuickJumper
            showSizeChanger={false}
            size="small"
            style={{ justifyContent: 'flex-end' }}
            total={topicsTotal}
          />
        </Flex>
      )}
    </Drawer>
  );
});

export default MessageTopicDrawer;

export { type TopicListItem } from '@/database/models/topic';
