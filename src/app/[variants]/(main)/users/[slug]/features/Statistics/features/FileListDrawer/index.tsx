'use client';

import type { ProColumns, ProTableProps } from '@ant-design/pro-components';
import { Drawer, Flex, Tag } from 'antd';
import { createStyles } from 'antd-style';
import { Hexagon } from 'lucide-react';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';

import Table from '@/components/Table';
import { trpcClient } from '@/libs/trpc/client';
import { FileItem } from '@/types/file';
import { SortQuery } from '@/types/query';
import { getFileIcon } from '@/utils/fileIcon';
import { formatDateWithTime, formatSize } from '@/utils/format';

interface FileListDrawerProps {
  onClose: () => void;
  open: boolean;
  userId: string;
}

const useStyles = createStyles(({ css, token }) => ({
  fileIcon: css`
    height: 32px;

    svg path {
      fill: ${token.colorPrimary};
    }
  `,
  fileNameContainer: css`
    display: flex;
    gap: 8px;
    align-items: center;
  `,
  tag: css`
    margin-inline-end: 0;
  `,
  wrapper: css`
    justify-content: space-between;
    padding-inline-end: 12px;
    font-weight: 500;
  `,
}));

const FileListDrawer = memo<FileListDrawerProps>(({ open, onClose, userId }) => {
  const { t } = useTranslation(['common', 'user']);
  const { styles } = useStyles();

  // 表格数据请求函数
  const request: ProTableProps<FileItem, any>['request'] = async (params, sorts) => {
    const result = await trpcClient.file.getFilesList.query({
      params: {
        ...params,
        userId, // 固定查询当前用户的文件
      },
      sorts: sorts as SortQuery,
    });

    return {
      data: result?.data || [],
      success: result?.success || false,
      total: result?.total || 0,
    };
  };

  // 表格列定义
  const columns: ProColumns<FileItem>[] = [
    {
      dataIndex: 'name',
      ellipsis: true,
      render: (_, record) => {
        const { name, chunkCount, knowledgeBases } = record;
        const fileIcon = getFileIcon(name);

        return (
          <Flex className={styles.wrapper}>
            <Flex className={styles.fileNameContainer} gap={12}>
              <span className={styles.fileIcon}>{fileIcon}</span>
              <span>{name}</span>
            </Flex>
            <Flex gap={8}>
              {knowledgeBases.map((kb) => (
                <Tag className={styles.tag} color="green" key={kb.id}>
                  {kb.name}
                </Tag>
              ))}
              {!!chunkCount && (
                <Tag className={styles.tag} color="purple">
                  <Flex align="center" gap={4}>
                    <Hexagon height={14} width={14} />
                    <span>{chunkCount}</span>
                  </Flex>
                </Tag>
              )}
            </Flex>
          </Flex>
        );
      },
      title: t('user:file.name'),
    },
    {
      dataIndex: 'createdAt',
      render: (_, record) => formatDateWithTime(record.createdAt),
      sorter: true,
      title: t('common:time.createdAt'),
      valueType: 'dateTime',
      width: 180,
    },
    {
      dataIndex: 'size',
      render: (_, record) => formatSize(record.size),
      sorter: true,
      title: t('user:file.size'),
      width: 160,
    },
  ];

  return (
    <Drawer
      destroyOnHidden
      onClose={onClose}
      open={open}
      placement="right"
      size="large"
      title={t('user:file.title')}
      width={1200}
    >
      <Table<FileItem>
        columns={columns}
        key="id"
        options={false}
        request={request}
        rowKey="id"
        search={false}
      />
    </Drawer>
  );
});

export default FileListDrawer;
