import { Flex, Skeleton } from 'antd';
import { createStyles } from 'antd-style';
import { memo } from 'react';

const useStyles = createStyles(({ css, token }) => ({
  messageCardSkeleton: css`
    padding-block: 16px;
    padding-inline: 24px;
    border-radius: 0 16px 16px;
    background: ${token.colorBgContainerSecondary};
  `,
  topicCardSkeleton: css`
    padding: 16px;
    border: 1px solid ${token.colorBorderSecondary};
    border-radius: ${token.borderRadius}px;
    background: ${token.colorBgContainer};

    &:hover {
      border-color: ${token.colorPrimary};
    }
  `,
}));

// 话题骨架屏
export const TopicSkeleton = memo(() => {
  const { styles } = useStyles();

  return (
    <div className={styles.topicCardSkeleton}>
      <Flex align="center" justify="space-between">
        <Skeleton.Input active size="small" style={{ width: 180 }} />
        {/* 创建时间 */}
        <Skeleton.Input active size="small" style={{ width: 140 }} />
      </Flex>
    </div>
  );
});

TopicSkeleton.displayName = 'TopicSkeleton';

// 消息骨架屏
export const MessageSkeleton = memo(() => {
  const { styles } = useStyles();

  return (
    <div className={styles.messageCardSkeleton}>
      <Flex gap={8} vertical>
        {/* 消息内容 - 模拟多行文本 */}
        <Flex gap={4} vertical>
          <Skeleton.Input active style={{ height: 16, width: '95%' }} />
          <Skeleton.Input active style={{ height: 16, width: '85%' }} />
          <Skeleton.Input active style={{ height: 16, width: '75%' }} />
        </Flex>

        {/* 底部标签和时间 */}
        <Flex gap={8}>
          {/* 角色标签 */}
          <Skeleton.Button active size="small" />
          {/* 创建时间 */}
          <Skeleton.Input active size="small" style={{ width: 130 }} />
        </Flex>
      </Flex>
    </div>
  );
});

MessageSkeleton.displayName = 'MessageSkeleton';

// 话题列表骨架屏
export const TopicListSkeleton = memo(() => {
  return (
    <Flex gap={16} vertical>
      {Array.from({ length: 5 }, (_, index) => (
        <TopicSkeleton key={index} />
      ))}
    </Flex>
  );
});

TopicListSkeleton.displayName = 'TopicListSkeleton';

// 消息列表骨架屏
export const MessageListSkeleton = memo(() => {
  return (
    <Flex gap={16} vertical>
      {Array.from({ length: 5 }, (_, index) => (
        <MessageSkeleton key={index} />
      ))}
    </Flex>
  );
});

MessageListSkeleton.displayName = 'MessageListSkeleton';
