export const FILE_TYPE_ICON_MAP = {
  // 位图
  '.bmp、.jpg、.jpeg、.png、.gif、.tiff、.tif': (
    <svg fill="none" height="32" viewBox="0 0 26 32" width="26" xmlns="http://www.w3.org/2000/svg">
      <path d="M20 10H26L16 0V6C16 8.20914 17.7909 10 20 10Z" fill="black" fillOpacity="0.15" />
      <path
        d="M16 0V6C16 8.20914 17.7909 10 20 10H26V28C26 30.2091 24.2091 32 22 32H4C1.79086 32 0 30.2091 0 28V4C0 1.79086 1.79086 0 4 0H16Z"
        fill="black"
        fillOpacity="0.06"
      />
      <path
        d="M0 28V4C0 1.79086 1.79086 0 4 0H16V6L16.0049 6.20605C16.1087 8.2512 17.7488 9.89135 19.7939 9.99512L20 10H26V28L25.9951 28.2061C25.8913 30.2512 24.2512 31.8913 22.2061 31.9951L22 32V31.583C23.979 31.583 25.583 29.979 25.583 28V10.417H20C17.5607 10.417 15.583 8.43926 15.583 6V0.416992H4C2.02098 0.416992 0.416992 2.02098 0.416992 4V28C0.416992 29.979 2.02098 31.583 4 31.583V32L3.79395 31.9951C1.68056 31.8879 0 30.14 0 28ZM22 31.583V32H4V31.583H22Z"
        fill="black"
        fillOpacity="0.15"
      />
      <path
        d="M18.3335 22.2761L16.4715 20.4141C16.3465 20.2891 16.1769 20.2188 16.0002 20.2188C15.8455 20.2188 15.6963 20.2727 15.5776 20.3698L15.5288 20.4141L10.6095 25.3334H17.6668C18.035 25.3334 18.3335 25.0349 18.3335 24.6667V22.2761ZM11.6668 18C11.6668 17.6319 11.3684 17.3334 11.0002 17.3334C10.632 17.3334 10.3335 17.6319 10.3335 18C10.3335 18.3682 10.632 18.6667 11.0002 18.6667C11.3684 18.6667 11.6668 18.3682 11.6668 18ZM18.3335 15.3334C18.3335 14.9652 18.035 14.6667 17.6668 14.6667H8.3335C7.96531 14.6667 7.66683 14.9652 7.66683 15.3334V24.6667C7.66683 25.0349 7.96531 25.3334 8.3335 25.3334H8.72412L14.5861 19.4714L14.7326 19.3386C15.0885 19.047 15.536 18.8855 16.0002 18.8855C16.5305 18.8855 17.0392 19.0965 17.4142 19.4714L18.3335 20.3907V15.3334ZM13.0002 18C13.0002 19.1046 12.1047 20 11.0002 20C9.89559 20 9.00016 19.1046 9.00016 18C9.00016 16.8955 9.89559 16 11.0002 16C12.1047 16 13.0002 16.8955 13.0002 18ZM19.6668 24.6667C19.6668 25.7713 18.7714 26.6667 17.6668 26.6667H8.3335C7.22893 26.6667 6.3335 25.7713 6.3335 24.6667V15.3334C6.3335 14.2288 7.22893 13.3334 8.3335 13.3334H17.6668C18.7714 13.3334 19.6668 14.2288 19.6668 15.3334V24.6667Z"
        fill="black"
      />
    </svg>
  ),
  // 编程与开发文件
  '.c、.cpp、.java、.py、.js、.html、.htm、.css、.class、.obj、.lib、.a、.so、.dll': (
    <svg fill="none" height="32" viewBox="0 0 26 32" width="26" xmlns="http://www.w3.org/2000/svg">
      <path d="M20 10H26L16 0V6C16 8.20914 17.7909 10 20 10Z" fill="black" fillOpacity="0.15" />
      <path
        d="M16 0V6C16 8.20914 17.7909 10 20 10H26V28C26 30.2091 24.2091 32 22 32H4C1.79086 32 0 30.2091 0 28V4C0 1.79086 1.79086 0 4 0H16Z"
        fill="black"
        fillOpacity="0.06"
      />
      <path
        d="M18.333 15.3334C18.333 14.9652 18.0345 14.6667 17.6663 14.6667H8.33301C7.96482 14.6667 7.66634 14.9652 7.66634 15.3334V24.6667C7.66634 25.0349 7.96482 25.3334 8.33301 25.3334H17.6663C18.0345 25.3334 18.333 25.0349 18.333 24.6667V15.3334ZM14.9997 20C15.3679 20 15.6663 20.2985 15.6663 20.6667C15.6663 21.0349 15.3679 21.3334 14.9997 21.3334H12.333C11.9648 21.3334 11.6663 21.0349 11.6663 20.6667C11.6663 20.2985 11.9648 20 12.333 20H14.9997ZM9.19499 16.1954C9.43906 15.9513 9.82506 15.9362 10.0869 16.1498L10.1377 16.1954L11.471 17.5287C11.7314 17.789 11.7314 18.211 11.471 18.4714L10.1377 19.8047C9.87735 20.0651 9.45534 20.0651 9.19499 19.8047C8.93464 19.5444 8.93464 19.1224 9.19499 18.862L10.057 18L9.19499 17.1381L9.14941 17.0873C8.93585 16.8254 8.95091 16.4394 9.19499 16.1954ZM19.6663 24.6667C19.6663 25.7713 18.7709 26.6667 17.6663 26.6667H8.33301C7.22844 26.6667 6.33301 25.7713 6.33301 24.6667V15.3334C6.33301 14.2288 7.22844 13.3334 8.33301 13.3334H17.6663C18.7709 13.3334 19.6663 14.2288 19.6663 15.3334V24.6667Z"
        fill="black"
      />
      <path
        d="M0 28V4C0 1.79086 1.79086 0 4 0H16V6L16.0049 6.20605C16.1087 8.2512 17.7488 9.89135 19.7939 9.99512L20 10H26V28L25.9951 28.2061C25.8913 30.2512 24.2512 31.8913 22.2061 31.9951L22 32V31.583C23.979 31.583 25.583 29.979 25.583 28V10.417H20C17.5607 10.417 15.583 8.43926 15.583 6V0.416992H4C2.02098 0.416992 0.416992 2.02098 0.416992 4V28C0.416992 29.979 2.02098 31.583 4 31.583V32L3.79395 31.9951C1.68056 31.8879 0 30.14 0 28ZM22 31.583V32H4V31.583H22Z"
        fill="black"
        fillOpacity="0.15"
      />
    </svg>
  ),
  // 数据库与数据文件
  '.db、.sqlite、.mdb、.accdb、.csv、.json、.xml': (
    <svg fill="none" height="32" viewBox="0 0 26 32" width="26" xmlns="http://www.w3.org/2000/svg">
      <path d="M20 10H26L16 0V6C16 8.20914 17.7909 10 20 10Z" fill="black" fillOpacity="0.15" />
      <path
        d="M16 0V6C16 8.20914 17.7909 10 20 10H26V28C26 30.2091 24.2091 32 22 32H4C1.79086 32 0 30.2091 0 28V4C0 1.79086 1.79086 0 4 0H16Z"
        fill="black"
        fillOpacity="0.06"
      />
      <path
        d="M18.333 21.6823C18.0682 21.818 17.7728 21.9404 17.4535 22.0468C16.2415 22.4508 14.6398 22.6666 12.9997 22.6666C11.3596 22.6666 9.75784 22.4508 8.5459 22.0468C8.22652 21.9404 7.93118 21.818 7.66634 21.6823V24.6679C7.66649 24.6689 7.66677 24.6708 7.66764 24.6738C7.66964 24.6805 7.67523 24.6944 7.68783 24.7155C7.71434 24.7597 7.76959 24.8282 7.87467 24.9127C8.08875 25.0849 8.44654 25.2748 8.96777 25.4485C10.0063 25.7947 11.4572 26 12.9997 26C14.5422 26 15.9931 25.7947 17.0316 25.4485C17.5528 25.2748 17.9106 25.0849 18.1247 24.9127C18.2298 24.8282 18.285 24.7597 18.3115 24.7155C18.3241 24.6944 18.3297 24.6805 18.3317 24.6738C18.3326 24.6708 18.3329 24.6689 18.333 24.6679V21.6823ZM18.333 17.0162C18.0654 17.1531 17.769 17.275 17.4535 17.3802C16.283 17.7703 14.7082 18 12.9997 18C11.2911 18 9.7163 17.7703 8.5459 17.3802C8.23031 17.275 7.93392 17.1531 7.66634 17.0162V20.0013C7.66649 20.0022 7.66677 20.0042 7.66764 20.0071C7.66964 20.0138 7.67523 20.0277 7.68783 20.0488C7.71434 20.093 7.76959 20.1615 7.87467 20.2461C8.08875 20.4183 8.44654 20.6081 8.96777 20.7819C10.0063 21.128 11.4572 21.3333 12.9997 21.3333C14.5422 21.3333 15.9931 21.128 17.0316 20.7819C17.5528 20.6081 17.9106 20.4183 18.1247 20.2461C18.2298 20.1615 18.285 20.093 18.3115 20.0488C18.3241 20.0277 18.3297 20.0138 18.3317 20.0071C18.3326 20.0042 18.3329 20.0022 18.333 20.0013V17.0162ZM18.3311 15.3229C18.3288 15.3154 18.3229 15.301 18.3096 15.2793C18.2813 15.2332 18.2235 15.1639 18.1156 15.0787C17.8954 14.9053 17.5356 14.7194 17.0316 14.5514C16.0304 14.2177 14.6048 14 12.9997 14C11.3945 14 9.96895 14.2177 8.96777 14.5514C8.46372 14.7194 8.1039 14.9053 7.88379 15.0787C7.7758 15.1639 7.71806 15.2332 7.68978 15.2793C7.67646 15.301 7.67059 15.3154 7.66829 15.3229C7.66619 15.3298 7.66634 15.3328 7.66634 15.3333C7.66634 15.3338 7.66619 15.3368 7.66829 15.3437C7.67059 15.3512 7.67646 15.3656 7.68978 15.3873C7.71806 15.4334 7.7758 15.5027 7.88379 15.5879C8.1039 15.7613 8.46372 15.9472 8.96777 16.1152C9.96895 16.4489 11.3945 16.6666 12.9997 16.6666C14.6048 16.6666 16.0304 16.4489 17.0316 16.1152C17.5356 15.9472 17.8954 15.7613 18.1156 15.5879C18.2235 15.5027 18.2813 15.4334 18.3096 15.3873C18.3229 15.3656 18.3288 15.3512 18.3311 15.3437C18.3332 15.3368 18.333 15.3338 18.333 15.3333C18.333 15.3328 18.3332 15.3298 18.3311 15.3229ZM19.6663 24.6666L19.6624 24.7682C19.6251 25.2701 19.3142 25.6665 18.9606 25.9511C18.5773 26.2595 18.0571 26.5123 17.4535 26.7135C16.2415 27.1175 14.6398 27.3333 12.9997 27.3333C11.3596 27.3333 9.75784 27.1175 8.5459 26.7135C7.94225 26.5123 7.42201 26.2595 7.03874 25.9511C6.6851 25.6665 6.3743 25.2701 6.33691 24.7682L6.33301 24.6666V15.3333C6.33301 14.7654 6.67874 14.3304 7.05827 14.0312C7.44533 13.7262 7.96427 13.4803 8.5459 13.2864C9.7163 12.8963 11.2911 12.6666 12.9997 12.6666C14.7082 12.6666 16.283 12.8963 17.4535 13.2864C18.0351 13.4803 18.554 13.7262 18.9411 14.0312C19.3206 14.3304 19.6663 14.7654 19.6663 15.3333V24.6666Z"
        fill="black"
      />
      <path
        d="M0 28V4C0 1.79086 1.79086 0 4 0H16V6L16.0049 6.20605C16.1087 8.2512 17.7488 9.89135 19.7939 9.99512L20 10H26V28L25.9951 28.2061C25.8913 30.2512 24.2512 31.8913 22.2061 31.9951L22 32V31.583C23.979 31.583 25.583 29.979 25.583 28V10.417H20C17.5607 10.417 15.583 8.43926 15.583 6V0.416992H4C2.02098 0.416992 0.416992 2.02098 0.416992 4V28C0.416992 29.979 2.02098 31.583 4 31.583V32L3.79395 31.9951C1.68056 31.8879 0 30.14 0 28ZM22 31.583V32H4V31.583H22Z"
        fill="black"
        fillOpacity="0.15"
      />
    </svg>
  ),
  // Word
  '.doc、.docx': (
    <svg fill="none" height="32" viewBox="0 0 26 32" width="26" xmlns="http://www.w3.org/2000/svg">
      <path d="M20 10H26L16 0V6C16 8.20914 17.7909 10 20 10Z" fill="black" fillOpacity="0.15" />
      <path
        d="M16 0V6C16 8.20914 17.7909 10 20 10H26V28C26 30.2091 24.2091 32 22 32H4C1.79086 32 0 30.2091 0 28V4C0 1.79086 1.79086 0 4 0H16Z"
        fill="black"
        fillOpacity="0.06"
      />
      <path
        d="M16.4247 25H14.7393L13 18.3478L11.2472 25H9.5618L7 16H8.75281L10.5056 22.922L12.3663 16H13.6202L15.4809 22.922L17.2337 16H19L16.4247 25Z"
        fill="black"
      />
      <path
        d="M0 28V4C0 1.79086 1.79086 0 4 0H16V6L16.0049 6.20605C16.1087 8.2512 17.7488 9.89135 19.7939 9.99512L20 10H26V28L25.9951 28.2061C25.8913 30.2512 24.2512 31.8913 22.2061 31.9951L22 32V31.583C23.979 31.583 25.583 29.979 25.583 28V10.417H20C17.5607 10.417 15.583 8.43926 15.583 6V0.416992H4C2.02098 0.416992 0.416992 2.02098 0.416992 4V28C0.416992 29.979 2.02098 31.583 4 31.583V32L3.79395 31.9951C1.68056 31.8879 0 30.14 0 28ZM22 31.583V32H4V31.583H22Z"
        fill="black"
        fillOpacity="0.15"
      />
    </svg>
  ),
  // 系统与配置文件
  '.exe、.dll、.sys、.ini、.reg': (
    <svg fill="none" height="32" viewBox="0 0 26 32" width="26" xmlns="http://www.w3.org/2000/svg">
      <path d="M20 10H26L16 0V6C16 8.20914 17.7909 10 20 10Z" fill="black" fillOpacity="0.15" />
      <path
        d="M16 0V6C16 8.20914 17.7909 10 20 10H26V28C26 30.2091 24.2091 32 22 32H4C1.79086 32 0 30.2091 0 28V4C0 1.79086 1.79086 0 4 0H16Z"
        fill="black"
        fillOpacity="0.06"
      />
      <g clip-path="url(#clip0_293_2068)">
        <path
          d="M14.9998 18.6666C14.9998 17.5651 15.8983 16.6666 16.9998 16.6666H17.9888C16.9124 15.0588 15.08 14 12.9998 14C12.0417 14 11.1366 14.2256 10.3332 14.625V15.3333C10.3332 15.6869 10.4737 16.026 10.7238 16.276C10.9738 16.526 11.3129 16.6666 11.6665 16.6666C12.1969 16.6666 12.7055 16.8775 13.0806 17.2526C13.4556 17.6276 13.6665 18.1362 13.6665 18.6666C13.6665 19.0318 13.968 19.3333 14.3332 19.3333C14.51 19.3333 14.6795 19.263 14.8045 19.138C14.9295 19.013 14.9998 18.8434 14.9998 18.6666ZM15.6665 25.3743C16.8383 24.7918 17.7924 23.8384 18.3748 22.6666H16.3332C16.1564 22.6666 15.9868 22.7369 15.8618 22.8619C15.7368 22.987 15.6665 23.1565 15.6665 23.3333V25.3743ZM6.99984 20C6.99984 22.8552 8.99478 25.2424 11.6665 25.8489V24C11.6665 23.8231 11.5962 23.6536 11.4712 23.5286C11.3618 23.4192 11.2183 23.3517 11.0656 23.3365L10.9998 23.3333C10.4694 23.3333 9.96085 23.1224 9.58577 22.7474C9.25764 22.4192 9.05511 21.989 9.0096 21.5312L8.99984 21.3333V20.6666C8.99984 20.4898 8.92955 20.3203 8.80452 20.1953C8.6795 20.0702 8.50998 20 8.33317 20H6.99984ZM20.3332 20C20.3332 24.05 17.0499 27.3333 12.9998 27.3333C12.7537 27.3333 12.5105 27.3205 12.2707 27.2968C12.2678 27.2966 12.265 27.2965 12.2622 27.2962C8.55852 26.9262 5.6665 23.8011 5.6665 20C5.6665 17.2924 7.13472 14.9291 9.31755 13.6588C9.33052 13.6508 9.34371 13.6431 9.35726 13.636C10.4306 13.0203 11.6737 12.6666 12.9998 12.6666C15.984 12.6666 18.5496 14.4501 19.6945 17.0084C19.7127 17.0409 19.7281 17.075 19.7407 17.1106C20.1213 17.9974 20.3332 18.9738 20.3332 20ZM16.3332 18.6666C16.3332 19.1971 16.1223 19.7056 15.7472 20.0807C15.3722 20.4558 14.8636 20.6666 14.3332 20.6666C13.2316 20.6666 12.3332 19.7681 12.3332 18.6666C12.3332 18.4898 12.2629 18.3203 12.1379 18.1953C12.0128 18.0702 11.8433 18 11.6665 18C10.9593 18 10.2812 17.7188 9.78109 17.2187C9.32569 16.7633 9.05228 16.1603 9.007 15.5221C8.09263 16.338 7.43098 17.4297 7.15023 18.6666H8.33317C8.8636 18.6666 9.37216 18.8775 9.74723 19.2526C10.1223 19.6276 10.3332 20.1362 10.3332 20.6666V21.3333L10.3364 21.399C10.3516 21.5517 10.4191 21.6952 10.5285 21.8046C10.6535 21.9297 10.823 22 10.9998 22L11.1978 22.0097C11.6555 22.0552 12.0858 22.2578 12.4139 22.5859C12.789 22.961 12.9998 23.4695 12.9998 24V26C13.4583 26 13.9042 25.9463 14.3332 25.8489V23.3333C14.3332 22.8029 14.544 22.2943 14.9191 21.9192C15.2942 21.5442 15.8027 21.3333 16.3332 21.3333H18.8494C18.9468 20.9044 18.9998 20.4583 18.9998 20C18.9998 19.2986 18.8786 18.6256 18.6574 18H16.9998C16.6347 18 16.3332 18.3015 16.3332 18.6666Z"
          fill="black"
        />
      </g>
      <path
        d="M0 28V4C0 1.79086 1.79086 0 4 0H16V6L16.0049 6.20605C16.1087 8.2512 17.7488 9.89135 19.7939 9.99512L20 10H26V28L25.9951 28.2061C25.8913 30.2512 24.2512 31.8913 22.2061 31.9951L22 32V31.583C23.979 31.583 25.583 29.979 25.583 28V10.417H20C17.5607 10.417 15.583 8.43926 15.583 6V0.416992H4C2.02098 0.416992 0.416992 2.02098 0.416992 4V28C0.416992 29.979 2.02098 31.583 4 31.583V32L3.79395 31.9951C1.68056 31.8879 0 30.14 0 28ZM22 31.583V32H4V31.583H22Z"
        fill="black"
        fillOpacity="0.15"
      />
      <defs>
        <clipPath id="clip0_293_2068">
          <rect fill="white" height="16" transform="translate(5 12)" width="16" />
        </clipPath>
      </defs>
    </svg>
  ),
  // 音频
  '.mp3、.wav、.flac、.ape、.ogg、.m4a、.wma': (
    <svg fill="none" height="32" viewBox="0 0 26 32" width="26" xmlns="http://www.w3.org/2000/svg">
      <path d="M20 10H26L16 0V6C16 8.20914 17.7909 10 20 10Z" fill="black" fillOpacity="0.15" />
      <path
        d="M16 0V6C16 8.20914 17.7909 10 20 10H26V28C26 30.2091 24.2091 32 22 32H4C1.79086 32 0 30.2091 0 28V4C0 1.79086 1.79086 0 4 0H16Z"
        fill="black"
        fillOpacity="0.06"
      />
      <path
        d="M0 28V4C0 1.79086 1.79086 0 4 0H16V6L16.0049 6.20605C16.1087 8.2512 17.7488 9.89135 19.7939 9.99512L20 10H26V28L25.9951 28.2061C25.8913 30.2512 24.2512 31.8913 22.2061 31.9951L22 32V31.583C23.979 31.583 25.583 29.979 25.583 28V10.417H20C17.5607 10.417 15.583 8.43926 15.583 6V0.416992H4C2.02098 0.416992 0.416992 2.02098 0.416992 4V28C0.416992 29.979 2.02098 31.583 4 31.583V32L3.79395 31.9951C1.68056 31.8879 0 30.14 0 28ZM22 31.583V32H4V31.583H22Z"
        fill="black"
        fillOpacity="0.15"
      />
      <path
        d="M10.3335 24C10.3335 23.2636 9.73654 22.6666 9.00016 22.6666C8.26378 22.6666 7.66683 23.2636 7.66683 24C7.66683 24.7363 8.26378 25.3333 9.00016 25.3333C9.73654 25.3333 10.3335 24.7363 10.3335 24ZM18.3335 22.6666C18.3335 21.9302 17.7365 21.3333 17.0002 21.3333C16.2638 21.3333 15.6668 21.9302 15.6668 22.6666C15.6668 23.403 16.2638 24 17.0002 24C17.7365 24 18.3335 23.403 18.3335 22.6666ZM19.6668 22.6666C19.6668 24.1394 18.4729 25.3333 17.0002 25.3333C15.5274 25.3333 14.3335 24.1394 14.3335 22.6666C14.3335 21.1939 15.5274 20 17.0002 20C17.486 20 17.9412 20.1304 18.3335 20.3574V14.7864L11.6668 15.8977V24C11.6668 25.4727 10.4729 26.6666 9.00016 26.6666C7.5274 26.6666 6.3335 25.4727 6.3335 24C6.3335 22.5272 7.5274 21.3333 9.00016 21.3333C9.486 21.3333 9.9412 21.4637 10.3335 21.6907V15.3333C10.3335 15.0074 10.5693 14.7293 10.8908 14.6757L18.8908 13.3424C19.084 13.3103 19.2817 13.3649 19.4312 13.4915C19.5806 13.6182 19.6668 13.8041 19.6668 14V22.6666Z"
        fill="black"
      />
    </svg>
  ),
  // 视频
  '.mp4、.avi、.mkv、.flv、.swf、.wmv、.mov': (
    <svg fill="none" height="32" viewBox="0 0 26 32" width="26" xmlns="http://www.w3.org/2000/svg">
      <path d="M20 10H26L16 0V6C16 8.20914 17.7909 10 20 10Z" fill="black" fillOpacity="0.15" />
      <path
        d="M16 0V6C16 8.20914 17.7909 10 20 10H26V28C26 30.2091 24.2091 32 22 32H4C1.79086 32 0 30.2091 0 28V4C0 1.79086 1.79086 0 4 0H16Z"
        fill="black"
        fillOpacity="0.06"
      />
      <path
        d="M14.9998 17.3334C14.9998 16.9652 14.7014 16.6667 14.3332 16.6667H7.6665C7.29831 16.6667 6.99984 16.9652 6.99984 17.3334V22.6667C6.99984 23.0349 7.29831 23.3334 7.6665 23.3334H14.3332C14.7014 23.3334 14.9998 23.0349 14.9998 22.6667V17.3334ZM16.3332 19.3822V20.3099L18.9998 22.0879V17.8269L16.3332 19.3822ZM16.3332 17.8392L18.8293 16.3829L18.9465 16.3243C19.0675 16.2735 19.1979 16.2472 19.3299 16.2468L19.4608 16.2546C19.5475 16.2658 19.6325 16.2884 19.7134 16.3217L19.8312 16.3796L19.9406 16.4525C20.01 16.5056 20.0722 16.5675 20.1255 16.6368L20.1984 16.7455L20.257 16.8633C20.3073 16.9845 20.3332 17.1148 20.3332 17.2468V22.711L20.3241 22.8457C20.3058 22.9792 20.261 23.1083 20.1912 23.2247C20.0983 23.3798 19.9646 23.5065 19.8052 23.5918C19.6458 23.6772 19.4662 23.7183 19.2856 23.7097C19.105 23.701 18.9297 23.6432 18.7791 23.543H18.7785L16.3332 21.9122V22.6667C16.3332 23.7713 15.4377 24.6667 14.3332 24.6667H7.6665C6.56193 24.6667 5.6665 23.7713 5.6665 22.6667V17.3334C5.6665 16.2288 6.56193 15.3334 7.6665 15.3334H14.3332C15.4377 15.3334 16.3332 16.2288 16.3332 17.3334V17.8392Z"
        fill="black"
      />
      <path
        d="M0 28V4C0 1.79086 1.79086 0 4 0H16V6L16.0049 6.20605C16.1087 8.2512 17.7488 9.89135 19.7939 9.99512L20 10H26V28L25.9951 28.2061C25.8913 30.2512 24.2512 31.8913 22.2061 31.9951L22 32V31.583C23.979 31.583 25.583 29.979 25.583 28V10.417H20C17.5607 10.417 15.583 8.43926 15.583 6V0.416992H4C2.02098 0.416992 0.416992 2.02098 0.416992 4V28C0.416992 29.979 2.02098 31.583 4 31.583V32L3.79395 31.9951C1.68056 31.8879 0 30.14 0 28ZM22 31.583V32H4V31.583H22Z"
        fill="black"
        fillOpacity="0.15"
      />
    </svg>
  ),
  // 电子书与出版物
  '.pdf、.epub、.mobi': (
    <svg fill="none" height="32" viewBox="0 0 26 32" width="26" xmlns="http://www.w3.org/2000/svg">
      <path d="M20 10H26L16 0V6C16 8.20914 17.7909 10 20 10Z" fill="black" fillOpacity="0.15" />
      <path
        d="M16 0V6C16 8.20914 17.7909 10 20 10H26V28C26 30.2091 24.2091 32 22 32H4C1.79086 32 0 30.2091 0 28V4C0 1.79086 1.79086 0 4 0H16Z"
        fill="black"
        fillOpacity="0.06"
      />
      <path
        d="M0 28V4C0 1.79086 1.79086 0 4 0H16V6L16.0049 6.20605C16.1087 8.2512 17.7488 9.89135 19.7939 9.99512L20 10H26V28L25.9951 28.2061C25.8913 30.2512 24.2512 31.8913 22.2061 31.9951L22 32V31.583C23.979 31.583 25.583 29.979 25.583 28V10.417H20C17.5607 10.417 15.583 8.43926 15.583 6V0.416992H4C2.02098 0.416992 0.416992 2.02098 0.416992 4V28C0.416992 29.979 2.02098 31.583 4 31.583V32L3.79395 31.9951C1.68056 31.8879 0 30.14 0 28ZM22 31.583V32H4V31.583H22Z"
        fill="black"
        fillOpacity="0.15"
      />
      <path
        d="M18.0526 23.4523C17.0029 23.3743 15.992 22.9845 15.1756 22.2828C13.5815 22.6336 12.0652 23.1404 10.5489 23.7642C9.34364 25.9084 8.21613 27 7.24414 27C7.04975 27 6.81647 26.961 6.66095 26.8441C6.23328 26.6491 6 26.2203 6 25.7915C6 25.4406 6.07776 24.4659 9.77131 22.8675C10.6267 21.3081 11.2876 19.7097 11.8319 18.0333C11.3654 17.0977 10.3545 14.7975 11.0543 13.628C11.2876 13.1991 11.7542 12.9652 12.2596 13.0042C12.6484 13.0042 13.0372 13.1991 13.2705 13.511C13.7759 14.2128 13.737 15.6942 13.0761 17.8774C13.6981 19.047 14.5146 20.0996 15.4866 20.9962C16.3031 20.8403 17.1195 20.7233 17.936 20.7233C19.7633 20.7623 20.0355 21.62 19.9966 22.1268C19.9966 23.4523 18.7136 23.4523 18.0526 23.4523ZM7.16638 25.8694L7.28302 25.8304C7.82734 25.6355 8.25501 25.2457 8.56605 24.7388C7.98285 24.9728 7.5163 25.3626 7.16638 25.8694ZM12.3374 14.1738H12.2207C12.1818 14.1738 12.1041 14.1738 12.0652 14.2128C11.9097 14.8755 12.0263 15.5772 12.2985 16.201C12.5318 15.5383 12.5318 14.8365 12.3374 14.1738ZM12.6095 19.8267L12.5706 19.9046L12.5318 19.8657C12.1818 20.7623 11.793 21.659 11.3654 22.5167L11.4431 22.4777V22.5557C12.2985 22.2438 13.2316 21.9709 14.0869 21.7759L14.0481 21.737H14.1647C13.5815 21.1522 13.0372 20.4894 12.6095 19.8267ZM17.8971 21.8929C17.5472 21.8929 17.2362 21.8929 16.8863 21.9709C17.275 22.1658 17.6638 22.2438 18.0526 22.2828C18.3248 22.3217 18.597 22.2828 18.8302 22.2048C18.8302 22.0878 18.6747 21.8929 17.8971 21.8929Z"
        fill="black"
      />
    </svg>
  ),

  // Power point
  '.ppt,.pptx': (
    <svg fill="none" height="32" viewBox="0 0 26 32" width="26" xmlns="http://www.w3.org/2000/svg">
      <path d="M20 10H26L16 0V6C16 8.20914 17.7909 10 20 10Z" fill="black" fillOpacity="0.15" />
      <path
        d="M16 0V6C16 8.20914 17.7909 10 20 10H26V28C26 30.2091 24.2091 32 22 32H4C1.79086 32 0 30.2091 0 28V4C0 1.79086 1.79086 0 4 0H16Z"
        fill="black"
        fillOpacity="0.06"
      />
      <path
        d="M0 28V4C0 1.79086 1.79086 0 4 0H16V6L16.0049 6.20605C16.1087 8.2512 17.7488 9.89135 19.7939 9.99512L20 10H26V28L25.9951 28.2061C25.8913 30.2512 24.2512 31.8913 22.2061 31.9951L22 32V31.583C23.979 31.583 25.583 29.979 25.583 28V10.417H20C17.5607 10.417 15.583 8.43926 15.583 6V0.416992H4C2.02098 0.416992 0.416992 2.02098 0.416992 4V28C0.416992 29.979 2.02098 31.583 4 31.583V32L3.79395 31.9951C1.68056 31.8879 0 30.14 0 28ZM22 31.583V32H4V31.583H22Z"
        fill="black"
        fillOpacity="0.15"
      />
      <path
        d="M10.8317 25.5H9V15.5H13.6027C15.7945 15.5 17 16.9243 17 18.6334C17 20.3276 15.7789 21.7519 13.6027 21.7519H10.8317V25.5ZM13.3523 20.2076C14.3855 20.2076 15.1213 19.578 15.1213 18.6334C15.1213 17.6739 14.3855 17.0442 13.3523 17.0442H10.8317V20.2076H13.3523Z"
        fill="black"
      />
    </svg>
  ),

  // 矢量图
  '.svg、.ai、.eps': (
    <svg fill="none" height="32" viewBox="0 0 26 32" width="26" xmlns="http://www.w3.org/2000/svg">
      <path d="M20 10H26L16 0V6C16 8.20914 17.7909 10 20 10Z" fill="black" fillOpacity="0.15" />
      <path
        d="M16 0V6C16 8.20914 17.7909 10 20 10H26V28C26 30.2091 24.2091 32 22 32H4C1.79086 32 0 30.2091 0 28V4C0 1.79086 1.79086 0 4 0H16Z"
        fill="black"
        fillOpacity="0.06"
      />
      <g clip-path="url(#clip0_293_2071)">
        <path
          d="M8.33317 23.3333C8.33317 23.1628 8.26854 23.0078 8.16325 22.8899C8.15391 22.8816 8.14423 22.8735 8.13525 22.8645C8.12606 22.8554 8.11775 22.8455 8.10921 22.8359C7.99141 22.731 7.83667 22.6666 7.6665 22.6666C7.29831 22.6666 6.99984 22.9651 6.99984 23.3333C6.99984 23.7015 7.29831 24 7.6665 24C8.03469 24 8.33317 23.7015 8.33317 23.3333ZM16.9998 14.6666C16.9998 14.2984 16.7014 14 16.3332 14C15.965 14 15.6665 14.2984 15.6665 14.6666C15.6665 14.8368 15.7309 14.9915 15.8358 15.1093C15.8454 15.1179 15.8552 15.1262 15.8644 15.1354C15.8734 15.1443 15.8815 15.154 15.8898 15.1634C16.0077 15.2687 16.1627 15.3333 16.3332 15.3333C16.7014 15.3333 16.9998 15.0348 16.9998 14.6666ZM18.3332 14.6666C18.3332 15.7712 17.4377 16.6666 16.3332 16.6666C16.0246 16.6666 15.7335 16.5942 15.4725 16.4694L14.1457 17.7962C14.9569 17.8389 15.8308 18.0165 16.6307 18.2311C17.5136 18.4679 18.3384 18.7574 18.9399 18.9863C19.2415 19.101 19.4892 19.2015 19.6619 19.2734C19.748 19.3092 19.8158 19.3379 19.8625 19.358C19.8857 19.368 19.904 19.376 19.9165 19.3815C19.9228 19.3842 19.928 19.3865 19.9315 19.388L19.9367 19.3906C19.9368 19.3906 19.9373 19.3908 19.6665 20L19.9373 19.3906C20.2738 19.5401 20.4254 19.9343 20.2759 20.2708C20.1357 20.5862 19.7805 20.7388 19.4595 20.6334L19.3957 20.6093C19.3952 20.6091 19.3942 20.6085 19.3931 20.608C19.3907 20.607 19.3871 20.605 19.382 20.6028C19.3717 20.5983 19.3554 20.5917 19.3345 20.5826C19.2925 20.5646 19.2297 20.5375 19.1489 20.5039C18.9871 20.4365 18.7527 20.3415 18.466 20.2324C17.8905 20.0134 17.111 19.7403 16.2856 19.5188C15.4541 19.2957 14.607 19.1339 13.8879 19.1198C13.1386 19.1051 12.6887 19.2538 12.4712 19.4713L12.3931 19.5638C12.219 19.8058 12.1068 20.2323 12.1196 20.888C12.1337 21.6071 12.2956 22.4542 12.5187 23.2858C12.7402 24.1111 13.0133 24.8907 13.2323 25.4661C13.3414 25.7529 13.4364 25.9872 13.5037 26.149C13.5374 26.2298 13.5644 26.2926 13.5825 26.3346C13.5916 26.3556 13.5982 26.3718 13.6027 26.3821C13.6049 26.3872 13.6069 26.3909 13.6079 26.3932L13.6092 26.3958L13.6333 26.4596C13.7387 26.7807 13.5861 27.1358 13.2707 27.276C12.9342 27.4255 12.54 27.2739 12.3905 26.9375L12.9998 26.6666L12.3905 26.9368L12.3879 26.9316C12.3863 26.9282 12.3841 26.9229 12.3813 26.9166C12.3759 26.9041 12.3679 26.8858 12.3579 26.8626C12.3378 26.8159 12.3091 26.7482 12.2733 26.6621C12.2014 26.4893 12.1009 26.2416 11.9862 25.9401C11.7572 25.3385 11.4678 24.5137 11.231 23.6308C11.0164 22.8309 10.8388 21.957 10.7961 21.1458L9.46924 22.4726C9.59407 22.7336 9.6665 23.0247 9.6665 23.3333C9.6665 24.4379 8.77107 25.3333 7.6665 25.3333C6.56193 25.3333 5.6665 24.4379 5.6665 23.3333C5.6665 22.2287 6.56193 21.3333 7.6665 21.3333C7.97481 21.3333 8.26574 21.4053 8.52653 21.5299L14.5298 15.5267C14.4052 15.2659 14.3332 14.9749 14.3332 14.6666C14.3332 13.5621 15.2286 12.6666 16.3332 12.6666C17.4377 12.6666 18.3332 13.5621 18.3332 14.6666Z"
          fill="black"
        />
      </g>
      <path
        d="M0 28V4C0 1.79086 1.79086 0 4 0H16V6L16.0049 6.20605C16.1087 8.2512 17.7488 9.89135 19.7939 9.99512L20 10H26V28L25.9951 28.2061C25.8913 30.2512 24.2512 31.8913 22.2061 31.9951L22 32V31.583C23.979 31.583 25.583 29.979 25.583 28V10.417H20C17.5607 10.417 15.583 8.43926 15.583 6V0.416992H4C2.02098 0.416992 0.416992 2.02098 0.416992 4V28C0.416992 29.979 2.02098 31.583 4 31.583V32L3.79395 31.9951C1.68056 31.8879 0 30.14 0 28ZM22 31.583V32H4V31.583H22Z"
        fill="black"
        fillOpacity="0.15"
      />
      <defs>
        <clipPath id="clip0_293_2071">
          <rect fill="white" height="16" transform="translate(5 12)" width="16" />
        </clipPath>
      </defs>
    </svg>
  ),

  // 文本文件
  '.txt、.rtf、.md': (
    <svg fill="none" height="32" viewBox="0 0 26 32" width="26" xmlns="http://www.w3.org/2000/svg">
      <path d="M20 10H26L16 0V6C16 8.20914 17.7909 10 20 10Z" fill="black" fillOpacity="0.15" />
      <path
        d="M16 0V6C16 8.20914 17.7909 10 20 10H26V28C26 30.2091 24.2091 32 22 32H4C1.79086 32 0 30.2091 0 28V4C0 1.79086 1.79086 0 4 0H16Z"
        fill="black"
        fillOpacity="0.06"
      />
      <path
        d="M17.6667 16.6667V15.3333H13.6667V24.6667H15C15.3682 24.6667 15.6667 24.9651 15.6667 25.3333C15.6667 25.7015 15.3682 26 15 26H11C10.6318 26 10.3333 25.7015 10.3333 25.3333C10.3333 24.9651 10.6318 24.6667 11 24.6667H12.3333V15.3333H8.33333V16.6667C8.33333 17.0349 8.03486 17.3333 7.66667 17.3333C7.29848 17.3333 7 17.0349 7 16.6667V15.3333C7 14.9797 7.14058 14.6407 7.39062 14.3906C7.64067 14.1406 7.97971 14 8.33333 14H17.6667C18.0203 14 18.3593 14.1406 18.6094 14.3906C18.8594 14.6407 19 14.9797 19 15.3333V16.6667C19 17.0349 18.7015 17.3333 18.3333 17.3333C17.9651 17.3333 17.6667 17.0349 17.6667 16.6667Z"
        fill="black"
      />
      <path
        d="M0 28V4C0 1.79086 1.79086 0 4 0H16V6L16.0049 6.20605C16.1087 8.2512 17.7488 9.89135 19.7939 9.99512L20 10H26V28L25.9951 28.2061C25.8913 30.2512 24.2512 31.8913 22.2061 31.9951L22 32V31.583C23.979 31.583 25.583 29.979 25.583 28V10.417H20C17.5607 10.417 15.583 8.43926 15.583 6V0.416992H4C2.02098 0.416992 0.416992 2.02098 0.416992 4V28C0.416992 29.979 2.02098 31.583 4 31.583V32L3.79395 31.9951C1.68056 31.8879 0 30.14 0 28ZM22 31.583V32H4V31.583H22Z"
        fill="black"
        fillOpacity="0.15"
      />
    </svg>
  ),
  // Excel
  '.xls,.xlsx': (
    <svg fill="none" height="32" viewBox="0 0 26 32" width="26" xmlns="http://www.w3.org/2000/svg">
      <path d="M20 10H26L16 0V6C16 8.20914 17.7909 10 20 10Z" fill="black" fillOpacity="0.15" />
      <path
        d="M16 0V6C16 8.20914 17.7909 10 20 10H26V28C26 30.2091 24.2091 32 22 32H4C1.79086 32 0 30.2091 0 28V4C0 1.79086 1.79086 0 4 0H16Z"
        fill="black"
        fillOpacity="0.06"
      />
      <path
        d="M17.5 25H15.5734L13 21.4783L10.4266 25H8.5L11.9266 20.3853L8.70642 16H10.633L13 19.2924L15.3532 16H17.2936L14.0596 20.3718L17.5 25Z"
        fill="black"
      />
      <path
        d="M0 28V4C0 1.79086 1.79086 0 4 0H16V6L16.0049 6.20605C16.1087 8.2512 17.7488 9.89135 19.7939 9.99512L20 10H26V28L25.9951 28.2061C25.8913 30.2512 24.2512 31.8913 22.2061 31.9951L22 32V31.583C23.979 31.583 25.583 29.979 25.583 28V10.417H20C17.5607 10.417 15.583 8.43926 15.583 6V0.416992H4C2.02098 0.416992 0.416992 2.02098 0.416992 4V28C0.416992 29.979 2.02098 31.583 4 31.583V32L3.79395 31.9951C1.68056 31.8879 0 30.14 0 28ZM22 31.583V32H4V31.583H22Z"
        fill="black"
        fillOpacity="0.15"
      />
    </svg>
  ),
  // 压缩与归档文件
  '.zip、.rar、.7z、.tar、.gz、.bz2、.iso': (
    <svg fill="none" height="32" viewBox="0 0 26 32" width="26" xmlns="http://www.w3.org/2000/svg">
      <path d="M20 10H26L16 0V6C16 8.20914 17.7909 10 20 10Z" fill="black" fillOpacity="0.15" />
      <path
        d="M16 0V6C16 8.20914 17.7909 10 20 10H26V28C26 30.2091 24.2091 32 22 32H4C1.79086 32 0 30.2091 0 28V4C0 1.79086 1.79086 0 4 0H16Z"
        fill="black"
        fillOpacity="0.06"
      />
      <path
        d="M0 28V4C0 1.79086 1.79086 0 4 0H16V6L16.0049 6.20605C16.1087 8.2512 17.7488 9.89135 19.7939 9.99512L20 10H26V28L25.9951 28.2061C25.8913 30.2512 24.2512 31.8913 22.2061 31.9951L22 32V31.583C23.979 31.583 25.583 29.979 25.583 28V10.417H20C17.5607 10.417 15.583 8.43926 15.583 6V0.416992H4C2.02098 0.416992 0.416992 2.02098 0.416992 4V28C0.416992 29.979 2.02098 31.583 4 31.583V32L3.79395 31.9951C1.68056 31.8879 0 30.14 0 28ZM22 31.583V32H4V31.583H22Z"
        fill="black"
        fillOpacity="0.15"
      />
      <path
        d="M12.3333 25.3333C12.3333 24.9651 12.0349 24.6666 11.6667 24.6666C11.2985 24.6666 11 24.9651 11 25.3333C11 25.7015 11.2985 26 11.6667 26C12.0349 26 12.3333 25.7015 12.3333 25.3333ZM7 25.3333V14.6666C7 14.1362 7.21086 13.6276 7.58594 13.2526C7.96101 12.8775 8.46957 12.6666 9 12.6666H15L15.0658 12.6699C15.2184 12.685 15.3619 12.7525 15.4714 12.8619L18.8047 16.1953C18.9297 16.3203 19 16.4898 19 16.6666V25.3333C19 25.8637 18.7891 26.3723 18.4141 26.7474C18.039 27.1224 17.5304 27.3333 17 27.3333H15.3333C14.9651 27.3333 14.6667 27.0348 14.6667 26.6666C14.6667 26.2984 14.9651 26 15.3333 26H17C17.1768 26 17.3463 25.9297 17.4714 25.8046C17.5964 25.6796 17.6667 25.5101 17.6667 25.3333V18H15.6667C15.1362 18 14.6277 17.7891 14.2526 17.414C13.8775 17.0389 13.6667 16.5304 13.6667 16V14H9C8.82319 14 8.65367 14.0702 8.52865 14.1953C8.40362 14.3203 8.33333 14.4898 8.33333 14.6666V25.3333L8.33919 25.4212C8.35086 25.5086 8.37963 25.5933 8.42448 25.6699C8.61048 25.9876 8.50392 26.396 8.1862 26.582C7.86853 26.768 7.46017 26.6613 7.27409 26.3437C7.11714 26.0756 7.02515 25.7749 7.00456 25.4661L7 25.3333ZM11 20V19.3333C11 18.9651 11.2985 18.6666 11.6667 18.6666C12.0349 18.6666 12.3333 18.9651 12.3333 19.3333V20C12.3333 20.3681 12.0349 20.6666 11.6667 20.6666C11.2985 20.6666 11 20.3681 11 20ZM11 16.6666V16C11 15.6318 11.2985 15.3333 11.6667 15.3333C12.0349 15.3333 12.3333 15.6318 12.3333 16V16.6666C12.3333 17.0348 12.0349 17.3333 11.6667 17.3333C11.2985 17.3333 11 17.0348 11 16.6666ZM13.6667 25.3333C13.6667 26.4379 12.7712 27.3333 11.6667 27.3333C10.5621 27.3333 9.66667 26.4379 9.66667 25.3333C9.66667 24.4627 10.2235 23.7239 11 23.4492V22.6666C11 22.2984 11.2985 22 11.6667 22C12.0349 22 12.3333 22.2984 12.3333 22.6666V23.4492C13.1098 23.7239 13.6667 24.4627 13.6667 25.3333ZM15 16C15 16.1768 15.0703 16.3463 15.1953 16.4713C15.3203 16.5963 15.4899 16.6666 15.6667 16.6666H17.3906L15 14.276V16Z"
        fill="black"
      />
    </svg>
  ),
};

export const FILE_DEFAULT_ICON = (
  <svg fill="none" height="32" viewBox="0 0 26 32" width="26" xmlns="http://www.w3.org/2000/svg">
    <path d="M20 10H26L16 0V6C16 8.20914 17.7909 10 20 10Z" fill="black" fillOpacity="0.15" />
    <path
      d="M16 0V6C16 8.20914 17.7909 10 20 10H26V28C26 30.2091 24.2091 32 22 32H4C1.79086 32 0 30.2091 0 28V4C0 1.79086 1.79086 0 4 0H16Z"
      fill="black"
      fillOpacity="0.06"
    />
    <path
      d="M7 25.3333V14.6666C7 14.1362 7.21086 13.6276 7.58594 13.2526C7.96101 12.8775 8.46957 12.6666 9 12.6666H15L15.0658 12.6699C15.2184 12.685 15.3619 12.7525 15.4714 12.8619L18.8047 16.1953C18.9297 16.3203 19 16.4898 19 16.6666V25.3333C19 25.8637 18.7891 26.3723 18.4141 26.7474C18.039 27.1224 17.5304 27.3333 17 27.3333H9C8.46957 27.3333 7.96101 27.1224 7.58594 26.7474C7.21086 26.3723 7 25.8637 7 25.3333ZM15.6667 22.6666C16.0349 22.6666 16.3333 22.9651 16.3333 23.3333C16.3333 23.7015 16.0349 24 15.6667 24H10.3333C9.96514 24 9.66667 23.7015 9.66667 23.3333C9.66667 22.9651 9.96514 22.6666 10.3333 22.6666H15.6667ZM15.6667 20C16.0349 20 16.3333 20.2984 16.3333 20.6666C16.3333 21.0348 16.0349 21.3333 15.6667 21.3333H10.3333C9.96514 21.3333 9.66667 21.0348 9.66667 20.6666C9.66667 20.2984 9.96514 20 10.3333 20H15.6667ZM11.6667 17.3333C12.0349 17.3333 12.3333 17.6318 12.3333 18C12.3333 18.3681 12.0349 18.6666 11.6667 18.6666H10.3333C9.96514 18.6666 9.66667 18.3681 9.66667 18C9.66667 17.6318 9.96514 17.3333 10.3333 17.3333H11.6667ZM15 16C15 16.1768 15.0703 16.3463 15.1953 16.4713C15.3203 16.5963 15.4899 16.6666 15.6667 16.6666H17.3906L15 14.276V16ZM8.33333 25.3333C8.33333 25.5101 8.40362 25.6796 8.52865 25.8046C8.65367 25.9297 8.82319 26 9 26H17C17.1768 26 17.3463 25.9297 17.4714 25.8046C17.5964 25.6796 17.6667 25.5101 17.6667 25.3333V18H15.6667C15.1362 18 14.6277 17.7891 14.2526 17.414C13.8775 17.0389 13.6667 16.5304 13.6667 16V14H9C8.82319 14 8.65367 14.0702 8.52865 14.1953C8.40362 14.3203 8.33333 14.4898 8.33333 14.6666V25.3333Z"
      fill="black"
    />
    <path
      d="M0 28V4C0 1.79086 1.79086 0 4 0H16V6L16.0049 6.20605C16.1087 8.2512 17.7488 9.89135 19.7939 9.99512L20 10H26V28L25.9951 28.2061C25.8913 30.2512 24.2512 31.8913 22.2061 31.9951L22 32V31.583C23.979 31.583 25.583 29.979 25.583 28V10.417H20C17.5607 10.417 15.583 8.43926 15.583 6V0.416992H4C2.02098 0.416992 0.416992 2.02098 0.416992 4V28C0.416992 29.979 2.02098 31.583 4 31.583V32L3.79395 31.9951C1.68056 31.8879 0 30.14 0 28ZM22 31.583V32H4V31.583H22Z"
      fill="black"
      fillOpacity="0.15"
    />
  </svg>
);
