'use client';

import type { ProColumns, ProTableProps } from '@ant-design/pro-components';
import { <PERSON><PERSON>, Drawer, Flex } from 'antd';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';

import Table from '@/components/Table';
import { trpcClient } from '@/libs/trpc/client';
import { SortQuery } from '@/types/query';
import { formatDateWithTime } from '@/utils/format';

interface AgentListItem {
  avatar: string | null;
  createdAt: Date;
  description: string | null;
  id: string;
  title: string | null;
  updatedAt: Date;
  userId: string;
}

interface AgentListDrawerProps {
  onClose: () => void;
  open: boolean;
  userId: string;
}

const AgentListDrawer = memo<AgentListDrawerProps>(({ open, onClose, userId }) => {
  const { t } = useTranslation(['common', 'user']);

  // 表格数据请求函数
  const request: ProTableProps<AgentListItem, any>['request'] = async (params, sorts) => {
    const result = await trpcClient.user.getUserAgents.query({
      params: {
        ...params,
        userId, // 固定查询当前用户的助手
      },
      sorts: sorts as SortQuery,
    });

    return {
      data: result?.data || [],
      success: result?.success || false,
      total: result?.total || 0,
    };
  };

  // 表格列定义
  const columns: ProColumns<AgentListItem>[] = [
    {
      dataIndex: 'title',
      ellipsis: true,
      render: (_, record) => (
        <Flex align={'center'} gap={8}>
          <Avatar
            size={40}
            src={record.avatar}
            style={{
              backgroundColor: record.avatar ? 'transparent' : '#f56a00',
              flexShrink: 0,
            }}
          >
            {record.title?.charAt(0)?.toUpperCase() || 'A'}
          </Avatar>
          <span>{record.title}</span>
        </Flex>
      ),
      title: t('user:agent.name'),
      width: 250,
    },
    {
      dataIndex: 'description',
      ellipsis: true,
      render: (_, record) => record.description || '--',
      title: t('user:agent.description'),
      width: 300,
    },
    {
      dataIndex: 'createdAt',
      render: (_, record) => formatDateWithTime(record.createdAt),
      sorter: true,
      title: t('common:time.createdAt'),
      valueType: 'dateTime',
      width: 180,
    },
  ];

  return (
    <Drawer
      onClose={onClose}
      open={open}
      placement="right"
      size="large"
      title={t('user:agent.title')}
      width={856}
    >
      <Table<AgentListItem>
        columns={columns}
        key="id"
        options={false}
        request={request}
        rowKey="id"
        search={false}
      />
    </Drawer>
  );
});

export default AgentListDrawer;
