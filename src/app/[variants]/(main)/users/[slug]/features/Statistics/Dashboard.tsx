'use client';

import { Divider } from 'antd';
import { parseAsBoolean, useQueryState } from 'nuqs';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import Statistic from '@/components/Statistic';
import StatisticCard from '@/components/StatisticCard';
import TitleWithPercentage from '@/components/StatisticCard/TitleWithPercentage';
import { trpcQuery } from '@/libs/trpc/client';
import { formatIntergerNumber } from '@/utils/format';

import AgentListDrawer from './features/AgentListDrawer';
import FileListDrawer from './features/FileListDrawer';
import MessageTopicDrawer from './features/MessageTopicDrawer';

const Dashboard = memo<{ userId: string }>(({ userId }) => {
  const { t } = useTranslation('common');
  const { data, isLoading } = trpcQuery.user.getUserStatisticsById.useQuery(userId);

  // 助手列表抽屉状态
  const [isAgentDrawerOpen, setIsAgentDrawerOpen] = useQueryState(
    'isAgentDrawerOpen',
    parseAsBoolean.withDefault(false),
  );
  // 消息&话题详情抽屉状态
  const [isMessageTopicDrawerOpen, setIsMessageTopicDrawerOpen] = useQueryState(
    'isMessageTopicDrawerOpen',
    parseAsBoolean.withDefault(false),
  );
  // 文件列表抽屉状态
  const [isFileDrawerOpen, setIsFileDrawerOpen] = useQueryState(
    'isFileDrawerOpen',
    parseAsBoolean.withDefault(false),
  );

  const handleAgentCardClick = () => {
    setIsAgentDrawerOpen(true);
  };

  const handleAgentDrawerClose = () => {
    setIsAgentDrawerOpen(false);
  };

  const handleMessageTopicCardClick = () => {
    setIsMessageTopicDrawerOpen(true);
  };

  const handleMessageTopicDrawerClose = () => {
    setIsMessageTopicDrawerOpen(false);
  };

  const handleFileCardClick = () => {
    setIsFileDrawerOpen(true);
  };

  const handleFileDrawerClose = () => {
    setIsFileDrawerOpen(false);
  };

  return (
    <Flexbox horizontal>
      <StatisticCard
        chartPlacement={'right'}
        loading={isLoading}
        onClick={handleAgentCardClick}
        statistic={{
          description: (
            <Statistic
              title={t('time.compare.prevMonth')}
              value={formatIntergerNumber(data?.agents.prevCount) || '--'}
            />
          ),
          precision: 0,
          value: data?.agents.count || '--',
        }}
        style={{ cursor: 'pointer' }}
        title={
          <TitleWithPercentage
            count={data?.agents.count}
            prvCount={data?.agents.prevCount}
            title={t('statistics.totalAssistants')}
          />
        }
        variant={'pure'}
      />
      <Divider style={{ height: '100%', margin: 0 }} type={'vertical'} />
      <StatisticCard
        chartPlacement={'right'}
        loading={isLoading}
        onClick={handleMessageTopicCardClick}
        statistic={{
          description: (
            <Statistic
              title={t('time.compare.prevMonth')}
              value={formatIntergerNumber(data?.topics.prevCount) || '--'}
            />
          ),
          precision: 0,
          value: data?.topics.count || '--',
        }}
        style={{ cursor: 'pointer' }}
        title={
          <TitleWithPercentage
            count={data?.topics.count}
            prvCount={data?.topics.prevCount}
            title={t('statistics.totalTopics')}
          />
        }
        variant={'pure'}
      />
      <Divider style={{ height: '100%', margin: 0 }} type={'vertical'} />
      <StatisticCard
        chartPlacement={'right'}
        loading={isLoading}
        onClick={handleMessageTopicCardClick}
        statistic={{
          description: (
            <Statistic
              title={t('time.compare.prevMonth')}
              value={formatIntergerNumber(data?.messages.prevCount) || '--'}
            />
          ),
          precision: 0,
          value: data?.messages.count || '--',
        }}
        style={{ cursor: 'pointer' }}
        title={
          <TitleWithPercentage
            count={data?.messages.count}
            prvCount={data?.messages.prevCount}
            title={t('statistics.totalMessages')}
          />
        }
        variant={'pure'}
      />
      <Divider style={{ height: '100%', margin: 0 }} type={'vertical'} />
      <StatisticCard
        chartPlacement={'right'}
        loading={isLoading}
        onClick={handleFileCardClick}
        statistic={{
          description: (
            <Statistic
              title={t('time.compare.prevMonth')}
              value={formatIntergerNumber(data?.files.prevCount) || '--'}
            />
          ),
          precision: 0,
          value: data?.files.count || '--',
        }}
        style={{ cursor: 'pointer' }}
        title={
          <TitleWithPercentage
            count={data?.files.count}
            prvCount={data?.files.prevCount}
            title={t('statistics.file')}
          />
        }
        variant={'pure'}
      />

      {/* 助手列表抽屉 */}
      <AgentListDrawer onClose={handleAgentDrawerClose} open={isAgentDrawerOpen} userId={userId} />

      {/* 消息&话题详情抽屉 */}
      <MessageTopicDrawer
        onClose={handleMessageTopicDrawerClose}
        open={isMessageTopicDrawerOpen}
        userId={userId}
      />

      {/* 文件列表抽屉 */}
      <FileListDrawer onClose={handleFileDrawerClose} open={isFileDrawerOpen} userId={userId} />
    </Flexbox>
  );
});

export default Dashboard;
