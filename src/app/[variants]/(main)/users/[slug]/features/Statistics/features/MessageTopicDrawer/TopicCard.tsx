import { ProCard } from '@ant-design/pro-components';
import { Markdown } from '@lobehub/ui';
import { Empty, Flex, Pagination, Tag, Typography } from 'antd';
import { createStyles } from 'antd-style';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { memo, useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { trpcClient } from '@/libs/trpc/client';
import { MessageItem } from '@/types/message';
import { formatDateWithTime } from '@/utils/format';

import { MessageListSkeleton } from './SkeletonLoading';
import { purifyContent } from './helpers';
import type { TopicListItem } from './index';

const { Text } = Typography;

interface TopicCardProps {
  topic: TopicListItem;
}

const useStyles = createStyles(({ css, token }) => ({
  messageCard: css`
    padding-block: 16px;
    padding-inline: 24px;
    border-radius: 0 16px 16px;
    background: ${token.colorBgContainerSecondary};

    .ant-tag {
      margin-inline-end: 0;
    }
  `,
  pagination: css`
    justify-content: flex-end;
  `,
  topicCard: css`
    background: ${token.colorBgContainer};

    .ant-pro-card-title {
      cursor: pointer;
      width: 100%;
    }
  `,
}));

const TopicCard = memo<TopicCardProps>(({ topic }) => {
  const { t } = useTranslation('user');
  const { styles } = useStyles();
  const [isExpanded, setIsExpanded] = useState(false);
  const [messages, setMessages] = useState<MessageItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [messagesTotal, setMessagesTotal] = useState(0);
  const [messagesCurrent, setMessagesCurrent] = useState(1);
  const [hasLoadedOnce, setHasLoadedOnce] = useState(false);

  // 加载指定话题的消息列表
  const loadMessages = useCallback(
    async (topicId: string, current = 1) => {
      setIsLoading(true);
      try {
        const result = await trpcClient.user.getUserMessages.query({
          params: {
            current,
            pageSize: 5,
            topicId,
            userId: topic.userId,
          },
        });

        if (result?.data) {
          setMessages(result.data);
          setMessagesTotal(result.total || 0);
          setMessagesCurrent(current);
          setHasLoadedOnce(true);
        }
      } catch (error) {
        console.error('Failed to load messages:', error);
        setMessages([]);
        setMessagesTotal(0);
      } finally {
        setIsLoading(false);
      }
    },
    [topic.userId],
  );

  // 当展开时加载消息
  useEffect(() => {
    if (isExpanded && !hasLoadedOnce) {
      loadMessages(topic.id);
    }
  }, [isExpanded, hasLoadedOnce, topic.id, loadMessages]);

  // 渲染消息角色标签
  const renderRoleTag = useCallback(
    (role: string) => {
      const roleMap = {
        assistant: { color: 'gold', text: t('drawer.messageCard.roles.assistant') },
        system: { color: 'error', text: t('drawer.messageCard.roles.system') },
        tool: { color: 'geekblue', text: t('drawer.messageCard.roles.tool') },
        user: { color: 'success', text: t('drawer.messageCard.roles.user') },
      };
      const roleInfo = roleMap[role as keyof typeof roleMap] || { color: 'default', text: role };
      return <Tag color={roleInfo.color}>{roleInfo.text}</Tag>;
    },
    [t],
  );

  return (
    <ProCard
      className={styles.topicCard}
      collapsed={!isExpanded}
      collapsible
      title={
        <Flex
          align="center"
          justify="space-between"
          onClick={() => setIsExpanded(!isExpanded)}
          style={{ width: '100%' }}
        >
          <Flex align="center" gap={8}>
            {isExpanded ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
            <Text strong>{topic.title || t('drawer.topic.untitled')}</Text>
          </Flex>
          <Text type="secondary">{formatDateWithTime(topic.createdAt)}</Text>
        </Flex>
      }
    >
      {isExpanded && (
        <Flex gap={16} vertical>
          {isLoading ? (
            <MessageListSkeleton />
          ) : messages.length === 0 ? (
            <Empty
              description={t('drawer.messageCard.empty')}
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          ) : (
            <Flex gap={16} vertical>
              {messages.map((message) => {
                return (
                  <div className={styles.messageCard} key={message.id}>
                    <Flex gap={8} vertical>
                      <Markdown variant={'chat'}>
                        {purifyContent(message.content || '') || t('drawer.topic.noContent')}
                      </Markdown>
                      <Flex gap={8}>
                        {renderRoleTag(message.role)}
                        <Text type="secondary">{formatDateWithTime(message.createdAt)}</Text>
                      </Flex>
                    </Flex>
                  </div>
                );
              })}
            </Flex>
          )}
          <Pagination
            className={styles.pagination}
            current={messagesCurrent}
            hideOnSinglePage
            onChange={(page) => loadMessages(topic.id, page)}
            pageSize={5}
            showQuickJumper
            showSizeChanger={false}
            size="small"
            total={messagesTotal}
          />
        </Flex>
      )}
    </ProCard>
  );
});

TopicCard.displayName = 'TopicCard';

export default TopicCard;
