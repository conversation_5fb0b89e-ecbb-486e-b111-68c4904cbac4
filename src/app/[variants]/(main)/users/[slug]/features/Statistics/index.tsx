'use client';

import { memo } from 'react';
import { useTranslation } from 'react-i18next';

import Block from '@/components/Block';

import Dashboard from './Dashboard';

const Statistics = memo<{ userId: string }>(({ userId }) => {
  const { t } = useTranslation('user');

  return (
    <Block
      desc={t('statistics.desc')}
      style={{ padding: 0 }}
      title={t('statistics.title')}
      variant={'card'}
    >
      <Dashboard userId={userId} />
    </Block>
  );
});

export default Statistics;
