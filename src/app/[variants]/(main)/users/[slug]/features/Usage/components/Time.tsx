import { Icon } from '@lobehub/ui';
import { useTheme } from 'antd-style';
import { Clock3Icon, ClockArrowUp } from 'lucide-react';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

const Time = memo<{
  expiredAt?: string;
  resetAt?: string;
}>(({ resetAt, expiredAt }) => {
  const { t } = useTranslation('user');
  const theme = useTheme();
  return (
    <Flexbox
      align={'center'}
      gap={16}
      horizontal
      style={{
        color: theme.colorTextDescription,
        fontSize: 12,
      }}
      wrap={'wrap'}
    >
      {resetAt && (
        <Flexbox align={'center'} gap={4} horizontal>
          <Icon icon={ClockArrowUp} />
          {t('usage.resetAt')}: <span style={{ fontWeight: 'bold' }}>{resetAt}</span>
        </Flexbox>
      )}
      {expiredAt && (
        <Flexbox align={'center'} gap={4} horizontal>
          <Icon icon={Clock3Icon} />
          {t('usage.expiredAt')}: <span style={{ fontWeight: 'bold' }}>{expiredAt}</span>
        </Flexbox>
      )}
    </Flexbox>
  );
});

export default Time;
