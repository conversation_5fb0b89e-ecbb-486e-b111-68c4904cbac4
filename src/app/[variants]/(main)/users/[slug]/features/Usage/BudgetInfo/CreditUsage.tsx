'use client';

import { Divider } from 'antd';
import { useTheme } from 'antd-style';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import CopyableLabel from '@/components/CopyableLabel';
import { Plans } from '@/types/subscription';
import { formatDateWithTime } from '@/utils/format';

import Loading from '../components/Loading';
import ProgressItem from '../components/ProgressItem';
import Time from '../components/Time';

dayjs.extend(duration);

interface CreditUsageProps {
  data?: {
    freeBudgetId: string;
    freeBudgetKey: string;
    plan: Plans;
    subscriptionBudgetId: string;
    subscriptionBudgetKey: string;
    usage: {
      free: {
        expiredAt: Date;
        percent: number;
        resetAt?: Date;
        total: string | number;
        used: string | number;
      };
      subscription?: {
        expiredAt: Date;
        percent: number;
        resetAt?: Date;
        total: string | number;
        used: string | number;
      };
    };
  };
  loading?: boolean;
}

const CreditUsage = memo<CreditUsageProps>(({ loading, data }) => {
  const { t } = useTranslation('subscription');
  const theme = useTheme();

  if (loading || !data) return <Loading />;

  const isFreePlan = data?.plan === Plans.Free;
  const isHobbyPlan = data?.plan === Plans.Hobby;
  const showSubscriptionCredit = !isFreePlan && !isHobbyPlan;

  return (
    <>
      {data?.usage.free && (
        <Flexbox gap={8}>
          <ProgressItem
            desc={
              <CopyableLabel
                style={{ fontFamily: theme.fontFamilyCode }}
                value={data.freeBudgetId}
              />
            }
            percent={data?.usage.free.percent}
            title={t('usage.credit.free.used')}
            usage={{
              total: [data?.usage.free.total, t('usage.used')].join(' '),
              used: data?.usage.free.used,
            }}
          />
          <Time
            expiredAt={formatDateWithTime(data?.usage.free.expiredAt)}
            resetAt={formatDateWithTime(data?.usage.free.resetAt)}
          />
        </Flexbox>
      )}
      {showSubscriptionCredit && data?.usage.subscription && (
        <>
          <Divider style={{ margin: 0 }} />
          <Flexbox gap={8}>
            <ProgressItem
              desc={
                <CopyableLabel
                  style={{ fontFamily: theme.fontFamilyCode }}
                  value={data.subscriptionBudgetId}
                />
              }
              percent={data.usage.subscription.percent}
              title={t('usage.credit.subscription.used')}
              usage={{
                total: [data.usage.subscription.total, t('usage.used')].join(' '),
                used: data.usage.subscription.used,
              }}
            />
            <Time
              expiredAt={formatDateWithTime(data?.usage.subscription.expiredAt)}
              resetAt={formatDateWithTime(data?.usage.subscription.resetAt)}
            />
          </Flexbox>
        </>
      )}
    </>
  );
});

export default CreditUsage;
