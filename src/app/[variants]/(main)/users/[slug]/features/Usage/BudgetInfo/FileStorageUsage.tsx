'use client';

import { memo } from 'react';
import { useTranslation } from 'react-i18next';

import { formatSize } from '@/utils/format';

import Loading from '../components/Loading';
import ProgressItem from '../components/ProgressItem';

const StorageUsage = memo<{
  data?: {
    percent: number;
    total: string | number;
    used: string | number;
  };
  loading?: boolean;
}>(({ data, loading }) => {
  const { t } = useTranslation('subscription');

  if (loading || !data) return <Loading />;

  return (
    <ProgressItem
      percent={data.percent}
      title={t('usage.storage.file.used')}
      usage={{
        total: [formatSize(Number(data.total)), t('usage.used')].join(' '),
        used: formatSize(Number(data.used)),
      }}
    />
  );
});

export default StorageUsage;
