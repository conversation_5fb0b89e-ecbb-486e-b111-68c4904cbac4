'use client';

import { Divider } from 'antd';
import { memo } from 'react';
import { Flexbox } from 'react-layout-kit';

import { trpcQuery } from '@/libs/trpc/client';

import { formatUsage } from '../components/formatUsage';
import CreditUsage from './CreditUsage';
import EmbeddingStorageUsage from './EmbeddingStorageUsage';
import FileStorageUsage from './FileStorageUsage';

const BudgetInfo = memo<{ userId: string }>(({ userId }) => {
  const { data, isLoading } = trpcQuery.user.getUserBudgetById.useQuery(userId);

  return (
    <Flexbox gap={24} padding={16}>
      <CreditUsage
        data={
          data
            ? ({
                ...data,
                usage: {
                  free: formatUsage(data?.usage?.free),
                  subscription: formatUsage(data?.usage?.subscription),
                },
              } as any)
            : undefined
        }
        loading={isLoading}
      />
      <Divider style={{ margin: 0 }} />
      <FileStorageUsage data={formatUsage(data?.usage?.fileStorage, false)} loading={isLoading} />
      <Divider style={{ margin: 0 }} />
      <EmbeddingStorageUsage
        data={formatUsage(data?.usage?.embeddingStorage)}
        loading={isLoading}
      />
    </Flexbox>
  );
});

export default BudgetInfo;
