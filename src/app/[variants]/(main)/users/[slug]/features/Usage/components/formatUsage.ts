import { formatIntergerNumber } from '@/utils/format';

export const formatUsage = (
  usage?: { [key: string]: any; limit: number; spend: number },
  format = true,
) => {
  if (!usage) return;
  return {
    ...usage,
    percent: (usage.spend / usage.limit) * 100,
    total: format ? formatIntergerNumber(usage.limit) : usage.limit,
    used: format ? formatIntergerNumber(usage.spend) : usage.spend,
  };
};
