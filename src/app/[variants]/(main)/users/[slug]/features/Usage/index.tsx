'use client';

import { ActionIcon, Modal, Segmented } from '@lobehub/ui';
import { MaximizeIcon } from 'lucide-react';
import { memo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import Block from '@/components/Block';

import BudgetInfo from './BudgetInfo';
import SpendTable from './SpendTable';

enum Tab {
  Budget = 'budget',
  Spend = 'spend',
}

const Usage = memo<{ userId: string }>(({ userId }) => {
  const { t } = useTranslation('user');
  const [tab, setTab] = useState<Tab>(Tab.Budget);
  const [open, setOpen] = useState(false);
  return (
    <>
      <Block
        desc={t('usage.desc')}
        extra={
          <>
            {tab === Tab.Spend && <ActionIcon icon={MaximizeIcon} onClick={() => setOpen(true)} />}
            <Segmented
              onChange={(v) => setTab(v as Tab)}
              options={[
                { label: t('usage.tab.budget'), value: Tab.Budget },
                { label: t('usage.tab.spend'), value: Tab.Spend },
              ]}
              value={tab}
            />
          </>
        }
        style={{
          padding: 0,
        }}
        title={t('usage.title')}
        variant={'card'}
      >
        {tab === Tab.Budget && <BudgetInfo userId={userId} />}
        {tab === Tab.Spend && <SpendTable type={'lite'} userId={userId} />}
      </Block>

      <Modal
        footer={null}
        onCancel={() => setOpen(false)}
        open={open}
        title={t('usage.title')}
        width={'min(90vw, 1440px)'}
      >
        <SpendTable userId={userId} />
      </Modal>
    </>
  );
});

export default Usage;
