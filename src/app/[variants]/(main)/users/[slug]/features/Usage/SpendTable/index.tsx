'use client';

import { ProColumns, ProTableProps } from '@ant-design/pro-components';
import { ModelIcon } from '@lobehub/icons';
import { Popover, Typography } from 'antd';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import Duration from '@/app/[variants]/(main)/channelBackUp/spend/features/SpendTable/Duration';
import SpendType from '@/app/[variants]/(main)/channelBackUp/spend/features/SpendTable/SpendType';
import TotalToken from '@/app/[variants]/(main)/channelBackUp/spend/features/SpendTable/TotalToken';
import { useDateRangePresets } from '@/components/DateRangePicker';
import Table from '@/components/Table';
import { trpcClient } from '@/libs/trpc/client';
import { SortQuery } from '@/types/query';
import { SpendItem } from '@/types/spend';

const SpendTable = memo<{ type?: 'lite' | 'full'; userId: string }>(({ type = 'full', userId }) => {
  const { t } = useTranslation('spend');
  const defaultPresets = useDateRangePresets() || [];
  const isLite = type === 'lite';

  const request: ProTableProps<any, any>['request'] = async (params, sorts) => {
    params.emailOrUsernameOrUserId = userId;
    if (isLite) {
      params.current = 1;
      params.pageSize = 10;
    }
    return trpcClient.channel.getUserSpendList.query({
      params,
      sorts: sorts as SortQuery,
    });
  };

  const columns: ProColumns<SpendItem>[] = [
    {
      dataIndex: 'startTime',
      hideInSearch: true,
      key: 'startTime',
      render: (dom, entity) => (
        <Flexbox style={{ width: 160 }}>
          {dom}
          <Typography.Text
            ellipsis={{ tooltip: true }}
            onClick={() => {
              window.open(`https://ipapi.co/?q=${entity.ip}`);
            }}
            style={{ cursor: 'pointer', fontSize: 10, lineHeight: '10px' }}
            type={'secondary'}
          >
            {entity.ip}
          </Typography.Text>
        </Flexbox>
      ),
      sorter: true,
      title: t('table.columns.startTime'),
      valueType: 'dateTime',
      width: 180,
    },
    {
      dataIndex: 'range',
      fieldProps: {
        presets: defaultPresets,
      },
      hideInTable: true,
      key: 'range',
      title: t('table.columns.startTime'),
      valueType: 'dateRange',
    },
    {
      dataIndex: 'type',
      key: 'type',
      render: (dom, entity) => <SpendType type={entity.type}>{dom}</SpendType>,
      title: t('table.columns.type.title'),
      valueEnum: {
        acompletion: {
          text: t('table.columns.type.enums.acompletion'),
        },
        aembedding: {
          text: t('table.columns.type.enums.aembedding'),
        },
        aimage_generation: { text: t('table.columns.type.enums.aimage_generation') },
      },
      width: 140,
    },
    {
      dataIndex: 'model',
      key: 'model',
      render: (_, entity) =>
        entity.model && (
          <Flexbox align={'center'} gap={4} horizontal style={{ textWrap: 'nowrap' }}>
            <ModelIcon model={entity.model} size={16} /> {entity.model}
          </Flexbox>
        ),
      title: t('table.columns.model'),
      width: 250,
    },

    {
      dataIndex: 'totalTokens',
      hideInSearch: true,
      key: 'totalTokens',
      render: (_, entity) =>
        !!entity.totalTokens && (
          <TotalToken
            completionTokens={entity.completionTokens}
            promptTokens={entity.promptTokens}
            totalTokens={entity.totalTokens}
          />
        ),
      sorter: true,
      title: t('table.columns.totalTokens'),
      width: 250,
    },
    {
      align: 'end',
      dataIndex: 'spend',
      hideInSearch: true,
      key: 'spend',
      sorter: true,
      title: t('table.columns.spend'),
      valueType: 'digit',
      width: 120,
    },
    {
      align: 'end',
      dataIndex: 'latency',
      hideInSearch: true,
      key: 'latency',
      render: (text, entity) => (
        <Popover
          content={
            <Duration
              TTFT={entity.TTFT}
              duration={entity.duration}
              latency={entity.latency}
              tokens={entity.completionTokens}
              type={entity.type}
            />
          }
          placement={'topRight'}
        >
          <div style={{ cursor: 'pointer' }}>{(entity.latency / 1000).toFixed(2)}s</div>
        </Popover>
      ),
      title: t('table.columns.duration'),
      width: 120,
    },
  ];

  return (
    <Table<SpendItem>
      columns={columns}
      headerTitle={!isLite ? t('table.title') : undefined}
      key={'id'}
      options={isLite ? false : undefined}
      pagination={isLite ? false : undefined}
      request={request}
      search={isLite ? false : undefined}
    />
  );
});

export default SpendTable;
