'use client';

import { memo } from 'react';
import { useTranslation } from 'react-i18next';

import Loading from '../components/Loading';
import ProgressItem from '../components/ProgressItem';

const EmbeddingStorageUsage = memo<{
  data?: {
    percent: number;
    total: string | number;
    used: string | number;
  };
  loading?: boolean;
}>(({ data, loading }) => {
  const { t } = useTranslation('subscription');

  if (loading || !data) return <Loading />;

  return (
    <ProgressItem
      percent={data.percent}
      title={t('usage.storage.embeddings.used')}
      usage={{
        total: [data.total, t('usage.used')].join(' '),
        used: data.used,
      }}
    />
  );
});

export default EmbeddingStorageUsage;
