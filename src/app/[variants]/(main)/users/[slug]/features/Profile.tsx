'use client';

import { memo } from 'react';
import { Flexbox } from 'react-layout-kit';

import Block from '@/components/Block';
import { featureFlagSelectors } from '@/store/featureFlag/selectors';
import { useFeatureFlagStore } from '@/store/featureFlag/store';

import Budget from './Budget';
import Statistics from './Statistics';
import Subscription from './Subscription';
import Usage from './Usage';
import UserInfo from './UserInfo';

interface ProfileProps {
  isMobile: boolean;
  userId: string;
}

const Profile = memo<ProfileProps>(({ userId, isMobile }) => {
  const subscriptionEnabled = useFeatureFlagStore(featureFlagSelectors.subscriptionEnabled);

  return (
    <>
      <Block
        flex={1}
        gap={32}
        paddingInline={isMobile ? 24 : 0}
        style={{ maxWidth: isMobile ? undefined : 350 }}
      >
        <UserInfo userId={userId} />
      </Block>
      <Flexbox
        flex={1}
        gap={32}
        style={{ minWidth: 300, overflow: 'hidden', position: 'relative' }}
        width={'100%'}
      >
        <Statistics userId={userId} />
        {subscriptionEnabled && <Subscription userId={userId} />}
        <Usage userId={userId} />
        <Budget userId={userId} />
      </Flexbox>
    </>
  );
});

export default Profile;
