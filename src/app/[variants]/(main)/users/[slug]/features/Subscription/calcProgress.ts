import dayjs from 'dayjs';

export const calcProgress = (
  billingCycleStart?: number | null,
  billingCycleEnd?: number | null,
) => {
  if (!billingCycleStart || !billingCycleEnd) return 0;

  const billStartDate = dayjs(billingCycleStart * 1000);
  const billEndDate = dayjs(billingCycleEnd * 1000);

  // 获取当前日期
  const currentDate = dayjs();

  // 计算账单总天数
  const totalDays = billEndDate.diff(billStartDate, 'day');

  // 计算已经过去的天数
  const passedDays = currentDate.diff(billStartDate, 'day');

  // 计算进度百分比
  const progress = (passedDays / totalDays) * 100;

  return progress > 100 ? 100 : progress;
};
