'use client';

import { SiStripe } from '@icons-pack/react-simple-icons';
import { ActionIcon, CopyButton, Icon, Modal, Tag, Tooltip } from '@lobehub/ui';
import { App, Progress, Table } from 'antd';
import { useTheme } from 'antd-style';
import { Clock3Icon, MaximizeIcon, XCircleIcon } from 'lucide-react';
import Link from 'next/link';
import { memo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';
import urlJoin from 'url-join';

import Block from '@/components/Block';
import { STRIPE_URL } from '@/const/url';
import PlanIcon from '@/features/PlanIcon';
import { trpcQuery } from '@/libs/trpc/client';
import { serverConfigSelectors, useServerConfigStore } from '@/store/serverConfig';
import { BillingMode, Recurring } from '@/types/subscription';
import {
  formatDate,
  formatDateWithTime,
  formatPrice,
  formatPricingWithCurrency,
} from '@/utils/format';

import ModalContent from './ModalContent';
import { calcProgress } from './calcProgress';

const Subscription = memo<{ userId: string }>(({ userId }) => {
  const { data, isLoading, refetch } = trpcQuery.user.getUserSubscriptionById.useQuery(userId);
  const { enableStripe } = useServerConfigStore(serverConfigSelectors.config);
  const { t } = useTranslation(['user', 'subscription']);
  const [open, setOpen] = useState(false);
  const [item, setItem] = useState<any>(null);
  const theme = useTheme();
  const { modal, message } = App.useApp();

  const cancelMutation = trpcQuery.user.cancelUserSubscription.useMutation({
    onError: () => {
      message.error(t('cancelSubscription.error', { ns: 'subscription' }));
    },
    onSuccess: () => {
      message.success(t('cancelSubscription.success', { ns: 'subscription' }));
    },
  });

  const handleCancelSubscription = (subscriptionId: string) => {
    modal.confirm({
      content: t('cancelSubscription.confirm', { ns: 'subscription' }),
      okButtonProps: { danger: true },
      onOk: async () => {
        await cancelMutation.mutateAsync({ subscriptionId });
        await refetch();
      },
      title: t('cancelSubscription.title', { ns: 'subscription' }),
    });
  };

  return (
    <>
      <Block
        desc={t('subscription.desc')}
        extra={
          <Flexbox
            align={'flex-end'}
            paddingBlock={8}
            paddingInline={16}
            style={{
              background: theme.colorFillTertiary,
              borderRadius: theme.borderRadius,
            }}
          >
            <div>
              {t('subscription.total')}{' '}
              <span
                style={{
                  fontWeight: 'bold',
                }}
              >
                $
                {formatPrice(
                  data ? data?.reduce((acc, item: any) => acc + item?.pricing || 0, 0) / 100 : 0,
                )}
              </span>
            </div>
            <div style={{ color: theme.colorTextDescription, fontSize: 12 }}>
              {t('subscription.count', { count: data?.length || 0 })}
            </div>
          </Flexbox>
        }
        gap={12}
        style={{
          padding: 0,
        }}
        title={t('subscription.title')}
        variant={'card'}
      >
        <Table
          columns={[
            {
              dataIndex: 'plan',
              key: 'plan',
              render: (plan, record) => {
                const isActive = record?.status === 0;
                const percent = isActive
                  ? calcProgress(record.billingCycleStart, record.billingCycleEnd)
                  : 0;
                return (
                  <Flexbox align={'center'} gap={12} horizontal>
                    <Tooltip
                      title={[
                        formatDate(record.billingCycleStart, 1000),
                        formatDate(record.billingCycleEnd, 1000),
                      ].join(' ~ ')}
                    >
                      {isActive ? (
                        <Progress
                          percent={percent}
                          showInfo={false}
                          size={24}
                          strokeWidth={16}
                          type="circle"
                        />
                      ) : (
                        <div
                          style={{
                            border: `2px dashed ${theme.colorFillSecondary}`,
                            borderRadius: 24,
                            height: 24,
                            width: 24,
                          }}
                        />
                      )}
                    </Tooltip>
                    <PlanIcon plan={plan} size={24} type={'combine'} />
                    {isActive ? (
                      <Tag
                        color={'success'}
                        style={{
                          margin: 0,
                        }}
                      >
                        {t('subscription.status.active')}
                      </Tag>
                    ) : (
                      <Tag
                        style={{
                          background: theme.colorFillTertiary,
                          color: theme.colorTextSecondary,
                          margin: 0,
                        }}
                      >
                        {t('subscription.status.inactive')}
                      </Tag>
                    )}
                    {isActive && record?.cancelAt && (
                      <Tag
                        icon={<Icon icon={Clock3Icon} />}
                        style={{
                          background: theme.colorFillTertiary,
                          color: theme.colorTextSecondary,
                          margin: 0,
                        }}
                      >
                        {t('subscription.cancelAt')}: {formatDate(record?.cancelAt, 1000)}
                      </Tag>
                    )}
                  </Flexbox>
                );
              },
              title: t('subscription.plan'),
              width: 400,
            },
            {
              dataIndex: 'mode',
              key: 'mode',
              render: (mode) => (
                <Tag>
                  {mode === BillingMode.Subscription
                    ? t('plans.subscribe', { ns: 'subscription' })
                    : t('plans.navs.payonce', { ns: 'subscription' })}
                </Tag>
              ),
              title: t('subscription.mode'),
              width: 120,
            },
            {
              dataIndex: 'recurring',
              key: 'recurring',
              render: (recurring, { mode, quantity }) => (
                <Tag>
                  {mode === BillingMode.Subscription
                    ? recurring === Recurring.Monthly
                      ? t('plans.navs.monthly', { ns: 'subscription' })
                      : t('plans.navs.yearly', { ns: 'subscription' })
                    : recurring === Recurring.Monthly
                      ? t('recurring.month', { ns: 'subscription', quantity })
                      : t('recurring.month', { ns: 'subscription', quantity: 12 })}
                </Tag>
              ),
              title: t('subscription.recurring'),
              width: 120,
            },
            {
              dataIndex: 'pricing',
              key: 'pricing',
              render: (pricing, { currency }) => (
                <Flexbox align={'baseline'} gap={8} horizontal>
                  <span
                    style={{
                      color: theme.colorTextDescription,
                    }}
                  >
                    {(currency ?? 'usd').toUpperCase()}
                  </span>
                  <span
                    style={{
                      fontWeight: 500,
                    }}
                  >
                    {formatPricingWithCurrency(pricing, currency)}
                  </span>
                </Flexbox>
              ),
              title: t('subscription.pricing'),
              width: 200,
            },
            {
              dataIndex: 'billingPaidAt',
              key: 'billingPaidAt',
              render: (billingPaidAt) => formatDateWithTime(billingPaidAt, 1000),
              title: t('subscription.billingPaidAt'),
              width: 200,
            },
            {
              dataIndex: 'billingCycleEnd',
              key: 'billingCycleEnd',
              render: (billingCycleEnd, { mode, status }) =>
                mode === BillingMode.Payment || status !== 0 ? (
                  <span style={{ color: theme.colorTextDescription }}>
                    {t('subscription.noNextBillingDate')}
                  </span>
                ) : (
                  formatDateWithTime(billingCycleEnd, 1000)
                ),
              title: t('subscription.nextBillingDate'),
              width: 200,
            },
            {
              align: 'right',
              dataIndex: 'id',
              fixed: 'right',
              key: 'detail',
              render: (id, record) => (
                <Flexbox gap={8} horizontal justify={'flex-end'}>
                  {enableStripe ? (
                    <Link href={urlJoin(STRIPE_URL, 'subscriptions', id)} target={'_blank'}>
                      <ActionIcon
                        active
                        fill={theme.colorText}
                        icon={SiStripe}
                        size={'small'}
                        title={t('actions.stripSubscriptions')}
                      />
                    </Link>
                  ) : (
                    <CopyButton
                      active
                      content={id}
                      size={'small'}
                      title={id}
                      tooltipProps={{
                        placement: 'topRight',
                      }}
                    />
                  )}
                  <ActionIcon
                    active
                    icon={MaximizeIcon}
                    onClick={() => {
                      setItem(record);
                      setOpen(true);
                    }}
                    size={'small'}
                    title={t('subscription.detail')}
                  />
                  {record.status === 0 && (
                    <ActionIcon
                      active
                      icon={XCircleIcon}
                      loading={cancelMutation.isPending}
                      onClick={() => handleCancelSubscription(id)}
                      size={'small'}
                      title={t('cancelSubscription.title', { ns: 'subscription' })}
                    />
                  )}
                </Flexbox>
              ),
              width: 80,
            },
          ]}
          dataSource={data}
          loading={isLoading}
          pagination={false}
          rowKey={'id'}
          scroll={{ x: 'max-content' }}
        />
      </Block>
      <Modal onCancel={() => setOpen(false)} open={open} title={t('subscription.title')}>
        {item && <ModalContent data={item} />}
      </Modal>
    </>
  );
});

export default Subscription;
