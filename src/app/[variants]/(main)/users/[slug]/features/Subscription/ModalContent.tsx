import { Tag } from '@lobehub/ui';
import { useTheme } from 'antd-style';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import CreatedTime from '@/components/CreatedTime';
import Descriptions, { DescriptionItem } from '@/components/Descriptions';
import PlanIcon from '@/features/PlanIcon';
import { serverConfigSelectors, useServerConfigStore } from '@/store/serverConfig';
import { BillingMode, Plans, Recurring } from '@/types/subscription';
import { formatDateWithTime, formatPrice } from '@/utils/format';
import { withNoSSR } from '@/utils/withNoSSR';

interface ModalContentProps {
  data: {
    billingCycleEnd: number;
    billingCycleStart: number;
    billingPaidAt: number;
    cancelAt: number;
    cancelAtPeriodEnd: boolean;
    createdAt: number;
    id: string;
    mode: BillingMode;
    nextBilling: {
      plan: Plans;
      pricing: number;
      recurring: Recurring;
    };
    plan: Plans;
    pricing: number;
    quantity?: number;
    recurring: Recurring;
    status: number;
    stripeId: string;
    updatedAt: number;
  };
}

const ModalContent = memo<ModalContentProps>(({ data }) => {
  const { t } = useTranslation(['user', 'subscription']);
  const theme = useTheme();
  const { enableStripe } = useServerConfigStore(serverConfigSelectors.config);

  const nextBilling: {
    plan: Plans;
    pricing: number;
    recurring: Recurring;
  } = data?.nextBilling as any;
  return (
    <Flexbox gap={16} key={data.id}>
      <Descriptions
        bordered
        items={[
          {
            copyable: true,
            key: 'id',
            label: 'ID',
            style: {
              fontFamily: theme.fontFamilyCode,
            },
            value: data?.id,
          },
          {
            key: 'plan',
            label: t('subscription.plan'),
            value: data?.plan ? (
              <PlanIcon plan={data?.plan as Plans} size={24} type={'combine'} />
            ) : (
              '--'
            ),
          },
          {
            key: 'status',
            label: t('subscription.status.title'),
            value:
              data?.status === 0 ? (
                <Tag color={'success'}>{t('subscription.status.active')}</Tag>
              ) : (
                <Tag>{t('subscription.status.inactive')}</Tag>
              ),
          },
        ]}
      />
      <Descriptions
        bordered
        items={
          [
            enableStripe && {
              copyable: true,
              key: 'stripeId',
              label: 'Stripe ID',
              style: {
                fontFamily: theme.fontFamilyCode,
              },
              value: data?.stripeId,
            },
            {
              key: 'pricing',
              label: t('subscription.pricing'),
              value: '$' + formatPrice(data?.pricing / 100 || 0),
            },
            {
              key: 'billingPaidAt',
              label: t('subscription.billingPaidAt'),
              value: formatDateWithTime(data?.billingPaidAt, 1000),
            },

            {
              key: 'mode',
              label: t('subscription.mode'),
              value:
                data?.mode === BillingMode.Subscription
                  ? t('plans.subscribe', { ns: 'subscription' })
                  : t('plans.navs.payonce', { ns: 'subscription' }),
            },
            {
              key: 'recurring',
              label: t('subscription.recurring'),
              value:
                data?.mode === BillingMode.Subscription
                  ? data?.recurring === Recurring.Monthly
                    ? t('plans.navs.monthly', { ns: 'subscription' })
                    : t('plans.navs.yearly', { ns: 'subscription' })
                  : data?.recurring === Recurring.Monthly
                    ? t('recurring.month', { ns: 'subscription', quantity: data?.quantity })
                    : t('recurring.oneYear', { ns: 'subscription' }),
            },
            {
              key: 'cancelAtPeriodEnd',
              label: t('subscription.cancelAtPeriodEnd'),
              value: data?.cancelAtPeriodEnd ? 'Yes' : 'No',
            },
            {
              key: 'billingCycleStart',
              label: t('subscription.billingCycleStart'),
              value: formatDateWithTime(data?.billingCycleStart, 1000),
            },
            {
              key: 'billingCycleEnd',
              label: t('subscription.billingCycleEnd'),
              value: formatDateWithTime(data?.billingCycleEnd, 1000),
            },
          ].filter(Boolean) as DescriptionItem[]
        }
      />
      {data?.cancelAt && (
        <Descriptions
          bordered
          items={[
            {
              key: 'cancelAt',
              label: t('subscription.cancelAt'),
              value: formatDateWithTime(data?.cancelAt, 1000),
            },
          ]}
        />
      )}
      {nextBilling && (
        <Descriptions
          bordered
          items={[
            {
              key: 'plan',
              label: t('subscription.plan'),
              value: nextBilling?.plan ? (
                <PlanIcon plan={nextBilling.plan} size={24} type={'combine'} />
              ) : (
                '--'
              ),
            },
            {
              key: 'pricing',
              label: t('subscription.pricing'),
              value: '$' + formatPrice(nextBilling?.pricing || 0),
            },
            {
              key: 'mode',
              label: t('subscription.recurring'),
              value:
                nextBilling?.recurring === Recurring.Monthly
                  ? t('plans.navs.monthly', { ns: 'subscription' })
                  : t('plans.navs.yearly', { ns: 'subscription' }),
            },
          ]}
          title={t('subscription.nextBilling')}
        />
      )}
      <CreatedTime
        createdAt={formatDateWithTime(data?.createdAt)}
        updatedAt={formatDateWithTime(data?.updatedAt)}
      />
    </Flexbox>
  );
});

export default withNoSSR(ModalContent);
