import { DatePicker, Form, InputNumber, Tag } from '@lobehub/ui';
import { useTheme } from 'antd-style';
import dayjs from 'dayjs';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import CreatedTime from '@/components/CreatedTime';
import Descriptions from '@/components/Descriptions';
import PlanIcon from '@/features/PlanIcon';
import { BudgetItem } from '@/types/subscription';
import { formatDateWithTime } from '@/utils/format';
import { today } from '@/utils/time';
import { withNoSSR } from '@/utils/withNoSSR';

interface EditBudgetProps {
  data: BudgetItem;
  onDataChange: (data: BudgetItem) => void;
}

const EditBudget = memo<EditBudgetProps>(({ data, onDataChange }) => {
  const { t } = useTranslation('user');
  const theme = useTheme();
  const isExpired = dayjs(data.expires).isBefore(today());
  const isOver = data.spend >= data.maxBudget;

  return (
    <Flexbox gap={16} key={data.id}>
      <Descriptions
        bordered
        items={[
          {
            copyable: true,
            key: 'token',
            label: 'TOKEN',
            style: {
              fontFamily: theme.fontFamilyCode,
            },
            value: data.token,
          },
          {
            copyable: true,
            key: 'key',
            label: 'Key 别名',
            style: {
              fontFamily: theme.fontFamilyCode,
            },
            value: data.key,
          },
          {
            key: 'metadata',
            label: t('budget.metadata'),
            value: (
              <Flexbox align={'center'} gap={8} horizontal justify={'flex-start'}>
                {data?.metadata?.plan && <PlanIcon plan={data.metadata.plan} type={'tag'} />}
                {data?.metadata?.tags &&
                  data.metadata.tags?.map((tag: string) => (
                    <Tag key={tag} style={{ margin: 0 }}>
                      {tag}
                    </Tag>
                  ))}
              </Flexbox>
            ),
          },
          {
            key: 'status',
            label: t('budget.status.title'),
            value: (
              <Flexbox gap={8} horizontal>
                {!isExpired && (
                  <Tag
                    color={'success'}
                    style={{
                      margin: 0,
                    }}
                  >
                    {t('budget.status.active')}
                  </Tag>
                )}
                {isOver && (
                  <Tag
                    color={'warning'}
                    style={{
                      margin: 0,
                    }}
                  >
                    {t('budget.status.over')}
                  </Tag>
                )}
                {isExpired && (
                  <Tag
                    style={{
                      background: theme.colorFillTertiary,
                      color: theme.colorTextSecondary,
                      margin: 0,
                    }}
                  >
                    {t('budget.status.expired')}
                  </Tag>
                )}
              </Flexbox>
            ),
          },
        ]}
      />
      <Form
        initialValues={{
          ...data,
          budgetResetAt: dayjs(data.budgetResetAt),
          expires: dayjs(data.expires),
        }}
        itemMinWidth={300}
        items={[
          {
            children: <InputNumber style={{ width: '100%' }} />,
            label: t('budget.used'),
            name: 'spend',
          },
          {
            children: <InputNumber style={{ width: '100%' }} />,
            label: t('budget.maxBudget'),
            name: 'maxBudget',
          },
          {
            children: (
              <DatePicker format="YYYY-MM-DD HH:mm:ss" showTime style={{ width: '100%' }} />
            ),
            label: t('budget.budgetResetAt'),
            name: 'budgetResetAt',
          },
          {
            children: (
              <DatePicker format="YYYY-MM-DD HH:mm:ss" showTime style={{ width: '100%' }} />
            ),
            label: t('budget.expires'),
            name: 'expires',
          },
        ]}
        itemsType={'flat'}
        onValuesChange={(_, values) => {
          onDataChange({
            ...values,
            budgetResetAt: values.budgetResetAt.toISOString(),
            expires: values.expires.toISOString(),
          });
        }}
      />

      <CreatedTime
        createdAt={formatDateWithTime(data?.createdAt)}
        updatedAt={formatDateWithTime(data?.updatedAt)}
      />
    </Flexbox>
  );
});

export default withNoSSR(EditBudget);
