'use client';

import { ActionIcon, Icon, Modal } from '@lobehub/ui';
import { App, Progress, Table, Tag } from 'antd';
import { useTheme } from 'antd-style';
import dayjs from 'dayjs';
import { Clock3Icon, EditIcon, MaximizeIcon } from 'lucide-react';
import { memo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import Block from '@/components/Block';
import PlanIcon from '@/features/PlanIcon';
import { trpcClient, trpcQuery } from '@/libs/trpc/client';
import { formatDate, formatDateWithTime, formatIntergerNumber } from '@/utils/format';
import { today } from '@/utils/time';

import EditBudget from './EditBudget';
import ModalContent from './ModalContent';

const Budget = memo<{ userId: string }>(({ userId }) => {
  const { data, isLoading, refetch } = trpcQuery.user.getUserBudgetDetailById.useQuery(userId);
  const { t } = useTranslation('user');
  const { t: tCommon } = useTranslation('common');
  const theme = useTheme();
  const { message } = App.useApp();
  const [isShowDetail, setIsShowDetail] = useState(false);
  const [isShowEdit, setIsShowEdit] = useState(false);
  const [item, setItem] = useState<any>(null);
  const [editItem, setEditItem] = useState<any>(null);
  const [editUpdating, setEditUpdating] = useState(false);

  return (
    <>
      <Block
        desc={t('budget.desc')}
        gap={12}
        style={{
          padding: 0,
        }}
        title={t('budget.title')}
        variant={'card'}
      >
        <Table
          columns={[
            {
              dataIndex: 'spend',
              key: 'spend',
              render: (spend, { blocked, expires, budgetResetAt, maxBudget }) => {
                const isExpired = dayjs(expires).isBefore(today());
                const isOver = spend >= (maxBudget || 0);
                return (
                  <Flexbox align={'center'} gap={8} horizontal>
                    <Progress
                      percent={(spend / (maxBudget || 1)) * 100}
                      showInfo={false}
                      size={24}
                      strokeColor={isOver ? theme.colorWarning : theme.colorInfo}
                      strokeWidth={16}
                      type="circle"
                    />
                    <div>{formatIntergerNumber((spend / (maxBudget || 1)) * 100)}%</div>
                    {blocked && (
                      <Tag
                        color={'error'}
                        style={{
                          margin: 0,
                        }}
                      >
                        {t('budget.status.blocked')}
                      </Tag>
                    )}
                    {!isExpired && !blocked && (
                      <Tag
                        color={'success'}
                        style={{
                          margin: 0,
                        }}
                      >
                        {t('budget.status.active')}
                      </Tag>
                    )}
                    {isOver && (
                      <Tag
                        color={'warning'}
                        style={{
                          margin: 0,
                        }}
                      >
                        {t('budget.status.over')}
                      </Tag>
                    )}
                    {isExpired && (
                      <Tag
                        style={{
                          background: theme.colorFillTertiary,
                          color: theme.colorTextSecondary,
                          margin: 0,
                        }}
                      >
                        {t('budget.status.expired')}
                      </Tag>
                    )}
                    {!isExpired && budgetResetAt && (
                      <Tag
                        icon={<Icon icon={Clock3Icon} />}
                        style={{
                          background: theme.colorFillTertiary,
                          color: theme.colorTextSecondary,
                          margin: 0,
                        }}
                      >
                        {t('budget.budgetResetAt')}: {formatDate(budgetResetAt)}
                      </Tag>
                    )}
                  </Flexbox>
                );
              },
              title: t('budget.spend'),
              width: 300,
            },
            {
              dataIndex: 'maxBudget',
              key: 'maxBudget',
              render: (maxBudget, { spend }) => (
                <div>
                  <span style={{ fontWeight: 'bold' }}>
                    {formatIntergerNumber(spend * 1_000_000)}
                  </span>
                  {['', '/', formatIntergerNumber(maxBudget * 1_000_000)].join(' ')}
                </div>
              ),
              title: t('budget.used'),
              width: 300,
            },
            {
              dataIndex: 'budgetDuration',
              key: 'budgetDuration',
              render: (budgetDuration) => (
                <Tag>{budgetDuration ? budgetDuration.toUpperCase() : t('budget.oneTime')}</Tag>
              ),
              title: t('budget.budgetDuration'),
              width: 100,
            },
            {
              dataIndex: 'createdAt',
              key: 'createdAt',
              render: (createdAt) => formatDateWithTime(createdAt),
              title: t('budget.createdAt'),
              width: 200,
            },
            {
              dataIndex: 'expires',
              key: 'expires',
              render: (expires) => formatDateWithTime(expires),
              title: t('budget.expires'),
              width: 200,
            },
            {
              dataIndex: 'metadata',
              key: 'metadata',
              render: ({ plan, tags } = {}) => (
                <Flexbox align={'center'} gap={8} horizontal justify={'flex-start'}>
                  {plan && <PlanIcon plan={plan} type={'tag'} />}
                  {tags &&
                    tags?.map((tag: string) => (
                      <Tag key={tag} style={{ margin: 0 }}>
                        {tag}
                      </Tag>
                    ))}
                </Flexbox>
              ),
              title: t('budget.metadata'),
              width: 200,
            },
            {
              align: 'right',
              dataIndex: 'token',
              fixed: 'right',
              key: 'token',
              render: (token, record) => {
                return (
                  <Flexbox gap={8} horizontal>
                    <ActionIcon
                      active
                      icon={MaximizeIcon}
                      onClick={() => {
                        setItem(record);
                        setIsShowDetail(true);
                      }}
                      size={'small'}
                      title={t('budget.detail')}
                    />
                    {!record.blocked && (
                      <ActionIcon
                        active
                        icon={EditIcon}
                        onClick={() => {
                          setItem(record);
                          setIsShowEdit(true);
                        }}
                        size={'small'}
                        title={t('budget.edit')}
                      />
                    )}
                  </Flexbox>
                );
              },
              width: 56,
            },
          ]}
          dataSource={data}
          loading={isLoading}
          pagination={false}
          rowKey={'token'}
          scroll={{ x: 'max-content' }}
        />
      </Block>
      <Modal
        cancelText={tCommon('button.close')}
        okButtonProps={{
          hidden: true,
        }}
        onCancel={() => setIsShowDetail(false)}
        open={isShowDetail}
        title={t('budget.title')}
      >
        {item && <ModalContent data={item} />}
      </Modal>
      <Modal
        okButtonProps={{
          loading: editUpdating,
        }}
        onCancel={() => setIsShowEdit(false)}
        onOk={async () => {
          setEditUpdating(true);
          await trpcClient.budget.updateBudgetKey.mutate({
            token: item.token,
            userId,
            value: editItem,
          });
          await refetch();
          setEditUpdating(false);
          setIsShowEdit(false);
          message.success(t('budget.updateSuccess'));
        }}
        open={isShowEdit}
        title={t('budget.edit')}
      >
        {item && <EditBudget data={item} onDataChange={setEditItem} />}
      </Modal>
    </>
  );
});

export default Budget;
