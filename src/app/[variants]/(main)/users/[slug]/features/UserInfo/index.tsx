'use client';

import { <PERSON><PERSON><PERSON><PERSON>, SiStripe } from '@icons-pack/react-simple-icons';
import { Avatar, Icon, Snippet, Tag, Tooltip } from '@lobehub/ui';
import { Divider, Skeleton, Typography } from 'antd';
import { useResponsive, useTheme } from 'antd-style';
import { BanIcon, CheckIcon, Clock3Icon, ClockArrowUp, LockIcon } from 'lucide-react';
import Link from 'next/link';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';
import urlJoin from 'url-join';

import { TimeLabel } from '@/components/CreatedTime';
import Descriptions from '@/components/Descriptions';
import TextButton from '@/components/TextButton';
import { STRIPE_URL } from '@/const/url';
import { trpcQuery } from '@/libs/trpc/client';
import { serverConfigSelectors, useServerConfigStore } from '@/store/serverConfig';
import { formatDateWithTime } from '@/utils/format';

import Actions from './Actions';
import CheckRefundable from './CheckRefundable';

const { Title, Paragraph } = Typography;

const UserInfo = memo<{ userId: string }>(({ userId }) => {
  const { data, isLoading } = trpcQuery.user.getUserInfoById.useQuery(userId);
  const { data: clerk, refetch } = trpcQuery.user.getUserClerkInfoById.useQuery(userId);
  const { enableStripe, enableClerk, clerkUrl } = useServerConfigStore(
    serverConfigSelectors.config,
  );
  const { t } = useTranslation('user');
  const theme = useTheme();
  const { mobile } = useResponsive();

  if (isLoading) {
    return (
      <>
        <Flexbox align={mobile ? 'center' : 'flex-start'} gap={24} horizontal={mobile}>
          <Skeleton.Avatar active size={mobile ? 64 : 120} />
          <Skeleton.Button active block size={'large'} style={{ minWidth: 200 }} />
        </Flexbox>
        <Skeleton.Button active block size={'large'} />
        <Skeleton active paragraph={{ rows: 4 }} title={false} />
        <Skeleton active paragraph={{ rows: 4 }} title={false} />
      </>
    );
  }

  return (
    <>
      <Flexbox align={mobile ? 'center' : 'flex-start'} gap={24} horizontal={mobile} width={'100%'}>
        <Avatar alt={data?.id} avatar={data?.avatar} size={mobile ? 64 : 120} />
        <Flexbox gap={4}>
          <Flexbox align={'center'} gap={8} horizontal>
            <Title
              ellipsis={{ rows: 1 }}
              level={2}
              style={{ fontSize: mobile ? 20 : 24, fontWeight: 'bold', margin: 0 }}
            >
              {[data?.firstName, data?.lastName].join(' ') || data?.username || '--'}
            </Title>
            <Tooltip title={t('table.user.isOnboard')}>
              <Tag
                color={data?.isOnboarded ? 'green' : undefined}
                icon={<Icon icon={CheckIcon} />}
                style={{ margin: 0 }}
              />
            </Tooltip>
            {clerk?.locked && (
              <Tooltip title={t('status.locked')}>
                <Tag color={'warning'} icon={<Icon icon={LockIcon} />} style={{ margin: 0 }} />
              </Tooltip>
            )}
            {clerk?.banned && (
              <Tooltip title={t('status.banned')}>
                <Tag color={'error'} icon={<Icon icon={BanIcon} />} style={{ margin: 0 }} />
              </Tooltip>
            )}
          </Flexbox>
          {clerk ? (
            <TimeLabel
              date={formatDateWithTime(clerk.lastActiveAt)}
              icon={ClockArrowUp}
              title={t('info.lastActiveAt')}
            />
          ) : (
            <Paragraph
              ellipsis={{ rows: 1 }}
              style={{ color: theme.colorTextSecondary, margin: 0 }}
            >
              {data?.email || '--'}{' '}
            </Paragraph>
          )}
        </Flexbox>
      </Flexbox>
      <Snippet copyable language={'txt'} style={{ width: '100%' }}>
        {data?.id || '--'}
      </Snippet>
      <Descriptions
        items={[
          {
            copyable: true,
            key: 'firstName',
            label: t('info.firstName'),
            value: data?.firstName,
          },
          {
            copyable: true,
            key: 'lastName',
            label: t('info.lastName'),
            value: data?.lastName,
          },
          {
            copyable: true,
            key: 'username',
            label: t('info.username'),
            value: data?.username,
          },
          {
            copyable: true,
            key: 'email',
            label: t('info.email'),
            value: data?.email,
          },
          {
            copyable: true,
            key: 'phone',
            label: t('info.phone'),
            value: data?.phone,
          },
        ]}
        labelWidth={100}
      />
      <Flexbox gap={16}>
        <CheckRefundable clerk={clerk} onRefreshUser={refetch} userId={userId} />
        {enableStripe && data?.stripId && (
          <>
            <Link
              href={urlJoin(STRIPE_URL, 'customers', data?.stripId || '')}
              style={{ color: 'inherit' }}
              target={'_blank'}
            >
              <TextButton icon={SiStripe} iconProps={{ fill: theme.colorText }}>
                {t('actions.strip')}
              </TextButton>
            </Link>
            <Divider style={{ margin: 0 }} />
          </>
        )}
        {enableClerk && (
          <>
            <Link
              href={urlJoin(clerkUrl, 'users', data?.id || '')}
              style={{ color: 'inherit' }}
              target={'_blank'}
            >
              <TextButton icon={SiClerk} iconProps={{ fill: theme.colorText }}>
                {t('actions.clerk')}
              </TextButton>
            </Link>
            <Divider style={{ margin: 0 }} />
            <Actions
              refetch={refetch}
              status={{ banned: clerk?.banned, locked: clerk?.locked }}
              userId={userId}
            />
          </>
        )}
      </Flexbox>
      <TimeLabel
        date={formatDateWithTime(data?.clerkCreatedAt)}
        icon={Clock3Icon}
        title={t('info.clerkCreatedAt')}
      />
    </>
  );
});

export default UserInfo;
