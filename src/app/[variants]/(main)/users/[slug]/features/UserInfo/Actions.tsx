import { Divider } from 'antd';
import { BanIcon, CircleDashedIcon, LockIcon, TrashIcon, UnlockIcon } from 'lucide-react';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';

import { useUserActions } from '@/app/[variants]/(main)/users/features/useUserActions';
import TextButton from '@/components/TextButton';

interface ActionsProps {
  refetch: () => void;
  status: {
    banned?: boolean;
    locked?: boolean;
  };
  userId: string;
}

const Actions = memo<ActionsProps>(({ refetch, userId, status }) => {
  const { t } = useTranslation('user');
  const { lockUser, banUser, deleteUser } = useUserActions({ onFinish: refetch });

  return (
    <>
      <TextButton
        danger
        icon={status.locked ? UnlockIcon : LockIcon}
        onClick={() => lockUser(userId, status.locked)}
      >
        {status.locked ? t('actions.unlock') : t('actions.lock')}
      </TextButton>
      <Divider style={{ margin: 0 }} />
      <TextButton
        danger
        icon={status.banned ? CircleDashedIcon : BanIcon}
        onClick={() => banUser(userId, status.banned)}
      >
        {status.banned ? t('actions.unban') : t('actions.ban')}
      </TextButton>
      <Divider style={{ margin: 0 }} />
      <TextButton danger icon={TrashIcon} onClick={() => deleteUser(userId)}>
        {t('actions.delete')}
      </TextButton>
      <Divider style={{ margin: 0 }} />
    </>
  );
});

export default Actions;
