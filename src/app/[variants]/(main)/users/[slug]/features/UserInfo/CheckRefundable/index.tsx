import { User } from '@clerk/nextjs/server';
import { Modal } from '@lobehub/ui';
import { App, Popconfirm } from 'antd';
import { createStyles } from 'antd-style';
import { Ban, Lock, SearchIcon, Trash } from 'lucide-react';
import { memo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import TextButton from '@/components/TextButton';
import { trpcClient } from '@/libs/trpc/client';

import ModalContent from './ModalContent';

const useStyles = createStyles(({ css, token }) => ({
  button: css`
    margin-inline-end: 12px;
    padding-block-end: 16px;
    border-block-end: 1px solid ${token.colorBorder};
  `,
  danger: css`
    color: ${token.colorError};
  `,
}));

const CheckRefundable = memo<{ clerk?: User; onRefreshUser: () => void; userId: string }>(
  ({ clerk, onRefreshUser, userId }) => {
    const [open, setOpen] = useState(false);
    const { t } = useTranslation('user');
    const { styles } = useStyles();
    const { message } = App.useApp();

    const { locked, banned } = clerk || {};

    console.log('clerk', clerk);

    // 锁定/解锁用户
    const handleLockUser = async () => {
      try {
        await trpcClient.user.lockUser.mutate({
          current: locked,
          userId,
        });

        message.success(t(locked ? 'actions.unlockSuccess' : 'actions.lockSuccess'));
        onRefreshUser();
      } catch {
        message.error(t(locked ? 'actions.unlockFailed' : 'actions.lockFailed'));
      }
    };

    // 封禁/解封用户
    const handleBanUser = async () => {
      try {
        await trpcClient.user.banUser.mutate({
          current: banned,
          userId,
        });

        message.success(t(banned ? 'actions.unbanSuccess' : 'actions.banSuccess'));
        onRefreshUser();
      } catch {
        message.error(t(banned ? 'actions.unbanFailed' : 'actions.banFailed'));
      }
    };

    // 删除用户
    const handleDeleteUser = async () => {
      try {
        await trpcClient.user.deleteUser.mutate({
          userId,
        });

        message.success(t('actions.deleteSuccess'));
        onRefreshUser();
      } catch {
        message.error(t('actions.deleteFailed'));
      }
    };

    const dangerButtonClassName = `${styles.button} ${styles.danger}`;

    return (
      <>
        <TextButton className={styles.button} icon={SearchIcon} onClick={() => setOpen(true)}>
          {t('refundable.title')}
        </TextButton>
        <Popconfirm
          onConfirm={handleLockUser}
          placement="topLeft"
          title={t(locked ? 'actions.unlockConfirm' : 'actions.lockConfirm')}
        >
          <TextButton className={dangerButtonClassName} icon={Lock}>
            {t(locked ? 'actions.unlock' : 'actions.lock')}
          </TextButton>
        </Popconfirm>
        <Popconfirm
          onConfirm={handleBanUser}
          placement="topLeft"
          title={t(banned ? 'actions.unbanConfirm' : 'actions.banConfirm')}
        >
          <TextButton className={dangerButtonClassName} icon={Ban}>
            {t(banned ? 'actions.unban' : 'actions.ban')}
          </TextButton>
        </Popconfirm>
        <Popconfirm
          onConfirm={handleDeleteUser}
          placement="topLeft"
          title={t('actions.deleteConfirm')}
        >
          <TextButton className={dangerButtonClassName} icon={Trash}>
            {t('actions.delete')}
          </TextButton>
        </Popconfirm>
        <Modal
          footer={null}
          onCancel={() => setOpen(false)}
          open={open}
          title={t('refundable.title')}
        >
          <ModalContent userId={userId} />
        </Modal>
      </>
    );
  },
);

export default CheckRefundable;
