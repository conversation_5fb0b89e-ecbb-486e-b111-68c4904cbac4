import { Tag } from '@lobehub/ui';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import Descriptions from '@/components/Descriptions';
import Loading from '@/components/Loading';
import PlanIcon from '@/features/PlanIcon';
import { trpcQuery } from '@/libs/trpc/client';
import { Plans } from '@/types/subscription';
import { formatDateWithTime, formatIntergerNumber, formatPrice, formatSize } from '@/utils/format';
import { withNoSSR } from '@/utils/withNoSSR';

const ModalContent = memo<{ userId: string }>(({ userId }) => {
  const { data, isLoading } = trpcQuery.user.getRefundableById.useQuery(userId);
  const { t } = useTranslation('user');
  if (isLoading || !data) return <Loading />;

  return (
    <Flexbox gap={16}>
      <Descriptions
        bordered
        items={[
          {
            key: 'currentPlan',
            label: t('refundable.form.currentPlan'),
            value: (
              <PlanIcon
                plan={(data.currentPlan?.plan as Plans) || Plans.Free}
                size={24}
                type={'combine'}
              />
            ),
          },
          {
            key: 'createdAt',
            label: t('refundable.form.createdAt'),
            value: formatDateWithTime(data.currentPlan?.createdAt),
          },
          {
            key: 'messagesWithCustomProvider',
            label: t('refundable.form.messagesWithCustomProvider'),
            value: formatIntergerNumber(data.messagesWithCustomProvider),
          },
          {
            key: 'subscriptionUsage',
            label: t('refundable.form.subscriptionUsage'),
            value: formatIntergerNumber(data.subscriptionUsage),
          },
          {
            key: 'suggest',
            label: t('refundable.form.suggest.title'),
            value: data.currentPlan ? (
              data.enable ? (
                <Tag color={'success'}>{t('refundable.form.suggest.allow')}</Tag>
              ) : (
                <Tag color={'error'}>{t('refundable.form.suggest.deny')}</Tag>
              )
            ) : (
              <Tag>{t('refundable.form.suggest.none')}</Tag>
            ),
          },
        ]}
        labelWidth={200}
      />
      <Descriptions
        bordered
        items={[
          {
            key: 'subscriptionHistory',
            label: t('refundable.form.subscriptionHistory'),
            value: formatIntergerNumber(data.plans.length),
          },
          {
            key: 'messages',
            label: t('refundable.form.messages'),
            value: formatIntergerNumber(data.messages),
          },
          {
            key: 'spendUsage',
            label: t('refundable.form.spendUsage'),
            value: '$' + formatPrice(data.spendUsage),
          },
          {
            key: 'fileUsage',
            label: t('refundable.form.fileUsage'),
            value: formatSize(data.fileUsage),
          },
          {
            key: 'embeddingUsage',
            label: t('refundable.form.embeddingUsage'),
            value: formatIntergerNumber(data.embeddingUsage),
          },
        ]}
        labelWidth={200}
      />
    </Flexbox>
  );
});

export default withNoSSR(ModalContent);
