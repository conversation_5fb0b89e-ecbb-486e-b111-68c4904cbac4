'use client';

import { useResponsive } from 'antd-style';
import { useQueryState } from 'nuqs';
import { memo } from 'react';
import { Flexbox } from 'react-layout-kit';

import RestrictionTabs from '@/app/[variants]/(main)/users/restriction/features/RestrictionTabs';
import { RestrictionType } from '@/types/restriction';

import Allowlist from './features/Allowlist';
import Blocklist from './features/Blocklist';

const Client = memo(() => {
  const [activeKey, setActiveKey] = useQueryState('restrictionType', {
    clearOnDefault: true,
    defaultValue: RestrictionType.Blocklist,
  });
  const { mobile } = useResponsive();

  return (
    <Flexbox gap={mobile ? 16 : 40}>
      <RestrictionTabs activeKey={activeKey as RestrictionType} setActiveKey={setActiveKey} />
      {activeKey === RestrictionType.Blocklist && <Blocklist />}
      {activeKey === RestrictionType.Allowlist && <Allowlist />}
    </Flexbox>
  );
});

export default Client;
