'use client';

import { ActionType, ProColumns, ProTableProps } from '@ant-design/pro-components';
import { ActionIcon, CopyButton } from '@lobehub/ui';
import { Popconfirm, Popover } from 'antd';
import { useTheme } from 'antd-style';
import { TrashIcon } from 'lucide-react';
import { parseAsInteger, useQueryState } from 'nuqs';
import { memo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import { useDateRangePresets } from '@/components/DateRangePicker';
import Table from '@/components/Table';
import UserInTable from '@/components/UserInTable';
import { trpcClient } from '@/libs/trpc/client';
import { SortQuery } from '@/types/query';
import { BlocklistItem } from '@/types/restriction';
import { withNoSSR } from '@/utils/withNoSSR';

import Actions from './Actions';

const request: ProTableProps<any, any>['request'] = async (params, sorts) =>
  trpcClient.restriction.getBlocklist.query({ params, sorts: sorts as SortQuery });

const Blocklist = memo(() => {
  const theme = useTheme();
  const { t } = useTranslation('user');
  const [loading, setLoading] = useState(false);
  const defaultPresets = useDateRangePresets() || [];
  const tableRef = useRef<ActionType>(null);
  const [currentPage, setCurrentPage] = useQueryState(
    'current',
    parseAsInteger.withDefault(1).withOptions({ clearOnDefault: true }),
  );
  const [pageSize, setPageSize] = useQueryState(
    'pageSize',
    parseAsInteger.withDefault(10).withOptions({ clearOnDefault: true }),
  );

  const handleDelete = async (id: string) => {
    setLoading(true);
    await trpcClient.restriction.deleteBlocklistIdentifier.query(id);
    tableRef.current?.reload();
    setLoading(false);
  };

  const columns: ProColumns<BlocklistItem>[] = [
    {
      dataIndex: 'identifier',
      hideInSearch: true,
      key: 'identifier',
      title: t('restriction.email'),
      width: 200,
    },
    {
      dataIndex: 'emailOrUsernameOrUserId',
      hideInTable: true,
      key: 'emailOrUsernameOrUserId',
      title: t('restriction.email'),
    },
    {
      dataIndex: 'range',
      fieldProps: {
        presets: defaultPresets,
      },
      hideInTable: true,
      key: 'range',
      title: t('restriction.createdAt'),
      valueType: 'dateRange',
    },
    {
      key: 'username',
      render: (_, entity) => {
        if (!entity.user) return '--';
        return (
          <UserInTable
            avatar={entity.user.avatar}
            email={entity.identifier}
            id={entity.user.id}
            username={entity.user.username}
          />
        );
      },
      title: t('table.columns.username'),
      width: 250,
    },
    {
      align: 'right',
      dataIndex: 'createdAt',
      hideInSearch: true,
      key: 'createdAt',
      sorter: true,
      title: t('restriction.createdAt'),
      valueType: 'dateTime',
      width: 200,
    },
    {
      align: 'right',
      dataIndex: 'id',
      fixed: 'right',
      hideInSearch: true,
      key: 'detail',
      render: (_, entity) => (
        <Flexbox gap={8} horizontal justify={'flex-end'}>
          <Popover
            arrow={false}
            content={
              <span
                style={{
                  fontFamily: theme.fontFamilyCode,
                }}
              >
                {entity.id}
              </span>
            }
            placement={'topRight'}
            trigger={['hover']}
          >
            <CopyButton active content={entity.id} size={'small'} />
          </Popover>
          <Popconfirm
            arrow={false}
            description={t('restriction.actions.deleteConfirmBlocklist')}
            okButtonProps={{
              danger: true,
              loading,
            }}
            onConfirm={() => handleDelete(entity.id)}
            placement={'topLeft'}
            title={t('restriction.actions.deleteBlocklist')}
          >
            <ActionIcon
              active
              icon={TrashIcon}
              size={'small'}
              style={{
                backgroundColor: theme.colorErrorBg,
                color: theme.colorError,
              }}
              title={t('restriction.actions.deleteBlocklist')}
            />
          </Popconfirm>
        </Flexbox>
      ),
      title: '',
      width: 56,
    },
  ];

  return (
    <>
      <Actions refetch={() => tableRef.current?.reload()} />
      <Table<BlocklistItem>
        actionRef={tableRef}
        columns={columns}
        headerTitle={t('restriction.blocklist')}
        key={'id'}
        pagination={{
          current: currentPage,
          onChange: (page) => {
            setCurrentPage(page);
          },
          onShowSizeChange: (current, size) => {
            setCurrentPage(current);
            setPageSize(size);
          },
          pageSize,
        }}
        request={request}
      />
    </>
  );
});

export default withNoSSR(Blocklist);
