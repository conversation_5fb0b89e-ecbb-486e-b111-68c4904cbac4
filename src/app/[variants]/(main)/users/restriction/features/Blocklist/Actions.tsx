'use client';

import { Button, Input, Modal, TextArea } from '@lobehub/ui';
import { Space } from 'antd';
import { useResponsive } from 'antd-style';
import { memo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import { trpcClient } from '@/libs/trpc/client';

import { ClerkButton } from '../ClerkButton';

const Actions = memo<{
  refetch: () => void;
}>(({ refetch }) => {
  const [loading, setLoading] = useState(false);
  // const [switchLoading, setSwitchLoading] = useState(false);
  // const {
  //   data,
  //   isLoading,
  //   refetch: refetchSetting,
  // } = trpcQuery.restriction.getRestrictionSettings.useQuery();
  // const { clerkUrl } = useServerConfigStore(serverConfigSelectors.config);
  const { t } = useTranslation('user');
  const [email, setEmail] = useState<string>();
  const [emails, setEmails] = useState<string>();
  const [open, setOpen] = useState(false);
  const { mobile } = useResponsive();

  const handleSubmit = async () => {
    if (!email) return;
    setLoading(true);
    await trpcClient.restriction.createBlocklistIdentifier.query(email);
    await refetch();
    setLoading(false);
  };

  const handleBatchAdd = async () => {
    if (!emails) return;
    setLoading(true);
    const emailList = emails
      .split('\n')
      .filter(Boolean)
      .filter((email) => email.includes('@'));
    await trpcClient.restriction.batchCreateBlocklistIdentifier.query(emailList);
    await refetch();
    setOpen(false);
    setLoading(false);
  };

  // const handleToggleBlocklist = async () => {
  //   setSwitchLoading(true);
  //   await trpcClient.restriction.updateRestrictionSettings.query({
  //     blocklist: !data?.blocklist,
  //   });
  //   await refetchSetting();
  //   setSwitchLoading(false);
  // };

  return (
    <>
      <Flexbox
        gap={8}
        horizontal
        justify={'space-between'}
        paddingInline={mobile ? 16 : 0}
        width={'100%'}
        wrap={'wrap'}
      >
        {/*<Flexbox gap={8} horizontal>*/}
        {/*  <div>{t('restriction.actions.enableBlocklist')}</div>*/}
        {/*  <Popconfirm*/}
        {/*    arrow={false}*/}
        {/*    description={*/}
        {/*      data?.blocklist*/}
        {/*        ? t('restriction.actions.disableConfirmBlocklist')*/}
        {/*        : t('restriction.actions.enableConfirmBlocklist')*/}
        {/*    }*/}
        {/*    okButtonProps={{*/}
        {/*      danger: true,*/}
        {/*      loading: switchLoading,*/}
        {/*    }}*/}
        {/*    onConfirm={handleToggleBlocklist}*/}
        {/*    title={*/}
        {/*      data?.blocklist*/}
        {/*        ? t('restriction.actions.disableBlocklist')*/}
        {/*        : t('restriction.actions.enableBlocklist')*/}
        {/*    }*/}
        {/*  >*/}
        {/*    <Switch loading={isLoading} value={data?.blocklist} />*/}
        {/*  </Popconfirm>*/}
        {/*</Flexbox>*/}
        <ClerkButton />
        <Flexbox gap={8} horizontal>
          <Button onClick={() => setOpen(true)}>{t('restriction.actions.batchAdd')}</Button>
          <Space.Compact>
            <Input
              addonBefore={'Email'}
              onChange={(e) => setEmail(e.target.value)}
              placeholder={'<EMAIL>'}
              value={email}
            />
            <Button loading={loading} onClick={handleSubmit} type="primary">
              {t('restriction.actions.add')}
            </Button>
          </Space.Compact>
        </Flexbox>
      </Flexbox>
      <Modal
        okButtonProps={{ loading }}
        okText={[
          t('restriction.actions.batchAdd'),
          emails ? `(${emails.split('\n').filter(Boolean).length})` : undefined,
        ].join(' ')}
        onCancel={() => setOpen(false)}
        onOk={handleBatchAdd}
        open={open}
        title={t('restriction.actions.batchAdd')}
      >
        <TextArea
          autoSize={{ maxRows: 50, minRows: 3 }}
          onChange={(e) => setEmails(e.target.value)}
          placeholder={['<EMAIL>', '<EMAIL>'].join('\n')}
          value={emails}
        />
      </Modal>
    </>
  );
});

export default Actions;
