import { <PERSON><PERSON><PERSON><PERSON> } from '@icons-pack/react-simple-icons';
import { Button } from '@lobehub/ui';
import { useResponsive } from 'antd-style';
import Link from 'next/link';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import urlJoin from 'url-join';

import { serverConfigSelectors, useServerConfigStore } from '@/store/serverConfig';

export const ClerkButton = memo(() => {
  const { clerkUrl } = useServerConfigStore(serverConfigSelectors.config);
  const { mobile } = useResponsive();
  const { t } = useTranslation('user');
  return (
    <Link
      href={urlJoin(clerkUrl, 'user-authentication/restrictions')}
      style={{
        width: mobile ? '100%' : undefined,
      }}
      target={'_blank'}
    >
      <Button block={mobile} icon={<SiClerk size={16} />}>
        {t('restriction.management')}
      </Button>
    </Link>
  );
});
