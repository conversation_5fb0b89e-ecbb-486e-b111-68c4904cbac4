'use client';

import { Tag } from '@lobehub/ui';
import { useResponsive } from 'antd-style';
import { parseAsInteger, useQueryState } from 'nuqs';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import { trpcQuery } from '@/libs/trpc/client';
import { RestrictionType } from '@/types/restriction';

import TabCard from '../../../features/TabCard';

const types = ['blocklist', 'allowlist'] as RestrictionType[];

interface RestrictionTabsProps {
  activeKey: RestrictionType;
  setActiveKey: (key: RestrictionType) => void;
}

const RestrictionTabs = memo<RestrictionTabsProps>(({ activeKey, setActiveKey }) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [_, setCurrentPage] = useQueryState(
    'current',
    parseAsInteger.withDefault(1).withOptions({ clearOnDefault: true }),
  );
  const { data, isLoading } = trpcQuery.restriction.getRestrictionTypes.useQuery();
  const { data: settings } = trpcQuery.restriction.getRestrictionSettings.useQuery();
  const { mobile } = useResponsive();
  const { t } = useTranslation('user');

  return (
    <Flexbox
      gap={12}
      horizontal
      style={{
        overflowX: 'auto',
        overflowY: 'hidden',
        paddingBottom: 4,
        paddingInline: mobile ? 16 : 0,
      }}
      width={'100%'}
    >
      {types.map((key) => (
        <TabCard
          active={activeKey === key}
          count={data?.[key]}
          extra={
            settings?.[key] ? (
              <Tag
                color={'success'}
                style={{
                  zoom: 0.8,
                }}
              >
                {t('restriction.status.on')}
              </Tag>
            ) : (
              <Tag
                style={{
                  zoom: 0.8,
                }}
              >
                {t('restriction.status.off')}
              </Tag>
            )
          }
          key={key}
          label={t(`restriction.types.${key}`)}
          loading={isLoading}
          onClick={() => {
            setActiveKey(key);
            setCurrentPage(1);
          }}
        />
      ))}
    </Flexbox>
  );
});

export default RestrictionTabs;
