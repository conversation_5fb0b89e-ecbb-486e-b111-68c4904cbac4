import { ProColumns, ProTableProps } from '@ant-design/pro-components';
import { ModelIcon, ProviderIcon } from '@lobehub/icons';
import { Markdown, Tag } from '@lobehub/ui';
import { Drawer, Typography } from 'antd';
import { createStyles } from 'antd-style';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import { useDateRangePresets } from '@/components/DateRangePicker';
import Table from '@/components/Table';
import { trpcClient } from '@/libs/trpc/client';
import { MessageItem } from '@/types/message';
import { SortQuery } from '@/types/query';

const { Paragraph } = Typography;

const useStyles = createStyles(({ token, css }) => ({
  tooltip: css`
    overflow: auto;
    display: block;

    width: 375px;
    height: 375px;
    border: 1px solid ${token.colorBorder};

    font-size: ${token.fontSizeSM}px;
    color: ${token.colorText} !important;

    background: ${token.colorBgElevated} !important;
  `,
}));

interface MessageModalProps {
  onClose: () => void;
  open: boolean;
  userId: string;
}

const MessageModal = memo<MessageModalProps>(({ open, onClose, userId }) => {
  const { t } = useTranslation('user');
  const defaultPresets = useDateRangePresets() || [];
  const { styles, theme } = useStyles();

  const request: ProTableProps<any, any>['request'] = async (params, sorts) =>
    trpcClient.user.getUserMessages.query({
      params: {
        userId,
        ...params,
      },
      sorts: sorts as SortQuery,
    });

  const columns: ProColumns<MessageItem>[] = [
    {
      dataIndex: 'range',
      fieldProps: {
        presets: defaultPresets,
      },
      hideInTable: true,
      key: 'range',
      title: t('messages.table.columns.createdAt'),
      valueType: 'dateRange',
    },
    {
      dataIndex: 'role',
      render: (_, entity) => {
        const colors = {
          assistant: 'gold',
          system: 'error',
          tool: 'geekblue',
          user: 'success',
        };

        return <Tag color={colors[entity.role]}>{entity.role}</Tag>;
      },
      title: t('messages.table.columns.role'),
      valueEnum: {
        assistant: { text: 'assistant' },
        system: { text: 'system' },
        tool: { text: 'tool' },
        user: { text: 'user' },
      },
      width: 100,
    },
    {
      dataIndex: 'content',
      key: 'content',
      render: (_, entity) => (
        <Paragraph
          ellipsis={{
            rows: 2,
            tooltip: {
              arrow: false,
              classNames: { body: styles.tooltip },
              title: <Markdown variant={'chat'}>{entity.content as string}</Markdown>,
            },
          }}
          style={{
            margin: 0,
          }}
        >
          {entity.content}
        </Paragraph>
      ),
      title: t('messages.table.columns.content'),
      width: 600,
    },
    {
      dataIndex: 'model',
      key: 'model',
      render: (_, entity) =>
        entity.model && (
          <Flexbox align={'center'} gap={4} horizontal style={{ textWrap: 'nowrap' }}>
            <ModelIcon model={entity.model} size={16} /> {entity.model}
          </Flexbox>
        ),
      title: t('messages.table.columns.modal'),
      width: 200,
    },
    {
      dataIndex: 'provider',
      key: 'provider',
      render: (_, entity) =>
        entity.model && (
          <Flexbox align={'center'} gap={4} horizontal style={{ textWrap: 'nowrap' }}>
            <ProviderIcon provider={entity.provider as any} size={16} /> {entity.provider}
          </Flexbox>
        ),
      title: t('messages.table.columns.provider'),
      width: 150,
    },
    {
      align: 'right',
      dataIndex: 'createdAt',
      hideInSearch: true,
      key: 'createdAt',
      sorter: true,
      title: t('messages.table.columns.createdAt'),
      valueType: 'dateTime',
      width: 200,
    },
  ];

  return (
    <Drawer
      height={'100%'}
      onClose={onClose}
      open={open}
      placement={'bottom'}
      styles={{
        body: {
          background: theme.colorBgContainer,
        },
        header: {
          background: theme.colorBgContainer,
        },
      }}
      title={t('drawer.message.title')}
    >
      <Table<MessageItem>
        columns={columns}
        headerTitle={t('table.title')}
        key={'id'}
        request={request}
      />
    </Drawer>
  );
});
export default MessageModal;
