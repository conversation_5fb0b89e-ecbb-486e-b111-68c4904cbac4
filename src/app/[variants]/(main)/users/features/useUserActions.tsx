import { App } from 'antd';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { trpcClient } from '@/libs/trpc/client';

export const useUserActions = ({ onFinish }: { onFinish: () => void }) => {
  const { t } = useTranslation('user');
  const [loading, setLoading] = useState(false);
  const { modal } = App.useApp();

  const handleLock = async (userId: string, current: boolean) => {
    setLoading(true);
    await trpcClient.user.lockUser.mutate({
      current,
      userId,
    });
    onFinish();
    setLoading(false);
  };

  const handleBan = async (userId: string, current: boolean) => {
    setLoading(true);
    await trpcClient.user.banUser.mutate({
      current,
      userId,
    });
    onFinish();
    setLoading(false);
  };

  const handleDelete = async (userId: string) => {
    setLoading(true);
    await trpcClient.user.deleteUser.mutate({
      userId,
    });
    onFinish();
    setLoading(false);
  };

  const lockUser = (userId: string, current?: boolean) =>
    modal.confirm({
      content: current ? t('actions.unlockConfirm') : t('actions.lockConfirm'),
      okButtonProps: {
        danger: true,
        type: 'primary',
      },
      onOk: async () => {
        return handleLock(userId, Boolean(current));
      },
      title: current ? t('actions.unlock') : t('actions.lock'),
    });

  const banUser = (userId: string, current?: boolean) =>
    modal.confirm({
      content: current ? t('actions.unbanConfirm') : t('actions.banConfirm'),
      okButtonProps: {
        danger: true,
        type: 'primary',
      },
      onOk: async () => {
        return handleBan(userId, Boolean(current));
      },
      title: current ? t('actions.unban') : t('actions.ban'),
    });

  const deleteUser = (userId: string) =>
    modal.confirm({
      content: t('actions.deleteConfirm'),
      okButtonProps: {
        danger: true,
        type: 'primary',
      },
      onOk: async () => {
        return handleDelete(userId);
      },
      title: t('actions.delete'),
    });

  return {
    banUser,
    deleteUser,
    isLoaidng: loading,
    lockUser,
  };
};
