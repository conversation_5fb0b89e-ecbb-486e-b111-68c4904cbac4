import { Spin } from 'antd';
import { createStyles } from 'antd-style';
import { ReactNode, memo } from 'react';
import { Flexbox } from 'react-layout-kit';

import { formatIntergerNumber } from '@/utils/format';

const useStyles = createStyles(({ css, token }) => ({
  active: css`
    color: ${token.colorText};
    background: ${token.colorBgElevated};
    box-shadow: 0 0 0 2px ${token.colorFill} inset;
  `,
  container: css`
    cursor: pointer;

    min-width: 160px;
    border-radius: ${token.borderRadiusLG}px;

    color: ${token.colorTextSecondary};

    box-shadow: 0 0 0 1px ${token.colorFillTertiary} inset;

    transition: box-shadow 0.2s ${token.motionEaseInOut};

    &:hover {
      box-shadow: 0 0 0 1px ${token.colorFill} inset;
    }
  `,
}));

interface TabCardProps {
  active: boolean;
  count?: number;
  extra?: ReactNode;
  label: string;
  loading?: boolean;
  onClick?: () => void;
}

const TabCard = memo<TabCardProps>(({ extra, loading, label, active, count, onClick }) => {
  const { styles, cx } = useStyles();

  return (
    <Flexbox
      className={cx(styles.container, active && styles.active)}
      flex={1}
      gap={4}
      onClick={onClick}
      paddingBlock={12}
      paddingInline={16}
      width={'100%'}
    >
      <Flexbox align={'center'} gap={8} horizontal>
        <span>{label}</span>
        {extra}
      </Flexbox>
      <div
        style={{
          fontSize: 16,
          fontWeight: 'bold',
        }}
      >
        {loading ? <Spin percent={'auto'} size={'small'} /> : formatIntergerNumber(count)}
      </div>
    </Flexbox>
  );
});

export default TabCard;
