import { Button, Progress, Row } from 'antd';
import { createGlobalStyle, createStyles, useTheme } from 'antd-style';
import { useTranslation } from 'react-i18next';

//todo  红色样式无效需要优化 :nth-last-child(-n+2)
const Global = createGlobalStyle`
    .ant-progress {
        transform: scaleX(-1);

        .ant-progress-steps-item-active {
            &:last-child {
                background-color: red !important;
            }
        }
        .ant-progress-steps-item {
            margin-inline-end: 6px;
            border-radius: 20px;

            &:last-child {
                margin: 0;
            }
        }
    }
`;

const ChannelProgress = ({
  percent,
  message,
  disabled = false,
}: {
  disabled?: boolean;
  message?: string;
  percent: number;
}) => {
  const theme = useTheme();
  const { t } = useTranslation('channel');
  const useStyles = createStyles(({ css }) => {
    return {
      channelBtn: css`
        height: 24px;
        padding-block: 0;
        padding-inline: 8px;

        font-size: 14px;
        line-height: 24px;
      `,
    };
  });
  const { styles } = useStyles();
  return (
    <>
      <Row
        align={'middle'}
        style={{
          background: theme.colorBgContainer,
          borderRadius: 6,
          height: 48,
          padding: '12px 16px',
        }}
        wrap={false}
      >
        <Global />
        <Progress
          percent={percent}
          showInfo={false}
          size={[8, 24]}
          steps={38}
          strokeColor={theme.colorSuccess}
        />
        {disabled ? (
          <Button
            className={styles.channelBtn}
            style={{ background: theme.colorTextDisabled, marginLeft: 8 }}
            type="primary"
          >
            {t('detail.disable')}
          </Button>
        ) : (
          <Button
            className={styles.channelBtn}
            style={{ background: theme.colorSuccess, marginLeft: 8 }}
            type="primary"
          >
            {t('detail.enable')}
          </Button>
        )}
      </Row>
      {message && (
        <Row style={{ color: theme.colorTextDescription, paddingLeft: 16 }}>{message}</Row>
      )}
    </>
  );
};

export default ChannelProgress;
