'use client';

import { Icon } from '@lobehub/ui';
import {
  Button,
  Collapse,
  CollapseProps,
  Descriptions,
  DescriptionsProps,
  Dropdown,
  Empty,
  MenuProps,
  Modal,
  Row,
  Skeleton,
  Space,
  Typography,
} from 'antd';
import { createStyles, useTheme } from 'antd-style';
import { Ban, <PERSON>Pie, CircleCheckBig, Ellipsis, Info, Pencil, Trash } from 'lucide-react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { trpcQuery } from '@/libs/trpc/client';
import { AiProviderWithModels } from '@/types/adminInfra';

import ChannelProgress from './ChannelProgress';
import ModuleList from './ModuleList';
import StatisticDrawer from './StatisticDrawer';

const ChannelDetail = ({
  activeChannel,
  handleEdit,
  handleDelete,
  handleToggle,
}: {
  activeChannel?: AiProviderWithModels;
  handleDelete: () => void;
  handleEdit: () => void;
  handleToggle: (enabled: boolean) => void;
}) => {
  const { Text, Paragraph } = Typography;
  const theme = useTheme();
  const { t } = useTranslation('channel');
  const useStyles = createStyles(({ css }) => {
    return {
      channelBtn: css`
        height: 28px;
        padding-block: 0;
        padding-inline: 8px;

        font-size: 14px;
        line-height: 28px;
      `,
    };
  });
  const { styles } = useStyles();

  const [statOpen, setStatOpen] = useState(false);

  const {
    data: providerInfo,
    isLoading: isProviderLoading,
    refetch,
  } = trpcQuery.adminInfra.getInfraProviderById.useQuery(
    activeChannel && activeChannel.id ? activeChannel.id : '',
    {
      enabled: !!activeChannel?.id,
    },
  );

  const detail = providerInfo?.data;
  const items: MenuProps['items'] = [
    {
      // @ts-ignore
      hidden: !!detail?.enabled,

      key: 'open',
      label: (
        <div>
          <Icon icon={CircleCheckBig} size={{ size: 14 }} style={{ marginRight: 8 }} />
          解除禁用
        </div>
      ),
      onClick: () => {
        Modal.confirm({
          content: t('detail.disabledConfirmContent'),
          icon: (
            <Icon icon={Info} size={{ size: 18 }} style={{ color: theme.gray, marginTop: 2 }} />
          ),
          okText: t('detail.confirm'),
          onOk: async () => {
            await handleToggle(true);
            refetch();
          },
          width: 300,
        });
      },
    },
    {
      // @ts-ignore
      hidden: !detail?.enabled,

      key: 'close',
      label: (
        <div style={{ color: theme.colorError }}>
          <Icon icon={Ban} size={{ size: 14 }} style={{ marginRight: 8 }} />
          禁用
        </div>
      ),
      onClick: () => {
        Modal.confirm({
          content: t('detail.disabledConfirmContent'),
          icon: (
            <Icon icon={Info} size={{ size: 18 }} style={{ color: theme.gray, marginTop: 2 }} />
          ),
          okButtonProps: {
            danger: true,
          },
          okText: t('detail.disable'),
          onOk: async () => {
            await handleToggle(false);
            refetch();
          },
          width: 300,
        });
      },
    },
    {
      key: 'delete',
      label: (
        <div style={{ color: theme.colorError }}>
          <Icon icon={Trash} size={{ size: 14 }} style={{ marginRight: 8 }} />
          删除
        </div>
      ),
      onClick: () => {
        Modal.confirm({
          content: t('detail.deleteConfirmContent'),
          icon: (
            <Icon icon={Info} size={{ size: 18 }} style={{ color: theme.gray, marginTop: 2 }} />
          ),
          okButtonProps: {
            danger: true,
          },
          okText: t('detail.delete'),
          onOk: handleDelete,
          width: 300,
        });
      },
    },
  ];
  // 获取API配置信息
  const getKeyVaults = () => {
    let res: DescriptionsProps['items'] = [];
    Object.keys(detail?.keyVaults || {}).forEach((key) => {
      res.push({
        children: (
          <Paragraph
            ellipsis={{
              rows: 1,
              tooltip: (
                <p style={{ width: '100%', wordBreak: 'break-all' }}>
                  {(detail?.keyVaults &&
                    (detail?.keyVaults as Record<string, string | undefined>)[key]) ||
                    ''}
                </p>
              ),
            }}
            style={{ margin: 0 }}
          >
            {(detail?.keyVaults && (detail.keyVaults as Record<string, string | undefined>)[key]) ||
              ''}
          </Paragraph>
        ),
        key: key,
        label: key,
      });
    });
    return res;
  };

  return (
    <div
      style={{
        backgroundColor: theme.grayBg,
        borderBottomRightRadius: 8,
        borderTopRightRadius: 8,
        display: 'flex',
        minHeight: '100%',
        padding: 24,
      }}
    >
      {/* 基本信息 */}
      {activeChannel ? (
        <div style={{ minHeight: '100%', width: '100%' }}>
          <Skeleton active loading={isProviderLoading}>
            <Row align="middle" justify="space-between" style={{ marginBottom: 8 }} wrap={false}>
              <Text ellipsis style={{ fontSize: 18, fontWeight: 500 }}>
                {detail?.name}
              </Text>
              <Space>
                <Button className={styles.channelBtn} onClick={() => setStatOpen(true)}>
                  <Icon icon={ChartPie} size={{ size: 14 }} />
                  {t('detail.statistic')}
                </Button>
                <Button className={styles.channelBtn} onClick={handleEdit}>
                  <Icon icon={Pencil} size={{ size: 14 }} />
                  {t('detail.edit')}
                </Button>
                <Dropdown menu={{ items }} placement="bottomLeft">
                  <Button className={styles.channelBtn}>
                    <Icon icon={Ellipsis} size={{ size: 14 }} />
                  </Button>
                </Dropdown>
              </Space>
            </Row>
            <Descriptions
              column={1}
              items={
                [
                  {
                    children: <Text>{detail?.settings?.sdkType}</Text>,
                    key: 'channelType',
                    label: t('detail.clouums.channelType'),
                  },
                  {
                    children: <Text>{detail?.description || '-'}</Text>,
                    key: 'channelDescription',
                    label: t('detail.clouums.channelDescription'),
                  },
                ] as DescriptionsProps['items']
              }
              size={'small'}
              style={{ marginBottom: 8 }}
            />
            <ChannelProgress disabled={!detail?.enabled} percent={30} />
            <Collapse
              bordered={false}
              items={
                [
                  {
                    children: <Descriptions column={2} items={getKeyVaults()} size={'small'} />,
                    key: '1',
                    label: (
                      <Text style={{ color: theme.colorText, fontWeight: 500 }}>
                        {t('detail.apiConfig')}
                      </Text>
                    ),
                  },
                ] as CollapseProps['items']
              }
              style={{ background: theme.colorBgContainer, margin: '16px 0' }}
            />
            <ModuleList detail={detail ?? undefined} />
          </Skeleton>
        </div>
      ) : (
        <div
          style={{
            alignItems: 'center',
            display: 'flex',
            justifyContent: 'center',
            width: '100%',
          }}
        >
          <Empty description={t('detail.empty')} image={Empty.PRESENTED_IMAGE_SIMPLE} />
        </div>
      )}
      <StatisticDrawer
        detail={providerInfo?.data ?? undefined}
        onClose={() => setStatOpen(false)}
        open={statOpen}
      />
    </div>
  );
};

export default ChannelDetail;
