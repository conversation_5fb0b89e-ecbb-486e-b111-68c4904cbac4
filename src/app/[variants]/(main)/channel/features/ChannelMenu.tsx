'use client';

import { Icon } from '@lobehub/ui';
import { Input, Row, Skeleton, Typography } from 'antd';
import { createGlobalStyle, createStyles, useTheme } from 'antd-style';
import { Bookmark, Codesandbox, Search } from 'lucide-react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { AiProviderWithModels } from '@/types/adminInfra';

const SearchGlobal = createGlobalStyle`
  .ant-input-group-addon{
    display: none!important;
  }
`;

const ChannelMenu = ({
  activeChannel,
  setActiveChannel,
  data,
  isLoading,
}: {
  activeChannel?: AiProviderWithModels;
  data: AiProviderWithModels[];
  isLoading: boolean;
  setActiveChannel: (channel: AiProviderWithModels) => void;
}) => {
  const { t } = useTranslation('channel');
  const { Text } = Typography;
  const theme = useTheme();
  const useStyles = createStyles(({ css }) => {
    return {
      activeState: css`
        cursor: pointer;

        position: relative;

        width: 50px;
        height: 24px;
        padding-block: 0;
        padding-inline: 16px 6px;
        border-radius: 4px;

        font-size: 14px;
        line-height: 24px;
        color: ${theme.colorSuccess};

        background: ${theme.colorSuccessBg};

        &::after {
          content: '';

          position: absolute;
          inset-block-start: 9px;
          inset-inline-start: 5px;

          width: 6px;
          height: 6px;
          border-radius: 100%;

          background: ${theme.colorSuccess};
        }
      `,
      channelActiveItem: css`
        height: 86px;
        padding-block: 16px;
        padding-inline: 12px;
        border-radius: 6px;
        cursor: pointer;
        color: ${theme.colorText};
        background: ${theme.colorBgContainer};

        .ant-typography {
          color: #000;
        }
      `,
      channelItem: css`
        height: 86px;
        padding-block: 16px;
        padding-inline: 12px;
        border-radius: 6px;
        cursor: pointer;
      `,
      defaultState: css`
        cursor: pointer;

        position: relative;

        width: 64px;
        height: 24px;
        padding-block: 0;
        padding-inline: 16px 6px;
        border-radius: 4px;

        font-size: 14px;
        line-height: 24px;
        color: ${theme.colorText};

        background: ${theme.colorTextPlaceholder};

        &::after {
          content: '';

          position: absolute;
          inset-block-start: 9px;
          inset-inline-start: 5px;

          width: 6px;
          height: 6px;
          border-radius: 100%;

          background: ${theme.colorText};
        }
      `,
      errorState: css`
        cursor: pointer;

        position: relative;

        width: 50px;
        height: 24px;
        padding-block: 0;
        padding-inline: 16px 6px;
        border-radius: 4px;

        font-size: 14px;
        line-height: 24px;
        color: ${theme.colorError};

        background: ${theme.colorErrorBg};

        &::after {
          content: '';

          position: absolute;
          inset-block-start: 9px;
          inset-inline-start: 5px;

          width: 6px;
          height: 6px;
          border-radius: 100%;

          background: ${theme.colorError};
        }
      `,
    };
  });
  const { styles } = useStyles();
  const [searchValue, setSearchValue] = useState('');

  return (
    <div
      style={{
        backgroundColor: theme.gray2A,
        borderBottomLeftRadius: 8,
        borderTopLeftRadius: 8,
        height: '100%',
        padding: '16px 12px',
      }}
    >
      <Skeleton active loading={isLoading}>
        <SearchGlobal />
        <Input.Search
          enterButton={false}
          onSearch={(value) => {
            setSearchValue(value);
          }}
          placeholder={t('menu.searchPlaceholder')}
          prefix={
            <Icon
              icon={Search}
              size={{ size: 16 }}
              style={{ color: theme.colorTextPlaceholder, marginRight: 8 }}
            />
          }
          style={{ width: '100%' }}
        />
        <div style={{ minHeight: 'calc(100% - 24px)', overflowY: 'auto', padding: '12px 0' }}>
          {data
            ?.filter((item: AiProviderWithModels) => item?.name?.includes(searchValue))
            ?.map((item: AiProviderWithModels) => (
              <div
                className={
                  activeChannel?.id === item.id ? styles.channelActiveItem : styles.channelItem
                }
                key={`${item.id}-channelMenu`}
                onClick={() => {
                  setActiveChannel(item);
                }}
              >
                <Row
                  align="middle"
                  justify={'space-between'}
                  style={{ cursor: 'pointer' }}
                  wrap={false}
                >
                  <Text
                    style={{
                      color: theme.colorText,
                      fontSize: 16,
                      fontWeight: 500,
                      lineHeight: '24px',
                    }}
                  >
                    {item.name}
                  </Text>
                  {item.enabled ? (
                    <div className={styles.activeState}>{t('menu.normal')}</div>
                  ) : (
                    <div className={styles.defaultState}>{t('menu.disabled')}</div>
                  )}
                </Row>
                <Row
                  align={'middle'}
                  style={{
                    color: theme.colorTextSecondary,
                    fontSize: 14,
                    lineHeight: '22px',
                    marginTop: 8,
                  }}
                >
                  <Icon icon={Codesandbox} size={{ size: 12 }} style={{ marginRight: 4 }} />
                  {/* @ts-ignore  */}
                  {t('menu.configured')}&nbsp;&nbsp;{item?.associatedModelsCount ?? ''}
                  <Icon
                    icon={Bookmark}
                    size={{ size: 12 }}
                    style={{ marginLeft: 12, marginRight: 4 }}
                  />
                  {item?.settings?.sdkType}
                </Row>
              </div>
            ))}
        </div>
      </Skeleton>
    </div>
  );
};

export default ChannelMenu;
