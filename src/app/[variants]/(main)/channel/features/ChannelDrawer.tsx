'use client';

import { <PERSON><PERSON>, Col, Drawer, Empty, Form, Input, Row, Select, Space, Switch, message } from 'antd';
import { createStyles, useTheme } from 'antd-style';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { INFRA_PROVIDER_TYPES } from '@/const/aiInfra';
import { trpcClient } from '@/libs/trpc/client';
import { AiProviderWithModels } from '@/types/adminInfra';

interface props {
  data?: AiProviderWithModels;
  onOk: () => void;
  open: boolean;
  setOpen: (open: boolean) => void;
}

const ChannelDrawer = ({ open, setOpen, data, onOk }: props) => {
  const theme = useTheme();
  const { t } = useTranslation('channel');
  const useStyles = createStyles(({ css }) => {
    return {
      formHead: css`
        position: relative;

        height: 22px;
        margin-block-end: 16px;
        padding-inline-start: 10px;

        font-size: 14px;
        font-weight: 500;
        line-height: 22px;
        color: ${theme.colorTextHeading};

        &::before {
          content: '';

          position: absolute;
          inset-block-start: 6px;
          inset-inline-start: 0;

          width: 2px;
          height: 10px;
          border-radius: 20px;

          background: ${theme.colorTextHeading};
        }
      `,
    };
  });
  const { styles } = useStyles();
  const isNew = !data;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const extendsItems = {
    azure_ai: {
      extends: [
        {
          field: 'apiKey',
          label: 'API Key',
          type: 'password',
        },
        {
          field: 'baseURL',
          label: 'Azure API Address',
          type: 'input',
        },
        {
          field: 'version',
          // todo 未找到自定义
          label: 'Azure API Version',

          span: 24,
          type: 'input',
        },
      ],
      label: 'Azure OpenAI',
      value: 'azure_ai',
    },
    bedrock: {
      extends: [
        {
          field: 'accessKeyId',
          label: 'Aws Access Key Id',
          type: 'password',
        },
        {
          field: 'secretAccessKey',
          label: 'Aws Secret Access Key',
          type: 'password',
        },
        {
          field: 'sessionToken',
          label: 'Aws Session Token',
          type: 'password',
        },
        {
          field: 'region',
          label: 'Aws Region',
          options: [
            { label: 'us-east-1', value: 'us-east-1' },
            { label: 'us-west-2', value: 'us-west-2' },
            { label: 'ap-southeast-1', value: 'ap-southeast-1' },
            { label: 'eu-central-1', value: 'eu-central-1' },
          ],
          type: 'select',
        },
      ],
      label: 'Bedrock',
      value: 'bedrock',
    },
    claude: {
      extends: [
        {
          field: 'apiKey',
          label: 'API Key',
          type: 'password',
        },
        {
          field: 'baseURL',
          label: 'API Proxy URL',
          type: 'input',
        },
      ],
      label: 'Claude',
      value: 'claude',
    },
    gemini: {
      extends: [
        {
          field: 'apiKey',
          label: 'API Key',
          type: 'password',
        },
        {
          field: 'baseURL',
          label: 'API Proxy URL',
          type: 'input',
        },
      ],
      label: 'Gemini',
      value: 'gemini',
    },
    openai: {
      extends: [
        {
          field: 'apiKey',
          label: 'API Key',
          type: 'password',
        },
        {
          field: 'baseURL',
          label: 'API Proxy URL',
          type: 'input',
        },
        {
          field: 'enableResponseApi',
          label: 'Use Response API Specification',
          span: 24,
          type: 'switch',
        },
      ],
      label: 'OpenAI',
      value: 'openai',
    },
    vertex_ai: {
      extends: [
        {
          field: 'apiKey',
          label: 'Vertex API Key',
          span: 24,
          type: 'password',
        },
      ],
      label: 'Vertex',
      value: 'vertex_ai',
    },
  };
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (isNew) {
        // 创建模式
        const createData = {
          ...values,
          source: 'custom' as const,
        };

        await trpcClient.adminInfra.createInfraProvider.mutate(createData);
        message.success(t('addChannel.addSuccess'));
      } else {
        // 编辑模式
        if (!data) return;

        await trpcClient.adminInfra.updateInfraProvider.mutate({
          data: values,
          providerId: data.id,
        });

        message.success(t('addChannel.editSuccess'));
      }

      form.resetFields();
      onOk();
    } catch (error: any) {
      message.error(
        `${isNew ? t('addChannel.addFailed') : t('addChannel.editFailed')}: ${error.message}`,
      );
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (open) {
      form.resetFields();
      if (data) {
        form.setFieldsValue(data);
      }
    }
  }, [open]);

  return (
    <Drawer
      footer={
        <Space style={{ justifyContent: 'end', marginTop: 24, width: '100%' }}>
          <Button onClick={() => setOpen(false)}>{t('addChannel.cancel')}</Button>
          <Button loading={loading} onClick={handleSubmit} type={'primary'}>
            {t('addChannel.confirm')}
          </Button>
        </Space>
      }
      onClose={() => setOpen(false)}
      open={open}
      title={isNew ? t('addChannel.add') : t('addChannel.edit')}
      width={860}
    >
      <Form form={form} layout="vertical">
        <Row className={styles.formHead}>{t('addChannel.basicInfo')}</Row>
        <Form.Item hidden name="litellmCredential">
          <Input />
        </Form.Item>
        <Form.Item
          label={t('addChannel.channelName')}
          name="name"
          rules={[{ message: t('addChannel.channelNameRequired'), required: true }]}
        >
          <Input placeholder={t('addChannel.channelNamePlaceholder')} style={{ width: 440 }} />
        </Form.Item>
        <Form.Item
          label={t('addChannel.channelId')}
          name="id"
          rules={[{ message: t('addChannel.channelIdRequired'), required: true }]}
        >
          <Input
            disabled={!isNew}
            placeholder={t('addChannel.channelIdPlaceholder')}
            style={{ width: 440 }}
          />
        </Form.Item>

        <Form.Item
          label={t('addChannel.channelType')}
          name={['settings', 'sdkType']}
          rules={[{ message: t('addChannel.channelTypeRequired'), required: true }]}
        >
          <Select
            options={INFRA_PROVIDER_TYPES}
            placeholder={t('addChannel.channelTypePlaceholder')}
            style={{ width: 216 }}
          />
        </Form.Item>
        <Form.Item label={t('addChannel.channelDescription')} name="description">
          <Input.TextArea
            placeholder={t('addChannel.channelDescriptionPlaceholder')}
            rows={2}
            style={{ width: 552 }}
          />
        </Form.Item>
        <Row className={styles.formHead} style={{ marginTop: 16 }}>
          {t('addChannel.apiConfig')}
        </Row>

        <Form.Item dependencies={[['settings', 'sdkType']]} noStyle>
          {() => {
            const sdkType = form.getFieldValue(['settings', 'sdkType']);
            if (!sdkType) {
              return (
                <Row align="middle" justify="center" style={{ width: '100%' }}>
                  <Empty
                    description={t('addChannel.channelTypePlaceholder')}
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                  />
                </Row>
              );
            }
            const extendsField = extendsItems[sdkType as keyof typeof extendsItems]?.extends || [];
            return (
              <Row gutter={[24, 0]} style={{ width: 704 }}>
                {extendsField.map((item: any) => (
                  <Col key={item.field} span={item.span || 12}>
                    <Form.Item label={item.label} name={['keyVaults', item.field]}>
                      {item.type === 'input' && (
                        <Input placeholder={item.placeholder} style={{ width: 328 }} />
                      )}
                      {item.type === 'password' && (
                        <Input.Password
                          autoComplete=""
                          placeholder={item.placeholder}
                          style={{ width: 328 }}
                        />
                      )}
                      {item.type === 'select' && (
                        <Select
                          options={item.options}
                          placeholder={item.placeholder}
                          style={{ width: 328 }}
                        />
                      )}
                      {item.type === 'switch' && <Switch />}
                    </Form.Item>
                  </Col>
                ))}
              </Row>
            );
          }}
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default ChannelDrawer;
