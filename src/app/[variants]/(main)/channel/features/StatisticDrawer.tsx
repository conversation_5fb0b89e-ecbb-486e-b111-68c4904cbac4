'use client';

import { AreaChart } from '@lobehub/charts';
import { <PERSON><PERSON>, Card, Col, DatePicker, Drawer, Row, Typography } from 'antd';
import { createStyles, useTheme } from 'antd-style';
import dayjs from 'dayjs';
import { memo, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { trpcClient } from '@/libs/trpc/client';
import { AiProviderWithModels } from '@/types/adminInfra';

import ChannelProgress from './ChannelProgress';

// 格式化 Token 数量
const formatTokens = (tokens: number) => {
  if (!tokens) return '0';
  if (tokens >= 1_000_000) {
    return `${(tokens / 1_000_000).toFixed(1)} M`;
  } else {
    return `${(tokens / 1000).toFixed(1)} K`;
  }
};

const StatisticDrawer = memo<{ detail?: AiProviderWithModels; onClose: () => void; open: boolean }>(
  ({ open, onClose, detail }) => {
    const { Text } = Typography;
    const theme = useTheme();
    const { t } = useTranslation('channel');
    const useStyles = createStyles(({ css }) => {
      return {
        formHead: css`
          position: relative;

          height: 22px;
          margin-block-end: 16px;
          padding-inline-start: 10px;

          font-size: 14px;
          font-weight: 500;
          line-height: 22px;
          color: ${theme.colorTextHeading};

          &::before {
            content: '';

            position: absolute;
            inset-block-start: 6px;
            inset-inline-start: 0;

            width: 2px;
            height: 10px;
            border-radius: 20px;

            background: ${theme.colorTextHeading};
          }
        `,
      };
    });
    const { styles } = useStyles();
    const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
      dayjs().subtract(1, 'day').startOf('day'),
      dayjs().subtract(1, 'day').endOf('day'),
    ]);
    const [loading, setLoading] = useState(false);
    const [usageData, setUsageData] = useState<any>(null);
    const responseTimeData = [
      { date: '2024-06-01', ms: 120 },
      { date: '2024-06-02', ms: 110 },
      { date: '2024-06-03', ms: 150 },
      { date: '2024-06-04', ms: 90 },
      { date: '2024-06-05', ms: 130 },
      { date: '2024-06-06', ms: 100 },
      { date: '2024-06-07', ms: 140 },
    ];

    // 获取使用统计数据
    const fetchUsageData = async () => {
      if (!detail) return;

      setLoading(true);
      try {
        const result = await trpcClient.adminInfra.getProviderUsageStats.query({
          providerId: detail?.id,
          range: [dateRange[0].toISOString(), dateRange[1].toISOString()],
        });

        if (result.success && result.data) {
          setUsageData(result.data);
        }
      } catch (error) {
        console.error(t('statisic.usageDataFailed'), error);
      } finally {
        setLoading(false);
      }
    };
    useEffect(() => {
      if (open && detail?.id) {
        fetchUsageData();
      }
    }, [open, detail, dateRange]);

    return (
      <Drawer onClose={onClose} open={open} title={t('statisic.performanceIndicator')} width={860}>
        <Row className={styles.formHead} style={{ marginBottom: 12 }}>
          {t('statisic.performanceIndicator')}
        </Row>
        <Card style={{ marginBottom: 12, paddingLeft: 0 }} styles={{ body: { padding: 0 } }}>
          <ChannelProgress
            disabled={!detail?.enabled}
            message={t('statisic.checkEvery60s')}
            percent={30}
          />
        </Card>

        <Card style={{ marginBottom: 12, padding: 0 }} styles={{ body: { padding: 0 } }}>
          <Row style={{ width: '100%' }}>
            <Col
              span={6}
              style={{
                borderRight: `1px solid ${theme.colorBorderSecondary}`,
                padding: '12px 16px',
              }}
            >
              <Text style={{ display: 'block', fontSize: 14, lineHeight: '22px' }}>
                {t('statisic.averageResponseTime')}
              </Text>
              <Text style={{ fontSize: 16, fontWeight: 500, lineHeight: '24px', marginTop: 4 }}>
                100ms
              </Text>
            </Col>
            <Col
              span={6}
              style={{
                borderRight: `1px solid ${theme.colorBorderSecondary}`,
                padding: '12px 16px',
              }}
            >
              <Text style={{ display: 'block', fontSize: 14, lineHeight: '22px' }}>
                {t('statisic.onlineRate')}
              </Text>
              <Text style={{ fontSize: 16, fontWeight: 500, lineHeight: '24px', marginTop: 4 }}>
                100%
              </Text>
            </Col>
            <Col
              span={6}
              style={{
                borderRight: `1px solid ${theme.colorBorderSecondary}`,
                padding: '12px 16px',
              }}
            >
              <Text style={{ display: 'block', fontSize: 14, lineHeight: '22px' }}>
                {t('statisic.onlineRate30day')}
              </Text>
              <Text style={{ fontSize: 16, fontWeight: 500, lineHeight: '24px', marginTop: 4 }}>
                100%
              </Text>
            </Col>
            <Col span={6} style={{ padding: '12px 16px' }}>
              <Text style={{ display: 'block', fontSize: 14, lineHeight: '22px' }}>
                {t('statisic.certificateValidity')}
              </Text>
              <Text style={{ fontSize: 16, fontWeight: 500, lineHeight: '24px', marginTop: 4 }}>
                233 days
              </Text>
            </Col>
          </Row>
        </Card>
        <Card style={{ marginBottom: 12, padding: 0 }} styles={{ body: { padding: '12px 16px' } }}>
          <Text
            style={{
              display: 'block',
              fontSize: 14,
              fontWeight: 500,
              lineHeight: '22px',
              marginBottom: 12,
            }}
          >
            {t('statisic.responseTimeDistribution')}
          </Text>
          <AreaChart
            categories={['ms']}
            data={responseTimeData}
            height={180}
            index="date"
            showGradient={true}
            showLegend={false}
            style={{ padding: 0 }}
            title={t('statisic.responseTimeDistribution')}
            valueFormatter={(v) => `${v} ms`}
            xAxisLabelFormatter={(v) => v as any}
            // colors={[theme.colorText]}
          />
        </Card>
        <Row className={styles.formHead} style={{ marginBottom: 12 }}>
          {t('statisic.usageData2')}
        </Row>
        <Row style={{ marginBottom: 12 }}>
          <DatePicker.RangePicker
            format="YYYY-MM-DD HH:mm:ss"
            onChange={(dates) => {
              if (dates && dates[0] && dates[1]) {
                setDateRange([dates[0], dates[1]]);
              }
            }}
            showTime
            style={{ width: 440 }}
            value={dateRange}
          />
        </Row>
        <Card
          loading={loading}
          style={{ marginBottom: 12, padding: 0 }}
          styles={{ body: { padding: 0 } }}
        >
          <Row style={{ width: '100%' }}>
            <Col
              span={8}
              style={{
                borderRight: `1px solid ${theme.colorBorderSecondary}`,
                padding: '12px 16px',
              }}
            >
              <Text style={{ display: 'block', fontSize: 14, lineHeight: '22px' }}>
                {t('statisic.apiCallCount')}
              </Text>
              <Text style={{ fontSize: 16, fontWeight: 500, lineHeight: '24px', marginTop: 4 }}>
                {usageData?.totalCalls}
              </Text>
            </Col>
            <Col
              span={8}
              style={{
                borderRight: `1px solid ${theme.colorBorderSecondary}`,
                padding: '12px 16px',
              }}
            >
              <Text style={{ display: 'block', fontSize: 14, lineHeight: '22px' }}>
                {t('statisic.tokenConsumptionInput')}
              </Text>
              <Text style={{ fontSize: 16, fontWeight: 500, lineHeight: '24px', marginTop: 4 }}>
                {formatTokens(usageData?.totalInputTokens)}
              </Text>
            </Col>
            <Col
              span={8}
              style={{
                borderRight: `1px solid ${theme.colorBorderSecondary}`,
                padding: '12px 16px',
              }}
            >
              <Text style={{ display: 'block', fontSize: 14, lineHeight: '22px' }}>
                {t('statisic.tokenConsumptionOutput')}
              </Text>
              <Text style={{ fontSize: 16, fontWeight: 500, lineHeight: '24px', marginTop: 4 }}>
                {formatTokens(usageData?.totalOutputTokens)}
              </Text>
            </Col>
          </Row>
        </Card>
        <Card
          loading={loading}
          style={{ marginBottom: 12, padding: 0 }}
          styles={{ body: { padding: 0 } }}
        >
          <Row style={{ width: '100%' }}>
            <Col span={24} style={{ padding: '12px 16px' }}>
              <Text style={{ display: 'block', fontSize: 14, lineHeight: '22px' }}>
                {t('statisic.cost')}
              </Text>
              <Text style={{ fontSize: 16, fontWeight: 500, lineHeight: '24px', marginTop: 4 }}>
                {usageData?.totalSpend.toFixed(4)} {t('statisic.costUnit')}
              </Text>
            </Col>
          </Row>
          {/* 费用详情 */}
          <Row gutter={[8, 8]} style={{ padding: 16 }}>
            {usageData?.modelStats.map((item: any) => {
              return (
                <Col key={item.modelId} span={12}>
                  <Row
                    align={'middle'}
                    justify={'space-between'}
                    style={{
                      background: theme.colorBgContainerSecondary,
                      borderRadius: 6,
                      padding: '12px 16px',
                    }}
                  >
                    <div>
                      <Avatar icon={'G'} size={24} style={{ marginRight: 8 }} />
                      <Text>{item.model}</Text>
                    </div>
                    <Text>
                      {item.spend.toFixed(4)} {t('statisic.costUnit')}
                    </Text>
                  </Row>
                </Col>
              );
            })}
          </Row>
        </Card>
      </Drawer>
    );
  },
);

export default StatisticDrawer;
