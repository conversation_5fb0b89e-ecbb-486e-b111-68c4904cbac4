'use client';

import { Icon } from '@lobehub/ui';
import {
  Avatar,
  Button,
  Col,
  Dropdown,
  Input,
  List,
  MenuProps,
  Modal,
  Row,
  Space,
  Spin,
  Switch,
  Tag,
  Typography,
  message,
} from 'antd';
import { createGlobalStyle, createStyles, useTheme } from 'antd-style';
import {
  Atom,
  Calendar,
  Earth,
  Ellipsis,
  Eye,
  Image,
  Info,
  Pencil,
  Plus,
  RefreshCcwDot,
  RotateCw,
  Search,
} from 'lucide-react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { trpcQuery } from '@/libs/trpc/client';
import { AiProviderWithModels } from '@/types/adminInfra';

import AddModelDrawer from './AddModelDrawer';

const SearchGlobal = createGlobalStyle`
    .ant-input-search {
        .ant-input-affix-wrapper {
            border-radius: 6px !important;
        }

        .ant-input-group-addon {
            display: none !important;
        }
    }
`;

const ModuleList = ({ detail }: { detail?: AiProviderWithModels }) => {
  const { Text } = Typography;
  const theme = useTheme();
  const [addModelOpen, setAddModelOpen] = useState<{ data?: any; open: boolean }>({
    open: false,
  });
  const useStyles = createStyles(({ css }) => {
    return {
      channelBtn: css`
        height: 28px;
        padding-block: 0;
        padding-inline: 8px;

        font-size: 14px;
        line-height: 28px;
      `,
    };
  });
  const { styles } = useStyles();

  const {
    data: models,
    isFetching: isLoadingModels,
    refetch,
  } = trpcQuery.adminInfra.getInfraModelsByProviderId.useQuery(detail?.id || '', {
    enabled: !!detail?.id,
  });
  const [isLoading, setIsLoading] = useState(false);
  const { t } = useTranslation('channel');
  const [searchValue, setSearchValue] = useState('');
  // 新增：hover 行 id
  const [hoveredId, setHoveredId] = useState<string | null>(null);
  // 同步模型
  const syncProviderModelsMutation = trpcQuery.adminInfra.syncProviderModels.useMutation();
  // 重置模型
  const clearProviderModelsMutation = trpcQuery.adminInfra.clearProviderModels.useMutation();

  // 切换模型状态
  const toggleProviderMutation = trpcQuery.adminInfra.toggleInfraModelStatus.useMutation();

  const items: MenuProps['items'] = [
    {
      key: 'reset',
      label: (
        <div style={{ color: theme.colorError }}>
          <Icon icon={RotateCw} size={{ size: 14 }} style={{ marginRight: 8 }} />
          {t('table.resetModel')}
        </div>
      ),
      onClick: () => {
        Modal.confirm({
          content: t('table.resetModelConfirm'),
          icon: (
            <Icon icon={Info} size={{ size: 18 }} style={{ color: theme.gray, marginTop: 2 }} />
          ),
          okButtonProps: {
            danger: true,
          },
          okText: t('table.resetModel'),
          onOk: async () => {
            try {
              if (!detail?.id) return;
              await clearProviderModelsMutation.mutateAsync(detail?.id);
              message.success(t('table.resetModelSuccess'));
              refetch();
            } catch (error: any) {
              message.error(t('table.resetModelFailedMessage', { error: error.message }));
            }
          },
          width: 300,
        });
      },
    },
  ];
  const handleSyncModels = async () => {
    try {
      if (!detail?.id) return;
      await syncProviderModelsMutation.mutateAsync(detail.id);
      message.success(t('table.syncModelSuccess'));
      refetch();
    } catch (error: any) {
      message.error(t('table.syncModelFailedMessage', { error: error.message }));
    }
  };

  // 获取模型行组件
  const getModelItem = (item: any) => {
    return (
      <List.Item
        key={item.id}
        onMouseEnter={() => setHoveredId(item.id)}
        onMouseLeave={() => setHoveredId(null)}
        style={{
          background: hoveredId === item.id ? theme.colorBgTextHover : 'transparent',
          borderRadius: 6,
          display: 'grid',
          gridTemplateColumns: '1fr 320px',
          paddingLeft: 12,
        }}
      >
        <List.Item.Meta
          avatar={<Avatar>{item?.displayName?.charAt(0) || 'U'}</Avatar>}
          description={
            <Text
              ellipsis={{
                // @ts-ignore
                rows: 2,
                tooltip: true,
              }}
              style={{ color: theme.colorTextDescription }}
            >
              {item?.description}
            </Text>
          }
          title={
            <Row align={'middle'} wrap={false}>
              <Text
                ellipsis={{
                  // @ts-ignore
                  rows: 1,
                  tooltip: true,
                }}
              >
                {item?.displayName}
              </Text>
              <Tag style={{ marginLeft: 8 }}>{item?.type}</Tag>
              {/* 只有 hover 时显示编辑按钮 */}
              {hoveredId === item.id && (
                <Tag
                  onClick={() => setAddModelOpen({ data: item, open: true })}
                  style={{ cursor: 'pointer' }}
                >
                  <Icon icon={Pencil} size={{ size: 14 }} />
                </Tag>
              )}
            </Row>
          }
        />
        <Row align={'middle'} gutter={8} justify={'end'} style={{ width: 320 }} wrap={false}>
          {item?.abilities && (item.abilities as any).vision && (
            <Tag>
              <Icon icon={Eye} size={{ size: 14 }} />
            </Tag>
          )}
          {item?.abilities && (item.abilities as any).functionCall && (
            <Tag>
              <Icon icon={Calendar} size={{ size: 14 }} />
            </Tag>
          )}
          {item?.abilities && (item.abilities as any).imageGeneration && (
            <Tag>
              <Icon icon={Image} size={{ size: 14 }} />
            </Tag>
          )}

          {item?.abilities && (item.abilities as any).reasoning && (
            <Tag>
              <Icon icon={Atom} size={{ size: 14 }} />
            </Tag>
          )}
          {item?.abilities && (item.abilities as any).search && (
            <Tag>
              <Icon icon={Earth} size={{ size: 14 }} />
            </Tag>
          )}
          <Tag>{Math.floor((item?.contextWindowTokens || 0) / 1000)}K</Tag>
          <Switch
            checked={item.enabled}
            onChange={async (checked) => {
              try {
                setIsLoading(true);
                if (!detail?.id) return;
                await toggleProviderMutation.mutateAsync({
                  enabled: checked,
                  modelId: item.id,
                  providerId: detail.id,
                });
                refetch();
              } catch {
                console.error('切换失败');
              } finally {
                setIsLoading(false);
              }
            }}
          />
        </Row>
      </List.Item>
    );
  };

  return (
    <div
      style={{
        background: theme.colorBgContainer,
        borderRadius: 6,
        minHeight: 'calc(100vh - 500px)',
        padding: '12px 16px',
      }}
    >
      <Spin spinning={isLoading || isLoadingModels}>
        <Row align={'middle'} justify={'space-between'} wrap={false}>
          <Col>
            <Text style={{ fontWeight: 500, marginRight: 8 }}>{t('table.modelList')}</Text>
            <span style={{ color: theme.colorTextSecondary, fontSize: 12 }}>
              {models?.data?.length || detail?.models?.length} {t('table.modelListCount')}
            </span>
          </Col>
          <Col>
            <Space>
              <SearchGlobal />
              <Input.Search
                enterButton={false}
                onSearch={(value) => {
                  setSearchValue(value);
                }}
                placeholder={t('table.searchModelPlaceholder')}
                prefix={
                  <Icon
                    icon={Search}
                    size={{ size: 16 }}
                    style={{ color: theme.colorTextPlaceholder, marginRight: 8 }}
                  />
                }
                size={'small'}
              />
              <Button
                className={styles.channelBtn}
                loading={isLoadingModels}
                onClick={handleSyncModels}
              >
                <Icon icon={RefreshCcwDot} size={{ size: 14 }} />
                {t('table.getModel')}
              </Button>
              <Button className={styles.channelBtn} onClick={() => setAddModelOpen({ open: true })}>
                <Icon icon={Plus} size={{ size: 14 }} />
              </Button>
              <Dropdown menu={{ items }} placement="bottomLeft">
                <Button className={styles.channelBtn}>
                  <Icon icon={Ellipsis} size={{ size: 14 }} />
                </Button>
              </Dropdown>
            </Space>
          </Col>
        </Row>
        <div
          style={{
            marginTop: 16,
            minHeight: 'calc(100% - 80px)',
          }}
        >
          <Text style={{ color: theme.colorTextDescription, fontSize: '14px', lineHeight: '22px' }}>
            {t('table.enabled')}
          </Text>
          <List
            dataSource={models?.data?.filter(
              (item) => item.enabled && item.displayName?.includes(searchValue),
            )}
            renderItem={(item) => getModelItem(item)}
            style={{ width: '100%' }}
          />
          <Text style={{ color: theme.colorTextDescription, fontSize: '14px', lineHeight: '22px' }}>
            {t('table.disabled')}
          </Text>
          <List
            dataSource={models?.data?.filter(
              (item) => !item.enabled && item.displayName?.includes(searchValue),
            )}
            renderItem={(item) => getModelItem(item)}
          />
        </div>
      </Spin>
      <AddModelDrawer
        data={addModelOpen.data}
        onClose={() => setAddModelOpen({ open: false })}
        onOk={() => {
          refetch();
        }}
        open={addModelOpen.open}
        selectedProviderId={detail?.id}
      />
    </div>
  );
};
export default ModuleList;
