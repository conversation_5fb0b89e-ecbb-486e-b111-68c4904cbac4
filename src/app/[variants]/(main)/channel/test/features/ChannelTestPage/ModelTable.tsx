'use client';

import { ModelIcon } from '@lobehub/icons';
import { ActionIcon, Tag } from '@lobehub/ui';
import { Button, Modal, Space, Switch, Table, message } from 'antd';
import { EyeIcon, PenIcon, PlusIcon, RefreshCwIcon, TrashIcon } from 'lucide-react';
import { memo } from 'react';
import { Flexbox } from 'react-layout-kit';

import { trpcQuery } from '@/libs/trpc/client';

interface ModelTableProps {
  loading: boolean;
  models: any[];
  onAddModel: () => void;
  onEditModel: (model: any) => void;
  onRefresh?: () => void;
  providerId?: string;
}

const handleViewModel = (modelId: string) => {
  message.info(`查看模型: ${modelId}`);
};

const handleEditModel = (model: any, onEdit: (model: any) => void) => {
  onEdit(model);
};

const handleDeleteModel = (
  providerId: string,
  modelId: string,
  modelName: string,
  deleteMutation: any,
  onRefresh?: () => void,
) => {
  Modal.confirm({
    cancelText: '取消',
    content: `确定要删除模型 "${modelName}" 吗？此操作不可撤销。`,
    okText: '删除',
    okType: 'danger',
    onOk: async () => {
      try {
        const result = await deleteMutation.mutateAsync({ modelId, providerId });
        if (result.success) {
          message.success('模型删除成功');
          onRefresh?.();
        } else {
          message.error(result.error || '模型删除失败');
        }
      } catch (error: any) {
        console.error('删除模型错误:', error);
        let errorMessage = '删除失败';

        if (error.message) {
          errorMessage = `删除失败: ${error.message}`;
        } else if (error.error) {
          errorMessage = `删除失败: ${error.error}`;
        } else if (typeof error === 'object') {
          errorMessage = `删除失败: ${JSON.stringify(error)}`;
        }

        message.error(errorMessage);
      }
    },
    title: '确认删除模型',
  });
};

const handleToggleModel = async (
  providerId: string,
  modelId: string,
  enabled: boolean,
  toggleMutation: any,
  onRefresh?: () => void,
) => {
  try {
    const result = await toggleMutation.mutateAsync({ enabled, modelId, providerId });
    if (result.success) {
      message.success(`${enabled ? '启用' : '禁用'}模型成功`);
      onRefresh?.();
    } else {
      message.error(`${enabled ? '启用' : '禁用'}模型失败`);
    }
  } catch (error: any) {
    message.error(`${enabled ? '启用' : '禁用'}模型失败: ${error.message || '未知错误'}`);
  }
};

const handleSyncModels = async (providerId: string, syncMutation: any, onRefresh?: () => void) => {
  try {
    await syncMutation.mutateAsync(providerId);
    message.success('同步模型成功');
    onRefresh?.();
  } catch (error: any) {
    message.error(`同步模型失败: ${error.message || '未知错误'}`);
  }
};

const ModelTable = memo<ModelTableProps>(
  ({ models, loading, onRefresh, onAddModel, onEditModel, providerId }) => {
    const toggleModelMutation = trpcQuery.adminInfra.toggleInfraModelStatus.useMutation();
    const deleteModelMutation = trpcQuery.adminInfra.deleteInfraModel.useMutation();
    const syncProviderModelsMutation = trpcQuery.adminInfra.syncProviderModels.useMutation();

    const columns = [
      {
        dataIndex: 'id',
        key: 'id',
        render: (value: string, record: any) => (
          <Flexbox align="center" gap={8} horizontal>
            <ModelIcon model={value} size={20} />
            <span>{record.displayName || value}</span>
            {record.abilities?.functionCall && (
              <Tag color="blue" size="small">
                Function Call
              </Tag>
            )}
            {record.abilities?.vision && (
              <Tag color="green" size="small">
                Vision
              </Tag>
            )}
            {record.contextWindowTokens && (
              <Tag color="orange" size="small">
                {Math.floor(record.contextWindowTokens / 1000)}K
              </Tag>
            )}
          </Flexbox>
        ),
        title: '模型名称',
      },
      {
        dataIndex: 'pricing',
        key: 'inputPrice',
        render: (pricing: any) => {
          return `$${pricing.input}/1M`;
        },
        title: '输入价格',
        width: 120,
      },
      {
        dataIndex: 'pricing',
        key: 'outputPrice',
        render: (pricing: any) => {
          return `$${pricing.output}/1M`;
        },
        title: '输出价格',
        width: 120,
      },
      {
        dataIndex: 'enabled',
        key: 'enabled',
        render: (enabled: boolean, record: any) => (
          <Switch
            checked={enabled}
            loading={toggleModelMutation.isPending}
            onChange={(checked) =>
              handleToggleModel(providerId!, record.id, checked, toggleModelMutation, onRefresh)
            }
            size="small"
          />
        ),
        title: '状态',
        width: 80,
      },
      {
        key: 'actions',
        render: (_: any, record: any) => (
          <Space size="small">
            <ActionIcon
              icon={EyeIcon}
              onClick={() => handleViewModel(record.id)}
              size="small"
              title="查看"
            />
            <ActionIcon
              icon={PenIcon}
              onClick={() => handleEditModel(record, onEditModel)}
              size="small"
              title="编辑"
            />
            <ActionIcon
              icon={TrashIcon}
              loading={deleteModelMutation.isPending}
              onClick={() =>
                handleDeleteModel(
                  providerId!,
                  record.id,
                  record.displayName || record.id,
                  deleteModelMutation,
                  onRefresh,
                )
              }
              size="small"
              title="删除"
            />
          </Space>
        ),
        title: '操作',
        width: 120,
      },
    ];

    return (
      <div>
        <div style={{ marginBottom: 16, textAlign: 'right' }}>
          <Space>
            {providerId && (
              <Button
                icon={<RefreshCwIcon size={16} />}
                loading={syncProviderModelsMutation.isPending}
                onClick={() => handleSyncModels(providerId, syncProviderModelsMutation, onRefresh)}
              >
                同步模型
              </Button>
            )}
            <Button icon={<PlusIcon size={16} />} onClick={onAddModel} type="primary">
              添加模型
            </Button>
          </Space>
        </div>
        <Table
          columns={columns}
          dataSource={models}
          loading={loading}
          pagination={{
            pageSize: 10,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total) => `共 ${total} 条`,
          }}
          rowKey="id"
          scroll={{ x: 'max-content' }}
          size="small"
        />
      </div>
    );
  },
);

export default ModelTable;
