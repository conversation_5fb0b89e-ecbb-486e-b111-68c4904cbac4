'use client';

import {
  Card,
  Col,
  Form,
  Input,
  InputNumber,
  Modal,
  Row,
  Select,
  Slider,
  Switch,
  Typography,
  message,
} from 'antd';
import { memo, useEffect, useState } from 'react';

import { trpcClient } from '@/libs/trpc/client';

interface AddModelModalProps {
  editData?: any;
  mode?: 'create' | 'edit';
  onCancel: () => void;
  onSuccess: () => void;
  open: boolean;
  selectedProviderId?: string;
}

const { TextArea } = Input;
const { Text } = Typography;

// 模型类型选项
const MODEL_TYPES = [
  { label: '大语言模型', value: 'chat' },
  { label: '图片生成模型', value: 'image' },
  { label: 'embedding模型', value: 'embedding' },
  { label: 'rerank模型', value: 'rerank' },
  { label: '视频生成', value: 'text2video' },
  { label: 'ASR模型', value: 'stt' },
  { label: 'TTS模型', value: 'tts' },
  { label: 'Realtime模型', value: 'realtime' },
];

// 上下文窗口预设值
const CONTEXT_WINDOW_MARKS = {
  0: '0',
  131_072: '128K',
  16_384: '16K',
  204_800: '200K',
  32_768: '32K',
  4096: '4K',
  65_536: '64K',
  8192: '8K',
};

const AddModelModal = memo<AddModelModalProps>(
  ({ open, onCancel, onSuccess, selectedProviderId, mode = 'create', editData }) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);

    // 初始化表单数据
    useEffect(() => {
      if (!open) return;

      form.resetFields();

      if (mode === 'edit' && editData) {
        // 编辑模式：回显数据
        form.setFieldsValue({
          'abilities.functionCall': editData.abilities?.functionCall || false,
          'abilities.imageGeneration': editData.abilities?.imageGeneration || false,
          'abilities.imageOutput': editData.abilities?.imageOutput || false,
          'abilities.reasoning': editData.abilities?.reasoning || false,
          'abilities.search': editData.abilities?.search || false,
          'abilities.vision': editData.abilities?.vision || false,
          'contextWindowTokens': editData.contextWindowTokens || 0,
          'description': editData.description || '',
          'displayName': editData.displayName || '',
          'enabled': editData.enabled ?? true,
          'id': editData.id,
          'inputPrice': editData.pricing?.input || 0,
          'outputPrice': editData.pricing?.output || 0,
          'type': editData.type || 'chat',
        });
      } else {
        // 创建模式：设置默认值
        form.setFieldsValue({
          'abilities.functionCall': false,
          'abilities.imageGeneration': false,
          'abilities.imageOutput': false,
          'abilities.reasoning': false,
          'abilities.search': false,
          'abilities.vision': false,
          'contextWindowTokens': 0,
          'enabled': true,
          'inputPrice': 0,
          'outputPrice': 0,
          'providerId': selectedProviderId,
          'type': 'chat',
        });
      }
    }, [open, selectedProviderId, form, mode, editData]);

    const handleSubmit = async () => {
      try {
        const values = await form.validateFields();
        setLoading(true);

        const submitData = {
          abilities: {
            functionCall: values['abilities.functionCall'] || false,
            imageGeneration: values['abilities.imageGeneration'] || false,
            imageOutput: values['abilities.imageOutput'] || false,
            reasoning: values['abilities.reasoning'] || false,
            search: values['abilities.search'] || false,
            vision: values['abilities.vision'] || false,
          },
          contextWindowTokens: values.contextWindowTokens || undefined,
          description: values.description,
          displayName: values.displayName,
          enabled: values.enabled,
          id: values.id,
          parameters: {},
          pricing: {
            input: values.inputPrice,
            output: values.outputPrice,
          },
          providerId: selectedProviderId!,
          source: 'custom' as const,
          type: values.type,
        };

        if (mode === 'create') {
          const result = await trpcClient.adminInfra.createInfraModel.mutate(submitData);
          if (result.success) {
            message.success('模型创建成功');
            form.resetFields();
            onSuccess();
          } else {
            message.error(result.error || '创建模型失败');
            return;
          }
        } else {
          const result = await trpcClient.adminInfra.updateInfraModel.mutate({
            data: submitData,
            modelId: editData!.id,
            providerId: editData!.providerId,
          });
          if (result.success) {
            message.success('模型更新成功');
            form.resetFields();
            onSuccess();
          } else {
            message.error(result.error || '更新模型失败');
            return;
          }
        }
      } catch (error: any) {
        message.error(`${mode === 'create' ? '创建' : '更新'}失败: ${error.message}`);
      } finally {
        setLoading(false);
      }
    };

    const handleCancel = () => {
      form.resetFields();
      onCancel();
    };

    return (
      <Modal
        cancelText="取消"
        confirmLoading={loading}
        okText={mode === 'create' ? '创建' : '更新'}
        onCancel={handleCancel}
        onOk={handleSubmit}
        open={open}
        title={mode === 'create' ? '添加模型' : '编辑模型'}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          preserve={false}
          style={{ maxHeight: '70vh', overflowY: 'auto', paddingRight: 8 }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="模型 ID"
                name="id"
                rules={[
                  { message: '请输入模型 ID', required: true },
                  { max: 150, message: '模型 ID 不能超过 150 个字符' },
                ]}
              >
                <Input
                  disabled={mode === 'edit'}
                  placeholder="请输入模型 ID，例如 gpt-4o, claude-3.5-sonnet..."
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="模型类型"
                name="type"
                rules={[{ message: '请选择模型类型', required: true }]}
              >
                <Select options={MODEL_TYPES} placeholder="请选择模型类型" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="模型显示名称"
            name="displayName"
            rules={[
              { message: '请输入模型显示名称', required: true },
              { max: 200, message: '显示名称不能超过 200 个字符' },
            ]}
          >
            <Input placeholder="请输入模型的显示名称，例如 ChatGPT, GPT-4o..." />
          </Form.Item>

          <Form.Item label="模型描述" name="description">
            <TextArea maxLength={500} placeholder="请输入模型描述（可选）" rows={3} showCount />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="输入价格"
                name="inputPrice"
                rules={[{ message: '请输入输入价格', required: true }]}
              >
                <InputNumber
                  addonAfter="元/M"
                  min={0}
                  placeholder="请输入输入价格"
                  precision={6}
                  step={0.001}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="输出价格"
                name="outputPrice"
                rules={[{ message: '请输入输出价格', required: true }]}
              >
                <InputNumber
                  addonAfter="元/M"
                  min={0}
                  placeholder="请输入输出价格"
                  precision={6}
                  step={0.001}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label="最大上下文窗口" name="contextWindowTokens">
            <Slider
              marks={CONTEXT_WINDOW_MARKS}
              max={204_800}
              min={0}
              step={1024}
              tooltip={{ formatter: (value) => `${Math.round(value! / 1024)}K` }}
            />
          </Form.Item>

          <Card size="small" style={{ marginBottom: 16 }} title="能力列表">
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Form.Item
                  name="abilities.functionCall"
                  style={{ marginBottom: 8 }}
                  valuePropName="checked"
                >
                  <Switch size="small" />
                </Form.Item>
                <Text style={{ fontSize: 12 }} type="secondary">
                  此配置仅在聚合模型使用工具的能力，允许调用工具类插件
                </Text>
              </Col>

              <Col span={12}>
                <Form.Item
                  name="abilities.vision"
                  style={{ marginBottom: 8 }}
                  valuePropName="checked"
                >
                  <Switch size="small" />
                </Form.Item>
                <Text style={{ fontSize: 12 }} type="secondary">
                  此配置仅会在应用增加视觉识别能力
                </Text>
              </Col>

              <Col span={12}>
                <Form.Item
                  name="abilities.reasoning"
                  style={{ marginBottom: 8 }}
                  valuePropName="checked"
                >
                  <Switch size="small" />
                </Form.Item>
                <Text style={{ fontSize: 12 }} type="secondary">
                  此配置启用模型深度推理能力
                </Text>
              </Col>

              <Col span={12}>
                <Form.Item
                  name="abilities.search"
                  style={{ marginBottom: 8 }}
                  valuePropName="checked"
                >
                  <Switch size="small" />
                </Form.Item>
                <Text style={{ fontSize: 12 }} type="secondary">
                  支持联网搜索功能
                </Text>
              </Col>

              <Col span={12}>
                <Form.Item
                  name="abilities.imageGeneration"
                  style={{ marginBottom: 8 }}
                  valuePropName="checked"
                >
                  <Switch size="small" />
                </Form.Item>
                <Text style={{ fontSize: 12 }} type="secondary">
                  支持图像生成功能
                </Text>
              </Col>
            </Row>
          </Card>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="启用" name="enabled" valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  },
);

export default AddModelModal;
