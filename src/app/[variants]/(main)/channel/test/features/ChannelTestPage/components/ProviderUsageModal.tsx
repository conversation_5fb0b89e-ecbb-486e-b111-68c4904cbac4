'use client';

import { Avatar, DatePicker, List, Modal, Spin, Statistic, Typography } from 'antd';
import dayjs from 'dayjs';
import { memo, useEffect, useState } from 'react';
import { Flexbox } from 'react-layout-kit';

import { trpcClient } from '@/libs/trpc/client';

const { RangePicker } = DatePicker;
const { Text } = Typography;

interface ProviderUsageModalProps {
  onCancel: () => void;
  open: boolean;
  providerId: string | null;
  providerName?: string;
}

interface UsageData {
  modelStats: Array<{
    calls: number;
    inputTokens: number;
    model: string;
    modelId: string;
    outputTokens: number;
    spend: number;
    totalTokens: number;
  }>;
  totalCalls: number;
  totalInputTokens: number;
  totalOutputTokens: number;
  totalSpend: number;
  totalTokens: number;
}

// 格式化 Token 数量
const formatTokens = (tokens: number) => {
  if (tokens >= 1_000_000) {
    return `${(tokens / 1_000_000).toFixed(1)} M`;
  }
  if (tokens >= 1000) {
    return `${(tokens / 1000).toFixed(1)} K`;
  }
  return tokens.toString();
};

// 格式化金额
const formatCurrency = (amount: number) => {
  // 直接使用原始金额，因为数据库中的spend已经是美元金额
  return amount.toFixed(4);
};

const ProviderUsageModal = memo<ProviderUsageModalProps>(
  ({ open, onCancel, providerId, providerName }) => {
    const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
      dayjs().subtract(1, 'day').startOf('day'),
      dayjs().subtract(1, 'day').endOf('day'),
    ]);
    const [loading, setLoading] = useState(false);
    const [usageData, setUsageData] = useState<UsageData | null>(null);

    // 获取使用统计数据
    const fetchUsageData = async () => {
      if (!providerId) return;

      setLoading(true);
      try {
        const result = await trpcClient.adminInfra.getProviderUsageStats.query({
          providerId,
          range: [dateRange[0].toISOString(), dateRange[1].toISOString()],
        });

        if (result.success && result.data) {
          setUsageData(result.data);
        }
      } catch (error) {
        console.error('获取使用统计失败:', error);
      } finally {
        setLoading(false);
      }
    };

    useEffect(() => {
      if (open && providerId) {
        fetchUsageData();
      }
    }, [open, providerId, dateRange]);

    return (
      <Modal
        destroyOnClose
        footer={null}
        onCancel={onCancel}
        open={open}
        title={
          <Flexbox align="center" gap={8} horizontal>
            <span>用量统计 - {providerName || providerId}</span>
          </Flexbox>
        }
        width={800}
      >
        <Flexbox gap={24}>
          {/* 时间选择器 */}
          <Flexbox>
            <RangePicker
              format="YYYY-MM-DD HH:mm:ss"
              onChange={(dates) => {
                if (dates && dates[0] && dates[1]) {
                  setDateRange([dates[0], dates[1]]);
                }
              }}
              showTime
              style={{ width: '100%' }}
              value={dateRange}
            />
          </Flexbox>

          {loading ? (
            <Flexbox align="center" justify="center" style={{ minHeight: 200 }}>
              <Spin size="large" />
            </Flexbox>
          ) : (
            usageData && (
              <Flexbox gap={20}>
                {/* 统计卡片 */}
                <Flexbox gap={16} horizontal>
                  <div
                    style={{
                      background: '#f8f9fa',
                      border: '1px solid #e9ecef',
                      borderRadius: 8,
                      minWidth: 120,
                      padding: '16px 20px',
                    }}
                  >
                    <Statistic
                      title="API 调用量"
                      value={usageData.totalCalls.toLocaleString()}
                      valueStyle={{ fontSize: 24, fontWeight: 600 }}
                    />
                  </div>

                  <div
                    style={{
                      background: '#f8f9fa',
                      border: '1px solid #e9ecef',
                      borderRadius: 8,
                      minWidth: 120,
                      padding: '16px 20px',
                    }}
                  >
                    <Statistic
                      suffix=""
                      title="Token 消耗量（输入）"
                      value={formatTokens(usageData.totalInputTokens)}
                      valueStyle={{ fontSize: 24, fontWeight: 600 }}
                    />
                  </div>

                  <div
                    style={{
                      background: '#f8f9fa',
                      border: '1px solid #e9ecef',
                      borderRadius: 8,
                      minWidth: 120,
                      padding: '16px 20px',
                    }}
                  >
                    <Statistic
                      suffix=""
                      title="Token 消耗量（输出）"
                      value={formatTokens(usageData.totalOutputTokens)}
                      valueStyle={{ fontSize: 24, fontWeight: 600 }}
                    />
                  </div>
                </Flexbox>

                {/* 成本支出 */}
                <div
                  style={{
                    background: '#f8f9fa',
                    border: '1px solid #e9ecef',
                    borderRadius: 8,
                    padding: '20px',
                  }}
                >
                  <Statistic
                    suffix="USD"
                    title="成本支出"
                    value={formatCurrency(usageData.totalSpend)}
                    valueStyle={{ color: '#1890ff', fontSize: 32, fontWeight: 600 }}
                  />
                </div>

                {/* 模型详细列表 */}
                {usageData.modelStats.length > 0 && (
                  <Flexbox>
                    <Text strong style={{ fontSize: 16, marginBottom: 12 }}>
                      模型使用详情
                    </Text>
                    <List
                      dataSource={usageData.modelStats}
                      renderItem={(item) => (
                        <List.Item style={{ borderBottom: '1px solid #f0f0f0', padding: '12px 0' }}>
                          <Flexbox horizontal justify="space-between" style={{ width: '100%' }}>
                            <Flexbox align="center" gap={12} horizontal>
                              <Avatar size="small" style={{ backgroundColor: '#87d068' }} />
                              <Text strong>{item.model || item.modelId}</Text>
                            </Flexbox>
                            <Flexbox gap={24} horizontal>
                              <Text>{item.calls} 次调用</Text>
                              <Text>{formatTokens(item.inputTokens)} 输入</Text>
                              <Text>{formatTokens(item.outputTokens)} 输出</Text>
                              <Text strong style={{ color: '#1890ff' }}>
                                ${formatCurrency(item.spend)}
                              </Text>
                            </Flexbox>
                          </Flexbox>
                        </List.Item>
                      )}
                    />
                  </Flexbox>
                )}

                {usageData.modelStats.length === 0 && (
                  <Flexbox
                    align="center"
                    justify="center"
                    style={{ color: '#999', minHeight: 100 }}
                  >
                    <Text>该时间段内暂无使用数据</Text>
                  </Flexbox>
                )}
              </Flexbox>
            )
          )}
        </Flexbox>
      </Modal>
    );
  },
);

ProviderUsageModal.displayName = 'ProviderUsageModal';

export default ProviderUsageModal;
