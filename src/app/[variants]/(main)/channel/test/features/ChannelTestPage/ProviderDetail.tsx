'use client';

import { ActionIcon, Dropdown, Icon } from '@lobehub/ui';
import { Descriptions, Empty, Skeleton, Switch, Tabs } from 'antd';
import { BarChart3Icon, EditIcon, MoreHorizontalIcon, TrashIcon } from 'lucide-react';
import { memo } from 'react';

import { INFRA_PROVIDER_TYPES } from '@/const/aiInfra';
import { trpcQuery } from '@/libs/trpc/client';
import { AiProviderWithModels } from '@/types/adminInfra';

import ModelTable from './ModelTable';

interface ProviderDetailProps {
  onAddModel: (providerId?: string) => void;
  onDeleteProvider: (provider: AiProviderWithModels) => void;
  onEditModel: (model: any) => void;
  onEditProvider: (provider: AiProviderWithModels) => void;
  onRefreshRef: (refreshFn: () => void) => void;
  onShowUsageStats: (provider: AiProviderWithModels) => void;
  onToggleProviderStatus: (provider: AiProviderWithModels, enabled: boolean) => void;
  providerId: string | null;
}

const ProviderDetail = memo<ProviderDetailProps>(
  ({
    providerId,
    onAddModel,
    onEditModel,
    onEditProvider,
    onDeleteProvider,
    onRefreshRef,
    onShowUsageStats,
    onToggleProviderStatus,
  }) => {
    const {
      data: providerInfo,
      isLoading: isProviderLoading,
      refetch,
    } = trpcQuery.adminInfra.getInfraProviderById.useQuery(providerId!, { enabled: !!providerId });

    const toggleProviderMutation = trpcQuery.adminInfra.toggleInfraProviderStatus.useMutation();

    // 将刷新函数传递给父组件
    onRefreshRef(refetch);

    if (!providerId) {
      return (
        <Empty
          description="请选择一个渠道"
          style={{
            marginTop: 100,
          }}
        />
      );
    }

    if (isProviderLoading) {
      return (
        <div style={{ padding: 24 }}>
          <Skeleton paragraph={{ rows: 8 }} title />
        </div>
      );
    }

    if (!providerInfo?.data) {
      return (
        <Empty
          description="渠道信息不存在"
          style={{
            marginTop: 100,
          }}
        />
      );
    }

    const provider = providerInfo.data;

    const handleEdit = () => {
      onEditProvider(provider);
    };

    const handleDelete = () => {
      onDeleteProvider(provider);
    };

    const handleToggleStatus = (enabled: boolean) => {
      onToggleProviderStatus(provider, enabled);
    };

    const handleShowUsageStats = () => {
      onShowUsageStats(provider);
    };

    const tabItems = [
      {
        children: (
          <Descriptions
            column={1}
            items={[
              {
                children: provider?.name,
                key: 'name',
                label: '渠道名称',
              },
              {
                children: provider?.id,
                key: 'id',
                label: '渠道编码',
              },
              {
                children:
                  INFRA_PROVIDER_TYPES.find((type) => type.value === provider?.settings?.sdkType)
                    ?.label || '--',
                key: 'sdkType',
                label: '渠道类型',
              },
              {
                children: (
                  <Switch
                    checked={provider?.enabled || false}
                    loading={toggleProviderMutation.isPending}
                    onChange={handleToggleStatus}
                    size="small"
                  />
                ),
                key: 'status',
                label: '状态',
              },
              {
                children: provider?.keyVaults?.baseURL || '默认',
                key: 'endpoint',
                label: 'Endpoint',
              },
              {
                children: provider?.description || '--',
                key: 'description',
                label: '渠道说明',
              },
              {
                children: `${provider?.models?.length || 0} 个模型`,
                key: 'models',
                label: '模型数量',
              },
            ]}
          />
        ),
        key: 'info',
        label: 'API 配置',
      },
    ];

    return (
      <div>
        <div
          style={{
            alignItems: 'center',
            display: 'flex',
            justifyContent: 'space-between',
            marginBottom: 16,
          }}
        >
          <span />
          <Dropdown
            menu={{
              items: [
                {
                  icon: <Icon icon={BarChart3Icon} />,
                  key: 'usage',
                  label: '用量统计',
                  onClick: handleShowUsageStats,
                },
                {
                  icon: <Icon icon={EditIcon} />,
                  key: 'edit',
                  label: '编辑渠道',
                  onClick: handleEdit,
                },
                {
                  danger: true,
                  icon: <Icon icon={TrashIcon} />,
                  key: 'delete',
                  label: '删除渠道',
                  onClick: handleDelete,
                },
              ],
            }}
            trigger={['click']}
          >
            <ActionIcon icon={MoreHorizontalIcon} size="small" title="更多操作" />
          </Dropdown>
        </div>

        <Tabs items={tabItems} style={{ marginBottom: 16 }} />

        <div style={{ marginTop: 24 }}>
          <h3 style={{ marginBottom: 16 }}>模型列表</h3>
          <ModelTable
            loading={isProviderLoading}
            models={provider?.models || []}
            onAddModel={() => onAddModel(providerId)}
            onEditModel={onEditModel}
            onRefresh={refetch}
            providerId={providerId}
          />
        </div>
      </div>
    );
  },
);

export default ProviderDetail;
