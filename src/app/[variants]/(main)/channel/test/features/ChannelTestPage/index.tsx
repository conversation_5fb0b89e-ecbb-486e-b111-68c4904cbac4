'use client';

import { Card, Modal, message } from 'antd';
import { memo, useRef, useState } from 'react';
import { Flexbox } from 'react-layout-kit';

import { trpcClient, trpcQuery } from '@/libs/trpc/client';
import { AiProviderWithModels } from '@/types/adminInfra';

import ProviderDetail from './ProviderDetail';
import ProviderList from './ProviderList';
import AddModelModal from './components/AddModelModal';
import ProviderModal from './components/AddProviderModal';
import ProviderUsageModal from './components/ProviderUsageModal';

const ChannelTestPage = memo(() => {
  const [selectedProviderId, setSelectedProviderId] = useState<string | null>(null);
  const [showProviderModal, setShowProviderModal] = useState(false);
  const [providerModalMode, setProviderModalMode] = useState<'create' | 'edit'>('create');
  const [editingProvider, setEditingProvider] = useState<AiProviderWithModels | null>(null);
  const [showAddModelModal, setShowAddModelModal] = useState(false);
  const [modelModalMode, setModelModalMode] = useState<'create' | 'edit'>('create');
  const [editingModel, setEditingModel] = useState<any>(null);
  const [selectedProviderForModel, setSelectedProviderForModel] = useState<string | null>(null);
  const [showUsageModal, setShowUsageModal] = useState(false);
  const [usageProvider, setUsageProvider] = useState<AiProviderWithModels | null>(null);

  // 数据刷新函数引用
  const providerDetailRefreshRef = useRef<(() => void) | null>(null);

  const { data, refetch } = trpcQuery.adminInfra.getAllInfraProviders.useQuery();

  const handleAddProvider = () => {
    setProviderModalMode('create');
    setEditingProvider(null);
    setShowProviderModal(true);
  };

  const handleEditProvider = (provider: AiProviderWithModels) => {
    setProviderModalMode('edit');
    setEditingProvider(provider);
    setShowProviderModal(true);
  };

  const handleDeleteProvider = (provider: AiProviderWithModels) => {
    Modal.confirm({
      cancelText: '取消',
      content: `确定要删除渠道 "${provider.name || provider.id}" 吗？此操作将同时删除该渠道下的所有模型，且不可撤销。`,
      okText: '删除',
      okType: 'danger',
      onOk: async () => {
        try {
          await trpcClient.adminInfra.deleteInfraProvider.mutate(provider.id);
          message.success('渠道删除成功');

          // 刷新渠道列表数据
          refetch();

          // 如果删除的是当前选中的渠道，清空选中状态
          if (provider.id === selectedProviderId) {
            setSelectedProviderId(null);
          }
        } catch (error: any) {
          message.error(`删除失败: ${error.message}`);
        }
      },
      title: '确认删除渠道',
    });
  };

  const handleToggleProviderStatus = async (provider: AiProviderWithModels, enabled: boolean) => {
    try {
      await trpcClient.adminInfra.toggleInfraProviderStatus.mutate({
        enabled,
        providerId: provider.id,
      });
      message.success(`渠道${enabled ? '启用' : '禁用'}成功`);

      refetch();

      // 如果是当前选中的渠道，也需要刷新详情
      if (provider.id === selectedProviderId && providerDetailRefreshRef.current) {
        providerDetailRefreshRef.current();
      }
    } catch (error: any) {
      message.error(`${enabled ? '启用' : '禁用'}失败: ${error.message}`);
    }
  };

  const handleAddModel = (providerId?: string) => {
    setModelModalMode('create');
    setEditingModel(null);
    setSelectedProviderForModel(providerId || null);
    setShowAddModelModal(true);
  };

  const handleEditModel = (model: any) => {
    setModelModalMode('edit');
    setEditingModel(model);
    setSelectedProviderForModel(model.providerId);
    setShowAddModelModal(true);
  };

  const handleProviderModalSuccess = () => {
    setShowProviderModal(false);
    setEditingProvider(null);

    refetch();

    // 如果是编辑模式且编辑的是当前选中的渠道，也需要刷新详情
    if (
      providerModalMode === 'edit' &&
      editingProvider &&
      editingProvider.id === selectedProviderId &&
      providerDetailRefreshRef.current
    ) {
      providerDetailRefreshRef.current();
    }
  };

  const handleModelModalSuccess = () => {
    setShowAddModelModal(false);
    setEditingModel(null);
    setSelectedProviderForModel(null);

    // 刷新当前渠道详情
    if (providerDetailRefreshRef.current) {
      providerDetailRefreshRef.current();
    }
  };

  const handleProviderModalCancel = () => {
    setShowProviderModal(false);
    setEditingProvider(null);
  };

  const handleShowUsageStats = (provider: AiProviderWithModels) => {
    setUsageProvider(provider);
    setShowUsageModal(true);
  };

  const handleUsageModalCancel = () => {
    setShowUsageModal(false);
    setUsageProvider(null);
  };

  return (
    <div style={{ padding: 24 }}>
      <h1 style={{ marginBottom: 24 }}>渠道管理</h1>
      <Flexbox gap={24} horizontal style={{ height: 'calc(100vh - 120px)' }}>
        <Card
          bodyStyle={{
            height: 'calc(100% - 57px)',
            overflow: 'auto',
            padding: 0,
          }}
          style={{
            height: '100%',
            overflow: 'hidden',
            width: 320,
          }}
          title="渠道列表"
        >
          <ProviderList
            onAddProvider={handleAddProvider}
            onSelectProvider={setSelectedProviderId}
            providers={data?.data || []}
            selectedProviderId={selectedProviderId}
          />
        </Card>

        <Card
          bodyStyle={{
            height: 'calc(100% - 57px)',
            overflow: 'auto',
            padding: selectedProviderId ? 24 : 0,
          }}
          style={{
            flex: 1,
            height: '100%',
            overflow: 'hidden',
          }}
          title={selectedProviderId ? '渠道详情' : ''}
        >
          <ProviderDetail
            onAddModel={handleAddModel}
            onDeleteProvider={handleDeleteProvider}
            onEditModel={handleEditModel}
            onEditProvider={handleEditProvider}
            onRefreshRef={(refreshFn) => {
              providerDetailRefreshRef.current = refreshFn;
            }}
            onShowUsageStats={handleShowUsageStats}
            onToggleProviderStatus={handleToggleProviderStatus}
            providerId={selectedProviderId}
          />
        </Card>
      </Flexbox>

      <ProviderModal
        editData={editingProvider}
        mode={providerModalMode}
        onCancel={handleProviderModalCancel}
        onSuccess={handleProviderModalSuccess}
        open={showProviderModal}
      />

      <AddModelModal
        editData={editingModel}
        mode={modelModalMode}
        onCancel={() => {
          setShowAddModelModal(false);
          setEditingModel(null);
          setSelectedProviderForModel(null);
        }}
        onSuccess={handleModelModalSuccess}
        open={showAddModelModal}
        selectedProviderId={selectedProviderForModel || undefined}
      />

      <ProviderUsageModal
        onCancel={handleUsageModalCancel}
        open={showUsageModal}
        providerId={usageProvider?.id || null}
        providerName={usageProvider?.name || ''}
      />
    </div>
  );
});

export default ChannelTestPage;
