'use client';

import { <PERSON><PERSON>, But<PERSON>, List, Tag } from 'antd';
import { PlusIcon } from 'lucide-react';
import { memo } from 'react';
import { Flexbox } from 'react-layout-kit';

import ChannelIcon from '@/features/ChannelIcon';
import { trpcQuery } from '@/libs/trpc/client';
import { AdminProviderSelectItem } from '@/types/adminInfra';

interface ProviderListProps {
  onAddProvider: () => void;
  onSelectProvider: (providerId: string) => void;
  providers: AdminProviderSelectItem[];
  selectedProviderId: string | null;
}

const ProviderList = memo<ProviderListProps>(
  ({ providers, selectedProviderId, onSelectProvider, onAddProvider }) => {
    // 为每个provider获取模型数量 - 使用独立的查询而不是在reduce中使用hooks
    const modelQueries = providers.map((provider) => ({
      providerId: provider.id,
      query: trpcQuery.adminInfra.getInfraModelsByProviderId.useQuery(provider.id, {
        enabled: !!provider.id,
      }),
    }));

    const providerModelCounts = modelQueries.reduce(
      (acc, { providerId, query }) => {
        acc[providerId] = query.data?.data?.length || 0;
        return acc;
      },
      {} as Record<string, number>,
    );

    return (
      <div>
        <div style={{ borderBottom: '1px solid #f0f0f0', padding: '12px 16px' }}>
          <Button block icon={<PlusIcon size={16} />} onClick={onAddProvider} type="primary">
            添加渠道
          </Button>
        </div>
        <List
          dataSource={providers}
          renderItem={(provider) => (
            <List.Item
              onClick={() => onSelectProvider(provider.id)}
              style={{
                backgroundColor: selectedProviderId === provider.id ? '#f0f9ff' : 'transparent',
                borderLeft:
                  selectedProviderId === provider.id
                    ? '3px solid #1890ff'
                    : '3px solid transparent',
                cursor: 'pointer',
                padding: '12px 16px',
              }}
            >
              <Flexbox style={{ width: '100%' }}>
                <Flexbox align="center" gap={8} horizontal justify="space-between">
                  <Flexbox align="center" gap={8} horizontal>
                    <ChannelIcon apiBase={provider.id} size={20} />
                    <span style={{ fontWeight: 500 }}>{provider.name || provider.id}</span>
                  </Flexbox>
                  <Badge
                    color={provider.enabled ? '#52c41a' : '#d9d9d9'}
                    count={providerModelCounts[provider.id] || 0}
                    size="small"
                  />
                </Flexbox>

                <Flexbox align="center" gap={4} horizontal style={{ marginTop: 4 }}>
                  <Tag color={provider.enabled ? 'green' : 'red'}>
                    {provider.enabled ? '正常' : '异常'}
                  </Tag>
                  <Tag>{provider.source || 'custom'}</Tag>
                </Flexbox>

                {provider.description && (
                  <div
                    style={{
                      color: '#666',
                      fontSize: 12,
                      marginTop: 4,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                    }}
                  >
                    {provider.description}
                  </div>
                )}
              </Flexbox>
            </List.Item>
          )}
        />
      </div>
    );
  },
);

export default ProviderList;
