'use client';

import { Form, Input, Modal, Select, message } from 'antd';
import { memo, useEffect, useState } from 'react';

import { INFRA_PROVIDER_TYPES } from '@/const/aiInfra';
import { trpcClient } from '@/libs/trpc/client';
import { AiProviderWithModels } from '@/types/adminInfra';

interface ProviderModalProps {
  editData?: AiProviderWithModels | null;
  mode: 'create' | 'edit';
  onCancel: () => void;
  onSuccess: () => void;
  open: boolean;
}

const ProviderModal = memo<ProviderModalProps>(({ open, mode, editData, onCancel, onSuccess }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 表单回显逻辑
  useEffect(() => {
    if (mode === 'edit' && editData && open) {
      form.setFieldsValue(editData);
    } else if (mode === 'create' && open) {
      form.resetFields();
    }
  }, [mode, editData, open, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (mode === 'create') {
        // 创建模式
        const createData = {
          ...values,
          source: 'custom' as const,
        };

        await trpcClient.adminInfra.createInfraProvider.mutate(createData);
        message.success('渠道添加成功');
      } else {
        // 编辑模式
        if (!editData) return;

        await trpcClient.adminInfra.updateInfraProvider.mutate({
          data: values,
          providerId: editData.id,
        });

        message.success('渠道更新成功');
      }

      form.resetFields();
      onSuccess();
    } catch (error: any) {
      message.error(`${mode === 'create' ? '添加' : '更新'}失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  const isEdit = mode === 'edit';
  const title = isEdit ? '编辑渠道' : '添加渠道';

  return (
    <Modal
      confirmLoading={loading}
      onCancel={handleCancel}
      onOk={handleSubmit}
      open={open}
      title={title}
      width={600}
    >
      <Form form={form} layout="vertical" preserve={false}>
        <div style={{ marginBottom: 16 }}>
          <h4 style={{ marginBottom: 16 }}>基本信息</h4>
          <Form.Item hidden name="litellmCredential">
            <Input />
          </Form.Item>

          <Form.Item
            label="渠道名称"
            name="name"
            rules={[{ message: '请输入渠道名称', required: true }]}
          >
            <Input placeholder="请输入渠道名称" />
          </Form.Item>

          <Form.Item
            label="渠道编码"
            name="id"
            rules={[{ message: '请输入渠道编码', required: true }]}
          >
            <Input disabled={isEdit} placeholder="请输入渠道编码" />
          </Form.Item>

          <Form.Item
            label="渠道类型"
            name={['settings', 'sdkType']}
            rules={[{ message: '请选择渠道类型', required: true }]}
          >
            <Select options={INFRA_PROVIDER_TYPES} placeholder="请选择渠道类型" />
          </Form.Item>

          <Form.Item label="渠道描述" name="description">
            <Input.TextArea placeholder="请输入渠道描述" rows={3} />
          </Form.Item>
        </div>

        <div>
          <h4 style={{ marginBottom: 16 }}>API 配置</h4>

          <Form.Item
            label="API Key"
            name={['keyVaults', 'apiKey']}
            rules={[{ message: '请输入 API Key', required: true }]}
          >
            <Input.Password
              placeholder={isEdit ? '****已设置，留空保持不变****' : '请输入 API Key'}
            />
          </Form.Item>

          <Form.Item label="API Endpoint" name={['keyVaults', 'baseURL']}>
            <Input placeholder="请输入 Endpoint" />
          </Form.Item>
        </div>
      </Form>
    </Modal>
  );
});

export default ProviderModal;
