'use client';

import { Grid } from '@lobehub/ui';
import { useResponsive } from 'antd-style';
import { Dayjs } from 'dayjs';
import { memo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import Block from '@/components/Block';
import DateRangePicker from '@/components/DateRangePicker';
import { daysAgo, today } from '@/utils/time';

import InstallFailureAnalysis from './InstallFailureAnalysis';
import Top10MostCalledPlugins from './Top10MostCalledPlugins';
import Top10PopularPlugins from './Top10PopularPlugins';

const MarketAnalysis = memo(() => {
  const { t } = useTranslation('market');
  const [value, setValue] = useState<[Dayjs, Dayjs]>([daysAgo(6), today()]);
  const { mobile } = useResponsive();

  return (
    <Block gap={16} title={t('dashboard.analysis.title')}>
      <DateRangePicker
        onRangeChange={setValue}
        showLabel
        style={{ marginInline: mobile ? 12 : 0 }}
        value={value}
      />
      <Grid maxItemWidth={400} rows={2}>
        <Top10PopularPlugins
          range={value}
          style={{
            gridColumn: mobile ? undefined : 'span 1',
          }}
        />
        <Top10MostCalledPlugins
          range={value}
          style={{
            gridColumn: mobile ? undefined : 'span 1',
          }}
        />
      </Grid>
      <InstallFailureAnalysis
        range={value}
        style={{
          gridColumn: mobile ? undefined : 'span 1',
        }}
      />
    </Block>
  );
});

export default MarketAnalysis;
