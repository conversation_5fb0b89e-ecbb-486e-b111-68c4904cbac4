'use client';

import { ProColumns } from '@ant-design/pro-components';
import { useTheme } from 'antd-style';
import { Dayjs } from 'dayjs';
import { CSSProperties, memo } from 'react';
import { useTranslation } from 'react-i18next';

import Block from '@/components/Block';
import Table from '@/components/Table';
import { trpcQuery } from '@/libs/trpc/client';
import { formatNumber } from '@/utils/format';

const InstallFailureAnalysis = memo<{ range: [Dayjs, Dayjs]; style?: CSSProperties }>(
  ({ style, range }) => {
    const { data, isLoading } = trpcQuery.market.overview.getInstallFailureAnalysis.useQuery({
      range: range.map((d) => d.format('YYYY-MM-DD')) as [string, string],
    });
    const { t } = useTranslation('market');
    const theme = useTheme();

    const columns: ProColumns[] = [
      {
        dataIndex: 'pluginName',
        key: 'pluginName',
        render: (text, record: any) => (
          <div>
            <div style={{ fontWeight: 500 }}>{text}</div>
            <div style={{ color: theme.colorTextSecondary, fontSize: 12 }}>
              {record.pluginIdentifier}
            </div>
          </div>
        ),
        title: t('dashboard.installFailure.pluginName'),
      },
      {
        dataIndex: 'failureCount',
        key: 'failureCount',
        render: (count) => formatNumber(count as number, 0),
        sorter: (a: any, b: any) => a.failureCount - b.failureCount,
        title: t('dashboard.installFailure.failureCount'),
      },
      {
        dataIndex: 'failureRate',
        key: 'failureRate',
        render: (rate) => `${((rate as number) * 100).toFixed(1)}%`,
        sorter: (a: any, b: any) => a.failureRate - b.failureRate,
        title: t('dashboard.installFailure.failureRate'),
      },
      {
        dataIndex: 'mostCommonError',
        key: 'mostCommonError',
        render: (error) => (
          <div
            style={{
              color: theme.colorError,
              maxWidth: 200,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {error}
          </div>
        ),
        title: t('dashboard.installFailure.mostCommonError'),
      },
      {
        dataIndex: 'totalInstallAttempts',
        key: 'totalInstallAttempts',
        render: (count) => formatNumber(count as number, 0),
        sorter: (a: any, b: any) => a.totalInstallAttempts - b.totalInstallAttempts,
        title: t('dashboard.installFailure.totalAttempts'),
      },
    ];

    return (
      <Block style={style} title={t('dashboard.installFailure.title')}>
        <Table
          columns={columns}
          dataSource={data || []}
          loading={isLoading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showTotal: (total, range) =>
              t('dashboard.installFailure.showTotal', {
                end: range[1],
                start: range[0],
                total,
              }),
          }}
          rowKey="pluginIdentifier"
          scroll={{ x: 800 }}
          size="small"
        />
      </Block>
    );
  },
);

export default InstallFailureAnalysis;
