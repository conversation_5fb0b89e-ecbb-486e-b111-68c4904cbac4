'use client';

import { BarList, BarListProps } from '@lobehub/charts';
import { Avatar } from '@lobehub/ui';
import { useTheme } from 'antd-style';
import { Dayjs } from 'dayjs';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { CSSProperties, memo } from 'react';
import { useTranslation } from 'react-i18next';
import urlJoin from 'url-join';

import StatisticCard from '@/components/StatisticCard';
import { trpcQuery } from '@/libs/trpc/client';
import { formatNumber } from '@/utils/format';

const Top10PopularPlugins = memo<{ range: [Dayjs, Dayjs]; style?: CSSProperties }>(
  ({ style, range }) => {
    const { data, isLoading } = trpcQuery.market.overview.getTop10PopularPlugins.useQuery({
      range: range.map((d) => d.format('YYYY-MM-DD')) as [string, string],
    });
    const { t } = useTranslation('market');
    const theme = useTheme();
    const router = useRouter();

    const bindData: BarListProps['data'] =
      data
        ?.map((item: any) => ({
          icon: (
            <Avatar
              alt={item.name || item.identifier}
              avatar={item.avatar}
              background={theme.colorFillSecondary}
              size={24}
            />
          ),
          id: item.identifier,
          name: (
            <Link href={urlJoin('/market/plugins', item.identifier)} style={{ color: 'inherit' }}>
              {item.name || item.identifier}
            </Link>
          ),
          value: item.installCount || 0,
        }))
        .sort((a: any, b: any) => b.value - a.value) || [];

    return (
      <StatisticCard
        chart={
          <BarList
            data={bindData}
            height={300}
            leftLabel={t('dashboard.plugins.name')}
            loading={isLoading}
            onValueChange={(item) => router.push(urlJoin('/market/plugins', item.id))}
            rightLabel={t('dashboard.plugins.installCount')}
            style={{ marginTop: 12 }}
            valueFormatter={(v) => formatNumber(v, 0)}
          />
        }
        chartPlacement={'right'}
        loading={isLoading}
        style={style}
        title={t('dashboard.plugins.top10Popular')}
      />
    );
  },
);

export default Top10PopularPlugins;
