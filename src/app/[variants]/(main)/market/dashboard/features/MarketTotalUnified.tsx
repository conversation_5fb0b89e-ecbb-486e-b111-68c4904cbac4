'use client';

import { Grid } from '@lobehub/ui';
import { useTheme } from 'antd-style';
import { CSSProperties, memo } from 'react';
import { useTranslation } from 'react-i18next';

import Statistic from '@/components/Statistic';
import StatisticCard from '@/components/StatisticCard';
import TitleWithPercentage from '@/components/StatisticCard/TitleWithPercentage';
import { trpcQuery } from '@/libs/trpc/client';
import { formatIntergerNumber } from '@/utils/format';

const StatCard = memo<{
  avg?: number;
  count?: number;
  highlight: string;
  loading?: boolean;
  prevAvg?: number;
  prevCount?: number;
  style?: CSSProperties;
  title: string;
}>(({ count, prevCount, avg, prevAvg, title, highlight, style, loading }) => {
  const { t } = useTranslation('market');

  const displayValue = avg !== undefined ? avg : count;
  const displayPrevValue = prevAvg !== undefined ? prevAvg : prevCount;
  const precision = avg !== undefined ? 1 : 0;

  return (
    <StatisticCard
      highlight={highlight}
      loading={loading}
      statistic={{
        description: (
          <Statistic
            title={t('dashboard.stats.lastMonth')}
            value={
              avg !== undefined
                ? displayPrevValue?.toFixed(1) || '--'
                : formatIntergerNumber(displayPrevValue) || '--'
            }
          />
        ),
        precision,
        value: displayValue || '--',
      }}
      style={style}
      title={<TitleWithPercentage count={displayValue} prvCount={displayPrevValue} title={title} />}
    />
  );
});

const MarketTotalUnified = memo(() => {
  const { data, isLoading } = trpcQuery.market.overview.getMarketOverviewStats.useQuery();
  const { t } = useTranslation('market');
  const theme = useTheme();

  return (
    <Grid maxItemWidth={320} rows={4}>
      <StatCard
        count={data?.plugins.count}
        highlight={theme.purple}
        loading={isLoading}
        prevCount={data?.plugins.prevCount}
        title={t('dashboard.stats.totalPlugins')}
      />
      <StatCard
        count={data?.installs.count}
        highlight={theme.green}
        loading={isLoading}
        prevCount={data?.installs.prevCount}
        title={t('dashboard.stats.totalInstalls')}
      />
      <StatCard
        count={data?.pluginCalls.count}
        highlight={theme.gold}
        loading={isLoading}
        prevCount={data?.pluginCalls.prevCount}
        title={t('dashboard.stats.pluginCalls')}
      />
      <StatCard
        count={data?.devices.count}
        highlight={theme.blue}
        loading={isLoading}
        prevCount={data?.devices.prevCount}
        title={t('dashboard.stats.devices')}
      />
    </Grid>
  );
});

export default MarketTotalUnified;
