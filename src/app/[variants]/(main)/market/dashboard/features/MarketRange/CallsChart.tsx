'use client';

import { AreaChart } from '@lobehub/charts';
import { useTheme } from 'antd-style';
import { CSSProperties, memo } from 'react';
import { useTranslation } from 'react-i18next';

import { DateRangeCompareValue } from '@/components/DateRangeCompare';
import { CompareType } from '@/components/DateRangeCompare/type';
import Statistic from '@/components/Statistic';
import StatisticCard from '@/components/StatisticCard';
import RangeTooltip from '@/components/StatisticCard/RangeTooltip';
import TitleWithPercentage from '@/components/StatisticCard/TitleWithPercentage';
import { labelFormatterByDisplayType } from '@/components/StatisticCard/labelFormatterByDisplayType';
import { trpcQuery } from '@/libs/trpc/client';
import { formatIntergerNumber } from '@/utils/format';

const CallsChart = memo<{ compare: DateRangeCompareValue; style?: CSSProperties }>(
  ({ compare, style }) => {
    const theme = useTheme();
    const { t } = useTranslation('market');
    const isDisablePrev = compare.compare === CompareType.Disabled;

    const { data, isLoading } = trpcQuery.market.overview.getRangeCalls.useQuery({
      display: compare.display,
      prevRange: compare.prevRange?.map((v) => v.format('YYYY-MM-DD')) as [string, string],
      range: compare.range.map((v) => v.format('YYYY-MM-DD')) as [string, string],
    });

    return (
      <StatisticCard
        chart={
          <AreaChart
            categories={['prevCount', 'count']}
            colors={[theme.gray5, theme.orange]}
            customTooltip={RangeTooltip}
            data={data?.data || []}
            height={140}
            index={'date'}
            loading={isLoading}
            showLegend={false}
            showYAxis={false}
            valueFormatter={(v) => formatIntergerNumber(v)}
            xAxisLabelFormatter={(v) => labelFormatterByDisplayType(v, compare.display)}
          />
        }
        loading={isLoading}
        statistic={{
          description: !isDisablePrev && (
            <Statistic
              title={t('dashboard.stats.prevPeriod')}
              value={formatIntergerNumber(data?.prevSum) || '--'}
            />
          ),
          precision: 0,
          value: data?.sum || '--',
        }}
        style={style}
        title={
          <TitleWithPercentage
            count={data?.sum}
            prvCount={data?.prevSum}
            title={t('dashboard.stats.callsTrend')}
          />
        }
      />
    );
  },
);

export default CallsChart; 