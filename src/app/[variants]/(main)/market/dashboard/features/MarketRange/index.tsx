'use client';

import { Grid } from '@lobehub/ui';
import { useResponsive } from 'antd-style';
import { memo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import Block from '@/components/Block';
import DateRangeCompare, {
  DateRangeCompareValue,
  defaultDateRangeCompareValue,
} from '@/components/DateRangeCompare';

import CallsChart from './CallsChart';
import DevicesChart from './DevicesChart';
import InstallsChart from './InstallsChart';
import PluginsChart from './PluginsChart';

const MarketRange = memo(() => {
  const { t } = useTranslation('market');
  const [compareValue, setCompareValue] = useState<DateRangeCompareValue>(
    defaultDateRangeCompareValue,
  );
  const { mobile } = useResponsive();

  return (
    <Block gap={16} title={t('dashboard.trends')}>
      <DateRangeCompare
        onChange={setCompareValue}
        showLabel
        style={{ marginInline: mobile ? 12 : 0 }}
        value={compareValue}
      />
      <Grid maxItemWidth={320} rows={4}>
        <PluginsChart compare={compareValue} />
        <InstallsChart compare={compareValue} />
        <CallsChart compare={compareValue} />
        <DevicesChart compare={compareValue} />
      </Grid>
    </Block>
  );
});

export default MarketRange;
