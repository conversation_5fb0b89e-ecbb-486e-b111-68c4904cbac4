'use client';

import { memo } from 'react';
import { Flexbox } from 'react-layout-kit';

import MarketAnalysis from './features/MarketAnalysis';
import MarketRange from './features/MarketRange';
import MarketTotalUnified from './features/MarketTotalUnified';

const Page = memo(() => {
  return (
    <Flexbox gap={24}>
      <MarketTotalUnified />
      <MarketRange />
      <MarketAnalysis />
    </Flexbox>
  );
});

export default Page;
