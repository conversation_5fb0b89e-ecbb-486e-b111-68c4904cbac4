'use client';

import { Tabs } from 'antd';
import { useQueryState } from 'nuqs';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';

import DependencyList from './features/DependencyList';
import PluginEnvList from './features/PluginEnvList';
import PluginList from './features/PluginList';

const Page = memo(() => {
  const { t } = useTranslation('market');
  const [currentTab, setCurrentTab] = useQueryState('tab', {
    clearOnDefault: true,
    defaultValue: 'plugins',
  });
  const items = [
    {
      children: <PluginList />,
      key: 'plugins',
      label: t('plugins.title'),
    },
    {
      children: <DependencyList />,
      key: 'dependencies',
      label: t('dependencies.title'),
    },
    {
      children: <PluginEnvList />,
      key: 'envs',
      label: t('env.title'),
    },
  ];

  return <Tabs activeKey={currentTab} items={items} onChange={setCurrentTab} />;
});

export default Page;
