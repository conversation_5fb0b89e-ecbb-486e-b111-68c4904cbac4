'use client';

import { ActionType } from '@ant-design/pro-components';
import { Icon } from '@lobehub/ui';
import { App, Button, Form, Input, Modal } from 'antd';
import { PlusIcon } from 'lucide-react';
import { RefObject, memo, useState } from 'react';

import { trpcQuery } from '@/libs/trpc/client';

const validateUrls = (_: any, value: string) => {
  if (!value) {
    return Promise.resolve(); // 允许为空，由 required 规则处理
  }
  const urls = value.split('\n').filter(Boolean);
  for (const url of urls) {
    const trimmedUrl = url.trim();
    if (!trimmedUrl) continue;
    try {
      new URL(trimmedUrl);
    } catch {
      return Promise.reject(new Error(`'${trimmedUrl}' 不是有效的 URL 格式`));
    }
    if (!/github\.com/.test(trimmedUrl)) {
      return Promise.reject(new Error(`'${trimmedUrl}' 不是有效的 GitHub 仓库 URL`));
    }
  }
  return Promise.resolve();
};

interface AddPluginProps {
  actionRef?: RefObject<ActionType | null>;
}

const AddPlugin = memo<AddPluginProps>(({ actionRef }) => {
  const { message } = App.useApp();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [form] = Form.useForm();

  // 创建插件的 mutation
  const createPluginMutation = trpcQuery.market.plugins.addPlugins.useMutation({
    onError: (error) => {
      message.error(`添加插件失败: ${error.message}`);
    },
    onSuccess: () => {
      // 成功添加后刷新表格
      actionRef?.current?.reload();
    },
  });

  const handleAddPlugin = () => {
    setIsModalOpen(true);
  };

  const handleModalOk = async () => {
    try {
      const { githubUrl } = await form.validateFields();
      const urls = githubUrl
        .split('\n')
        .map((url: string) => url.trim())
        .filter(Boolean);

      if (urls.length === 0) {
        message.warning('请输入至少一个有效的 GitHub URL');
        return;
      }

      await createPluginMutation.mutateAsync({ githubUrls: urls });

      message.success(`成功添加 ${urls.length} 个插件`);
      setIsModalOpen(false);
      form.resetFields();
    } catch (error) {
      // onError 在 mutation 中已经处理了失败的提示，这里可以不用再提示
      console.error('表单验证或插件添加失败:', error);
    }
  };

  const handleModalCancel = () => {
    setIsModalOpen(false);
    form.resetFields();
  };

  return (
    <>
      <Button icon={<Icon icon={PlusIcon} />} key="add" onClick={handleAddPlugin} type="primary">
        新增插件
      </Button>

      <Modal
        cancelText="取消"
        confirmLoading={createPluginMutation.isPending}
        okText="确定"
        onCancel={handleModalCancel}
        onOk={handleModalOk}
        open={isModalOpen}
        title="新增插件"
      >
        <Form form={form} layout="vertical" style={{ marginTop: 16 }}>
          <Form.Item
            label="GitHub URL"
            name="githubUrl"
            rules={[
              { message: '请输入 GitHub URL，一行一个', required: true },
              { validator: validateUrls },
            ]}
          >
            <Input.TextArea
              placeholder="请输入 GitHub 仓库 URL，一行一个，例如：
https://github.com/lobehub/chat-plugin-template"
              rows={6}
              size="large"
            />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
});

export default AddPlugin;
