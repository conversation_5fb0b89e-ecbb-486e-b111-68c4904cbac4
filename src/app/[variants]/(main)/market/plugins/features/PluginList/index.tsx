'use client';

import { ActionType, ProColumns, ProTableProps } from '@ant-design/pro-components';
import { AdminPluginItem } from '@lobehub/market-sdk';
import { Avatar, Icon, Tag } from '@lobehub/ui';
import { useTheme } from 'antd-style';
import {
  BadgeCheck,
  BadgeIcon,
  LayersIcon,
  NotepadText,
  PocketKnife,
  StarIcon,
} from 'lucide-react';
import Link from 'next/link';
import { memo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import Table from '@/components/Table';
import { trpcClient } from '@/libs/trpc/client';

import Actions from './Actions';
import AddPlugin from './AddPlugin';

// 优化请求函数
const request: ProTableProps<any, any>['request'] = async (params: any, sorts) => {
  if (sorts && Object.keys(sorts).length > 0) {
    const [sortField, sortOrder] = Object.entries(sorts)[0];
    params.sortField = sortField;
    params.sortOrder = sortOrder;
  }

  return trpcClient.market.plugins.getPlugins.query(params);
};

const PluginList = memo(() => {
  const { t } = useTranslation('market');
  const tableRef = useRef<ActionType>(null);
  const theme = useTheme();

  const columns: ProColumns<AdminPluginItem>[] = [
    {
      dataIndex: 'keyword',
      hideInTable: true,
      key: 'keyword',
      sorter: true,
      title: t('plugins.table.columns.name'),
      width: 260,
    },
    {
      dataIndex: 'identifier',
      hideInSearch: true,
      key: 'identifier',
      render: (_, record) => (
        <Link href={`/market/plugins/${record.identifier}`} style={{ color: 'inherit' }}>
          <Flexbox align={'flex-start'} gap={12} horizontal>
            <Avatar avatar={record.icon} size={24} style={{ width: 24 }} />
            <Flexbox gap={4}>
              <Flexbox align="center" gap={4} horizontal>
                {record.name}{' '}
                {record.isFeatured && (
                  <Tag color={'gold'}>
                    <Icon icon={StarIcon} />
                  </Tag>
                )}
              </Flexbox>

              <Flexbox align="center" gap={8} horizontal>
                <Tag>{record.identifier}</Tag>
                <span
                  style={{
                    color: theme.colorTextDescription,
                    fontSize: 12,
                  }}
                >
                  {record.author}
                </span>
              </Flexbox>
            </Flexbox>
          </Flexbox>
        </Link>
      ),
      sorter: true,
      title: t('plugins.table.columns.name'),
      width: 260,
    },
    {
      dataIndex: 'isFeatured',
      hideInTable: true,
      key: 'isFeatured',
      title: t('plugins.table.columns.isFeatured.title'),
      valueEnum: {
        false: '未精选',
        true: '精选',
      },
    },
    {
      dataIndex: 'isValidated',
      hideInSearch: true,
      key: 'isValidated',
      render: (_, record) => {
        return (
          <Flexbox gap={4} horizontal>
            {record.isValidated ? (
              <Tag color={'green'}>
                <Icon icon={BadgeCheck} />
              </Tag>
            ) : (
              <Tag>
                <Icon icon={BadgeIcon} />
              </Tag>
            )}
            {record.toolsCount! > 0 && (
              <Tag icon={<Icon icon={PocketKnife} />}>{record.toolsCount}</Tag>
            )}
            {record.resourcesCount! > 0 && (
              <Tag icon={<Icon icon={LayersIcon} />}>{record.resourcesCount}</Tag>
            )}
            {record.promptsCount! > 0 && (
              <Tag icon={<Icon icon={NotepadText} />}>{record.promptsCount}</Tag>
            )}
          </Flexbox>
        );
      },
      sorter: true,
      title: t('plugins.table.columns.capabilities.title'),
      valueType: 'digit',
      width: 100,
    },
    {
      dataIndex: 'status',
      key: 'status',
      render: (_, record) => {
        // const status = record.status;
        const visibility = record.visibility;
        const status = record.status;
        return (
          <Flexbox gap={4} horizontal>
            <Tag color={status === 'published' ? 'info' : 'default'}>
              {t(`plugins.status.${status}`)}
            </Tag>
            <Tag
              color={
                visibility === 'public'
                  ? 'success'
                  : visibility === 'private'
                    ? 'default'
                    : 'warning'
              }
            >
              {t(`plugins.table.columns.visibility.${visibility}`)}
            </Tag>
          </Flexbox>
        );
      },
      title: t('plugins.table.columns.status'),
      valueEnum: {
        archived: { status: 'warning', text: t('plugins.status.archived') },
        deprecated: { status: 'error', text: t('plugins.status.deprecated') },
        published: { status: 'success', text: t('plugins.status.published') },
        unpublished: { status: 'default', text: t('plugins.status.unpublished') },
      },
      width: 80,
    },
    {
      dataIndex: 'visibility',
      hideInTable: true,
      key: 'visibility',
      title: t('plugins.table.columns.visibility.title'),
      valueEnum: {
        private: { status: 'default', text: t('plugins.table.columns.visibility.private') },
        public: { status: 'success', text: t('plugins.table.columns.visibility.public') },
      },
    },
    {
      dataIndex: 'installCount',
      hideInSearch: true,
      key: 'installCount',
      render: (_, record) => {
        return (
          <Flexbox horizontal>
            {record.installCount} / {record.ratingAverage || '0'}
          </Flexbox>
        );
      },
      sorter: true,
      title: t('plugins.table.columns.stats'),
      valueType: 'digit',
      width: 60,
    },
    {
      dataIndex: 'updatedAt',
      defaultSortOrder: 'descend',
      hideInSearch: true,
      key: 'updatedAt',
      sorter: true,
      title: t('plugins.table.columns.updatedAt'),
      valueType: 'dateTime',
      width: 100,
    },
    {
      dataIndex: 'createdAt',
      hideInSearch: true,
      key: 'createdAt',
      sorter: true,
      title: t('plugins.table.columns.createdAt'),
      valueType: 'dateTime',
      width: 100,
    },
    {
      fixed: 'right',
      hideInSearch: true,
      key: 'actions',
      render: (_, record) => <Actions actionRef={tableRef} entity={record} />,
      title: t('plugins.table.actions.title'),
      width: 40,
    },
  ];

  return (
    <Table<AdminPluginItem>
      actionRef={tableRef}
      columns={columns}
      defaultPageSize={20}
      headerTitle={t('plugins.title')}
      key={'id'}
      request={request}
      rowKey={'id'}
      scroll={{ x: 1200 }}
      toolBarRender={() => [<AddPlugin actionRef={tableRef} key="add" />]}
    />
  );
});

export default PluginList;
