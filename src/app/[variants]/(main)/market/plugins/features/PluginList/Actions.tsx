import { ActionType } from '@ant-design/pro-components';
import { AdminPluginItem } from '@lobehub/market-sdk';
import { ActionIcon, Dropdown, Icon } from '@lobehub/ui';
import { App } from 'antd';
import {
  EyeIcon,
  EyeOffIcon,
  GithubIcon,
  MoreHorizontalIcon,
  StarIcon,
  StarOffIcon,
  TrashIcon,
} from 'lucide-react';
import Link from 'next/link';
import { RefObject, memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import { trpcQuery } from '@/libs/trpc/client';

interface ActionsProps {
  actionRef: RefObject<ActionType | null>;
  entity: AdminPluginItem;
}

const Actions = memo<ActionsProps>(({ entity, actionRef }) => {
  const { t } = useTranslation('market');
  const { message, modal } = App.useApp();

  const updatePluginMutation = trpcQuery.market.plugins.updatePlugin.useMutation({
    onSuccess: () => {
      actionRef.current?.reload();
    },
  });

  const deletePluginMutation = trpcQuery.market.plugins.deletePlugin.useMutation({
    onSuccess: () => {
      message.success(t('plugins.actions.deleteSuccess'));
      actionRef.current?.reload();
    },
  });

  const handleStatusChange = async () => {
    message.loading(t('plugins.actions.updating'));
    await updatePluginMutation.mutateAsync({
      data: {
        status: entity.status === 'published' ? 'unpublished' : 'published',
      },
      id: entity.id,
    });
    message.success(t('plugins.actions.statusUpdateSuccess'));
  };

  const handleVisibilityChange = async () => {
    message.loading(t('plugins.actions.updating'));
    await updatePluginMutation.mutateAsync({
      data: { isFeatured: !entity.isFeatured },
      id: entity.id,
    });
    message.success(t('plugins.actions.isFeaturedUpdateSuccess'));
  };

  const handleDelete = () => {
    modal.confirm({
      content: t('plugins.deleteConfirmContent'),
      okButtonProps: {
        danger: true,
      },
      onOk: async () => {
        await deletePluginMutation.mutateAsync({ id: entity.id });
      },
      title: t('plugins.deleteConfirm'),
    });
  };

  return (
    <Flexbox gap={8} horizontal justify={'flex-end'}>
      {entity.homepage && (
        <Link href={entity.homepage} target={'_blank'}>
          <ActionIcon active icon={GithubIcon} size={'small'} title={'Github'} />
        </Link>
      )}
      <Dropdown
        menu={{
          items: [
            {
              icon: <Icon icon={entity.status === 'published' ? EyeOffIcon : EyeIcon} />,
              key: 'status',
              label:
                entity.status === 'published'
                  ? t('plugins.table.actions.unpublish')
                  : t('plugins.table.actions.publish'),
              onClick: handleStatusChange,
            },
            {
              icon: <Icon icon={!entity.isFeatured ? StarIcon : StarOffIcon} />,
              key: 'isFeatured',
              label: entity.isFeatured
                ? t('plugins.table.actions.makeUnFeatured')
                : t('plugins.table.actions.makeFeatured'),
              onClick: handleVisibilityChange,
            },
            {
              danger: true,
              icon: <Icon icon={TrashIcon} />,
              key: 'delete',
              label: t('plugins.table.actions.delete'),
              onClick: handleDelete,
            },
          ],
        }}
        trigger={['click']}
      >
        <ActionIcon active icon={MoreHorizontalIcon} size={'small'} />
      </Dropdown>
    </Flexbox>
  );
});

export default Actions;
