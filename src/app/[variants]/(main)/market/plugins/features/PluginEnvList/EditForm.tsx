import { App, Form, Input, Modal } from 'antd';
import { memo, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import { trpcQuery } from '@/libs/trpc/client';
import { cleanObject } from '@/utils/object';

import { PluginEnv } from './type';

interface EditFormProps {
  initialValues?: PluginEnv;
  isCreate?: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  open: boolean;
}

const EditForm = memo<EditFormProps>(({ open, onCancel, onSuccess, initialValues, isCreate }) => {
  const { t } = useTranslation('market');
  const [form] = Form.useForm();
  const { message } = App.useApp();
  const createMutation = trpcQuery.market.env.createPluginEnv?.useMutation?.({
    onSuccess: () => {
      message.success(t('env.addSuccess', { defaultValue: '新增成功' }));
      onSuccess?.();
      onCancel();
    },
  });
  const updateMutation = trpcQuery.market.env.updatePluginEnv.useMutation({
    onSuccess: () => {
      message.success(t('env.editSuccess'));
      onSuccess?.();
      onCancel();
    },
  });

  const isEdit = !isCreate;

  const handleSubmit = async () => {
    const values = await form.validateFields();
    const cleanedValues = cleanObject(values);
    if (isEdit) {
      await updateMutation.mutateAsync({ data: cleanedValues, id: initialValues!.id });
    } else {
      await createMutation.mutateAsync(cleanedValues);
    }
  };

  useEffect(() => {
    if (open) form.setFieldsValue(initialValues);
  }, [initialValues, open]);

  return (
    <Modal
      confirmLoading={isEdit ? updateMutation.isPending : createMutation?.isPending}
      destroyOnHidden
      onCancel={onCancel}
      onOk={handleSubmit}
      open={open}
      title={isEdit ? t('env.edit') : t('env.add', { defaultValue: '新增环境变量' })}
      width={480}
    >
      <Form
        form={form}
        initialValues={initialValues}
        layout="vertical"
        style={{ marginTop: 24, width: '100%' }}
      >
        {!isEdit && (
          <Form.Item
            label={t('env.table.columns.identifier')}
            name="identifier"
            rules={[
              {
                message: t('env.identifierRequired', { defaultValue: '请选择插件' }),
                required: true,
              },
            ]}
          >
            <Input placeholder={t('env.table.columns.identifier')} variant={'filled'} />
          </Form.Item>
        )}
        <Form.Item label={t('env.table.columns.key')} name="key" rules={[{ required: true }]}>
          <Input variant={'filled'} />
        </Form.Item>
        <Form.Item label={t('env.table.columns.value')} name="value" rules={[{ required: true }]}>
          <Input.Password variant={'filled'} />
        </Form.Item>
        <Form.Item label={t('env.table.columns.description')} name="description">
          <Input.TextArea variant={'filled'} />
        </Form.Item>
      </Form>
    </Modal>
  );
});

export default EditForm;
