'use client';

import { ActionType, ProColumns, ProTableProps } from '@ant-design/pro-components';
import { ActionIcon, Button, Icon } from '@lobehub/ui';
import { Space, Tooltip, Typography } from 'antd';
import { ExternalLink, EyeIcon, EyeOffIcon } from 'lucide-react';
import Link from 'next/link';
import { memo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import Table from '@/components/Table';
import { trpcClient } from '@/libs/trpc/client';
import { cleanObject } from '@/utils/object';

import Actions from './Actions';
import EditForm from './EditForm';
import { PluginEnv } from './type';

const request: ProTableProps<PluginEnv, any>['request'] = async (params, sorts) => {
  if (sorts && Object.keys(sorts).length > 0) {
    const [sortField, sortOrder] = Object.entries(sorts)[0];
    params.sortField = sortField;
    params.sortOrder = sortOrder;
  }
  const res = await trpcClient.market.env.getPluginEnvs.query(cleanObject(params));

  return { ...res, success: true };
};

const PluginEnvList = memo(() => {
  const { t } = useTranslation('market');
  const tableRef = useRef<ActionType>(null);
  const [editingEnv, setEditingEnv] = useState<PluginEnv | null>(null);
  const [viewValueId, setViewValueId] = useState<number | null>(null);
  const [createModalOpen, setCreateModalOpen] = useState(false);

  const columns: ProColumns<PluginEnv>[] = [
    {
      dataIndex: 'identifier',
      key: 'identifier',
      render: (text) => (
        <Flexbox gap={4} horizontal>
          <div>{text}</div>
          <Link href={`/market/plugins/${text}`} style={{ color: 'inherit' }}>
            <Icon icon={ExternalLink} />
          </Link>
        </Flexbox>
      ),
      title: t('env.table.columns.identifier'),
      width: 120,
    },
    {
      dataIndex: 'key',
      key: 'key',
      render: (text, record) => (
        <Flexbox>
          <code>{text}</code>
          {record.description && (
            <Typography.Text type={'secondary'}>{record.description}</Typography.Text>
          )}
        </Flexbox>
      ),
      title: t('env.table.columns.key'),
      width: 100,
    },
    {
      dataIndex: 'value',
      hideInSearch: true,
      key: 'value',
      render: (_, record) => {
        const isShow = viewValueId === record.id;
        return (
          <Space>
            <Flexbox width={200}>
              {isShow ? (
                record.value
              ) : (
                <Typography.Text type={'secondary'}>
                  *******************************
                </Typography.Text>
              )}
            </Flexbox>
            <Tooltip title={t(isShow ? 'env.hideValue' : 'env.showValue')}>
              <ActionIcon
                icon={isShow ? EyeOffIcon : EyeIcon}
                onClick={() => setViewValueId(isShow ? null : record.id)}
                size="small"
              />
            </Tooltip>
          </Space>
        );
      },
      title: t('env.table.columns.value'),
      width: 200,
    },
    {
      fixed: 'right',
      hideInSearch: true,
      key: 'actions',
      render: (_, record) => (
        <Actions
          entity={record}
          onEdit={() => setEditingEnv(record)}
          onSuccess={() => tableRef.current?.reload()}
        />
      ),
      title: t('env.table.columns.actions'),
      width: 40,
    },
  ];

  const toolBarRender = () => [
    <Button key="add" onClick={() => setCreateModalOpen(true)} type="primary">
      {t('env.table.actions.add', { defaultValue: '新增' })}
    </Button>,
  ];

  return (
    <Flexbox>
      <Table<PluginEnv>
        actionRef={tableRef}
        columns={columns}
        defaultPageSize={100}
        headerTitle={t('env.title')}
        options={{
          density: false,
          setting: false,
        }}
        request={request}
        rowKey={'id'}
        scroll={{ x: 900 }}
        search={{
          defaultCollapsed: false,
          filterType: 'light',
        }}
        toolBarRender={toolBarRender}
      />
      <EditForm
        initialValues={editingEnv!}
        onCancel={() => setEditingEnv(null)}
        onSuccess={() => tableRef.current?.reload()}
        open={!!editingEnv}
      />
      <EditForm
        initialValues={undefined}
        isCreate
        onCancel={() => setCreateModalOpen(false)}
        onSuccess={() => {
          setCreateModalOpen(false);
          tableRef.current?.reload();
        }}
        open={createModalOpen}
      />
    </Flexbox>
  );
});

export default PluginEnvList;
