import { ActionIcon, Dropdown, Icon } from '@lobehub/ui';
import { App } from 'antd';
import { MoreHorizontalIcon, PencilIcon, TrashIcon } from 'lucide-react';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import { trpcQuery } from '@/libs/trpc/client';

import { PluginEnv } from './type';

interface ActionsProps {
  entity: PluginEnv;
  onEdit: () => void;
  onSuccess?: () => void;
}

const Actions = memo<ActionsProps>(({ entity, onEdit, onSuccess }) => {
  const { t } = useTranslation('market');
  const { modal, message } = App.useApp();

  const deleteMutation = trpcQuery.market.env.deletePluginEnv.useMutation({
    onSuccess: () => {
      message.success(t('env.deleteSuccess'));
      onSuccess?.();
    },
  });

  const handleDelete = () => {
    modal.confirm({
      content: t('env.deleteConfirmContent', { identifier: entity.identifier, key: entity.key }),
      okButtonProps: { danger: true },
      onOk: async () => {
        await deleteMutation.mutateAsync({ id: entity.id });
      },
      title: t('env.deleteConfirm'),
    });
  };

  return (
    <Flexbox gap={8} horizontal justify={'flex-end'}>
      <ActionIcon
        icon={PencilIcon}
        onClick={onEdit}
        size={'small'}
        title={t('env.table.actions.edit')}
      />
      <Dropdown
        menu={{
          items: [
            {
              danger: true,
              icon: <Icon icon={TrashIcon} />,
              key: 'delete',
              label: t('env.table.actions.delete'),
              onClick: handleDelete,
            },
          ],
        }}
        trigger={['click']}
      >
        <ActionIcon icon={MoreHorizontalIcon} size={'small'} />
      </Dropdown>
    </Flexbox>
  );
});

export default Actions;
