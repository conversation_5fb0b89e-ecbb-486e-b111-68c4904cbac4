'use client';

import { Block } from '@lobehub/ui';
import { App, Form, Input, Modal, Space } from 'antd';
import { AppleIcon, InfoIcon } from 'lucide-react';
import { memo, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import { trpcQuery } from '@/libs/trpc/client';
import { cleanObject } from '@/utils/object';

interface EditFormProps {
  initialValues?: any;
  onCancel: () => void;
  onSuccess: () => void;
  open: boolean;
}

const platforms = [
  { icon: <AppleIcon size={16} />, key: 'macos', label: 'macOS' },
  { icon: <InfoIcon size={16} />, key: 'manual', label: 'Manual' },
  { icon: <AppleIcon size={16} />, key: 'windows', label: 'Windows' },
  { icon: <AppleIcon size={16} />, key: 'linux_debian', label: 'Linux (Debian)' },
];

const EditForm = memo<EditFormProps>(({ open, onCancel, onSuccess, initialValues }) => {
  const { t } = useTranslation('market');
  const [form] = Form.useForm();
  const { message } = App.useApp();
  const updateMutation = trpcQuery.market.dependencies.updateSystemDependency.useMutation({
    onSuccess: () => {
      message.success(t('dependencies.actions.editSuccess'));
      onSuccess?.();
      onCancel();
    },
  });

  const handleSubmit = async () => {
    const values = await form.validateFields();
    const cleanedValues = cleanObject(values);
    updateMutation.mutate({ id: initialValues.id, ...cleanedValues });
  };

  useEffect(() => {
    form.setFieldsValue(initialValues);
  }, [initialValues]);

  return (
    <Modal
      confirmLoading={updateMutation.isPending}
      destroyOnHidden
      onCancel={onCancel}
      onOk={handleSubmit}
      open={open}
      title={t('dependencies.edit.title')}
      width={960}
    >
      <Form
        form={form}
        initialValues={initialValues}
        layout="vertical"
        style={{ marginTop: 24, width: '100%' }}
      >
        <Flexbox gap={24} horizontal>
          <Flexbox flex={1}>
            <Form.Item label={t('dependencies.form.name')} name="name" rules={[{ required: true }]}>
              <Input variant={'filled'} />
            </Form.Item>
            <Form.Item label={t('dependencies.form.type')} name="type">
              <Input variant={'filled'} />
            </Form.Item>

            <Form.Item label={t('dependencies.form.checkCommand')} name="checkCommand">
              <Input variant={'filled'} />
            </Form.Item>

            <Form.Item label={t('dependencies.form.description')} name="description">
              <Input.TextArea variant={'filled'} />
            </Form.Item>
          </Flexbox>
          <Form.Item label={t('dependencies.form.installInstructions')} style={{ flex: 1 }}>
            <Block padding={12}>
              <Space direction="vertical" size={12} style={{ width: '100%' }}>
                {platforms.map(({ key, label, icon }) => (
                  <Flexbox gap={8} key={key} paddingBlock={4}>
                    <Flexbox align={'center'} gap={8} horizontal>
                      {icon} {label}
                    </Flexbox>
                    <Form.Item name={['installInstructions', key]} noStyle>
                      <Input.TextArea
                        autoSize={{ maxRows: 4, minRows: 2 }}
                        placeholder={t('dependencies.form.installInstructionsPlaceholder', {
                          platform: label,
                        })}
                      />
                    </Form.Item>
                  </Flexbox>
                ))}
              </Space>
            </Block>
          </Form.Item>
        </Flexbox>
      </Form>
    </Modal>
  );
});

export default EditForm;
