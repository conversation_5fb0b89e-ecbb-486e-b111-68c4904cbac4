'use client';

import { ActionIcon, Dropdown, Icon } from '@lobehub/ui';
import { App } from 'antd';
import { MoreHorizontalIcon, PencilIcon, TrashIcon } from 'lucide-react';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import { trpcQuery } from '@/libs/trpc/client';

import { Dependency } from './index';

interface ActionsProps {
  entity: Dependency;
  onEdit: () => void;
  onSuccess?: () => void;
}

const Actions = memo<ActionsProps>(({ entity, onEdit, onSuccess }) => {
  const { t } = useTranslation('market');

  const { modal } = App.useApp();

  const deleteMutation = trpcQuery.market.dependencies.deleteSystemDependency.useMutation({
    onSuccess: () => {
      onSuccess?.();
    },
  });

  const handleDelete = () => {
    modal.confirm({
      content: t('dependencies.deleteConfirmContent'),
      okButtonProps: {
        danger: true,
      },
      onOk: async () => {
        await deleteMutation.mutateAsync({ id: entity.id });
      },
      title: t('dependencies.deleteConfirm'),
    });
  };

  return (
    <Flexbox gap={8} horizontal justify={'flex-end'}>
      <ActionIcon
        icon={PencilIcon}
        onClick={onEdit}
        size={'small'}
        title={t('dependencies.table.actions.edit')}
      />
      <Dropdown
        menu={{
          items: [
            {
              danger: true,
              icon: <Icon icon={TrashIcon} />,
              key: 'delete',
              label: t('dependencies.table.actions.delete'),
              onClick: handleDelete,
            },
          ],
        }}
        trigger={['click']}
      >
        <ActionIcon icon={MoreHorizontalIcon} size={'small'} />
      </Dropdown>
    </Flexbox>
  );
});

export default Actions;
