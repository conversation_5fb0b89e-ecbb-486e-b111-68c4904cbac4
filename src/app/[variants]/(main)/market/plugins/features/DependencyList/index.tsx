'use client';

import { ActionType, ProColumns, ProTableProps } from '@ant-design/pro-components';
import { SystemDependency } from '@lobehub/market-sdk';
import { Highlighter, Snippet, Tag } from '@lobehub/ui';
import { memo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';
import { z } from 'zod';

import Table from '@/components/Table';
import { trpcClient } from '@/libs/trpc/client';
import { CommonQuerySchema } from '@/types/query';

import Actions from './Actions';
import EditForm from './EditForm';

// 类型定义
export interface Dependency extends SystemDependency {
  id: number;
  type: string;
}

// 查询参数类型
export const DependencyQuerySchema = z
  .object({
    name: z.string().optional(),
    type: z.string().optional(),
  })
  .merge(CommonQuerySchema);

// 请求函数
const request: ProTableProps<Dependency, any>['request'] = async (params, sorts) => {
  if (sorts && Object.keys(sorts).length > 0) {
    const [sortField, sortOrder] = Object.entries(sorts)[0];
    params.sortField = sortField;
    params.sortOrder = sortOrder;
  }
  return trpcClient.market.dependencies.getSystemDependencies.query(params);
};

const DependencyList = memo(() => {
  const { t } = useTranslation('market');
  const [editingRecord, setEditingRecord] = useState<Dependency | null>(null);

  const tableRef = useRef<ActionType>(null);
  const columns: ProColumns<Dependency>[] = [
    {
      dataIndex: 'name',
      key: 'name',
      render: (_, record: Dependency) => {
        return (
          <Flexbox>
            {record.name}
            <div>{record.description}</div>
          </Flexbox>
        );
      },
      title: t('dependencies.table.columns.name'),
      width: 40,
    },
    {
      dataIndex: 'type',
      key: 'type',
      render: (_, record) => <Tag>{record.type}</Tag>,
      title: t('dependencies.table.columns.type'),
      width: 40,
    },
    {
      dataIndex: 'checkCommand',
      key: 'checkCommand',
      render: (_, record) => <Snippet language={'fish'}>{String(record.checkCommand)}</Snippet>,
      title: t('dependencies.table.columns.checkCommand'),
      width: 80,
    },
    {
      dataIndex: 'installInstructions',
      key: 'installInstructions',
      render: (_, record) => {
        return (
          <Highlighter language={'json'}>
            {JSON.stringify(record.installInstructions, null, 2)}
          </Highlighter>
        );
      },
      title: t('dependencies.table.columns.installInstructions'),
      width: 200,
    },

    {
      fixed: 'right',
      hideInSearch: true,
      key: 'actions',
      render: (_, record) => (
        <Actions
          entity={record}
          onEdit={() => setEditingRecord(record)}
          onSuccess={() => tableRef.current?.reload()}
        />
      ),
      title: t('dependencies.table.actions.title'),
      width: 40,
    },
  ];

  return (
    <Flexbox>
      <Table<Dependency>
        actionRef={tableRef}
        columns={columns}
        defaultPageSize={100}
        headerTitle={t('dependencies.title')}
        key={'id'}
        request={request}
        rowKey={'id'}
        scroll={{ x: 1200 }}
        search={false}
      />

      <EditForm
        initialValues={editingRecord}
        onCancel={() => setEditingRecord(null)}
        onSuccess={() => {
          tableRef.current?.reload();
        }}
        open={!!editingRecord}
      />
    </Flexbox>
  );
});

export default DependencyList;
