'use client';

import { Block, Button, Icon } from '@lobehub/ui';
import { App, Skeleton, Typography } from 'antd';
import { PlusIcon } from 'lucide-react';
import { memo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import { trpcQuery } from '@/libs/trpc/client';

import DeploymentForm from './DeploymentForm';
import DeploymentItem from './DeploymentItem';
import { DeploymentFormData } from './types';

interface DeploymentOptionsProps {
  pluginId: number;
  versionId: number;
}

const DeploymentOptions = memo<DeploymentOptionsProps>(({ pluginId, versionId }) => {
  const { t } = useTranslation('market');

  const [isFormOpen, setIsFormOpen] = useState(false);

  const { message } = App.useApp();
  const utils = trpcQuery.useUtils();

  // 获取部署选项列表
  const { data: options, isLoading } = trpcQuery.market.plugins.getDeploymentOptions.useQuery({
    pluginId,
    versionId,
  });

  // 创建部署选项
  const createDeploymentOption = trpcQuery.market.plugins.createDeploymentOption.useMutation();

  const handleAdd = () => {
    setIsFormOpen(true);
  };

  const handleFormSubmit = async (values: DeploymentFormData) => {
    try {
      // 创建新选项
      await createDeploymentOption.mutateAsync({
        data: {
          connectionArgs: values.connection.args,
          connectionCommand: values.connection.command,
          connectionType: values.connection.type,
          description: values.description || undefined,
          installationDetails: values.installationDetails,
          installationMethod: values.installationMethod,
          isRecommended: values.isRecommended,
        },
        pluginId,
        versionId,
      });
      await utils.market.plugins.getDeploymentOptions.invalidate({ pluginId, versionId });

      message.success(t('plugins.deployment.addSuccess'));
    } catch {
      message.error(t('plugins.deployment.saveError'));
    }

    setIsFormOpen(false);
  };

  return (
    <Block padding={'16px 8px 8px'}>
      <Flexbox align="center" distribution="space-between" horizontal paddingInline={8}>
        <div>
          <Typography.Title level={4}>{t('plugins.deployment.title')}</Typography.Title>
          <Typography.Text type="secondary">{t('plugins.deployment.description')}</Typography.Text>
        </div>
        <Button onClick={handleAdd} type="primary">
          <Icon icon={PlusIcon} />
          {t('plugins.deployment.add')}
        </Button>
      </Flexbox>

      <Flexbox gap={4} style={{ marginTop: 16 }}>
        {isLoading || !options ? (
          <Flexbox paddingInline={12}>
            <Skeleton active title={false} />
          </Flexbox>
        ) : options.length === 0 ? (
          <Block padding={32} style={{ textAlign: 'center' }} variant="outlined">
            <Typography.Text type="secondary">{t('plugins.deployment.empty')}</Typography.Text>
          </Block>
        ) : (
          options.map((option) => (
            <DeploymentItem
              key={option.id}
              option={option}
              pluginId={pluginId}
              versionId={versionId}
            />
          ))
        )}
      </Flexbox>

      {/* Add Form Modal */}
      <DeploymentForm
        onCancel={() => {
          setIsFormOpen(false);
        }}
        onOk={handleFormSubmit}
        open={isFormOpen}
      />
    </Block>
  );
});

export default DeploymentOptions;
