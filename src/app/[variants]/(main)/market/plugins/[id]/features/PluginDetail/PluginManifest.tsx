import { PluginManifest } from '@lobehub/market-types';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>er, Icon, Tabs, Tag } from '@lobehub/ui';
import { Card, Descriptions, Space, Typography } from 'antd';
import { LayersIcon, NotepadText, PocketKnife } from 'lucide-react';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

interface ManifestProps {
  manifest: PluginManifest;
}

const Manifest = memo<ManifestProps>(({ manifest }) => {
  const { t } = useTranslation('market');

  return (
    <Card>
      <Flexbox>
        <Flexbox gap={8} horizontal>
          <Typography.Title level={5}>{t('plugins.manifest.title')}</Typography.Title>
        </Flexbox>

        <Tabs
          items={[
            {
              children: (
                <Flexbox gap={24}>
                  <div>
                    <Typography.Title level={5}>{t('plugins.manifest.basicInfo')}</Typography.Title>
                    <Descriptions
                      column={1}
                      items={[
                        {
                          children: manifest.name,
                          key: 'name',
                          label: t('plugins.manifest.fields.name'),
                        },
                        {
                          children: manifest.version,
                          key: 'version',
                          label: t('plugins.manifest.fields.version'),
                        },
                        {
                          children: manifest.author?.name,
                          key: 'author',
                          label: t('plugins.manifest.fields.author'),
                        },
                        {
                          children: manifest.description,
                          key: 'description',
                          label: t('plugins.manifest.fields.description'),
                        },
                      ]}
                    />
                  </div>

                  <div>
                    <Typography.Title level={5}>
                      {t('plugins.manifest.contentSummary')}
                    </Typography.Title>
                    <Space wrap>
                      {Array.isArray(manifest.tools) && manifest.tools.length > 0 && (
                        <Tag icon={<Icon icon={PocketKnife} />}>{manifest.tools.length}</Tag>
                      )}
                      {Array.isArray(manifest.resources) && manifest.resources.length > 0 && (
                        <Tag icon={<Icon icon={LayersIcon} />}>{manifest.resources.length}</Tag>
                      )}
                      {Array.isArray(manifest.prompts) && manifest.prompts.length > 0 && (
                        <Tag icon={<Icon icon={NotepadText} />}>{manifest.prompts.length}</Tag>
                      )}
                    </Space>
                  </div>
                </Flexbox>
              ),
              key: 'overview',
              label: t('plugins.manifest.tabs.overview'),
            },
            {
              children:
                Array.isArray(manifest.tools) && manifest.tools.length > 0 ? (
                  <Collapse
                    gap={4}
                    items={manifest.tools.map((item) => ({
                      children: (
                        <Highlighter language={'json'} variant={'borderless'}>
                          {JSON.stringify(item.inputSchema, null, 2)}
                        </Highlighter>
                      ),
                      desc: item.description,
                      label: item.name,
                    }))}
                  />
                ) : (
                  <Typography.Text
                    style={{ display: 'block', padding: '32px', textAlign: 'center' }}
                    type="secondary"
                  >
                    {t('plugins.manifest.noTools')}
                  </Typography.Text>
                ),
              icon: <Icon icon={PocketKnife} />,
              key: 'tools',
              label: t('plugins.manifest.tabs.tools'),
            },
            {
              children:
                Array.isArray(manifest.prompts) && manifest.prompts.length > 0 ? (
                  <Collapse
                    gap={4}
                    items={manifest.prompts.map((item) => ({
                      children: (
                        <Highlighter language={'json'} variant={'borderless'}>
                          {JSON.stringify(item.arguments, null, 2)}
                        </Highlighter>
                      ),
                      desc: item.description,
                      label: item.name,
                    }))}
                  />
                ) : (
                  <Typography.Text
                    style={{ display: 'block', padding: '32px', textAlign: 'center' }}
                    type="secondary"
                  >
                    {t('plugins.manifest.noPrompts')}
                  </Typography.Text>
                ),
              icon: <Icon icon={NotepadText} />,
              key: 'prompts',
              label: t('plugins.manifest.tabs.prompts'),
            },
            {
              children:
                Array.isArray(manifest.resources) && manifest.resources.length > 0 ? (
                  <Flexbox gap={16}>
                    {manifest.resources.map((resource: any, index: number) => (
                      <Card key={index}>
                        <Typography.Title level={5}>
                          {resource.name || `Resource ${index + 1}`}
                        </Typography.Title>
                        {resource.description && (
                          <Typography.Paragraph>{resource.description}</Typography.Paragraph>
                        )}
                      </Card>
                    ))}
                  </Flexbox>
                ) : (
                  <Typography.Text
                    style={{ display: 'block', padding: '32px', textAlign: 'center' }}
                    type="secondary"
                  >
                    {t('plugins.manifest.noResources')}
                  </Typography.Text>
                ),
              icon: <Icon icon={LayersIcon} />,
              key: 'resources',
              label: t('plugins.manifest.tabs.resources'),
            },
            {
              children: (
                <Highlighter language={'json'}>{JSON.stringify(manifest, null, 2)}</Highlighter>
              ),
              key: 'raw',
              label: t('plugins.manifest.tabs.raw'),
            },
          ]}
        />
      </Flexbox>
    </Card>
  );
});

export default Manifest;
