'use client';

import { ActionIcon, Block, Dropdown, Icon, Tabs, Tag } from '@lobehub/ui';
import { Badge, Button, Descriptions, Skeleton, Space, Typography } from 'antd';
import {
  EyeIcon,
  HeartIcon,
  HistoryIcon,
  InfoIcon,
  LockIcon,
  MoreHorizontalIcon,
  StarIcon,
  TagIcon,
  TrashIcon,
  UnlockIcon,
  UserIcon,
} from 'lucide-react';
import Image from 'next/image';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import { trpcQuery } from '@/libs/trpc/client';

import DeploymentOptions from './DeploymentOptions';
import PluginManifest from './PluginManifest';
import PluginStats from './PluginStats';
import VersionHistory from './VersionHistory';

interface PluginDetailProps {
  id: string;
}

const PluginDetail = memo<PluginDetailProps>(({ id }) => {
  const { t } = useTranslation('market');

  const { data: plugin, isLoading } = trpcQuery.market.plugins.getPlugin.useQuery({ id });

  if (isLoading || !plugin) {
    return <Skeleton active title={false} />;
  }

  const latestVersion = plugin.versions.find((v) => v.isLatest) || plugin.versions[0];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'published': {
        return <Badge status="success" text={t('plugins.status.published')} />;
      }
      default: {
        // @ts-ignore
        return <Badge status="default" text={t(`plugins.status.${status}`)} />;
      }
    }
  };

  const getVisibilityBadge = (visibility: string) => {
    switch (visibility) {
      case 'public': {
        return <Badge status="processing" text={t('plugins.visibility.public')} />;
      }
      case 'private': {
        return (
          <Badge
            status="default"
            text={
              <Space>
                <LockIcon size={14} />
                {t('plugins.visibility.private')}
              </Space>
            }
          />
        );
      }
      case 'unlisted': {
        return (
          <Badge
            status="default"
            text={
              <Space>
                <EyeIcon size={14} />
                {t('plugins.visibility.unlisted')}
              </Space>
            }
          />
        );
      }
      default: {
        return <Badge status="default" text={visibility} />;
      }
    }
  };

  return (
    <Flexbox>
      {/* Header */}
      <Flexbox align="center" gap={16} horizontal width={'100%'}>
        {plugin.icon ? (
          <Image
            alt={'image'}
            height={64}
            src={plugin.icon}
            style={{ borderRadius: 8 }}
            unoptimized
            width={64}
          />
        ) : (
          <div
            style={{
              alignItems: 'center',
              borderRadius: 8,
              display: 'flex',
              height: 64,
              justifyContent: 'center',
              width: 64,
            }}
          >
            <Icon icon="code" size={32} />
          </div>
        )}

        <Flexbox align={'center'} distribution="space-between" gap={8} horizontal width={'100%'}>
          <Flexbox gap={8}>
            <Flexbox align={'center'} gap={8} horizontal>
              <Typography.Title level={4} style={{ margin: 0 }}>
                {plugin.name}
              </Typography.Title>
              {plugin.isFeatured && <Badge status="processing" text={t('plugins.featured')} />}
              {getStatusBadge(plugin.status)}
              {getVisibilityBadge(plugin.visibility)}
            </Flexbox>
            <Typography.Text type="secondary">
              <Typography.Text code>{plugin.identifier}</Typography.Text> • v{latestVersion.version}
            </Typography.Text>
          </Flexbox>
          <Space>
            <Button href={`/market/plugins/${plugin.id}/edit`} type="primary">
              {t('plugins.actions.edit')}
            </Button>
            <Dropdown
              menu={{
                items: [
                  {
                    icon: <Icon icon={plugin.status === 'published' ? EyeIcon : EyeIcon} />,
                    key: 'status',
                    label:
                      plugin.status === 'published'
                        ? t('plugins.actions.unpublish')
                        : t('plugins.actions.publish'),
                  },
                  {
                    icon: <Icon icon={plugin.visibility === 'public' ? LockIcon : UnlockIcon} />,
                    key: 'visibility',
                    label:
                      plugin.visibility === 'public'
                        ? t('plugins.actions.makePrivate')
                        : t('plugins.actions.makePublic'),
                  },
                  {
                    danger: true,
                    icon: <Icon icon={TrashIcon} />,
                    key: 'delete',
                    label: t('plugins.actions.delete'),
                  },
                ],
              }}
              trigger={['click']}
            >
              <ActionIcon icon={MoreHorizontalIcon} />
            </Dropdown>
          </Space>
        </Flexbox>
      </Flexbox>

      {/* Main content */}
      <Flexbox gap={24} horizontal style={{ marginTop: 12 }}>
        <Tabs
          items={[
            {
              children: <DeploymentOptions pluginId={plugin.id} versionId={latestVersion?.id} />,
              key: 'deployment',
              label: t('plugins.tabs.deployment'),
            },
            {
              children: (
                <Flexbox gap={24}>
                  <Flexbox gap={8}>
                    <Typography.Title level={5}>
                      {t('plugins.details.description')}
                    </Typography.Title>

                    <Typography.Paragraph style={{ marginBottom: 0 }} type={'secondary'}>
                      {plugin.description}
                    </Typography.Paragraph>
                  </Flexbox>

                  <Descriptions
                    column={2}
                    items={[
                      {
                        children: plugin.category,
                        key: 'category',
                        label: t('plugins.details.category'),
                      },
                      {
                        children: plugin.author || t('plugins.details.notSpecified'),
                        key: 'author',
                        label: t('plugins.details.author'),
                      },
                      {
                        children: new Date(plugin.createdAt).toLocaleDateString(),
                        key: 'created',
                        label: t('plugins.details.created'),
                      },
                      {
                        children: new Date(plugin.updatedAt).toLocaleDateString(),
                        key: 'updated',
                        label: t('plugins.details.updated'),
                      },
                    ]}
                  />

                  <Flexbox gap={8}>
                    <Typography.Title level={5}>{t('plugins.details.tags')}</Typography.Title>
                    <Space wrap>
                      {plugin.tags?.map((tag) => (
                        <Tag icon={<TagIcon size={14} />} key={tag}>
                          {tag}
                        </Tag>
                      ))}
                    </Space>
                  </Flexbox>
                </Flexbox>
              ),
              key: 'overview',
              label: t('plugins.tabs.overview'),
            },
            {
              children: <PluginManifest manifest={plugin.manifest || {}} />,
              key: 'manifest',
              label: t('plugins.tabs.manifest'),
            },
            {
              children: (
                <PluginStats
                  installCount={plugin.installCount}
                  ratingAverage={plugin.ratingAverage}
                  ratingCount={plugin.ratingCount}
                  reviewCount={plugin.ratingCount}
                />
              ),
              key: 'stats',
              label: t('plugins.tabs.stats'),
            },
            {
              children: <VersionHistory pluginId={plugin.id} versions={plugin.versions} />,
              key: 'versions',
              label: t('plugins.tabs.versions'),
            },
          ]}
          style={{ flex: 3 }}
        />

        <Block padding={24} style={{ flex: 1, marginTop: 12 }} variant={'outlined'}>
          <Typography.Title level={5}>{t('plugins.stats.title')}</Typography.Title>
          <Flexbox gap={16}>
            <Descriptions
              column={1}
              items={[
                {
                  children: plugin.installCount?.toLocaleString(),
                  key: 'installs',
                  label: (
                    <Space>
                      <Icon icon={StarIcon} />
                      {t('plugins.stats.installs')}
                    </Space>
                  ),
                },
                {
                  children: plugin.ratingAverage ? (
                    <Space>
                      {plugin.ratingAverage.toFixed(1)}
                      <Typography.Text type="secondary">
                        ({plugin.ratingCount} {t('plugins.stats.ratings')})
                      </Typography.Text>
                    </Space>
                  ) : (
                    t('plugins.stats.noRatings')
                  ),
                  key: 'rating',
                  label: (
                    <Space>
                      <Icon icon={HeartIcon} />
                      {t('plugins.stats.rating')}
                    </Space>
                  ),
                },
                {
                  children: plugin.ratingCount,
                  key: 'reviews',
                  label: (
                    <Space>
                      <Icon icon={HistoryIcon} />
                      {t('plugins.stats.reviews')}
                    </Space>
                  ),
                },
                {
                  children: plugin.ownerId,
                  key: 'owner',
                  label: (
                    <Space>
                      <Icon icon={UserIcon} />
                      {t('plugins.stats.owner')}
                    </Space>
                  ),
                },
                {
                  children: plugin.id,
                  key: 'pluginId',
                  label: (
                    <Space>
                      <Icon icon={InfoIcon} />
                      {t('plugins.stats.pluginId')}
                    </Space>
                  ),
                },
                {
                  children: new Date(plugin.createdAt).toLocaleDateString(),
                  key: 'created',
                  label: (
                    <Space>
                      <Icon icon={HistoryIcon} />
                      {t('plugins.stats.created')}
                    </Space>
                  ),
                },
                {
                  children: new Date(plugin.updatedAt).toLocaleDateString(),
                  key: 'updated',
                  label: (
                    <Space>
                      <Icon icon={HistoryIcon} />
                      {t('plugins.stats.updated')}
                    </Space>
                  ),
                },
              ]}
            />
          </Flexbox>

          {/*<Flexbox gap={8} style={{ marginTop: 16 }}>*/}
          {/*  <Button block href={`/market/plugins/${plugin.id}/edit`}>*/}
          {/*    {t('plugins.actions.editDetails')}*/}
          {/*  </Button>*/}
          {/*  <Button block href={`/market/plugins/${plugin.id}/versions/new`}>*/}
          {/*    {t('plugins.actions.addVersion')}*/}
          {/*  </Button>*/}
          {/*  <Button block danger href={`/market/plugins/${plugin.id}/delete`}>*/}
          {/*    {t('plugins.actions.delete')}*/}
          {/*  </Button>*/}
          {/*</Flexbox>*/}
        </Block>
      </Flexbox>
    </Flexbox>
  );
});

export default PluginDetail;
