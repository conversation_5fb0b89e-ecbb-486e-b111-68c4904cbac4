import { <PERSON><PERSON>, Button, Card, Space, Typography } from 'antd';
import { CheckIcon, ClockIcon, FileJsonIcon } from 'lucide-react';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

interface Version {
  createdAt: string;
  id: number;
  isLatest: boolean;
  isValidated: boolean;
  manifestUrl: string | null;
  meta: Record<string, any> | null;
  pluginId: number;
  promptsCount: number;
  resourcesCount: number;
  toolsCount: number;
  updatedAt: string;
  version: string;
}

interface VersionHistoryProps {
  pluginId: number;
  versions: Version[];
}

const VersionHistory = memo<VersionHistoryProps>(({ versions, pluginId }) => {
  const { t } = useTranslation('market');

  return (
    <Card>
      <Flexbox gap={16}>
        <Flexbox gap={8} horizontal>
          <Typography.Title level={5}>{t('plugins.versions.title')}</Typography.Title>
          <Typography.Text type="secondary">{t('plugins.versions.description')}</Typography.Text>
        </Flexbox>

        <Flexbox gap={16}>
          {versions.map((version) => (
            <Card hoverable key={version.id}>
              <Flexbox gap={16}>
                <Flexbox gap={8} horizontal>
                  <Typography.Title level={5}>v{version.version}</Typography.Title>
                  {version.isLatest && (
                    <Badge status="success" text={t('plugins.versions.latest')} />
                  )}
                  {version.isValidated && (
                    <Badge
                      status="success"
                      text={
                        <Space>
                          <CheckIcon size={14} />
                          {t('plugins.versions.validated')}
                        </Space>
                      }
                    />
                  )}
                </Flexbox>

                <Typography.Text type="secondary">
                  <Space>
                    <ClockIcon size={14} />
                    {new Date(version.createdAt).toLocaleDateString()}
                  </Space>
                </Typography.Text>

                {version.meta?.releaseNotes && (
                  <Typography.Paragraph>{version.meta.releaseNotes}</Typography.Paragraph>
                )}

                <Flexbox gap={8} horizontal>
                  <Typography.Text type="secondary">
                    {t('plugins.versions.tools')}: {version.toolsCount}
                  </Typography.Text>
                  <Typography.Text type="secondary">
                    {t('plugins.versions.prompts')}: {version.promptsCount}
                  </Typography.Text>
                  <Typography.Text type="secondary">
                    {t('plugins.versions.resources')}: {version.resourcesCount}
                  </Typography.Text>
                </Flexbox>

                <Flexbox gap={8} horizontal>
                  {version.manifestUrl && (
                    <Button
                      href={version.manifestUrl}
                      icon={<FileJsonIcon size={14} />}
                      size="small"
                      target="_blank"
                    >
                      {t('plugins.versions.viewManifest')}
                    </Button>
                  )}
                  <Button href={`/market/plugins/${pluginId}/versions/${version.id}`} size="small">
                    {t('plugins.versions.view')}
                  </Button>
                </Flexbox>
              </Flexbox>
            </Card>
          ))}

          <Button href={`/market/plugins/${pluginId}/versions/new`} type="primary">
            {t('plugins.versions.addNew')}
          </Button>
        </Flexbox>
      </Flexbox>
    </Card>
  );
});

export default VersionHistory;
