'use client';

import { AdminDeploymentOption } from '@lobehub/market-types';
import { ActionIcon, Block, Dropdown, Icon, Tag } from '@lobehub/ui';
import { App, Space, Typography } from 'antd';
import {
  CheckIcon,
  CodeIcon,
  EditIcon,
  MoreHorizontalIcon,
  TerminalIcon,
  TrashIcon,
} from 'lucide-react';
import { memo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import { trpcQuery } from '@/libs/trpc/client';

import DeploymentForm from './DeploymentForm';
import InstallationMethodIcon from './DepsIcon';

interface DeploymentItemProps {
  option: AdminDeploymentOption;
  pluginId: number;
  versionId: number;
}

const renderCommandLine = (option: AdminDeploymentOption) => {
  const { command, args } = option.connection;
  return `${command} ${args?.join(' ')}`;
};

const DeploymentItem = memo<DeploymentItemProps>(({ option, versionId, pluginId }) => {
  const { t } = useTranslation('market');
  const [isFormOpen, setIsFormOpen] = useState(false);

  const { modal, message } = App.useApp();
  const utils = trpcQuery.useUtils();

  // 删除部署选项
  const deleteDeploymentOption = trpcQuery.market.plugins.deleteDeploymentOption.useMutation();

  // 更新部署选项
  const updateDeploymentOption = trpcQuery.market.plugins.updateDeploymentOption.useMutation({});

  const getConnectionTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'stdio': {
        return <Icon icon={TerminalIcon} />;
      }
      default: {
        return <Icon icon={CodeIcon} />;
      }
    }
  };

  const handleDelete = async () => {
    modal.confirm({
      content: t('plugins.deployment.deleteConfirmMessage'),
      okButtonProps: {
        danger: true,
      },
      onOk: async () => {
        await deleteDeploymentOption.mutateAsync({
          optionId: option.id,
          pluginId,
          versionId,
        });

        await utils.market.plugins.getDeploymentOptions.invalidate({ pluginId, versionId });

        message.success(t('plugins.deployment.deleteSuccess'));
      },
      title: t('plugins.deployment.confirmDelete'),
    });
  };

  const handleFormSubmit = async (editedOption: any) => {
    try {
      // 更新现有选项
      await updateDeploymentOption.mutateAsync({
        data: {
          connectionArgs: editedOption.connection.args,
          connectionCommand: editedOption.connection.command,
          connectionType: editedOption.connection.type,
          description: editedOption.description || undefined,
          installationDetails: editedOption.installationDetails,
          installationMethod: editedOption.installationMethod,
          isRecommended: editedOption.isRecommended,
        },
        optionId: option.id,
        pluginId,
        versionId,
      });
      await utils.market.plugins.getDeploymentOptions.invalidate({ pluginId, versionId });

      message.success(t('plugins.deployment.updateSuccess'));
      setIsFormOpen(false);
    } catch {
      message.error(t('plugins.deployment.saveError'));
    }
  };

  return (
    <Block padding={16} variant="outlined">
      <Flexbox distribution="space-between" horizontal>
        <Flexbox gap={8}>
          <Flexbox align="center" gap={8} horizontal>
            <InstallationMethodIcon method={option.installationMethod} size={16} />

            <Typography.Title level={5} style={{ margin: 0 }}>
              {option.installationMethod.toUpperCase()} {t('plugins.deployment.installation')}
            </Typography.Title>
            {option.isRecommended && (
              <Tag color="success">
                <Icon icon={CheckIcon} />
                {t('plugins.deployment.recommended')}
              </Tag>
            )}
          </Flexbox>

          {option.description && (
            <Typography.Text type="secondary">{option.description}</Typography.Text>
          )}

          <Flexbox gap={12} horizontal>
            <Typography.Text>
              <Typography.Text type="secondary">
                {t('plugins.deployment.connectionType')}:
              </Typography.Text>{' '}
              <Space>
                {getConnectionTypeIcon(option.connection.type)}
                {option.connection.type}
              </Space>
            </Typography.Text>
          </Flexbox>
          <Flexbox gap={12} horizontal>
            {option.installationDetails?.packageName && (
              <Flexbox gap={4} horizontal>
                <Typography.Text type="secondary">
                  {t('plugins.deployment.packageName')}:
                </Typography.Text>{' '}
                <Tag>{option.installationDetails.packageName}</Tag>
              </Flexbox>
            )}

            <Flexbox gap={4} horizontal>
              <Typography.Text type="secondary">
                {t('plugins.deployment.commandLine')}:
              </Typography.Text>{' '}
              <Tag>{renderCommandLine(option)}</Tag>
            </Flexbox>
          </Flexbox>

          {option.systemDependencies && option.systemDependencies.length > 0 && (
            <Typography.Text>
              <Typography.Text type="secondary">
                {t('plugins.deployment.systemDependencies')}:
              </Typography.Text>{' '}
              <Space>
                {option.systemDependencies.map((dep, i) => (
                  <Tag key={i}>{dep.name}</Tag>
                ))}
              </Space>
            </Typography.Text>
          )}
        </Flexbox>

        <Dropdown
          menu={{
            items: [
              {
                icon: <Icon icon={EditIcon} />,
                key: 'edit',
                label: t('plugins.deployment.edit'),
                onClick: () => {
                  setIsFormOpen(true);
                },
              },
              {
                danger: true,
                icon: <Icon icon={TrashIcon} />,
                key: 'delete',
                label: t('plugins.deployment.delete'),
                onClick: handleDelete,
              },
            ],
          }}
          trigger={['click']}
        >
          <ActionIcon icon={MoreHorizontalIcon} />
        </Dropdown>
      </Flexbox>

      <DeploymentForm
        data={option}
        isEdit
        onCancel={() => {
          setIsFormOpen(false);
        }}
        onOk={handleFormSubmit}
        open={isFormOpen}
      />
    </Block>
  );
});

export default DeploymentItem;
