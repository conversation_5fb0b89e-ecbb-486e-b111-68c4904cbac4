'use client';

import { Block, Modal } from '@lobehub/ui';
import { Form, Input, Radio, Select, Switch, Typography } from 'antd';
import { memo, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import { DeploymentFormData } from './types';

interface DeploymentFormProps {
  data?: DeploymentFormData;
  isEdit?: boolean;
  onCancel: () => void;
  onOk: (values: DeploymentFormData) => Promise<void>;
  open: boolean;
}

const InstallationMethodOptions = [
  { label: 'NPM', value: 'npm' },
  { label: 'NPX', value: 'npx' },
  { label: 'UV', value: 'uv' },
  { label: 'Python', value: 'python' },
  { label: 'Docker', value: 'docker' },
  { label: 'Go', value: 'go' },
];

const ConnectionTypeOptions = [
  { label: 'Stdio', value: 'stdio' },
  { label: 'Streamable HTTP', value: 'http' },
];

const DeploymentForm = memo<DeploymentFormProps>(({ open, isEdit, data, onCancel, onOk }) => {
  const { t } = useTranslation('market');
  const [form] = Form.useForm();

  const [submitting, setSubmitting] = useState(false);
  const handleOk = async () => {
    try {
      setSubmitting(true);
      const values = await form.validateFields();
      await onOk(values);
      setSubmitting(false);
    } catch {
      // 表单验证失败
    }
  };

  const installationMethod = Form.useWatch('installationMethod', form);
  const showPackageName = useMemo(() => {
    return ['npm', 'python'].includes(installationMethod);
  }, [installationMethod]);

  return (
    <Modal
      cancelText={t('plugins.deployment.cancel')}
      okButtonProps={{
        loading: submitting,
      }}
      okText={t('plugins.deployment.save')}
      onCancel={onCancel}
      onOk={handleOk}
      open={open}
      title={isEdit ? t('plugins.deployment.editTitle') : t('plugins.deployment.addTitle')}
    >
      <Form form={form} initialValues={data} layout="vertical">
        <Flexbox gap={24}>
          {/* 安装方式分组 */}
          <div>
            <Typography.Title level={5}>{t('plugins.deployment.installation')}</Typography.Title>
            <Block gap={16} padding={8}>
              <Form.Item
                label={t('plugins.deployment.installationMethod')}
                name="installationMethod"
                rules={[
                  { message: t('plugins.deployment.installationMethodRequired'), required: true },
                ]}
              >
                <Select options={InstallationMethodOptions} />
              </Form.Item>

              {showPackageName && (
                <Form.Item
                  label={t('plugins.deployment.packageName')}
                  name={['installationDetails', 'packageName']}
                  rules={[{ message: t('plugins.deployment.packageNameRequired'), required: true }]}
                >
                  <Input />
                </Form.Item>
              )}

              <Form.Item
                label={t('plugins.deployment.recommended')}
                name="isRecommended"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
              <Form.Item
                label={t('plugins.deployment.description')}
                name="description"
                style={{ marginBottom: 0 }}
              >
                <Input.TextArea placeholder={t('plugins.deployment.descriptionPlaceholder')} />
              </Form.Item>
            </Block>
          </div>

          {/* 连接方式分组 */}
          <div>
            <Typography.Title level={5}>{t('plugins.deployment.connection')}</Typography.Title>
            <Block gap={16} padding={8}>
              <Form.Item
                label={t('plugins.deployment.connectionType')}
                name={['connection', 'type']}
                rules={[
                  { message: t('plugins.deployment.connectionTypeRequired'), required: true },
                ]}
              >
                <Radio.Group options={ConnectionTypeOptions} />
              </Form.Item>

              <Form.Item
                label={t('plugins.deployment.command')}
                name={['connection', 'command']}
                rules={[{ message: t('plugins.deployment.commandRequired'), required: true }]}
              >
                <Input />
              </Form.Item>

              <Form.Item
                getValueFromEvent={(e) =>
                  e.target.value.split(' ').filter((arg: string) => arg.trim() !== '')
                }
                getValueProps={(value) => ({ value: value?.join(' ') || '' })}
                label={t('plugins.deployment.args')}
                name={['connection', 'args']}
                style={{ marginBottom: 0 }}
              >
                <Input placeholder={t('plugins.deployment.argsPlaceholder')} />
              </Form.Item>
            </Block>
          </div>
        </Flexbox>
      </Form>
    </Modal>
  );
});

export default DeploymentForm;
