import { Card, Typography } from 'antd';
import { DownloadIcon, HeartIcon, StarIcon } from 'lucide-react';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

interface PluginStatsProps {
  installCount?: number;
  ratingAverage?: number | null;
  ratingCount?: number;
  reviewCount?: number;
}

const PluginStats = memo<PluginStatsProps>(
  ({ installCount, ratingAverage, ratingCount, reviewCount }) => {
    const { t } = useTranslation('market');

    return (
      <Flexbox gap={24}>
        <Card>
          <Typography.Title level={5}>{t('plugins.stats.installationStats')}</Typography.Title>
          <Flexbox gap={16}>
            <div
              style={{
                alignItems: 'center',
                background: '#f5f5f5',
                borderRadius: '8px',
                display: 'flex',
                flexDirection: 'column',
                padding: '16px',
                width: '100%',
              }}
            >
              <DownloadIcon size={32} style={{ color: '#1890ff', marginBottom: '8px' }} />
              <Typography.Title level={3} style={{ margin: 0 }}>
                {installCount?.toLocaleString()}
              </Typography.Title>
              <Typography.Text type="secondary">{t('plugins.stats.totalInstalls')}</Typography.Text>
            </div>

            {/* Installation trend chart placeholder */}
            <div
              style={{
                alignItems: 'center',
                background: '#f5f5f5',
                borderRadius: '8px',
                display: 'flex',
                height: '192px',
                justifyContent: 'center',
              }}
            >
              <Typography.Text type="secondary">
                {t('plugins.stats.installationTrend')}
              </Typography.Text>
            </div>
          </Flexbox>
        </Card>

        <Card>
          <Typography.Title level={5}>{t('plugins.stats.ratingsAndReviews')}</Typography.Title>
          <Flexbox gap={16}>
            <Flexbox gap={16} horizontal>
              <div
                style={{
                  alignItems: 'center',
                  background: '#f5f5f5',
                  borderRadius: '8px',
                  display: 'flex',
                  flex: 1,
                  flexDirection: 'column',
                  padding: '16px',
                }}
              >
                <StarIcon size={24} style={{ color: '#faad14', marginBottom: '8px' }} />
                <Typography.Title level={3} style={{ margin: 0 }}>
                  {ratingAverage ? ratingAverage.toFixed(1) : '-'}
                </Typography.Title>
                <Typography.Text type="secondary">
                  {ratingCount} {t('plugins.stats.ratings')}
                </Typography.Text>
              </div>

              <div
                style={{
                  alignItems: 'center',
                  background: '#f5f5f5',
                  borderRadius: '8px',
                  display: 'flex',
                  flex: 1,
                  flexDirection: 'column',
                  padding: '16px',
                }}
              >
                <HeartIcon size={24} style={{ color: '#f5222d', marginBottom: '8px' }} />
                <Typography.Title level={3} style={{ margin: 0 }}>
                  {reviewCount}
                </Typography.Title>
                <Typography.Text type="secondary">{t('plugins.stats.reviews')}</Typography.Text>
              </div>
            </Flexbox>

            {/* Rating distribution chart placeholder */}
            <div>
              <Typography.Text strong>{t('plugins.stats.ratingDistribution')}</Typography.Text>
              <div
                style={{
                  alignItems: 'center',
                  background: '#f5f5f5',
                  borderRadius: '8px',
                  display: 'flex',
                  height: '128px',
                  justifyContent: 'center',
                  marginTop: '8px',
                }}
              >
                <Typography.Text type="secondary">
                  {t('plugins.stats.ratingDistributionChart')}
                </Typography.Text>
              </div>
            </div>
          </Flexbox>
        </Card>
      </Flexbox>
    );
  },
);

export default PluginStats;
