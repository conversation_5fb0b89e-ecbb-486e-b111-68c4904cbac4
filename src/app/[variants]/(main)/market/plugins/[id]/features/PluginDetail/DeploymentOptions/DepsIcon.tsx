// 定义预设的命令选项
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>tjs,
  <PERSON>N<PERSON>,
  SiPnpm,
  SiPyt<PERSON>,
} from '@icons-pack/react-simple-icons';
import { Icon } from '@lobehub/ui';
import { DownloadIcon } from 'lucide-react';
import { FC, memo } from 'react';

const SystemDependenciesOptions: {
  // 假设图标是 React 函数组件
  color?: string;
  icon?: FC<{ color?: string; size?: number }>;
  size?: number;
  value: string;
}[] = [
  { color: '#CB3837', icon: SiNpm, value: 'npx' },
  { color: '#CB3837', icon: SiNpm, value: 'npm' },
  { color: '#F69220', icon: SiPnpm, value: 'pnpm' },
  { color: '#F69220', icon: SiPnpm, value: 'pnpx' },
  { color: '#339933', icon: SiNodedotjs, value: 'node' },
  { color: '#efe2d2', icon: Si<PERSON><PERSON>, value: 'bun' },
  { color: '#efe2d2', icon: SiB<PERSON>, value: 'bunx' },
  { color: '#DE5FE9', icon: SiPython, value: 'uv' },
  { color: '#3776AB', icon: SiPython, value: 'python' },
  { color: '#2496ED', icon: SiDocker, value: 'docker' },
  { color: '#01ade0', icon: SiGo, size: 24, value: 'go' },
];

const InstallationMethodIcon = memo<{ method: string; size?: number }>(({ method, size }) => {
  const item = SystemDependenciesOptions.find((item) => method === item.value);

  if (item) {
    return <Icon color={item.color} fill={item.color} icon={item.icon} size={item.size || size} />;
  }

  return <Icon icon={DownloadIcon} />;
});

export default InstallationMethodIcon;
