'use client';

import { ModelIcon } from '@lobehub/icons';
import { Tag } from '@lobehub/ui';
import { Button, Card, Descriptions, Space, Table, message } from 'antd';
import { ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { memo, use, useEffect, useState } from 'react';
import { Flexbox } from 'react-layout-kit';

import { trpcClient } from '@/libs/trpc/client';
import { AdminModelSelectItem } from '@/types/adminInfra';
import { AggregatedModelWithAssociationModels } from '@/types/aggregatedModel';

import AddChannelModelModal from '../AddChannelModelModal';

interface EditChannelModelData {
  aggregatedModelId: string;
  infraModelId: string;
  infraProviderId: string;
  litellmModelId: string;
  weight?: number | null;
}

interface ModelDetailProps {
  params: Promise<{ id: string }>;
}

const ModelDetail = memo<ModelDetailProps>(({ params }) => {
  const { id } = use(params);
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [modelDetail, setModelDetail] = useState<AggregatedModelWithAssociationModels | null>(null);
  const [addChannelModalOpen, setAddChannelModalOpen] = useState(false);
  const [editChannelModalOpen, setEditChannelModalOpen] = useState(false);
  const [editChannelData, setEditChannelData] = useState<EditChannelModelData | null>(null);

  // 加载模型详情信息
  const loadModelDetail = async () => {
    if (!id) return;

    try {
      setLoading(true);
      const result = await trpcClient.aggregatedModel.getModelInfo.query(id);
      if (result.success && result.data) {
        setModelDetail(result.data);
      } else {
        message.error('模型不存在');
        router.push('/model-management/list');
      }
    } catch (error: any) {
      message.error(`加载模型详情失败: ${error.message}`);
      router.push('/model-management/list');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadModelDetail();
  }, [id]);

  // 编辑关联模型
  const handleEditAssociation = async (record: AdminModelSelectItem) => {
    if (!modelDetail) return;

    try {
      // 获取关联模型的详细信息
      const result = await trpcClient.aggregatedModel.getInfraModelRelationDetail.query({
        aggregatedModelId: modelDetail.id,
        infraModelId: record.id,
        infraProviderId: record.providerId,
      });

      if (result.success && result.data) {
        setEditChannelData({
          aggregatedModelId: result.data.aggregatedModelId,
          infraModelId: result.data.infraModelId,
          infraProviderId: result.data.infraProviderId,
          litellmModelId: result.data.litellmModelId,
          weight: result.data.litellmParams?.weight,
        });
        setEditChannelModalOpen(true);
      } else {
        message.error(result.error || '获取关联信息失败');
      }
    } catch (error: any) {
      message.error(`获取关联信息失败: ${error.message}`);
    }
  };

  // 删除关联模型
  const handleDeleteAssociation = async (record: AdminModelSelectItem) => {
    if (!modelDetail) return;

    try {
      const result = await trpcClient.aggregatedModel.deleteInfraModelRelation.mutate({
        aggregatedModelId: modelDetail.id,
        infraModelId: record.id,
        infraProviderId: record.providerId,
      });

      if (result.success) {
        message.success('关联模型删除成功');
        loadModelDetail(); // 重新加载数据
      } else {
        message.error(result.error || '删除关联模型失败');
      }
    } catch (error: any) {
      message.error(`删除关联失败: ${error.message}`);
    }
  };

  // 关联模型表格列定义
  const associatedModelColumns = [
    {
      dataIndex: 'id',
      key: 'id',
      render: (value: string) => (
        <Flexbox align="center" gap={8} horizontal>
          <ModelIcon model={value} size={16} />
          <span>{value}</span>
        </Flexbox>
      ),
      title: '模型ID',
    },
    {
      dataIndex: 'displayName',
      key: 'displayName',
      render: (value: string, record: AdminModelSelectItem) => value || record.id,
      title: '显示名称',
    },
    {
      dataIndex: 'weight',
      key: 'weight',
      render: (value: number | null) => value || '--',
      title: '权重',
    },
    {
      dataIndex: 'balance',
      key: 'balance',
      render: (value: string | null) => value || '--',
      title: '负载均衡',
    },
    {
      dataIndex: 'enabled',
      key: 'enabled',
      render: (enabled: boolean) => (
        <Tag color={enabled ? 'success' : 'default'}>{enabled ? '启用' : '禁用'}</Tag>
      ),
      title: '状态',
    },
    {
      key: 'actions',
      render: (_: any, record: AdminModelSelectItem) => (
        <Space size="small">
          <Button onClick={() => handleEditAssociation(record)} size="small" type="link">
            编辑
          </Button>
          <Button danger onClick={() => handleDeleteAssociation(record)} size="small" type="link">
            删除关联
          </Button>
        </Space>
      ),
      title: '操作',
    },
  ];

  if (loading) {
    return <div style={{ padding: 24, textAlign: 'center' }}>加载中...</div>;
  }

  if (!modelDetail) {
    return null;
  }

  return (
    <Flexbox gap={24} padding={24}>
      {/* 页面头部 */}
      <Flexbox gap={16} horizontal>
        <Button
          icon={<ArrowLeft size={16} />}
          onClick={() => router.push('/model-management/list')}
        >
          返回列表
        </Button>
        <Flexbox align="center" gap={8} horizontal>
          <ModelIcon model={modelDetail.id} size={32} />
          <h1 style={{ margin: 0 }}>{modelDetail.displayName || modelDetail.id}</h1>
        </Flexbox>
      </Flexbox>

      {/* 基础信息 */}
      <Card title="基础信息">
        <Descriptions bordered column={2} size="small">
          <Descriptions.Item label="模型ID" span={2}>
            {modelDetail.id}
          </Descriptions.Item>
          <Descriptions.Item label="显示名称">{modelDetail.displayName || '--'}</Descriptions.Item>
          <Descriptions.Item label="模型类型">
            <Tag>
              {modelDetail.type === 'chat' && '对话'}
              {modelDetail.type === 'embedding' && '嵌入'}
              {modelDetail.type === 'image' && '图像'}
              {modelDetail.type === 'audio' && '音频'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="启用状态">
            <Tag color={modelDetail.enabled ? 'success' : 'default'}>
              {modelDetail.enabled ? '启用' : '禁用'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="上下文窗口">
            {modelDetail.contextWindowTokens
              ? `${Math.floor(modelDetail.contextWindowTokens / 1000)}K tokens`
              : '--'}
          </Descriptions.Item>
          <Descriptions.Item label="描述" span={2}>
            {modelDetail.description || '--'}
          </Descriptions.Item>
        </Descriptions>
      </Card>

      {/* 定价信息 */}
      <Card title="定价信息">
        <Descriptions bordered column={2} size="small">
          <Descriptions.Item label="输入价格">
            {modelDetail.pricing?.input ? `$${modelDetail.pricing.input}/token` : '--'}
          </Descriptions.Item>
          <Descriptions.Item label="输出价格">
            {modelDetail.pricing?.output ? `$${modelDetail.pricing.output}/token` : '--'}
          </Descriptions.Item>
        </Descriptions>
      </Card>

      {/* 时间信息 */}
      <Card title="时间信息">
        <Descriptions bordered column={2} size="small">
          <Descriptions.Item label="创建时间">
            {new Date(modelDetail.createdAt).toLocaleString()}
          </Descriptions.Item>
          <Descriptions.Item label="更新时间">
            {new Date(modelDetail.updatedAt).toLocaleString()}
          </Descriptions.Item>
        </Descriptions>
      </Card>

      {/* 关联模型 */}
      <Card
        extra={
          <Button onClick={() => setAddChannelModalOpen(true)} size="small" type="primary">
            添加关联
          </Button>
        }
        title={`关联的渠道模型 (${modelDetail.models?.length || 0})`}
      >
        <Table
          columns={associatedModelColumns}
          dataSource={modelDetail.models || []}
          locale={{ emptyText: '暂无关联的渠道模型' }}
          pagination={false}
          rowKey="id"
          size="small"
        />
      </Card>

      {/* 添加渠道模型关联Modal */}
      <AddChannelModelModal
        aggregatedModelId={modelDetail.id}
        onCancel={() => setAddChannelModalOpen(false)}
        onSuccess={() => {
          setAddChannelModalOpen(false);
          loadModelDetail(); // 重新加载数据
        }}
        open={addChannelModalOpen}
      />

      {/* 编辑渠道模型关联Modal */}
      <AddChannelModelModal
        aggregatedModelId={modelDetail.id}
        editData={editChannelData}
        mode="edit"
        onCancel={() => {
          setEditChannelModalOpen(false);
          setEditChannelData(null);
        }}
        onSuccess={() => {
          setEditChannelModalOpen(false);
          setEditChannelData(null);
          loadModelDetail(); // 重新加载数据
        }}
        open={editChannelModalOpen}
      />
    </Flexbox>
  );
});

export default ModelDetail;
