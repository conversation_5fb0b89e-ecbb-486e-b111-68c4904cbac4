'use client';

import { Form, InputNumber, Modal, Select, message } from 'antd';
import { memo, useEffect, useState } from 'react';

import { trpcClient } from '@/libs/trpc/client';
import { AdminModelSelectItem, AdminProviderSelectItem } from '@/types/adminInfra';

interface EditChannelModelData {
  aggregatedModelId: string;
  infraModelId: string;
  infraProviderId: string;
  litellmModelId: string;
  weight?: number | null;
}

interface AddChannelModelModalProps {
  aggregatedModelId: string | null;
  editData?: EditChannelModelData | null;
  mode?: 'add' | 'edit';
  onCancel: () => void;
  onSuccess: () => void;
  open: boolean;
}

const AddChannelModelModal = memo<AddChannelModelModalProps>(
  ({ open, aggregatedModelId, editData, mode = 'add', onCancel, onSuccess }) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [providers, setProviders] = useState<AdminProviderSelectItem[]>([]);
    const [models, setModels] = useState<AdminModelSelectItem[]>([]);
    const [selectedProvider, setSelectedProvider] = useState<string | null>(null);

    // 加载渠道列表
    useEffect(() => {
      const loadProviders = async () => {
        try {
          const result = await trpcClient.adminInfra.getAllInfraProviders.query();
          if (result.success) {
            setProviders(result.data || []);
          }
        } catch (error: any) {
          message.error(`加载渠道列表失败: ${error.message}`);
        }
      };

      if (open) {
        loadProviders();
      }
    }, [open]);

    // 编辑模式数据回显
    useEffect(() => {
      if (mode === 'edit' && editData && open) {
        form.setFieldsValue({
          infraModelId: editData.infraModelId,
          infraProviderId: editData.infraProviderId,
          weight: editData.weight,
        });
        setSelectedProvider(editData.infraProviderId);
      } else if (mode === 'add') {
        form.resetFields();
        setSelectedProvider(null);
      }
    }, [mode, editData, open, form]);

    // 当选择渠道时加载对应的模型列表
    useEffect(() => {
      const loadModels = async () => {
        if (!selectedProvider) {
          setModels([]);
          return;
        }

        try {
          const result =
            await trpcClient.adminInfra.getInfraModelsByProviderId.query(selectedProvider);
          if (result.success) {
            setModels(result.data || []);
          }
        } catch (error: any) {
          message.error(`加载模型列表失败: ${error.message}`);
        }
      };

      loadModels();
    }, [selectedProvider]);

    const handleProviderChange = (value: string) => {
      setSelectedProvider(value);
      form.setFieldsValue({ infraModelId: undefined }); // 清空模型选择
    };

    const handleSubmit = async () => {
      if (!aggregatedModelId) return;

      try {
        const values = await form.validateFields();
        setLoading(true);

        if (mode === 'edit' && editData) {
          // 编辑模式：更新关联模型的 LiteLLM 参数
          const result = await trpcClient.aggregatedModel.updateInfraModelRelation.mutate({
            litellmModelId: editData.litellmModelId,
            litellmParams: {
              weight: values.weight,
            },
          });

          if (result.success) {
            message.success('关联模型更新成功');
            form.resetFields();
            setSelectedProvider(null);
            onSuccess();
          } else {
            message.error(result.error || '更新失败');
          }
        } else {
          // 添加模式：创建新的关联
          const result = await trpcClient.aggregatedModel.addInfraModelRelation.mutate({
            aggregatedModelId,
            infraModelId: values.infraModelId,
            infraProviderId: values.infraProviderId,
            litellmParams: {
              weight: values.weight,
            },
          });

          if (result.success) {
            message.success('渠道模型关联成功');
            form.resetFields();
            setSelectedProvider(null);
            onSuccess();
          } else {
            message.error(result.error || '关联失败');
          }
        }
      } catch (error: any) {
        message.error(`${mode === 'edit' ? '更新' : '关联'}失败: ${error.message}`);
      } finally {
        setLoading(false);
      }
    };

    const handleCancel = () => {
      form.resetFields();
      setSelectedProvider(null);
      onCancel();
    };

    return (
      <Modal
        confirmLoading={loading}
        onCancel={handleCancel}
        onOk={handleSubmit}
        open={open}
        title={mode === 'edit' ? '编辑渠道模型关联' : '添加渠道模型关联'}
        width={500}
      >
        <Form form={form} layout="vertical" preserve={false}>
          <Form.Item
            label="选择渠道"
            name="infraProviderId"
            rules={[{ message: '请选择渠道', required: true }]}
          >
            <Select
              disabled={mode === 'edit'}
              filterOption={(input, option) =>
                (option?.children as any)?.toLowerCase().includes(input.toLowerCase())
              }
              onChange={handleProviderChange}
              placeholder="请选择渠道"
              showSearch
            >
              {providers.map((provider) => (
                <Select.Option key={provider.id} value={provider.id}>
                  {provider.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label="选择模型"
            name="infraModelId"
            rules={[{ message: '请选择模型', required: true }]}
          >
            <Select
              disabled={mode === 'edit' || !selectedProvider}
              filterOption={(input, option) =>
                (option?.children as any)?.toLowerCase().includes(input.toLowerCase())
              }
              placeholder="请先选择渠道"
              showSearch
            >
              {models.map((model) => (
                <Select.Option key={model.id} value={model.id}>
                  {model.displayName || model.id}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item label="权重" name="weight" tooltip="用于负载均衡的权重值，数值越大权重越高">
            <InputNumber
              max={100}
              min={0}
              placeholder="请输入权重值（可选）"
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Form>
      </Modal>
    );
  },
);

export default AddChannelModelModal;
