'use client';

import { ProColumns, ProTableProps } from '@ant-design/pro-components';
import { ModelIcon } from '@lobehub/icons';
import { ActionIcon, Tag } from '@lobehub/ui';
import { Modal, Space, Switch, message } from 'antd';
import { EyeIcon, PenIcon, PlusIcon, TrashIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { memo, useRef, useState } from 'react';
import { Flexbox } from 'react-layout-kit';

import Table from '@/components/Table';
import { trpcClient } from '@/libs/trpc/client';
import { AggregatedModelItem } from '@/types/aggregatedModel';

import ModelFormModal from './components/ModelFormModal';

interface TableData extends AggregatedModelItem {
  key: string;
}

// 表格数据请求函数
const request: ProTableProps<TableData, any>['request'] = async (params) => {
  const result = await trpcClient.aggregatedModel.getAllModels.query({
    enabled: params.enabled,
    limit: params.pageSize,
    offset: ((params.current || 1) - 1) * (params.pageSize || 20),
    type: params.type,
  });

  return {
    data: result?.data?.map((item) => ({ ...item, key: item.id })) || [],
    success: result?.success || false,
    total: result?.data?.length || 0,
  };
};

const ModelManagementList = memo(() => {
  const tableRef = useRef<any>(null);
  const router = useRouter();

  // Modal states
  const [modalOpen, setModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create');
  const [currentModel, setCurrentModel] = useState<AggregatedModelItem | null>(null);

  // 删除模型
  const handleDelete = async (record: TableData) => {
    Modal.confirm({
      cancelText: '取消',
      content: `确定要删除模型 "${record.displayName || record.id}" 吗？此操作不可撤销。`,
      okText: '删除',
      okType: 'danger',
      onOk: async () => {
        try {
          await trpcClient.aggregatedModel.deleteModel.mutate(record.id);
          message.success('模型删除成功');
          tableRef.current?.reload();
        } catch (error: any) {
          message.error(`删除失败: ${error.message}`);
        }
      },
      title: '确认删除',
    });
  };

  // 切换模型状态
  const handleToggleStatus = async (record: TableData, enabled: boolean) => {
    try {
      await trpcClient.aggregatedModel.toggleModelStatus.mutate({
        enabled,
        id: record.id,
      });
      message.success(`模型${enabled ? '启用' : '禁用'}成功`);
      tableRef.current?.reload();
    } catch (error: any) {
      message.error(`操作失败: ${error.message}`);
    }
  };

  // 查看详情
  const handleViewDetail = (record: TableData) => {
    router.push(`/model-management/${record.id}`);
  };

  // 编辑模型
  const handleEdit = (record: TableData) => {
    setCurrentModel(record);
    setModalMode('edit');
    setModalOpen(true);
  };

  // 创建模型
  const handleCreate = () => {
    setCurrentModel(null);
    setModalMode('create');
    setModalOpen(true);
  };

  // 表格列定义
  const columns: ProColumns<TableData>[] = [
    {
      dataIndex: 'id',
      key: 'id',
      render: (_, record) => (
        <Flexbox align="center" gap={8} horizontal>
          <ModelIcon model={record.id} size={20} />
          <span>{record.id}</span>
        </Flexbox>
      ),
      title: '模型ID',
      width: 200,
    },
    {
      dataIndex: 'displayName',
      key: 'displayName',
      render: (_, record) => record.displayName || record.id,
      title: '显示名称',
      width: 200,
    },
    {
      dataIndex: 'type',
      key: 'type',
      title: '类型',
      valueEnum: {
        audio: { text: '音频' },
        chat: { text: '对话' },
        embedding: { text: '嵌入' },
        image: { text: '图像' },
      },
      width: 100,
    },
    {
      dataIndex: 'enabled',
      key: 'enabled',
      render: (_, record) => (
        <Switch
          checked={record.enabled}
          onChange={(checked) => handleToggleStatus(record, checked)}
          size="small"
        />
      ),
      title: '状态',
      valueEnum: {
        false: { status: 'Default', text: '禁用' },
        true: { status: 'Success', text: '启用' },
      },
      width: 100,
    },
    {
      dataIndex: 'pricing',
      hideInSearch: true,
      key: 'pricing',
      render: (_, record) => {
        const pricing = record.pricing;
        if (!pricing || (!pricing.input && !pricing.output)) {
          return <Tag color="default">未设置</Tag>;
        }
        return (
          <Flexbox gap={4}>
            {pricing.input && <Tag color="blue">输入: ${pricing.input}</Tag>}
            {pricing.output && <Tag color="green">输出: ${pricing.output}</Tag>}
          </Flexbox>
        );
      },
      title: '定价信息',
      width: 150,
    },
    {
      dataIndex: 'contextWindowTokens',
      hideInSearch: true,
      key: 'contextWindowTokens',
      render: (_, record) => {
        const value = record.contextWindowTokens;
        return value ? `${Math.floor(value / 1000)}K` : '--';
      },
      title: '上下文窗口',
      width: 120,
    },
    {
      dataIndex: 'associatedModelsCount',
      hideInSearch: true,
      key: 'associatedModelsCount',
      title: '关联模型数',
      width: 120,
    },
    {
      dataIndex: 'createdAt',
      hideInSearch: true,
      key: 'createdAt',
      sorter: true,
      title: '创建时间',
      valueType: 'dateTime',
      width: 180,
    },
    {
      fixed: 'right',
      hideInSearch: true,
      key: 'actions',
      render: (_, record) => (
        <Space size="small">
          <ActionIcon
            icon={EyeIcon}
            onClick={() => handleViewDetail(record)}
            size="small"
            title="查看详情"
          />
          <ActionIcon icon={PenIcon} onClick={() => handleEdit(record)} size="small" title="编辑" />
          <ActionIcon
            icon={TrashIcon}
            onClick={() => handleDelete(record)}
            size="small"
            style={{ color: '#ff4d4f' }}
            title="删除"
          />
        </Space>
      ),
      title: '操作',
      width: 120,
    },
  ];

  return (
    <Flexbox gap={16} padding={24}>
      <Table<TableData>
        actionRef={tableRef}
        columns={columns}
        headerTitle="聚合模型管理"
        key="id"
        request={request}
        rowKey="id"
        scroll={{ x: 1200 }}
        toolBarRender={() => [
          <ActionIcon
            icon={PlusIcon}
            key="create"
            onClick={handleCreate}
            size="large"
            title="新建模型"
          />,
        ]}
      />

      {/* 模型表单Modal */}
      <ModelFormModal
        initialData={currentModel}
        mode={modalMode}
        onCancel={() => {
          setModalOpen(false);
          setCurrentModel(null);
        }}
        onSuccess={() => {
          setModalOpen(false);
          setCurrentModel(null);
          tableRef.current?.reload();
        }}
        open={modalOpen}
      />
    </Flexbox>
  );
});

export default ModelManagementList;
