'use client';

import {
  Card,
  Col,
  Form,
  Input,
  InputNumber,
  Modal,
  Row,
  Select,
  Slider,
  Switch,
  Typography,
  message,
} from 'antd';
import { memo, useEffect, useState } from 'react';

import { trpcClient } from '@/libs/trpc/client';
import { AggregatedModelItem } from '@/types/aggregatedModel';

interface ModelFormModalProps {
  initialData?: AggregatedModelItem | null;
  mode: 'create' | 'edit';
  onCancel: () => void;
  onSuccess: () => void;
  open: boolean;
}

const { TextArea } = Input;
const { Text } = Typography;

// 模型类型选项
const MODEL_TYPES = [
  { label: '大语言模型', value: 'chat' },
  { label: '图片生成模型', value: 'image' },
  { label: 'embedding模型', value: 'embedding' },
  { label: 'rerank模型', value: 'rerank' },
  { label: '视频生成', value: 'text2video' },
  { label: 'ASR模型', value: 'stt' },
  { label: 'TTS模型', value: 'tts' },
  { label: 'Realtime模型', value: 'realtime' },
];

// 上下文窗口预设值
const CONTEXT_WINDOW_MARKS = {
  0: '0',
  131_072: '128K',
  16_384: '16K',
  204_800: '200K',
  32_768: '32K',
  4096: '4K',
  65_536: '64K',
  8192: '8K',
};

const ModelFormModal = memo<ModelFormModalProps>(
  ({ open, onCancel, onSuccess, mode, initialData }) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [fallbackModels, setFallbackModels] = useState<AggregatedModelItem[]>([]);

    // 获取可用的Fallback模型列表
    const fetchFallbackModels = async () => {
      try {
        const result = await trpcClient.aggregatedModel.getAllModels.query({
          enabled: true,
          limit: 1000,
        });
        if (result.success) {
          setFallbackModels(result.data || []);
        }
      } catch (error) {
        console.error('获取Fallback模型列表失败:', error);
      }
    };

    // 获取Fallback模型列表
    useEffect(() => {
      if (open) {
        fetchFallbackModels();
      }
    }, [open]);

    // 初始化表单数据
    useEffect(() => {
      if (!open) return;

      if (mode === 'edit' && initialData) {
        // 编辑模式：回显数据
        console.log('Setting form values for edit mode:', initialData);
        const formData = {
          'abilities.functionCall': initialData.abilities?.functionCall || false,
          'abilities.imageOutput': initialData.abilities?.imageOutput || false,
          'abilities.reasoning': initialData.abilities?.reasoning || false,
          'abilities.search': initialData.abilities?.search || false,
          'abilities.vision': initialData.abilities?.vision || false,
          'contextWindowTokens': initialData.contextWindowTokens || 0,
          'description': initialData.description || '',
          'displayName': initialData.displayName || '',
          'enabled': initialData.enabled ?? true,
          'fallbackModelId': initialData.fallbackModelId || undefined,
          'id': initialData.id,
          'inputPrice': initialData.pricing?.input || 0,
          'outputPrice': initialData.pricing?.output || 0,
          'type': initialData.type || 'chat',
        };

        form.setFieldsValue(formData);
      } else {
        // 创建模式：重置表单并设置默认值
        form.resetFields();
        form.setFieldsValue({
          'abilities.functionCall': false,
          'abilities.imageOutput': false,
          'abilities.reasoning': false,
          'abilities.search': false,
          'abilities.vision': false,
          'contextWindowTokens': 0,
          'enabled': true,
          'inputPrice': 0,
          'outputPrice': 0,
          'type': 'chat',
        });
      }
    }, [open, mode, initialData, form]);

    const handleSubmit = async () => {
      try {
        const values = await form.validateFields();
        setLoading(true);

        const submitData = {
          abilities: {
            functionCall: values['abilities.functionCall'] || false,
            imageOutput: values['abilities.imageOutput'] || false,
            reasoning: values['abilities.reasoning'] || false,
            search: values['abilities.search'] || false,
            vision: values['abilities.vision'] || false,
          },
          contextWindowTokens: values.contextWindowTokens || undefined,
          description: values.description,
          displayName: values.displayName,
          enabled: values.enabled,
          fallbackModelId: values.fallbackModelId || undefined,
          id: values.id,
          pricing: {
            input: values.inputPrice,
            output: values.outputPrice,
          },
          type: values.type,
        };

        if (mode === 'create') {
          await trpcClient.aggregatedModel.createModel.mutate(submitData);
          message.success('模型创建成功');
        } else {
          await trpcClient.aggregatedModel.updateModel.mutate({
            data: submitData,
            id: initialData!.id,
          });
          message.success('模型更新成功');
        }

        form.resetFields();
        onSuccess();
      } catch (error: any) {
        message.error(`${mode === 'create' ? '创建' : '更新'}失败: ${error.message}`);
      } finally {
        setLoading(false);
      }
    };

    const handleCancel = () => {
      form.resetFields();
      onCancel();
    };

    return (
      <Modal
        cancelText="取消"
        confirmLoading={loading}
        okText={mode === 'create' ? '创建' : '更新'}
        onCancel={handleCancel}
        onOk={handleSubmit}
        open={open}
        title={mode === 'create' ? '添加模型' : '编辑模型'}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          preserve={false}
          style={{ maxHeight: '70vh', overflowY: 'auto', paddingRight: 8 }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="模型 ID"
                name="id"
                rules={[
                  { message: '请输入模型 ID', required: true },
                  { max: 150, message: '模型 ID 不能超过 150 个字符' },
                ]}
              >
                <Input
                  disabled={mode === 'edit'}
                  placeholder="请输入模型 ID，例如 gpt-4o, claude-3.5-sonnet..."
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="模型类型"
                name="type"
                rules={[{ message: '请选择模型类型', required: true }]}
              >
                <Select options={MODEL_TYPES} placeholder="请选择模型类型" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="模型显示名称"
            name="displayName"
            rules={[
              { message: '请输入模型显示名称', required: true },
              { max: 200, message: '显示名称不能超过 200 个字符' },
            ]}
          >
            <Input placeholder="请输入模型的显示名称，例如 ChatGPT, GPT-4o..." />
          </Form.Item>

          <Form.Item label="模型描述" name="description">
            <TextArea maxLength={500} placeholder="请输入模型描述（可选）" rows={3} showCount />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="输入价格"
                name="inputPrice"
                rules={[{ message: '请输入输入价格', required: true }]}
              >
                <InputNumber
                  addonAfter="元/M"
                  min={0}
                  placeholder="请输入输入价格"
                  precision={6}
                  step={0.001}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="输出价格"
                name="outputPrice"
                rules={[{ message: '请输入输出价格', required: true }]}
              >
                <InputNumber
                  addonAfter="元/M"
                  min={0}
                  placeholder="请输入输出价格"
                  precision={6}
                  step={0.001}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label="最大上下文窗口" name="contextWindowTokens">
            <Slider
              marks={CONTEXT_WINDOW_MARKS}
              max={204_800}
              min={0}
              step={1024}
              tooltip={{ formatter: (value) => `${Math.round(value! / 1024)}K` }}
            />
          </Form.Item>

          <Card size="small" style={{ marginBottom: 16 }} title="能力列表">
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Form.Item
                  name="abilities.functionCall"
                  style={{ marginBottom: 8 }}
                  valuePropName="checked"
                >
                  <Switch size="small" />
                </Form.Item>
                <Text style={{ fontSize: 12 }} type="secondary">
                  此配置仅在聚合模型使用工具的能力，允许调用工具类插件，但检查是否是真正能够使用工具的模型本身，请查看可用的。
                </Text>
              </Col>

              <Col span={12}>
                <Form.Item
                  name="abilities.vision"
                  style={{ marginBottom: 8 }}
                  valuePropName="checked"
                >
                  <Switch size="small" />
                </Form.Item>
                <Text style={{ fontSize: 12 }} type="secondary">
                  此配置仅会在应用增加它能的识视觉能完成调用，具体是否支持视觉完成调用是具体的视觉调训。
                </Text>
              </Col>

              <Col span={12}>
                <Form.Item
                  name="abilities.reasoning"
                  style={{ marginBottom: 8 }}
                  valuePropName="checked"
                >
                  <Switch size="small" />
                </Form.Item>
                <Text style={{ fontSize: 12 }} type="secondary">
                  此配置仅在聚合模型使用工具的能力，允许调用工具类插件，但检查是否是真正能够使用工具的模型本身，请查看可用的。
                </Text>
              </Col>

              <Col span={12}>
                <Form.Item
                  name="abilities.search"
                  style={{ marginBottom: 8 }}
                  valuePropName="checked"
                >
                  <Switch size="small" />
                </Form.Item>
                <Text style={{ fontSize: 12 }} type="secondary">
                  一段关于联网搜索的说明文字
                </Text>
              </Col>

              <Col span={12}>
                <Form.Item
                  name="abilities.imageGeneration"
                  style={{ marginBottom: 8 }}
                  valuePropName="checked"
                >
                  <Switch size="small" />
                </Form.Item>
                <Text style={{ fontSize: 12 }} type="secondary">
                  一段关于图像生成的说明文字
                </Text>
              </Col>
            </Row>
          </Card>

          <Card size="small" style={{ marginBottom: 16 }} title="Fallback 模型">
            <Form.Item name="fallbackModelId" style={{ marginBottom: 8 }}>
              <Select
                allowClear
                filterOption={(input, option) =>
                  (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                }
                options={fallbackModels.map((model) => ({
                  label: model.displayName || model.id,
                  value: model.id,
                }))}
                placeholder="请选择 Fallback 模型"
                showSearch
              />
            </Form.Item>
            <Text style={{ fontSize: 12 }} type="secondary">
              默认不勾选，勾选后出现&ldquo;Fallback 模型 (必填)&rdquo;
            </Text>
          </Card>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="启用" name="enabled" valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  },
);

export default ModelFormModal;
