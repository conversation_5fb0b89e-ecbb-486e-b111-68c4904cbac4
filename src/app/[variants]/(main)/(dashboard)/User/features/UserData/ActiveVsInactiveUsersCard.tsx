'use client';

import { Don<PERSON><PERSON><PERSON>, Legend } from '@lobehub/charts';
import { useTheme } from 'antd-style';
import { Dayjs } from 'dayjs';
import { CSSProperties, memo } from 'react';
import { useTranslation } from 'react-i18next';

import StatisticCard from '@/components/StatisticCard';
import { trpcQuery } from '@/libs/trpc/client';
import { formatIntergerNumber, formatRatio } from '@/utils/format';

const ActiveVsInactiveUsersCard = memo<{ range: [Dayjs, Dayjs]; style?: CSSProperties }>(
  ({ style, range }) => {
    const { t } = useTranslation('common');
    const theme = useTheme();

    const { data, isLoading } = trpcQuery.user.getActiveVsInactiveUsers.useQuery({
      range: range.map((d) => d.format('YYYY-MM-DD')) as [string, string],
    });
    const colors = [theme.geekblue, theme.geekblue5, theme.gray5];

    return (
      <StatisticCard
        chart={
          <DonutChart
            category={'count'}
            colors={colors}
            data={[
              {
                count: data?.active || 0,
                name: t('statistics.activeUsers'),
              },
              {
                count: data?.normal || 0,
                name: t('statistics.normalUsers'),
              },
              {
                count: data?.inactive || 0,
                name: t('statistics.inactiveUsers'),
              },
            ]}
            donutLabel={`${t('statistics.activeUser')} ${formatRatio(data?.active, data?.total)}`}
            height={160}
            index={'name'}
            loading={isLoading}
            onValueChange={console.log}
            valueFormatter={(v) => `${formatIntergerNumber(v)} (${formatRatio(v, data?.total)})`}
            variant={'donut'}
          />
        }
        loading={isLoading}
        statistic={{
          description: (
            <Legend
              categories={[
                `${t('statistics.activeUser')} (${formatRatio(data?.active, data?.total)})`,
                `${t('statistics.normalUser')} (${formatRatio(data?.normal, data?.total)})`,
                `${t('statistics.inactiveUser')} (${formatRatio(data?.inactive, data?.total)})`,
              ]}
              colors={colors}
            />
          ),
          precision: 0,
          value: data?.active || '--',
        }}
        style={style}
        title={t('statistics.activeUsers')}
      />
    );
  },
);

export default ActiveVsInactiveUsersCard;
