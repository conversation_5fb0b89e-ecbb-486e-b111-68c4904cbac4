'use client';

import { Grid } from '@lobehub/ui';
import { useResponsive } from 'antd-style';
import { memo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import Block from '@/components/Block';
import DateRangeCompare, {
  DateRangeCompareValue,
  defaultDateRangeCompareValue,
} from '@/components/DateRangeCompare';

import Users from '../../../features/Users';
import ActiveVsInactiveUsersCard from './ActiveVsInactiveUsersCard';
import FreeVsSubscriptionUsersCard from './FreeVsSubscriptionUsersCard';

const UserData = memo(() => {
  const { t } = useTranslation('common');
  const { mobile } = useResponsive();
  const [compareValue, setCompareValue] = useState<DateRangeCompareValue>(
    defaultDateRangeCompareValue,
  );

  return (
    <Block gap={16} title={t('statisticGroup.userData')}>
      <DateRangeCompare
        onChange={setCompareValue}
        showLabel
        style={{ marginInline: mobile ? 12 : 0 }}
        value={compareValue}
      />
      <Grid maxItemWidth={320} rows={4}>
        <Users
          compare={compareValue}
          style={{
            gridColumn: mobile ? undefined : 'span 2',
          }}
        />
        <ActiveVsInactiveUsersCard range={compareValue.range} />
        <FreeVsSubscriptionUsersCard range={compareValue.range} />
      </Grid>
    </Block>
  );
});

export default UserData;
