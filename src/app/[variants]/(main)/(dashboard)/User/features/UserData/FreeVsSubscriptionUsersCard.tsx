'use client';

import { Don<PERSON><PERSON><PERSON>, <PERSON> } from '@lobehub/charts';
import { Segmented } from '@lobehub/ui';
import { useTheme } from 'antd-style';
import { Dayjs } from 'dayjs';
import { CSSProperties, memo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import StatisticCard from '@/components/StatisticCard';
import { trpcQuery } from '@/libs/trpc/client';
import { formatIntergerNumber, formatRatio } from '@/utils/format';

enum UsersType {
  Active = 'active',
  Effective = 'effective',
  Total = 'total',
}

const FreeVsSubscriptionUsersCard = memo<{ range: [Dayjs, Dayjs]; style?: CSSProperties }>(
  ({ range, style }) => {
    const [type, setType] = useState<UsersType>(UsersType.Effective);
    const { t } = useTranslation('common');
    const theme = useTheme();

    const { data, isLoading } = trpcQuery.user.getFreeVsSubscriptionUsers.useQuery({
      range: range.map((d) => d.format('YYYY-MM-DD')) as [string, string],
      type,
    });

    return (
      <StatisticCard
        chart={
          <DonutChart
            category={'count'}
            colors={[theme.gold, theme.geekblue]}
            data={[
              {
                count: data?.subscription || 0,
                name: t('statistics.subscriptionUsers'),
              },
              {
                count: data?.free || 0,
                name: t('statistics.freeUsers'),
              },
            ]}
            donutLabel={`${t('statistics.subscriptionUser')} ${formatRatio(data?.subscription, data?.total)}`}
            height={160}
            index={'name'}
            loading={isLoading}
            onValueChange={console.log}
            valueFormatter={(v) => `${formatIntergerNumber(v)} (${formatRatio(v, data?.total)})`}
            variant={'donut'}
          />
        }
        extra={
          <Segmented
            onChange={(value) => setType(value as UsersType)}
            options={[
              { label: t('statistics.active'), value: UsersType.Active },
              { label: t('statistics.effective'), value: UsersType.Effective },
              { label: t('statistics.all'), value: UsersType.Total },
            ]}
            value={type}
          />
        }
        loading={isLoading}
        statistic={{
          description: (
            <Legend
              categories={[
                `${t('statistics.subscriptionUser')} (${formatRatio(data?.subscription, data?.total)})`,
                `${t('statistics.freeUser')} (${formatRatio(data?.free, data?.total)})`,
              ]}
              colors={[theme.gold, theme.geekblue]}
            />
          ),
          precision: 0,
          value: data?.subscription || '--',
        }}
        style={style}
        title={t('statistics.subscriptionUsers')}
      />
    );
  },
);

export default FreeVsSubscriptionUsersCard;
