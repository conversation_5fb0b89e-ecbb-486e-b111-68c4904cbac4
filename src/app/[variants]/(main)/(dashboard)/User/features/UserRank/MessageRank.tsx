'use client';

import { BarList, BarListProps } from '@lobehub/charts';
import { Avatar } from '@lobehub/ui';
import { useTheme } from 'antd-style';
import { Dayjs } from 'dayjs';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { CSSProperties, memo } from 'react';
import { useTranslation } from 'react-i18next';
import urlJoin from 'url-join';

import StatisticCard from '@/components/StatisticCard';
import { trpcQuery } from '@/libs/trpc/client';
import { formatIntergerNumber } from '@/utils/format';

const MessageRank = memo<{ range: [Dayjs, Dayjs]; style?: CSSProperties }>(({ style, range }) => {
  const { data, isLoading } = trpcQuery.overview.getMessageRank.useQuery({
    range: range.map((d) => d.format('YYYY-MM-DD')) as [string, string],
  });
  const { t } = useTranslation('common');
  const theme = useTheme();
  const router = useRouter();

  const bindData: BarListProps['data'] =
    data
      ?.map((item) => ({
        icon: (
          <Avatar
            alt={item.username || item.id}
            avatar={item.avatar}
            background={theme.colorFillSecondary}
            size={24}
          />
        ),
        id: item.id,
        name: (
          <Link href={urlJoin('/users', item.id)} style={{ color: 'inherit' }}>
            {item.username || item.id}
          </Link>
        ),
        value: item.count,
      }))
      .sort((a, b) => b.value - a.value) || [];

  return (
    <StatisticCard
      chart={
        <BarList
          data={bindData}
          height={180}
          leftLabel={t('statistics.username')}
          loading={isLoading}
          onValueChange={(item) => router.push(urlJoin('/users', item.id))}
          rightLabel={t('statistics.messages')}
          style={{ marginTop: 12 }}
          valueFormatter={formatIntergerNumber}
        />
      }
      chartPlacement={'right'}
      loading={isLoading}
      style={style}
      title={t('rank.messages')}
    />
  );
});

export default MessageRank;
