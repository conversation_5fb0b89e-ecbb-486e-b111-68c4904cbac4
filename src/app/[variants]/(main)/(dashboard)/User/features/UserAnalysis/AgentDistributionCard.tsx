'use client';

import { <PERSON><PERSON><PERSON> } from '@lobehub/charts';
import { Dayjs } from 'dayjs';
import { CSSProperties, memo, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import InlineTable from '@/components/InlineTable';
import StatisticCard from '@/components/StatisticCard';
import { trpcQuery } from '@/libs/trpc/client';
import { formatIntergerNumber } from '@/utils/format';

const AgentDistributionCard = memo<{ range: [Dayjs, Dayjs]; style?: CSSProperties }>(
  ({ style, range }) => {
    const { data, isLoading } = trpcQuery.overview.getAgentDistribution.useQuery({
      range: range.map((d) => d.format('YYYY-MM-DD')) as [string, string],
    });
    const { t } = useTranslation('common');

    const bindData = useMemo(
      () => [
        {
          average: Number(data?.all.average),
          max: Number(data?.all.max),
          median: Number(data?.all.median),
          name: t('statistics.allUser'),
          per80: Number(data?.all.per80),
          per90: Number(data?.all.per90),
        },
        {
          average: Number(data?.active.average),
          max: Number(data?.active.max),
          median: Number(data?.active.median),
          name: t('statistics.activeUser'),
          per80: Number(data?.active.per80),
          per90: Number(data?.active.per90),
        },
        {
          average: Number(data?.free.average),
          max: Number(data?.free.max),
          median: Number(data?.free.median),
          name: t('statistics.freeUser'),
          per80: Number(data?.free.per80),
          per90: Number(data?.free.per90),
        },
        {
          average: Number(data?.subscription.average),
          max: Number(data?.subscription.max),
          median: Number(data?.subscription.median),
          name: t('statistics.subscriptionUser'),
          per80: Number(data?.subscription.per80),
          per90: Number(data?.subscription.per90),
        },
      ],
      [data, t],
    );

    return (
      <StatisticCard
        chart={
          <BarChart
            categories={['average', 'median', 'per80', 'per90']}
            customCategories={{
              average: t('statistics.average'),
              median: t('statistics.median'),
              per80: t('statistics.per80'),
              per90: t('statistics.per90'),
            }}
            data={bindData}
            height={180}
            index={'name'}
            loading={isLoading}
            showYAxis={false}
            valueFormatter={(v) => formatIntergerNumber(v)}
          />
        }
        footer={
          <InlineTable
            columns={[
              {
                dataIndex: 'name',
                key: 'name',
                title: t('statistics.userGroup'),
              },
              {
                dataIndex: 'average',
                key: 'average',
                render: formatIntergerNumber,
                title: t('statistics.average'),
              },

              {
                dataIndex: 'median',
                key: 'median',
                render: formatIntergerNumber,
                title: t('statistics.median'),
              },
              {
                dataIndex: 'per80',
                key: 'per80',
                render: formatIntergerNumber,
                title: t('statistics.per80'),
              },
              {
                dataIndex: 'per90',
                key: 'per90',
                render: formatIntergerNumber,
                title: t('statistics.per90'),
              },
              {
                dataIndex: 'max',
                key: 'max',
                render: formatIntergerNumber,
                title: t('statistics.max'),
              },
            ]}
            dataSource={bindData}
            loading={isLoading}
            rowKey={'name'}
          />
        }
        loading={isLoading}
        style={style}
        title={t('statistics.agentDistribution')}
      />
    );
  },
);

export default AgentDistributionCard;
