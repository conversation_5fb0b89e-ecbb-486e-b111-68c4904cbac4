'use client';

import { Grid } from '@lobehub/ui';
import { useResponsive } from 'antd-style';
import { Dayjs } from 'dayjs';
import { memo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import Block from '@/components/Block';
import DateRangePicker from '@/components/DateRangePicker';
import { daysAgo, today } from '@/utils/time';

import AgentDistributionCard from './AgentDistributionCard';
import MessageDistributionCard from './MessageDistributionCard';
import TopicDistributionCard from './TopicDistributionCard';
import WeeklyActiveDistributionCard from './WeeklyActiveDistributionCard';

const UserAnalysis = memo(() => {
  const { t } = useTranslation('common');
  const [value, setValue] = useState<[Dayjs, Dayjs]>([daysAgo(6), today()]);
  const { mobile } = useResponsive();

  return (
    <Block gap={16} title={t('statisticGroup.userAnalysis')}>
      <DateRangePicker
        onRangeChange={setValue}
        showLabel
        style={{ marginInline: mobile ? 12 : 0 }}
        value={value}
      />
      <Grid maxItemWidth={320} rows={4}>
        <WeeklyActiveDistributionCard range={value} />
        <MessageDistributionCard range={value} />
        <AgentDistributionCard range={value} />
        <TopicDistributionCard range={value} />
      </Grid>
    </Block>
  );
});

export default UserAnalysis;
