import { Grid } from '@lobehub/ui';
import { memo } from 'react';

import Block from '@/components/Block';

import TotalAssistants from '../../../features/Total/TotalAssistants';
import TotalMessages from '../../../features/Total/TotalMessages';
import TotalTopics from '../../../features/Total/TotalTopics';
import TotalUsers from '../../../features/Total/TotalUsers';

const UserOverview = memo(() => {
  return (
    <Block gap={16}>
      <Grid maxItemWidth={320} rows={4}>
        <TotalUsers />
        <TotalMessages />
        <TotalAssistants />
        <TotalTopics />
      </Grid>
    </Block>
  );
});

export default UserOverview;
