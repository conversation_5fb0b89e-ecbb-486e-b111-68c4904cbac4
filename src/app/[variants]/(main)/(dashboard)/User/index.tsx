import { withNoSSR } from '@/utils/withNoSSR';

import UserAnalysis from './features/UserAnalysis';
import UserData from './features/UserData';
import UserOverview from './features/UserOverview';
import UserRank from './features/UserRank';

const User = () => {
  return (
    <>
      <UserOverview />
      <UserData />
      <UserAnalysis />
      <UserRank />
    </>
  );
};

User.displayName = 'UsersDashboard';

export default withNoSSR(User);
