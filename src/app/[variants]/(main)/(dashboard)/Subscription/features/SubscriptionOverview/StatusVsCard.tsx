'use client';

import { <PERSON><PERSON><PERSON><PERSON>, Legend } from '@lobehub/charts';
import { useTheme } from 'antd-style';
import { CSSProperties, memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import StatisticCard from '@/components/StatisticCard';
import { trpcQuery } from '@/libs/trpc/client';
import { Status } from '@/types/subscription';
import { formatIntergerNumber, formatRatio } from '@/utils/format';

const StatusVsCard = memo<{ style?: CSSProperties }>(({ style }) => {
  const { t } = useTranslation(['common', 'subscription']);
  const theme = useTheme();

  const { data, isLoading } = trpcQuery.subscript.getTotalStatusCount.useQuery();

  const colors = [theme.geekblue, theme.colorWarning, theme.gray];

  return (
    <StatisticCard
      chart={
        <DonutChart
          category={'count'}
          colors={colors}
          data={[
            {
              count: data?.[Status.ActiveNotCancelling] || 0,
              name: t('status.activeNotCancelling', { ns: 'subscription' }),
            },
            {
              count: data?.[Status.ActiveFutureCancellation] || 0,
              name: t('status.activeFutureCancellation', { ns: 'subscription' }),
            },
            {
              count: data?.[Status.Cancelled] || 0,
              name: t('status.cancelled', { ns: 'subscription' }),
            },
          ]}
          height={160}
          index={'name'}
          loading={isLoading}
          onValueChange={console.log}
          valueFormatter={(v) => `${formatIntergerNumber(v)} (${formatRatio(v, data?.total)})`}
          variant={'donut'}
        />
      }
      loading={isLoading}
      statistic={{
        description: (
          <Flexbox height={46}>
            <Legend
              categories={[
                `${t('status.activeNotCancelling', { ns: 'subscription' })} (${formatRatio(data?.[Status.ActiveNotCancelling], data?.total)})`,
                `${t('status.activeFutureCancellation', { ns: 'subscription' })} (${formatRatio(data?.[Status.ActiveFutureCancellation], data?.total)})`,
                `${t('status.cancelled', { ns: 'subscription' })} (${formatRatio(data?.[Status.Cancelled], data?.total)})`,
              ]}
              colors={colors}
            />
          </Flexbox>
        ),
        precision: 0,
        suffix: `/ ${formatIntergerNumber(data?.[Status.ActiveFutureCancellation])}`,
        value: data?.[Status.ActiveNotCancelling] || '--',
      }}
      style={style}
      title={t('statistics.subscriptionStatusDistribution')}
    />
  );
});

export default StatusVsCard;
