'use client';

import { CSSProperties, memo } from 'react';
import { useTranslation } from 'react-i18next';

import Statistic from '@/components/Statistic';
import StatisticCard from '@/components/StatisticCard';
import TitleWithPercentage from '@/components/StatisticCard/TitleWithPercentage';
import { trpcQuery } from '@/libs/trpc/client';
import { Plans } from '@/types/subscription';
import { formatIntergerNumber } from '@/utils/format';

const TotalHobby = memo<{ style?: CSSProperties }>(({ style }) => {
  const { data, isLoading } = trpcQuery.subscript.getTotalSubscriptions.useQuery(Plans.Hobby);
  const { t } = useTranslation(['common', 'subscription']);

  return (
    <StatisticCard
      chartPlacement={'right'}
      highlight={'#2271ED'}
      loading={isLoading}
      statistic={{
        description: (
          <Statistic
            title={t('time.compare.prevMonth')}
            value={formatIntergerNumber(data?.prevCount) || '--'}
          />
        ),
        precision: 0,
        value: data?.count || '--',
      }}
      style={style}
      title={
        <TitleWithPercentage
          count={data?.count}
          prvCount={data?.prevCount}
          title={t('plans.plan.hobby.title', { ns: 'subscription' })}
        />
      }
    />
  );
});

export default TotalHobby;
