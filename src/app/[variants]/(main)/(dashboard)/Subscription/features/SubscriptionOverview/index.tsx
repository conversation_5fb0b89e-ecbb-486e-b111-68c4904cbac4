'use client';

import { Grid } from '@lobehub/ui';
import { memo } from 'react';

import Block from '@/components/Block';

import ModeVsCard from './ModeVsCard';
import PlansVsCard from './PlansVsCard';
import RecurringVsCard from './RecurringVsCard';
import StatusVsCard from './StatusVsCard';
import TotalHobby from './TotalHobby';
import TotalPremium from './TotalPremium';
import TotalStarter from './TotalStarter';
import TotalUltimate from './TotalUltimate';

const SubscriptionOverview = memo(() => {
  return (
    <Block gap={16}>
      <Grid maxItemWidth={320} rows={4}>
        <TotalHobby />
        <TotalStarter />
        <TotalPremium />
        <TotalUltimate />
      </Grid>
      <Grid maxItemWidth={320} rows={4}>
        <PlansVsCard />
        <ModeVsCard />
        <RecurringVsCard />
        <StatusVsCard />
      </Grid>
    </Block>
  );
});

export default SubscriptionOverview;
