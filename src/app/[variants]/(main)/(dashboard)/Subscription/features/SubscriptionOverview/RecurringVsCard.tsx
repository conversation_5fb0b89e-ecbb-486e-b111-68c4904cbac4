'use client';

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from '@lobehub/charts';
import { useTheme } from 'antd-style';
import { CSSProperties, memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import StatisticCard from '@/components/StatisticCard';
import { trpcQuery } from '@/libs/trpc/client';
import { Recurring } from '@/types/subscription';
import { formatIntergerNumber, formatRatio } from '@/utils/format';

const RecurringVsCard = memo<{ style?: CSSProperties }>(({ style }) => {
  const { t } = useTranslation(['common', 'subscription']);
  const theme = useTheme();

  const colors = [theme.geekblue10, theme.geekblue7];

  const { data, isLoading } = trpcQuery.subscript.getTotalRecurringCount.useQuery();

  return (
    <StatisticCard
      chart={
        <DonutChart
          category={'count'}
          colors={colors}
          data={[
            {
              count: data?.[Recurring.Yearly] || 0,
              name: t('plans.navs.yearly', { ns: 'subscription' }),
            },
            {
              count: data?.[Recurring.Monthly] || 0,
              name: t('plans.navs.monthly', { ns: 'subscription' }),
            },
          ]}
          height={160}
          index={'name'}
          loading={isLoading}
          onValueChange={console.log}
          valueFormatter={(v) => `${formatIntergerNumber(v)} (${formatRatio(v, data?.total)})`}
          variant={'donut'}
        />
      }
      loading={isLoading}
      statistic={{
        description: (
          <Flexbox height={46}>
            <Legend
              categories={[
                `${t('plans.navs.yearly', { ns: 'subscription' })} (${formatRatio(data?.[Recurring.Yearly], data?.total)})`,
                `${t('plans.navs.monthly', { ns: 'subscription' })} (${formatRatio(data?.[Recurring.Monthly], data?.total)})`,
              ]}
              colors={colors}
            />
          </Flexbox>
        ),
        precision: 0,
        suffix: `/ ${formatIntergerNumber(data?.[Recurring.Monthly])}`,
        value: data?.[Recurring.Yearly] || '--',
      }}
      style={style}
      title={t('statistics.subscriptionRecurringDistribution')}
    />
  );
});

export default RecurringVsCard;
