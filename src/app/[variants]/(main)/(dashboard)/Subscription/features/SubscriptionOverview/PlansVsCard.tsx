'use client';

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from '@lobehub/charts';
import { useTheme } from 'antd-style';
import { CSSProperties, memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import StatisticCard from '@/components/StatisticCard';
import { trpcQuery } from '@/libs/trpc/client';
import { Plans } from '@/types/subscription';
import { formatIntergerNumber, formatRatio } from '@/utils/format';

const PlansVsCard = memo<{ style?: CSSProperties }>(({ style }) => {
  const { t } = useTranslation(['common', 'subscription']);
  const theme = useTheme();

  const { data, isLoading } = trpcQuery.subscript.getTotalPlanCount.useQuery();
  const colors = [theme.geekblue, theme.orange, theme.gray, theme.gold];

  return (
    <StatisticCard
      chart={
        <DonutChart
          category={'count'}
          colors={colors}
          data={[
            {
              count: data?.[Plans.Hobby] || 0,
              name: t('plans.plan.hobby.title', { ns: 'subscription' }),
            },
            {
              count: data?.[Plans.Starter] || 0,
              name: t('plans.plan.starter.title', { ns: 'subscription' }),
            },
            {
              count: data?.[Plans.Premium] || 0,
              name: t('plans.plan.premium.title', { ns: 'subscription' }),
            },
            {
              count: data?.[Plans.Ultimate] || 0,
              name: t('plans.plan.ultimate.title', { ns: 'subscription' }),
            },
          ]}
          height={160}
          index={'name'}
          loading={isLoading}
          onValueChange={console.log}
          valueFormatter={(v) => `${formatIntergerNumber(v)} (${formatRatio(v, data?.total)})`}
          variant={'donut'}
        />
      }
      loading={isLoading}
      statistic={{
        description: (
          <Flexbox height={46}>
            <Legend
              categories={[
                `${t('plans.plan.hobby.title', { ns: 'subscription' })} (${formatRatio(data?.[Plans.Hobby], data?.total)})`,
                `${t('plans.plan.starter.title', { ns: 'subscription' })} (${formatRatio(data?.[Plans.Starter], data?.total)})`,
                `${t('plans.plan.premium.title', { ns: 'subscription' })} (${formatRatio(data?.[Plans.Premium], data?.total)})`,
                `${t('plans.plan.ultimate.title', { ns: 'subscription' })} (${formatRatio(data?.[Plans.Ultimate], data?.total)})`,
              ]}
              colors={colors}
            />
          </Flexbox>
        ),
        precision: 0,
        value: data?.total || '--',
      }}
      style={style}
      title={t('statistics.subscriptionPlansDistribution')}
    />
  );
});

export default PlansVsCard;
