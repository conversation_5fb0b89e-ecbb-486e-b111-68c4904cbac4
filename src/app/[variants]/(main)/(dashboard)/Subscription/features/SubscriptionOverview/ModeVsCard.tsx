'use client';

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from '@lobehub/charts';
import { useTheme } from 'antd-style';
import { CSSProperties, memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import StatisticCard from '@/components/StatisticCard';
import { trpcQuery } from '@/libs/trpc/client';
import { BillingMode } from '@/types/subscription';
import { formatIntergerNumber, formatRatio } from '@/utils/format';

const ModeVsCard = memo<{ style?: CSSProperties }>(({ style }) => {
  const { t } = useTranslation(['common', 'subscription']);

  const theme = useTheme();

  const { data, isLoading } = trpcQuery.subscript.getTotalModeCount.useQuery();

  const colors = [theme.geekblue, theme.colorSuccess];

  return (
    <StatisticCard
      chart={
        <DonutChart
          category={'count'}
          colors={colors}
          data={[
            {
              count: data?.[BillingMode.Subscription] || 0,
              name: t('plans.subscribe', { ns: 'subscription' }),
            },
            {
              count: data?.[BillingMode.Payment] || 0,
              name: t('plans.navs.payonce', { ns: 'subscription' }),
            },
          ]}
          height={160}
          index={'name'}
          loading={isLoading}
          onValueChange={console.log}
          valueFormatter={(v) => `${formatIntergerNumber(v)} (${formatRatio(v, data?.total)})`}
          variant={'donut'}
        />
      }
      loading={isLoading}
      statistic={{
        description: (
          <Flexbox height={46}>
            <Legend
              categories={[
                `${t('plans.subscribe', { ns: 'subscription' })} (${formatRatio(data?.[BillingMode.Subscription], data?.total)})`,
                `${t('plans.navs.payonce', { ns: 'subscription' })} (${formatRatio(data?.[BillingMode.Payment], data?.total)})`,
              ]}
              colors={colors}
            />
          </Flexbox>
        ),
        precision: 0,
        suffix: `/ ${formatIntergerNumber(data?.[BillingMode.Payment])}`,
        value: data?.[BillingMode.Subscription] || '--',
      }}
      style={style}
      title={t('statistics.subscriptionModeDistribution')}
    />
  );
});

export default ModeVsCard;
