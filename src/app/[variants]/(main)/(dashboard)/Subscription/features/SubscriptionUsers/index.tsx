'use client';

import { Grid } from '@lobehub/ui';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';

import Block from '@/components/Block';

import FutureCancelUsers from './FutureCancelUsers';
import RecentSubscriptUsers from './RecentSubscriptUsers';

const SubscriptionUsers = memo(() => {
  const { t } = useTranslation('common');

  return (
    <Block title={t('statisticGroup.subscriptionUsers')}>
      <Grid maxItemWidth={320} rows={2}>
        <RecentSubscriptUsers />
        <FutureCancelUsers />
      </Grid>
    </Block>
  );
});

export default SubscriptionUsers;
