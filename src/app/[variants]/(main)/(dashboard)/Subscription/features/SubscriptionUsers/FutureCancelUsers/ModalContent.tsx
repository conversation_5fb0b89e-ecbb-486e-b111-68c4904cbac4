'use client';

import { Table, TableProps } from 'antd';
import { memo, useState } from 'react';

import { trpcQuery } from '@/libs/trpc/client';
import { withNoSSR } from '@/utils/withNoSSR';

const ModalContent = memo<{ columns: TableProps['columns']; total: number }>(
  ({ columns, total }) => {
    const [pagination, setPagination] = useState<{
      current?: number;
      pageSize?: number;
    }>({
      current: 1,
      pageSize: 10,
    });
    const { data, isLoading } = trpcQuery.subscript.getFutureCancelUsers.useQuery(pagination);

    return (
      <Table
        columns={columns}
        dataSource={data}
        loading={isLoading}
        onChange={(pagination) => setPagination(pagination)}
        pagination={{ ...pagination, size: 'small', style: { paddingInline: 16 }, total }}
      />
    );
  },
);

export default withNoSSR(ModalContent);
