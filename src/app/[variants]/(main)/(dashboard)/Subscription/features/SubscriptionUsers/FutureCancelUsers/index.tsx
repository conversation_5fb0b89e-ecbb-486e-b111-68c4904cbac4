'use client';

import { ActionIcon, Avatar, Modal } from '@lobehub/ui';
import { TableProps } from 'antd';
import { useTheme } from 'antd-style';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { MaximizeIcon } from 'lucide-react';
import Link from 'next/link';
import { CSSProperties, memo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';
import urlJoin from 'url-join';

import InlineTable from '@/components/InlineTable';
import StatisticCard from '@/components/StatisticCard';
import PlanIcon from '@/features/PlanIcon';
import { trpcQuery } from '@/libs/trpc/client';
import { BillingMode, Recurring } from '@/types/subscription';

import ModalContent from './ModalContent';

dayjs.extend(relativeTime);

const FutureCancelUsers = memo<{ style?: CSSProperties }>(({ style }) => {
  const { data, isLoading } = trpcQuery.subscript.getFutureCancelUsers.useQuery();
  const { data: total } = trpcQuery.subscript.getTotalFutureCancelUsers.useQuery();
  const { t } = useTranslation(['common', 'subscription']);
  const [open, setOpen] = useState(false);
  const theme = useTheme();

  const columns: TableProps['columns'] = [
    {
      dataIndex: 'id',
      render: (value, { avatar, username, id }) => (
        <Link href={urlJoin('/users', id)} style={{ color: 'inherit' }}>
          <Flexbox align={'center'} gap={8} horizontal>
            <Avatar
              alt={username || value}
              avatar={avatar}
              background={theme.colorFillSecondary}
              size={20}
            />
            {username || value}
          </Flexbox>
        </Link>
      ),
      title: t('statistics.username'),
      width: 200,
    },
    {
      dataIndex: 'plan',
      render: (value) => <PlanIcon plan={value} type={'tag'} />,
      title: t('plans.current', { ns: 'subscription' }),
    },
    {
      dataIndex: 'mode',
      render: (value) => (
        <div>
          {t(value === BillingMode.Subscription ? 'plans.subscribe' : 'plans.navs.payonce', {
            ns: 'subscription',
          })}
        </div>
      ),
      title: t('payment.title', { ns: 'subscription' }),
    },
    {
      dataIndex: 'recurring',
      render: (value) => (
        <div>
          {t(value === Recurring.Monthly ? 'recurring.monthly' : 'recurring.yearly', {
            ns: 'subscription',
          })}
        </div>
      ),
      title: t('recurring.title', { ns: 'subscription' }),
    },
    {
      align: 'right',
      dataIndex: 'cancelAt',
      render: (value) => <div style={{ color: theme.colorWarning }}>{dayjs(value).fromNow()}</div>,
      title: t('cancelAt', { ns: 'subscription' }),
    },
    {
      align: 'right',
      dataIndex: 'cancelAt',
      render: (value) => <div style={{ color: theme.colorTextSecondary }}>{value}</div>,
    },
  ];

  return (
    <>
      <StatisticCard
        extra={
          <ActionIcon
            icon={MaximizeIcon}
            onClick={() => setOpen(true)}
            title={t('statistics.detail')}
          />
        }
        footer={
          <InlineTable
            columns={columns}
            dataSource={data?.slice(0, 10)}
            hoverToActive={false}
            loading={isLoading}
          />
        }
        loading={isLoading}
        statistic={{
          precision: 0,
          value: total || '--',
        }}
        style={style}
        title={t('statisticGroup.futureCancelUsers')}
      />
      <Modal
        footer={null}
        onCancel={() => setOpen(false)}
        open={open}
        paddings={{
          desktop: 0,
          mobile: 0,
        }}
        title={t('statisticGroup.futureCancelUsers')}
        width={1024}
      >
        <ModalContent columns={columns} total={total || 10} />
      </Modal>
    </>
  );
});

export default FutureCancelUsers;
