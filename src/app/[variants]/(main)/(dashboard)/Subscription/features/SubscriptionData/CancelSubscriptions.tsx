'use client';

import { AreaChart } from '@lobehub/charts';
import { useTheme } from 'antd-style';
import { CSSProperties, memo } from 'react';
import { useTranslation } from 'react-i18next';

import { DateRangeCompareValue } from '@/components/DateRangeCompare';
import { CompareType } from '@/components/DateRangeCompare/type';
import Statistic from '@/components/Statistic';
import StatisticCard from '@/components/StatisticCard';
import RangeTooltip from '@/components/StatisticCard/RangeTooltip';
import TitleWithPercentage from '@/components/StatisticCard/TitleWithPercentage';
import { labelFormatterByDisplayType } from '@/components/StatisticCard/labelFormatterByDisplayType';
import { trpcQuery } from '@/libs/trpc/client';
import { formatIntergerNumber } from '@/utils/format';

const CancelSubscriptions = memo<{ compare: DateRangeCompareValue; style?: CSSProperties }>(
  ({ compare, style }) => {
    const theme = useTheme();
    const { t } = useTranslation('common');
    const isDisablePrev = compare.compare === CompareType.Disabled;

    const { data, isLoading } = trpcQuery.subscript.getRangeCancel.useQuery({
      display: compare.display,
      prevRange: compare.prevRange?.map((v) => v.format('YYYY-MM-DD')) as [string, string],
      range: compare.range.map((v) => v.format('YYYY-MM-DD')) as [string, string],
    });

    return (
      <StatisticCard
        chart={
          <AreaChart
            categories={['prevCount', 'count']}
            colors={[theme.gray5, theme.colorError]}
            customTooltip={(props) => RangeTooltip({ ...props, inverseColor: true })}
            data={data?.data || []}
            height={180}
            index={'date'}
            loading={isLoading}
            showLegend={false}
            showYAxis={false}
            valueFormatter={(v) => formatIntergerNumber(v)}
            xAxisLabelFormatter={(v) => labelFormatterByDisplayType(v, compare.display)}
          />
        }
        loading={isLoading}
        statistic={{
          description: !isDisablePrev && (
            <Statistic
              title={t('time.compare.prev')}
              value={formatIntergerNumber(data?.prevSum) || '--'}
            />
          ),
          precision: 0,
          value: data?.sum || '--',
        }}
        style={style}
        title={
          <TitleWithPercentage
            count={data?.sum}
            inverseColor
            prvCount={data?.prevSum}
            title={t('statistics.cancelSubscriptions')}
          />
        }
      />
    );
  },
);

export default CancelSubscriptions;
