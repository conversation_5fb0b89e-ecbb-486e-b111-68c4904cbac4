'use client';

import { Grid } from '@lobehub/ui';
import { useResponsive } from 'antd-style';
import { memo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import Block from '@/components/Block';
import DateRangeCompare, {
  DateRangeCompareValue,
  defaultDateRangeCompareValue,
} from '@/components/DateRangeCompare';

import Subscriptions from '../../../features/Subscriptions';
import CancelSubscriptions from './CancelSubscriptions';

const SubscriptionData = memo(() => {
  const { t } = useTranslation('common');
  const [compareValue, setCompareValue] = useState<DateRangeCompareValue>(
    defaultDateRangeCompareValue,
  );
  const { mobile } = useResponsive();

  return (
    <Block gap={16} title={t('statisticGroup.subscriptionData')}>
      <DateRangeCompare
        onChange={setCompareValue}
        showLabel
        style={{ marginInline: mobile ? 12 : 0 }}
        value={compareValue}
      />
      <Grid maxItemWidth={320} rows={2}>
        <Subscriptions compare={compareValue} />
        <CancelSubscriptions compare={compareValue} />
      </Grid>
    </Block>
  );
});

export default SubscriptionData;
