import { withNoSSR } from '@/utils/withNoSSR';

import SubscriptionData from './features/SubscriptionData';
import SubscriptionOverview from './features/SubscriptionOverview';
import SubscriptionUsers from './features/SubscriptionUsers';

const Subscription = () => {
  return (
    <>
      <SubscriptionOverview />
      <SubscriptionData />
      <SubscriptionUsers />
    </>
  );
};

Subscription.displayName = 'SubscriptionDashboard';

export default withNoSSR(Subscription);
