'use client';

import { BarList } from '@lobehub/charts';
import { memo, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import ChannelIcon from '@/features/ChannelIcon';
import { trpcQuery } from '@/libs/trpc/client';
import { formatPrice } from '@/utils/format';

const SpendDetail = memo<{
  range: [string, string];
  type?: 'lite' | 'full';
}>(({ range, type = 'full' }) => {
  const { t } = useTranslation('common');
  const { data, isLoading } = trpcQuery.overview.getRangeSpendDetails.useQuery({ range });

  const modelData = useMemo(() => {
    const barData = type === 'lite' ? data?.slice(0, 4) : data;
    return barData?.map((item) => ({
      ...item,
      icon: <ChannelIcon apiBase={item.name} size={20} />,
    }));
  }, [data, type]);

  return (
    <BarList
      data={modelData || []}
      height={180}
      leftLabel={t('statistics.channel')}
      loading={isLoading}
      rightLabel={t('statistics.spend')}
      valueFormatter={(v) => '$' + formatPrice(v)}
    />
  );
});

export default SpendDetail;
