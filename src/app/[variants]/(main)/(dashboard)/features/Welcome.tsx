'use client';

import { ClerkLoaded, ClerkLoading } from '@clerk/nextjs';
import { Skeleton, Typography } from 'antd';
import { useResponsive } from 'antd-style';
import { PropsWithChildren, memo, useCallback } from 'react';
import { Trans } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import UserAvatar from '@/features/User/UserAvatar';
import { useUserStore } from '@/store/user';
import { userProfileSelectors } from '@/store/user/selectors';

const Welcome = memo(() => {
  const { mobile } = useResponsive();
  const [nickname, username] = useUserStore((s) => [
    userProfileSelectors.nickName(s),
    userProfileSelectors.username(s),
  ]);

  const UserInfo = useCallback(
    ({ children }: PropsWithChildren) => (
      <>
        <ClerkLoading>
          <Skeleton.Button active size={'small'} />
        </ClerkLoading>
        <ClerkLoaded>
          <Flexbox align={'center'} gap={6} horizontal>
            <UserAvatar size={20} />
            <Typography.Text style={{ fontWeight: 500 }}>{children}</Typography.Text>
          </Flexbox>
        </ClerkLoaded>
      </>
    ),
    [],
  );

  return (
    <Typography.Text
      style={{
        alignItems: 'center',
        display: mobile ? 'none' : 'flex',
        flexWrap: 'wrap',
        gap: 4,
      }}
      type="secondary"
    >
      <Trans i18nKey={'welcome'} ns={'common'} tOptions={{ username: nickname || username }}>
        欢迎回来 <UserInfo>username</UserInfo>, 以下是最新的数据概览
      </Trans>
    </Typography.Text>
  );
});
export default Welcome;
