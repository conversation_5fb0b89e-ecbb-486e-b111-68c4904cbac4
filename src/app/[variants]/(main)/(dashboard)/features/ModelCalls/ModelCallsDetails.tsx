'use client';

import { BarList } from '@lobehub/charts';
import { ModelIcon } from '@lobehub/icons';
import { memo, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { trpcQuery } from '@/libs/trpc/client';
import { formatIntergerNumber } from '@/utils/format';

const ModelCallsDetails = memo<{
  range: [string, string];
  type?: 'lite' | 'full';
}>(({ range, type = 'full' }) => {
  const { t } = useTranslation('common');
  const { data, isLoading } = trpcQuery.overview.getRangeModelCallsDetails.useQuery({ range });

  const modelData = useMemo(() => {
    const barData = type === 'lite' ? data?.slice(0, 4) : data;
    return barData?.map((item) => ({
      ...item,
      icon: <ModelIcon model={item.name as any} size={20} />,
    }));
  }, [data, type]);

  return (
    <BarList
      data={modelData || []}
      height={180}
      leftLabel={t('statistics.model')}
      loading={isLoading}
      rightLabel={t('statistics.modelCalls')}
      valueFormatter={(v) => formatIntergerNumber(v)}
    />
  );
});

export default ModelCallsDetails;
