'use client';

import { AreaChart } from '@lobehub/charts';
import { ActionIcon, Modal, Segmented } from '@lobehub/ui';
import { useTheme } from 'antd-style';
import { MaximizeIcon } from 'lucide-react';
import { CSSProperties, memo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import { DateRangeCompareValue } from '@/components/DateRangeCompare';
import { CompareType } from '@/components/DateRangeCompare/type';
import Statistic from '@/components/Statistic';
import StatisticCard from '@/components/StatisticCard';
import RangeTooltip from '@/components/StatisticCard/RangeTooltip';
import TitleWithPercentage from '@/components/StatisticCard/TitleWithPercentage';
import { labelFormatterByDisplayType } from '@/components/StatisticCard/labelFormatterByDisplayType';
import { trpcQuery } from '@/libs/trpc/client';
import { formatIntergerNumber } from '@/utils/format';

import ModelCallsDetails from './ModelCallsDetails';

enum ChartType {
  Detail = 'detail',
  Total = 'total',
}

const ModelCalls = memo<{ compare: DateRangeCompareValue; style?: CSSProperties }>(
  ({ compare, style }) => {
    const theme = useTheme();
    const { t } = useTranslation('common');
    const [type, setType] = useState<ChartType>(ChartType.Total);
    const [open, setOpen] = useState(false);

    const isDisablePrev = compare.compare === CompareType.Disabled;

    const { data, isLoading } = trpcQuery.overview.getRangeModelCalls.useQuery({
      display: compare.display,
      prevRange: compare.prevRange?.map((v) => v.format('YYYY-MM-DD')) as [string, string],
      range: compare.range.map((v) => v.format('YYYY-MM-DD')) as [string, string],
    });

    return (
      <>
        <StatisticCard
          chart={
            type === ChartType.Total ? (
              <AreaChart
                categories={['prevCount', 'count']}
                colors={[theme.gray5, theme.geekblue]}
                customTooltip={RangeTooltip}
                data={data?.data || []}
                height={180}
                index={'date'}
                loading={isLoading}
                showLegend={false}
                showYAxis={false}
                valueFormatter={(v) => formatIntergerNumber(v)}
                xAxisLabelFormatter={(v) => labelFormatterByDisplayType(v, compare.display)}
              />
            ) : (
              <ModelCallsDetails
                range={compare.range.map((v) => v.format('YYYY-MM-DD')) as [string, string]}
                type={'lite'}
              />
            )
          }
          extra={
            <Flexbox align={'center'} gap={8} horizontal>
              {type === ChartType.Detail && (
                <ActionIcon
                  icon={MaximizeIcon}
                  onClick={() => setOpen(true)}
                  title={t('statistics.detail')}
                />
              )}
              <Segmented
                onChange={(value) => setType(value as ChartType)}
                options={[
                  { label: t('statistics.forTotal'), value: ChartType.Total },
                  { label: t('statistics.forDetail'), value: ChartType.Detail },
                ]}
                value={type}
              />
            </Flexbox>
          }
          loading={isLoading}
          statistic={{
            description: !isDisablePrev && (
              <Statistic
                title={t('time.compare.prev')}
                value={formatIntergerNumber(data?.prevSum) || '--'}
              />
            ),
            precision: 0,
            value: data?.sum || '--',
          }}
          style={style}
          title={
            <TitleWithPercentage
              count={data?.sum}
              prvCount={data?.prevSum}
              title={t('statistics.modelCalls')}
            />
          }
        />
        <Modal
          footer={null}
          onCancel={() => setOpen(false)}
          open={open}
          title={t('statistics.modelCalls')}
        >
          <ModelCallsDetails
            range={compare.range.map((v) => v.format('YYYY-MM-DD')) as [string, string]}
          />
        </Modal>
      </>
    );
  },
);

export default ModelCalls;
