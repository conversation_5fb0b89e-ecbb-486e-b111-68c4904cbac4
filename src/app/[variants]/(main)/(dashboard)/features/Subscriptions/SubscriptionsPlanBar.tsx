'use client';

import { Bar<PERSON><PERSON>, BarChartProps } from '@lobehub/charts';
import { useTheme } from 'antd-style';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';

import { DisplayType } from '@/components/DateRangeCompare/type';
import { labelFormatterByDisplayType } from '@/components/StatisticCard/labelFormatterByDisplayType';
import { trpcQuery } from '@/libs/trpc/client';
import { Plans } from '@/types/subscription';
import { formatIntergerNumber } from '@/utils/format';

const SubscriptionsPlanBar = memo<{
  display: DisplayType;
  range: [string, string];
  xAxisLabelFormatter?: BarChartProps['xAxisLabelFormatter'];
}>(({ display, range, xAxisLabelFormatter }) => {
  const theme = useTheme();
  const { t } = useTranslation('subscription');

  const { data, isLoading } = trpcQuery.overview.getRangePlans.useQuery({
    display,
    range,
  });

  return (
    <BarChart
      categories={[Plans.Hobby, Plans.Starter, Plans.Premium, Plans.Ultimate]}
      colors={[theme.geekblue, theme.orange, theme.gray, theme.gold]}
      customCategories={{
        [Plans.Hobby]: t('plans.plan.hobby.title'),
        [Plans.Starter]: t('plans.plan.starter.title'),
        [Plans.Premium]: t('plans.plan.premium.title'),
        [Plans.Ultimate]: t('plans.plan.ultimate.title'),
      }}
      data={data || []}
      height={180}
      index={'date'}
      loading={isLoading}
      showYAxis={false}
      stack
      valueFormatter={(v) => formatIntergerNumber(v)}
      xAxisLabelFormatter={(v) =>
        xAxisLabelFormatter ? xAxisLabelFormatter(v) : labelFormatterByDisplayType(v, display)
      }
    />
  );
});

export default SubscriptionsPlanBar;
