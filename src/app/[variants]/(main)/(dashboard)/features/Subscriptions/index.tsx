'use client';

import { AreaChart } from '@lobehub/charts';
import { Segmented } from '@lobehub/ui';
import { useTheme } from 'antd-style';
import { CSSProperties, memo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { DateRangeCompareValue } from '@/components/DateRangeCompare';
import { CompareType } from '@/components/DateRangeCompare/type';
import Statistic from '@/components/Statistic';
import StatisticCard from '@/components/StatisticCard';
import RangeTooltip from '@/components/StatisticCard/RangeTooltip';
import TitleWithPercentage from '@/components/StatisticCard/TitleWithPercentage';
import { labelFormatterByDisplayType } from '@/components/StatisticCard/labelFormatterByDisplayType';
import { trpcQuery } from '@/libs/trpc/client';
import { formatIntergerNumber } from '@/utils/format';

import SubscriptionsPlanBar from './SubscriptionsPlanBar';

enum ChartType {
  Plans = 'plans',
  Total = 'total',
}

const Subscriptions = memo<{ compare: DateRangeCompareValue; style?: CSSProperties }>(
  ({ compare, style }) => {
    const theme = useTheme();
    const { t } = useTranslation('common');
    const [type, setType] = useState<ChartType>(ChartType.Total);
    const isDisablePrev = compare.compare === CompareType.Disabled;

    const { data, isLoading } = trpcQuery.overview.getRangeSubscriptions.useQuery({
      display: compare.display,
      prevRange: compare.prevRange?.map((v) => v.format('YYYY-MM-DD')) as [string, string],
      range: compare.range.map((v) => v.format('YYYY-MM-DD')) as [string, string],
    });

    return (
      <StatisticCard
        chart={
          type === ChartType.Total ? (
            <AreaChart
              categories={['prevCount', 'count']}
              colors={[theme.gray5, theme.geekblue]}
              customTooltip={RangeTooltip}
              data={data?.data || []}
              height={180}
              index={'date'}
              loading={isLoading}
              showLegend={false}
              showYAxis={false}
              valueFormatter={(v) => formatIntergerNumber(v)}
              xAxisLabelFormatter={(v) => labelFormatterByDisplayType(v, compare.display)}
            />
          ) : (
            <SubscriptionsPlanBar
              display={compare.display}
              range={compare.range.map((v) => v.format('YYYY-MM-DD')) as [string, string]}
            />
          )
        }
        extra={
          <Segmented
            onChange={(value) => setType(value as ChartType)}
            options={[
              { label: t('statistics.forTotal'), value: ChartType.Total },
              { label: t('statistics.forPlan'), value: ChartType.Plans },
            ]}
            value={type}
          />
        }
        loading={isLoading}
        statistic={{
          description: !isDisablePrev && (
            <Statistic
              title={t('time.compare.prev')}
              value={formatIntergerNumber(data?.prevSum) || '--'}
            />
          ),
          precision: 0,
          value: data?.sum || '--',
        }}
        style={style}
        title={
          <TitleWithPercentage
            count={data?.sum}
            prvCount={data?.prevSum}
            title={t('statistics.newSubscriptions')}
          />
        }
      />
    );
  },
);

export default Subscriptions;
