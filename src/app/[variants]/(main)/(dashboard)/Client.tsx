'use client';

import { useQueryState } from 'nuqs';
import { memo } from 'react';
import { Flexbox } from 'react-layout-kit';

import Channel from './Channel';
import Overview from './Overview';
import Subscription from './Subscription';
import User from './User';
import Nav, { DashboardTab } from './features/Nav';
import Welcome from './features/Welcome';

const Client = memo(() => {
  const [activeKey, setActiveKey] = useQueryState('tab', {
    clearOnDefault: true,
    defaultValue: DashboardTab.Overview,
  });
  return (
    <Flexbox gap={16}>
      <Welcome />
      <Nav activeKey={activeKey as DashboardTab} setActiveKey={setActiveKey} />
      <Flexbox gap={24}>
        {activeKey === DashboardTab.Overview && <Overview />}
        {activeKey === DashboardTab.User && <User />}
        {activeKey === DashboardTab.Subscription && <Subscription />}
        {activeKey === DashboardTab.Channel && <Channel />}
      </Flexbox>
    </Flexbox>
  );
});

export default Client;
