'use client';

import { Grid } from '@lobehub/ui';
import { useResponsive } from 'antd-style';
import { memo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import Block from '@/components/Block';
import DateRangeCompare, {
  DateRangeCompareValue,
  defaultDateRangeCompareValue,
} from '@/components/DateRangeCompare';
import { featureFlagSelectors } from '@/store/featureFlag/selectors';
import { useFeatureFlagStore } from '@/store/featureFlag/store';

import ModelCalls from '../../../features/ModelCalls';
import Spend from '../../../features/Spend';
import Subscriptions from '../../../features/Subscriptions';
import Users from '../../../features/Users';

const OverviewRange = memo(() => {
  const { t } = useTranslation('common');
  const [compareValue, setCompareValue] = useState<DateRangeCompareValue>(
    defaultDateRangeCompareValue,
  );
  const { mobile } = useResponsive();

  const subscriptionEnabled = useFeatureFlagStore(featureFlagSelectors.subscriptionEnabled);
  const rows = subscriptionEnabled ? 4 : 3;

  return (
    <Block gap={16} title={t('title.overview')}>
      <DateRangeCompare
        onChange={setCompareValue}
        showLabel
        style={{ marginInline: mobile ? 12 : 0 }}
        value={compareValue}
      />
      <Grid maxItemWidth={320} rows={rows}>
        <Users compare={compareValue} />
        {subscriptionEnabled && <Subscriptions compare={compareValue} />}
        <Spend compare={compareValue} />
        <ModelCalls compare={compareValue} />
      </Grid>
    </Block>
  );
});

export default OverviewRange;
