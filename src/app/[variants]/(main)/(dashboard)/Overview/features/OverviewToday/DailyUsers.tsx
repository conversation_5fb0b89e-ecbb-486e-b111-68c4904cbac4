'use client';

import { AreaChart } from '@lobehub/charts';
import { useTheme } from 'antd-style';
import dayjs from 'dayjs';
import { CSSProperties, memo } from 'react';
import { useTranslation } from 'react-i18next';

import { DisplayType } from '@/components/DateRangeCompare/type';
import Statistic from '@/components/Statistic';
import StatisticCard from '@/components/StatisticCard';
import RangeTooltip from '@/components/StatisticCard/RangeTooltip';
import TitleWithPercentage from '@/components/StatisticCard/TitleWithPercentage';
import { trpcQuery } from '@/libs/trpc/client';
import { formatIntergerNumber } from '@/utils/format';
import { thisWeek, today, weeksAgo } from '@/utils/time';

const DailyUsers = memo<{ style?: CSSProperties }>(({ style }) => {
  const theme = useTheme();
  const { t } = useTranslation('common');

  const { data, isLoading } = trpcQuery.overview.getRangeUsers.useQuery({
    display: DisplayType.EveryDay,
    prevRange: [weeksAgo(2).format('YYYY-MM-DD'), weeksAgo(2).endOf('week').format('YYYY-MM-DD')],
    range: [thisWeek().format('YYYY-MM-DD'), today().format('YYYY-MM-DD')],
  });

  return (
    <StatisticCard
      chart={
        <AreaChart
          categories={['prevCount', 'count']}
          colors={[theme.gray5, theme.geekblue]}
          customTooltip={RangeTooltip}
          data={data?.data || []}
          height={180}
          index={'date'}
          loading={isLoading}
          showLegend={false}
          showYAxis={false}
          valueFormatter={(v) => formatIntergerNumber(v)}
          xAxisLabelFormatter={(v) => dayjs(v).format('ddd')}
        />
      }
      loading={isLoading}
      statistic={{
        description: (
          <Statistic
            title={t('time.compare.prevWeek')}
            value={formatIntergerNumber(data?.prevCurrent) || '--'}
          />
        ),
        precision: 0,
        value: data?.current || '--',
      }}
      style={style}
      title={
        <TitleWithPercentage
          count={data?.current}
          prvCount={data?.prevCurrent}
          title={t('statistics.newUsersToday')}
        />
      }
    />
  );
});

export default DailyUsers;
