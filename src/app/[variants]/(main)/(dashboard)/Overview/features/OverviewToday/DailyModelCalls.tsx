'use client';

import { AreaChart } from '@lobehub/charts';
import { ActionIcon, Modal, Segmented } from '@lobehub/ui';
import { useTheme } from 'antd-style';
import dayjs from 'dayjs';
import { MaximizeIcon } from 'lucide-react';
import { CSSProperties, memo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import { DisplayType } from '@/components/DateRangeCompare/type';
import Statistic from '@/components/Statistic';
import StatisticCard from '@/components/StatisticCard';
import RangeTooltip from '@/components/StatisticCard/RangeTooltip';
import TitleWithPercentage from '@/components/StatisticCard/TitleWithPercentage';
import { trpcQuery } from '@/libs/trpc/client';
import { formatIntergerNumber } from '@/utils/format';
import { thisWeek, today, weeksAgo } from '@/utils/time';

import ModelCallsDetails from '../../../features/ModelCalls/ModelCallsDetails';

enum ChartType {
  Detail = 'detail',
  Total = 'total',
}

const DailyModelCalls = memo<{ style?: CSSProperties }>(({ style }) => {
  const theme = useTheme();
  const { t } = useTranslation('common');
  const [type, setType] = useState<ChartType>(ChartType.Total);
  const [open, setOpen] = useState(false);

  const { data, isLoading } = trpcQuery.overview.getRangeModelCalls.useQuery({
    display: DisplayType.EveryDay,
    prevRange: [weeksAgo(2).format('YYYY-MM-DD'), weeksAgo(2).endOf('week').format('YYYY-MM-DD')],
    range: [thisWeek().format('YYYY-MM-DD'), today().format('YYYY-MM-DD')],
  });

  return (
    <>
      <StatisticCard
        chart={
          type === ChartType.Total ? (
            <AreaChart
              categories={['prevCount', 'count']}
              colors={[theme.gray5, theme.geekblue]}
              customTooltip={RangeTooltip}
              data={data?.data || []}
              height={180}
              index={'date'}
              loading={isLoading}
              showLegend={false}
              showYAxis={false}
              valueFormatter={(v) => formatIntergerNumber(v)}
              xAxisLabelFormatter={(v) => dayjs(v).format('ddd')}
            />
          ) : (
            <ModelCallsDetails
              range={[today().format('YYYY-MM-DD'), today().add(1, 'day').format('YYYY-MM-DD')]}
              type={'lite'}
            />
          )
        }
        extra={
          <Flexbox align={'center'} gap={8} horizontal>
            {type === ChartType.Detail && (
              <ActionIcon
                icon={MaximizeIcon}
                onClick={() => setOpen(true)}
                title={t('statistics.detail')}
              />
            )}
            <Segmented
              onChange={(value) => setType(value as ChartType)}
              options={[
                { label: t('statistics.forTotal'), value: ChartType.Total },
                { label: t('statistics.forDetail'), value: ChartType.Detail },
              ]}
              value={type}
            />
          </Flexbox>
        }
        loading={isLoading}
        statistic={{
          description: (
            <Statistic
              title={t('time.compare.prevWeek')}
              value={formatIntergerNumber(data?.prevCurrent) || '--'}
            />
          ),
          precision: 0,
          value: data?.current || '--',
        }}
        style={style}
        title={
          <TitleWithPercentage
            count={data?.current}
            prvCount={data?.prevCurrent}
            title={t('statistics.modelCallsToday')}
          />
        }
      />
      <Modal
        footer={null}
        onCancel={() => setOpen(false)}
        open={open}
        title={t('statistics.modelCallsToday')}
      >
        <ModelCallsDetails
          range={[today().format('YYYY-MM-DD'), today().add(1, 'day').format('YYYY-MM-DD')]}
        />
      </Modal>
    </>
  );
});

export default DailyModelCalls;
