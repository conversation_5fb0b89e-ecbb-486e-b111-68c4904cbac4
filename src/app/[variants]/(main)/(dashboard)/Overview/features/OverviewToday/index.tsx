'use client';

import { Grid } from '@lobehub/ui';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';

import Block from '@/components/Block';
import { featureFlagSelectors } from '@/store/featureFlag/selectors';
import { useFeatureFlagStore } from '@/store/featureFlag/store';

import DailyModelCalls from './DailyModelCalls';
import DailySpend from './DailySpend';
import DailySubscriptions from './DailySubscriptions';
import DailyUsers from './DailyUsers';

const OverviewToday = memo(() => {
  const { t } = useTranslation('common');

  const subscriptionEnabled = useFeatureFlagStore(featureFlagSelectors.subscriptionEnabled);
  const rows = subscriptionEnabled ? 4 : 3;

  return (
    <Block title={t('title.today')}>
      <Grid maxItemWidth={320} rows={rows}>
        <DailyUsers />
        {subscriptionEnabled && <DailySubscriptions />}
        <DailySpend />
        <DailyModelCalls />
      </Grid>
    </Block>
  );
});

export default OverviewToday;
