'use client';

import { Grid } from '@lobehub/ui';
import { memo } from 'react';

import { featureFlagSelectors } from '@/store/featureFlag/selectors';
import { useFeatureFlagStore } from '@/store/featureFlag/store';

import TotalAssistants from '../../../features/Total/TotalAssistants';
import TotalMessages from '../../../features/Total/TotalMessages';
import TotalUsers from '../../../features/Total/TotalUsers';
import TotalSubscriptions from './TotalSubscriptions';

const OverviewTotal = memo(() => {
  const subscriptionEnabled = useFeatureFlagStore(featureFlagSelectors.subscriptionEnabled);
  const rows = subscriptionEnabled ? 4 : 3;

  return (
    <Grid maxItemWidth={320} rows={rows}>
      <TotalUsers />
      {subscriptionEnabled && <TotalSubscriptions />}
      <TotalMessages />
      <TotalAssistants />
    </Grid>
  );
});

export default OverviewTotal;
