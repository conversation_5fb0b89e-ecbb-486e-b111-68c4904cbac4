import { ActionIcon, Avatar, Tooltip } from '@lobehub/ui';
import { DiscordIcon } from '@lobehub/ui/icons';
import { createStyles } from 'antd-style';
import Link from 'next/link';
import { CSSProperties, memo } from 'react';
import { Center, Flexbox } from 'react-layout-kit';
import useS<PERSON> from 'swr';

import StatisticCard from '@/components/StatisticCard';
import { DISCORD_ID } from '@/const/url';

const useStyles = createStyles(({ css }) => {
  return {
    discord: css`
      overflow: hidden auto;
      max-height: 180px;
    `,
  };
});

const DiscordCard = memo<{ style?: CSSProperties }>(({ style }) => {
  const { data, isLoading } = useSWR('discord', () =>
    fetch(`https://discord.com/api/guilds/${DISCORD_ID}/widget.json`).then((res) => res.json()),
  );
  const { styles, theme } = useStyles();

  return (
    <StatisticCard
      chart={
        <Flexbox className={styles.discord} gap={6} horizontal wrap={'wrap'}>
          {(data as any)?.members
            ?.filter((item: any) => item.status === 'online')
            .map((item: any) => (
              <Tooltip key={item.id} title={item.username}>
                <Avatar
                  alt={item.username}
                  avatar={item.avatar_url || item.username}
                  background={theme.colorFillQuaternary}
                  size={32}
                  title={item.username}
                />
              </Tooltip>
            ))}
          <Center
            height={32}
            style={{ background: theme.colorFillQuaternary, borderRadius: '50%' }}
            width={32}
          >
            ...
          </Center>
        </Flexbox>
      }
      extra={
        data?.instant_invite && (
          <Link href={data.instant_invite} target={'_blank'}>
            <ActionIcon icon={DiscordIcon} />
          </Link>
        )
      }
      loading={isLoading}
      statistic={{
        description: (
          <Flexbox align={'center'} gap={4} horizontal style={{ fontSize: 12 }}>
            <Center height={6} style={{ background: '#52c41a', borderRadius: '50%' }} width={6} />
            Online
          </Flexbox>
        ),
        value: data?.presence_count || '--',
      }}
      style={style}
      title={'Discord'}
    />
  );
});

export default DiscordCard;
