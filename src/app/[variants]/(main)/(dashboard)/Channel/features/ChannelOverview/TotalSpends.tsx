'use client';

import { useTheme } from 'antd-style';
import { CSSProperties, memo } from 'react';
import { useTranslation } from 'react-i18next';

import Statistic from '@/components/Statistic';
import StatisticCard from '@/components/StatisticCard';
import TitleWithPercentage from '@/components/StatisticCard/TitleWithPercentage';
import { trpcQuery } from '@/libs/trpc/client';
import { formatPrice } from '@/utils/format';

const TotalUsers = memo<{ style?: CSSProperties }>(({ style }) => {
  const { data, isLoading } = trpcQuery.channel.getTotalSpend.useQuery();
  const { t } = useTranslation('common');
  const theme = useTheme();

  return (
    <StatisticCard
      highlight={theme.geekblue}
      loading={isLoading}
      statistic={{
        description: (
          <Statistic
            title={t('time.compare.prevMonth')}
            value={'$' + formatPrice(data?.prevCount) || '--.--'}
          />
        ),
        precision: 0,
        prefix: '$',
        value: data?.count || '--.--',
      }}
      style={style}
      title={
        <TitleWithPercentage
          count={data?.count}
          prvCount={data?.prevCount}
          title={t('statistics.totalSpends')}
        />
      }
    />
  );
});

export default TotalUsers;
