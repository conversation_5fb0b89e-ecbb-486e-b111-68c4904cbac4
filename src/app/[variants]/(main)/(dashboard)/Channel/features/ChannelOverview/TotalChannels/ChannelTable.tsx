import { ModelIcon } from '@lobehub/icons';
import { Collapse, Tag } from '@lobehub/ui';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import ModelFeatureTags from '@/app/[variants]/(main)/channel/features/ModelFeatureTags';
import InlineTable from '@/components/InlineTable';
import LabelWithKey from '@/components/LabelWithKey';
import ChannelIcon from '@/features/ChannelIcon';
import { ChannelItem } from '@/types/litellm';
import { formatPrice } from '@/utils/format';
import { withNoSSR } from '@/utils/withNoSSR';

const ChannelTable = memo<{ data: ChannelItem[]; loading?: boolean }>(({ data, loading }) => {
  const { t } = useTranslation('common');
  return (
    <Collapse
      defaultActiveKey={data?.map((item) => item.name)}
      expandIconPosition={'end'}
      gap={16}
      items={data?.map((item) => ({
        children: (
          <InlineTable
            columns={[
              {
                dataIndex: 'model_name',
                key: 'name',
                render: (value, record) => (
                  <Flexbox align={'center'} gap={8} horizontal key={value}>
                    <ModelIcon model={value} size={20} />
                    {value}
                    <ModelFeatureTags
                      functionCall={record.model_info.supports_function_calling}
                      tokens={record.model_info.max_tokens}
                      vision={record.model_info.supports_vision}
                    />
                  </Flexbox>
                ),
                title: t('statistics.model'),
                width: 400,
              },
              {
                dataIndex: ['model_info', 'input_cost_per_token'],
                key: 'input',
                render: (value) => '$' + formatPrice(value * 1000 * 1000),
                title: <LabelWithKey tag={'1M Token'} title={t('models.input')} />,
              },
              {
                dataIndex: ['model_info', 'output_cost_per_token'],
                key: 'output',
                render: (value) => '$' + formatPrice(value * 1000 * 1000),
                title: <LabelWithKey tag={'1M Token'} title={t('models.output')} />,
              },
            ]}
            dataSource={item.models}
            hoverToActive={false}
            loading={loading}
            rowKey={'model_name'}
          />
        ),
        extra: <Tag>{item.models.length}</Tag>,
        key: item.name,
        label: (
          <Flexbox align={'center'} gap={8} horizontal>
            <ChannelIcon apiBase={item.name} size={24} />
            {item.name}
          </Flexbox>
        ),
      }))}
      padding={{
        body: 0,
      }}
    />
  );
});

export default withNoSSR(ChannelTable);
