'use client';

import { CSSProperties, memo } from 'react';
import { useTranslation } from 'react-i18next';

import Statistic from '@/components/Statistic';
import StatisticCard from '@/components/StatisticCard';
import TitleWithPercentage from '@/components/StatisticCard/TitleWithPercentage';
import { trpcQuery } from '@/libs/trpc/client';
import { formatIntergerNumber } from '@/utils/format';

const TotalModelCalls = memo<{ style?: CSSProperties }>(({ style }) => {
  const { data, isLoading } = trpcQuery.channel.getTotalModelCalls.useQuery();
  const { t } = useTranslation('common');

  return (
    <StatisticCard
      loading={isLoading}
      statistic={{
        description: (
          <Statistic
            title={t('time.compare.prevMonth')}
            value={formatIntergerNumber(data?.prevCount) || '--'}
          />
        ),
        precision: 0,
        value: data?.count || '--',
      }}
      style={style}
      title={
        <TitleWithPercentage
          count={data?.count}
          prvCount={data?.prevCount}
          title={t('statistics.totalModelCalls')}
        />
      }
    />
  );
});

export default TotalModelCalls;
