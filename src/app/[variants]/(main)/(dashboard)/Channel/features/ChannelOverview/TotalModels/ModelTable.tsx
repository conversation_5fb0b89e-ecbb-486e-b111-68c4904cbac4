import { CategoryBar, useThemeColorRange } from '@lobehub/charts';
import { ModelIcon } from '@lobehub/icons';
import { Collapse, CopyButton, Tag } from '@lobehub/ui';
import { Popover, Skeleton } from 'antd';
import { useTheme } from 'antd-style';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import InlineTable from '@/components/InlineTable';
import ChannelIcon from '@/features/ChannelIcon';
import { trpcQuery } from '@/libs/trpc/client';
import { calcModelWeight } from '@/utils/calcModelWeight';
import { formatApiBase, formatIntergerNumber } from '@/utils/format';
import { withNoSSR } from '@/utils/withNoSSR';

const ModelTable = memo(() => {
  const { t } = useTranslation('common');
  const theme = useTheme();
  const { data, isLoading } = trpcQuery.channel.getModelGroups.useQuery();
  const themeColorRange = useThemeColorRange();

  if (isLoading) return <Skeleton active paragraph={{ rows: 8 }} title={false} />;

  return (
    <Collapse
      defaultActiveKey={Object.keys(data || [])?.map((item) => item)}
      expandIconPosition={'end'}
      gap={16}
      items={Object.entries(data || [])?.map(([key, channel]) => {
        const weightGroup = calcModelWeight(channel);
        return {
          children: (
            <Flexbox>
              <CategoryBar
                colors={themeColorRange}
                showLabels={false}
                size={2}
                values={weightGroup.map((item) => item.weight)}
              />
              <InlineTable
                columns={[
                  {
                    dataIndex: ['litellm_params', 'api_base'],
                    key: 'api',
                    render: (value, record, index) => {
                      const isChannel = !!value;
                      const provider = isChannel ? value : record.model_info.litellm_provider;
                      return (
                        <Flexbox align={'center'} gap={12} horizontal key={value}>
                          <ChannelIcon
                            apiBase={provider}
                            style={{
                              boxShadow: `0 0 0 2px ${theme.colorBgContainer}, 0 0 0 4px ${themeColorRange[index]}`,
                              boxSizing: 'content-box',
                            }}
                          />
                          {isChannel ? formatApiBase(provider) : provider}
                        </Flexbox>
                      );
                    },
                    title: t('statistics.channel'),
                    width: 200,
                  },
                  {
                    align: 'right',
                    dataIndex: ['litellm_params', 'weight'],
                    key: 'weight',
                    render: (value) => formatIntergerNumber(value),
                    title: t('statistics.weight'),
                  },
                  {
                    align: 'right',
                    dataIndex: ['litellm_params', 'rpm'],
                    key: 'rpm',
                    render: (value, record) =>
                      [
                        formatIntergerNumber(value),
                        formatIntergerNumber(record.litellm_params.tpm),
                      ].join(' / '),
                    title: 'RPM / TPM',
                  },
                  {
                    align: 'right',
                    dataIndex: ['model_info', 'id'],
                    key: 'loadBalance',
                    render: (value) => {
                      const weight = weightGroup.find((item) => item.id === value)?.weight || 0;
                      return (
                        <span
                          style={{
                            fontWeight: 'bold',
                          }}
                        >
                          {formatIntergerNumber(weight * 100) + '%'}
                        </span>
                      );
                    },
                    title: t('statistics.loadBalance'),
                  },
                  {
                    align: 'right',
                    dataIndex: ['model_info', 'id'],
                    key: 'id',
                    render: (value) => (
                      <Flexbox gap={8} horizontal justify={'flex-end'}>
                        <Popover
                          arrow={false}
                          content={
                            <span
                              style={{
                                fontFamily: theme.fontFamilyCode,
                              }}
                            >
                              {value}
                            </span>
                          }
                          placement={'topRight'}
                          trigger={['hover']}
                        >
                          <CopyButton active content={value} size={'small'} />
                        </Popover>
                      </Flexbox>
                    ),
                  },
                ]}
                dataSource={channel}
                hoverToActive={false}
                loading={isLoading}
                rowKey={(record) => record.model_info.id}
              />
            </Flexbox>
          ),
          extra: <Tag>{channel.length}</Tag>,
          key,
          label: (
            <Flexbox align={'center'} gap={8} horizontal>
              <ModelIcon model={key} size={24} />
              {key}
            </Flexbox>
          ),
        };
      })}
      padding={{
        body: 0,
      }}
    />
  );
});

export default withNoSSR(ModelTable);
