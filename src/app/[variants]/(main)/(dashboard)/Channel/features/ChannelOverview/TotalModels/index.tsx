'use client';

import { ModelIcon } from '@lobehub/icons';
import { ActionIcon, Modal } from '@lobehub/ui';
import { useTheme } from 'antd-style';
import { MaximizeIcon } from 'lucide-react';
import { CSSProperties, memo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import StatisticCard from '@/components/StatisticCard';
import { trpcQuery } from '@/libs/trpc/client';

import ModelTable from './ModelTable';

const TotalModels = memo<{ style?: CSSProperties }>(({ style }) => {
  const { data, isLoading } = trpcQuery.channel.getModels.useQuery();

  const { t } = useTranslation('common');
  const [open, setOpen] = useState(false);
  const theme = useTheme();

  return (
    <>
      <StatisticCard
        extra={
          <ActionIcon
            icon={MaximizeIcon}
            onClick={() => setOpen(true)}
            title={t('statistics.detail')}
          />
        }
        loading={isLoading}
        statistic={{
          description: (
            <Flexbox horizontal wrap={'wrap'}>
              {data?.map((item, i) => {
                if (data[i - 1] && item.id.slice(0, 3) === data[i - 1]?.id.slice(0, 3)) return null;
                return (
                  <ModelIcon
                    key={item.id}
                    model={item.id}
                    size={18}
                    style={{
                      border: `2px solid ${theme.colorBgContainer}`,
                      boxSizing: 'content-box',
                      marginRight: -8,
                      zIndex: i + 1,
                    }}
                  />
                );
              })}
            </Flexbox>
          ),
          precision: 0,
          value: data?.length || '--',
        }}
        style={style}
        title={t('statistics.totalModels')}
      />
      <Modal
        footer={null}
        onCancel={() => setOpen(false)}
        open={open}
        title={t('statistics.totalModels')}
      >
        <ModelTable />
      </Modal>
    </>
  );
});

export default TotalModels;
