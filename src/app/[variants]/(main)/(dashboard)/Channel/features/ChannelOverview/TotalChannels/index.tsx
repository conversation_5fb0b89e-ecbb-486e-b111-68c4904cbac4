'use client';

import { ActionIcon, Modal } from '@lobehub/ui';
import { useTheme } from 'antd-style';
import { MaximizeIcon } from 'lucide-react';
import { CSSProperties, memo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import StatisticCard from '@/components/StatisticCard';
import ChannelIcon from '@/features/ChannelIcon';
import { trpcQuery } from '@/libs/trpc/client';

import ChannelTable from './ChannelTable';

const TotalChannels = memo<{ style?: CSSProperties }>(({ style }) => {
  const { data, isLoading } = trpcQuery.channel.getChannels.useQuery();

  const { t } = useTranslation('common');
  const [open, setOpen] = useState(false);
  const theme = useTheme();

  return (
    <>
      <StatisticCard
        extra={
          <ActionIcon
            icon={MaximizeIcon}
            onClick={() => setOpen(true)}
            title={t('statistics.detail')}
          />
        }
        loading={isLoading}
        statistic={{
          description: (
            <Flexbox horizontal wrap={'wrap'}>
              {data?.map((item, i) => (
                <ChannelIcon
                  apiBase={item.name}
                  key={item.name}
                  size={18}
                  style={{
                    border: `2px solid ${theme.colorBgContainer}`,
                    boxSizing: 'content-box',
                    marginRight: -8,
                    zIndex: i + 1,
                  }}
                />
              ))}
            </Flexbox>
          ),
          precision: 0,
          value: data?.length || '--',
        }}
        style={style}
        title={t('statistics.totalChannels')}
      />
      <Modal
        footer={null}
        onCancel={() => setOpen(false)}
        open={open}
        title={t('statistics.totalChannels')}
      >
        {data && <ChannelTable data={data} loading={isLoading} />}
      </Modal>
    </>
  );
});

export default TotalChannels;
