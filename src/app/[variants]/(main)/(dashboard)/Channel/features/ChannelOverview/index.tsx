'use client';

import { Grid } from '@lobehub/ui';
import { memo } from 'react';

import Block from '@/components/Block';

import TotalChannels from './TotalChannels';
import TotalModelCalls from './TotalModelCalls';
import TotalModels from './TotalModels';
import TotalSpend from './TotalSpends';

const ChannelOverview = memo(() => {
  return (
    <Block gap={16}>
      <Grid maxItemWidth={320} rows={4}>
        <TotalSpend />
        <TotalModelCalls />
        <TotalChannels />
        <TotalModels />
      </Grid>
    </Block>
  );
});

export default ChannelOverview;
