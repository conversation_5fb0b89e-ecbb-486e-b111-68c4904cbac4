'use client';

import { Bar<PERSON><PERSON>, ChartTooltipFrame, ChartTooltipRow } from '@lobehub/charts';
import { Divider, Typography } from 'antd';
import { CSSProperties, memo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import { DateRangeCompareValue } from '@/components/DateRangeCompare';
import StatisticCard from '@/components/StatisticCard';
import { labelFormatterByDisplayType } from '@/components/StatisticCard/labelFormatterByDisplayType';
import { trpcQuery } from '@/libs/trpc/client';
import { formatTokenNumber, formatTokenToArray } from '@/utils/format';

const Token = memo<{ compare: DateRangeCompareValue; style?: CSSProperties }>(
  ({ compare, style }) => {
    const { t } = useTranslation('common');
    const [sum, setSum] = useState<null | number>(null);
    const { data, isLoading } = trpcQuery.overview.getRangeToken.useQuery({
      display: compare.display,
      range: compare.range.map((v) => v.format('YYYY-MM-DD')) as [string, string],
    });

    return (
      <StatisticCard
        chart={
          <BarChart
            categories={data?.models || []}
            customTooltip={({ active, payload, label, valueFormatter }) => {
              if (active && payload) {
                return (
                  <ChartTooltipFrame>
                    <Flexbox
                      horizontal
                      justify={'space-between'}
                      paddingBlock={8}
                      paddingInline={16}
                    >
                      <Typography.Paragraph ellipsis style={{ margin: 0 }}>
                        {label}
                      </Typography.Paragraph>
                      <span style={{ fontWeight: 'bold' }}>
                        {formatTokenNumber(
                          payload.reduce((acc: number, cur: any) => acc + cur.value, 0),
                        )}
                      </span>
                    </Flexbox>
                    <Divider style={{ margin: 0 }} />
                    <Flexbox
                      gap={4}
                      paddingBlock={8}
                      paddingInline={16}
                      style={{ flexDirection: 'column-reverse', marginTop: 4 }}
                    >
                      {payload.map(({ value, color, name }: any, idx: number) => (
                        <ChartTooltipRow
                          color={color}
                          key={`id-${idx}`}
                          name={name}
                          value={(valueFormatter as any)?.(value)}
                        />
                      ))}
                    </Flexbox>
                  </ChartTooltipFrame>
                );
              }
              return null;
            }}
            data={data?.data || []}
            height={540}
            index={'date'}
            loading={isLoading}
            onValueChange={(v) => {
              if (!v?.categoryClicked) setSum(null);
              const token = data?.data.reduce((acc: number, cur: any) => {
                if (!v?.categoryClicked || !cur?.[v.categoryClicked]) return acc;
                return acc + (cur?.[v.categoryClicked] || 0);
              }, 0) as number;
              setSum(token);
            }}
            showYAxis={false}
            stack
            valueFormatter={formatTokenNumber}
            xAxisLabelFormatter={(v) => labelFormatterByDisplayType(v, compare.display)}
          />
        }
        loading={isLoading}
        statistic={{
          precision: 0,
          suffix: formatTokenToArray(sum || data?.sum)[1],
          value: formatTokenToArray(sum || data?.sum)[0] || '--',
        }}
        style={style}
        title={t('statistics.token')}
      />
    );
  },
);

export default Token;
