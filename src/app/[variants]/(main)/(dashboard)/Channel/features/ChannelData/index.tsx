'use client';

import { Grid } from '@lobehub/ui';
import { useResponsive } from 'antd-style';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import Block from '@/components/Block';
import DateRangeCompare, {
  DateRangeCompareValue,
  defaultDateRangeCompareValue,
} from '@/components/DateRangeCompare';

import ModelCalls from '../../../features/ModelCalls';
import Spend from '../../../features/Spend';
import Token from './Token';

const ChannelData = () => {
  const { t } = useTranslation('common');
  const { mobile } = useResponsive();
  const [compareValue, setCompareValue] = useState<DateRangeCompareValue>(
    defaultDateRangeCompareValue,
  );

  return (
    <Block gap={16} title={t('statisticGroup.channelData')}>
      <DateRangeCompare
        onChange={setCompareValue}
        showLabel
        style={{ marginInline: mobile ? 12 : 0 }}
        value={compareValue}
      />
      <Grid maxItemWidth={320} rows={2}>
        <Spend compare={compareValue} />
        <Token
          compare={compareValue}
          style={{
            gridRow: mobile ? undefined : 'span 2',
          }}
        />
        <ModelCalls compare={compareValue} />
      </Grid>
    </Block>
  );
};

export default ChannelData;
