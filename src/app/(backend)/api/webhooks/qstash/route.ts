import { verifySignatureAppRouter } from '@upstash/qstash/nextjs';
import { NextResponse } from 'next/server';

import { taskManager } from '@/server/modules/AsyncTaskManager';

/**
 * QStash Webhook 统一入口
 *
 * @description 接收所有 QStash 任务，验证签名，并根据任务名称分发给对应的处理器
 */
export const POST = verifySignatureAppRouter(async (request: Request) => {
  // 解析任务并分发
  try {
    const body = await request.json();
    const { taskName, ...taskPayload } = body;

    if (!taskName) {
      return NextResponse.json({ error: 'taskName is required', success: false }, { status: 400 });
    }

    // 3. 执行任务
    const result = await taskManager.run(taskName, taskPayload);

    return NextResponse.json({ result, success: true });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`QStash Webhook Error: ${errorMessage}`, error);
    return NextResponse.json({ error: errorMessage, success: false }, { status: 500 });
  }
});
