import { get } from '@vercel/edge-config';
import { NextResponse } from 'next/server';

import { DEFAULT_WEBHOOK_SETTINGS } from '@/const/webhook';
import { checkVercelEnv } from '@/envs/vercel';
import { DiscordClient } from '@/server/modules/discord';
import { merge } from '@/utils/merge';

export const GET = async (): Promise<NextResponse> => {
  // Check if the Vercel environment is valid
  const isVercelEnvValid = checkVercelEnv();
  if (!isVercelEnvValid) {
    return NextResponse.json({ error: 'Invalid Vercel environment' }, { status: 500 });
  }
  // Send a message to the Discord webhook
  const data = await get('webhooks');
  const config = merge(DEFAULT_WEBHOOK_SETTINGS, data);
  try {
    const client = new DiscordClient(config);
    await client.send();

    return NextResponse.json({ error: 'Message sent successfully' }, { status: 200 });
  } catch (error) {
    console.error('Error sending Discord webhook:', error);
    return NextResponse.json({ error: 'Failed to send message' }, { status: 500 });
  }
};
