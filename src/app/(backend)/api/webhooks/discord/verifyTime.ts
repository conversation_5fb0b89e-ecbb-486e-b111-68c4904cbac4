export const verifyTime = (time: string) => {
  const now = new Date();
  const currentHour = now.getHours();
  const currentMinute = now.getMinutes();
  const [configHour, configMinute] = time.split(':').map(Number);
  // 如果配置的分钟是 0，检查 0-29 分钟段
  // 如果配置的分钟是 30，检查 30-59 分钟段
  if (configHour === currentHour) {
    if (configMinute === 0 && currentMinute < 30)
      return {
        configHour,
        configMinute,
        currentHour,
        currentMinute,
        status: true,
      };
    if (configMinute === 30 && currentMinute >= 30)
      return {
        configHour,
        configMinute,
        currentHour,
        currentMinute,
        status: true,
      };
  }
  return {
    configHour,
    configMinute,
    currentHour,
    currentMinute,
    status: false,
  };
};
