import { get } from '@vercel/edge-config';
import { NextResponse } from 'next/server';

import { DEFAULT_WEBHOOK_SETTINGS } from '@/const/webhook';
import { checkVercelEnv } from '@/envs/vercel';
import { DiscordClient } from '@/server/modules/discord';
import { merge } from '@/utils/merge';

import { verifySignature } from './verifySignature';
import { verifyTime } from './verifyTime';

export const GET = async (): Promise<NextResponse> => {
  // Check if the request is authorized
  const isValid = await verifySignature();
  if (!isValid) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  // Check if the Vercel environment is valid
  const isVercelEnvValid = checkVercelEnv();
  if (!isVercelEnvValid) {
    return NextResponse.json({ error: 'Invalid Vercel environment' }, { status: 500 });
  }
  // Check if the time is valid
  const data = await get('webhooks');
  const config = merge(DEFAULT_WEBHOOK_SETTINGS, data);
  const { time, enabled } = config;

  if (!enabled) return NextResponse.json({ error: 'Webhook is disabled' }, { status: 200 });

  const isTimeValid = verifyTime(time);
  if (!isTimeValid.status)
    return NextResponse.json({ error: 'Time is not valid', ...isTimeValid }, { status: 200 });

  // Send a message to the Discord webhook
  try {
    const client = new DiscordClient(config);
    await client.send();

    return NextResponse.json(
      { message: 'Message sent successfully', ...isTimeValid },
      { status: 200 },
    );
  } catch (error) {
    console.error('Error sending Discord webhook:', error);
    return NextResponse.json({ error: 'Failed to send message' }, { status: 500 });
  }
};
