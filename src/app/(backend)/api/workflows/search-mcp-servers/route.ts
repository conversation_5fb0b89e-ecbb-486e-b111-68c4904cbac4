import { serve } from '@upstash/workflow/nextjs';
import { MCPSearcher } from 'mcp-crawler';

import { redisService } from '@/server/services/redis';
import { workflowService } from '@/server/services/workflow';

export const { POST } = serve(async (context) => {
  // 步骤 1: 搜索 GitHub 仓库
  const searchResults = await context.run('search-github-repos', async () => {
    try {
      const { maxPages } = (context.requestPayload || {}) as any;
      const searcher = new MCPSearcher();

      console.log(`🔍 使用默认搜索策略搜索 MCP Server`);
      const results = await searcher.search({ maxPages });

      console.log(`搜索完成，找到 ${results.length} 个仓库`);
      return results;
    } catch (error) {
      console.error('GitHub 搜索失败:', error);
      throw error;
    }
  });

  // 步骤 2: 检查 Redis 中已处理的 URL
  const newUrls = await context.run('check-processed-urls', async () => {
    const allUrls = searchResults.map((result) => result.url);

    // 获取新的（未处理的）URL
    const newUrls = await redisService.getNewUrls(allUrls);
    const existingCount = allUrls.length - newUrls.length;

    console.log(
      `检查完成: 总共 ${allUrls.length} 个仓库，其中 ${existingCount} 个已处理，${newUrls.length} 个为新发现`,
    );

    return newUrls;
  });

  // 步骤 3: 调用 add-mcp-plugins 处理新发现的仓库
  const result = await context.run('import-new-plugins', async () => {
    if (newUrls.length === 0) {
      console.log('没有新的 MCP Server 需要处理');
      return {
        import: {
          failed: 0,
          failedUrls: [],
          skipped: 0,
          success: 0,
        },
        search: {
          existing: searchResults.length,
          failed: 0,
          failedUrls: [],
          newFound: 0,
          total: searchResults.length,
        },
      };
    }

    console.log(`开始处理 ${newUrls.length} 个新发现的 MCP Server...`);

    try {
      // 调用现有的 add-mcp-plugins workflow
      const importResult = await workflowService.trigger('add-mcp-plugins', {
        githubUrls: newUrls,
      });

      // 将处理的URL标记为已处理（无论成功或失败）
      await redisService.addProcessedUrls(newUrls);

      // 更新最后更新时间
      await redisService.updateLastUpdated();

      console.log('🎉 处理完成!');
      console.log(`导入结果:`, importResult);

      return {
        import: importResult,
        search: {
          existing: searchResults.length - newUrls.length,
          failed: 0,
          failedUrls: [],
          newFound: newUrls.length,
          total: searchResults.length,
        },
      };
    } catch (error) {
      console.error('导入插件时出错:', error);

      // 即使失败也标记为已处理，避免重复尝试
      await redisService.addProcessedUrls(newUrls);

      return {
        import: {
          error: error instanceof Error ? error.message : '未知错误',
          failed: newUrls.length,
          failedUrls: newUrls,
          skipped: 0,
          success: 0,
        },
        search: {
          existing: searchResults.length - newUrls.length,
          failed: 0,
          failedUrls: [],
          newFound: newUrls.length,
          total: searchResults.length,
        },
      };
    }
  });

  // 输出最终统计信息
  const stats = await redisService.getStats();
  console.log(`Redis 当前存储: ${stats.total} 个已处理的URL，最后更新: ${stats.lastUpdated}`);

  return result;
});
