import { serve } from '@upstash/workflow/nextjs';

import { workflowService } from '@/server/services/workflow';
import { CrawlRequest } from '@/server/workflow/crawlerMCPPlugins';

export const maxDuration = 90;

export const { POST } = serve(async (context) => {
  // Step 1: parse request
  const { githubUrls } = await context.run('parse-request', async () => {
    const body = context.requestPayload as CrawlRequest;

    if (!body.githubUrls || !Array.isArray(body.githubUrls) || body.githubUrls.length === 0) {
      throw new Error('`githubUrls` 必须是一个非空数组');
    }

    console.log(`收到 ${body.githubUrls.length} 个 GitHub URL，开始处理...`);

    return { githubUrls: body.githubUrls };
  });

  // Step 2: batch call mcp plugin generation ,avoid  QStash rate limit

  const results = await Promise.all(
    githubUrls.map((url) =>
      context.run<any>(`add-github:${url}`, () =>
        workflowService.trigger(
          'add-mcp-plugin',
          { githubUrl: url },
          {
            flowControl: { key: 'add-mcp-plugin', parallelism: 20, rate: 20 },
          },
        ),
      ),
    ),
  );

  const successful = results.filter((r) => r.status === 'fulfilled').length;
  const failed = results.filter((r) => r.status === 'rejected').length;

  console.log(`批量处理完成: 成功 ${successful} 个，失败 ${failed} 个`);

  return {
    failed,
    results: results.map((result, index) => ({
      status: result.status,
      url: githubUrls[index],
      ...(result.status === 'fulfilled'
        ? { data: result.value }
        : { error: result.reason?.message || 'Unknown error' }),
    })),
    successful,
    total: githubUrls.length,
  };
});
