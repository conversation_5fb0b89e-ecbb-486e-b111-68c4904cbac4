import { MarketAdmin } from '@lobehub/market-sdk';
import { serve } from '@upstash/workflow/nextjs';

import { workflowService } from '@/server/services/workflow';

const marketAdmin = new MarketAdmin({
  apiKey: process.env.MARKET_ADMIN_API_KEY,
  baseURL: process.env.MARKET_BASE_URL,
});

export const { POST } = serve(async (context) => {
  // 步骤 1: 解析并验证请求
  const identifiers = await context.run('get-identifiers', async () => {
    const response = await marketAdmin.plugins.getIncompleteI18nPlugins();

    return response.map((plugin) => plugin.identifier);
  });

  // 步骤 2: 并行生成多语言翻译
  return await Promise.all(
    identifiers.map((identifier) =>
      context.run<any>(`trigger-i18n:${identifier}`, () =>
        workflowService.trigger('add-mcp-i18n', { identifier }),
      ),
    ),
  );
});
