import { MarketAdmin } from '@lobehub/market-sdk';
import { serve } from '@upstash/workflow/nextjs';

import { CrawlerMCPPlugins } from '@/server/workflow/crawlerMCPPlugins';

const crawlerMCPPlugins = new CrawlerMCPPlugins();

const marketAdmin = new MarketAdmin({
  apiKey: process.env.MARKET_ADMIN_API_KEY,
  baseURL: process.env.MARKET_BASE_URL,
});

export const maxDuration = 60;

export interface GenerateSummariesResult {
  skipped: number;
  success: number;
  total: number;
}

export const { POST } = serve(async (context) => {
  // 步骤 1: 获取需要生成 summary 的插件列表
  const pluginsWithoutSummary = await context.run('fetch-plugins-without-summary', async () => {
    const plugins = await marketAdmin.plugins.getVerifiedPluginsWithoutSummary();

    if (plugins.length === 0) {
      return [];
    }

    console.log(`📋 找到 ${plugins.length} 个需要生成 summary 的插件`);
    return plugins;
  });

  // 如果没有需要处理的插件，直接返回
  if (pluginsWithoutSummary.length === 0) {
    return '✅ 所有已验证的插件都已经有 summary 了';
  }

  // 步骤 3: 并行生成所有插件的 summary
  const results = await Promise.all(
    pluginsWithoutSummary.map((plugin) =>
      context.run<{
        identifier: string;
        skipped?: boolean;
        success?: boolean;
      }>(`generate-summary:plugin-${plugin.identifier}`, async () => {
        try {
          console.log(
            `🎯 正在为插件 "${plugin.manifest?.name}" (ID: ${plugin.identifier}) 生成 summary...`,
          );

          // 生成 summary
          const manifest = await crawlerMCPPlugins.generateOverview(plugin.manifest as any);

          if (!manifest.overview?.summary) {
            console.warn(`⚠️  无法为插件 "${plugin.manifest!.name}" 生成 summary，跳过处理`);

            return { identifier: plugin.identifier, skipped: true, success: true };
          }

          // 更新插件的 summary

          await marketAdmin.plugins.updatePluginVersion(plugin.identifier, plugin.versionId, {
            summary: manifest.overview.summary,
          });

          console.log(`✅ 成功为插件 "${plugin.identifier}" 生成并更新 summary`);

          return { identifier: plugin.identifier, success: true };
        } catch (error) {
          console.error(`❌ 为插件 "${plugin.identifier}" 生成 summary 失败:`, error);

          return { identifier: plugin.identifier, success: false };
        }
      }),
    ),
  );

  // 步骤 4: 汇总结果
  return await context.run('summarize-results', async () => {
    const processed = results.filter((r) => r.success).length;

    const skipped = results.filter((r) => r.success && r.skipped).length;

    const finalResult: GenerateSummariesResult = {
      skipped,
      success: processed,
      total: pluginsWithoutSummary.length,
    };

    console.log('📊 Summary 生成完成统计:');
    console.log(`  总计: ${finalResult.total} 个插件`);
    console.log(`  成功: ${finalResult.success} 个`);

    return finalResult;
  });
});
