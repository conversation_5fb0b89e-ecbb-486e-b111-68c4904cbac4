import { MarketAdmin } from '@lobehub/market-sdk';
import { serve } from '@upstash/workflow/nextjs';
import { BadgeChecker } from 'mcp-crawler';

const marketAdmin = new MarketAdmin({
  apiKey: process.env.MARKET_ADMIN_API_KEY,
  baseURL: process.env.MARKET_BASE_URL,
});

const badgeChecker = new BadgeChecker();

export interface CheckSinglePluginRequest {
  identifier: string;
}

export const { POST } = serve(
  async (context) => {
    const { identifier } = context.requestPayload as CheckSinglePluginRequest;

    return context.run(`check-single-plugin-badge:${identifier}`, async () => {
      console.log(`🔍 开始检查插件 "${identifier}" 的 MCP Badge...`);

      // 通过 MarketAdmin SDK 获取插件信息
      const plugin = await marketAdmin.plugins.getPlugin(identifier);

      if (!plugin) {
        return `⚠️  未找到插件 "${identifier}"`;
      }

      console.log(`📋 获取到插件信息: ${plugin.name}`);

      // 使用 BadgeChecker 检查 MCP Badge
      const result = await badgeChecker.checkPluginBadge(
        identifier,
        plugin.github?.url,
        plugin.homepage,
      );

      if (result.hasBadge) {
        await marketAdmin.plugins.updatePlugin(plugin.id, { isClaimed: true });

        // revalidate
        await fetch(`https://lobehub.com/api/revalidate/mcp/${identifier}`);
      }

      return `插件 "${identifier}" 检查完成: ${result.hasBadge ? '✅ 已被认领' : '暂未认领'}`;
    });
  },

  { flowControl: { key: 'check-plugin-badges', parallelism: 3 } },
);
