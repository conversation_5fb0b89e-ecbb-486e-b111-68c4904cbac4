import { serve } from '@upstash/workflow/nextjs';

import { McpI18nRequest, mcpI18nHandler } from '@/server/workflow/mcpI18nHandler';

export const { POST } = serve(
  async (context) => {
    // 步骤 1: 解析并验证请求
    const { identifier, version, targetLocales, versionId } = await context.run(
      'parse-request',
      async () => {
        const body = context.requestPayload as McpI18nRequest;

        const validatedParams = await mcpI18nHandler.validateRequest(body);

        console.log(`🌏 收到 i18n 请求: ${validatedParams.identifier}，开始处理...`);
        console.log(
          `插件标识符: ${validatedParams.identifier}, 版本: ${validatedParams.version}, 目标语言(${validatedParams.targetLocales.length}): ${validatedParams.targetLocales.join(', ')}`,
        );

        return validatedParams;
      },
    );

    // 步骤 2: 检查缺失的翻译
    const missingLocales = await context.run('check-missing-localizations', async () => {
      const missing = await mcpI18nHandler.checkMissingLocalizations(
        identifier,
        versionId,
        targetLocales,
      );

      console.log(`需要生成翻译的语言（${missing.length}）: ${missing.join(', ')}`);
      return missing;
    });

    if (missingLocales.length === 0) {
      return `${identifier}@${version} 不需要翻译`;
    }

    // 步骤 3: 获取插件 manifest
    const manifest = await context.run('fetch-plugin-manifest', async () => {
      try {
        const fetchedManifest = await mcpI18nHandler.fetchPluginManifest(identifier, version);

        console.log(`✅ 成功获取插件 ${identifier}@${fetchedManifest.version} manifest`);
        return fetchedManifest;
      } catch (error) {
        console.error(`❌ 获取插件 manifest 失败:`, error);
        throw error;
      }
    });

    // 步骤 4: 并行生成多语言翻译
    const localizations = await Promise.all(
      missingLocales.map((locale) =>
        context.run<any>(`generate-i18n:${locale}`, () =>
          mcpI18nHandler.generateSingleLocalization(manifest, locale),
        ),
      ),
    );
    console.log('翻译数据：', localizations);

    // 步骤 5: 保存国际化数据到数据库
    return await context.run('save-i18n-data', async () => {
      const successLocalizations = localizations.filter((l) => !!l);
      const failureLocalizations = localizations.filter((l) => !l);

      console.log(
        `i18n 生成完成: 成功 ${successLocalizations.length} 个，失败 ${failureLocalizations.length} 个`,
      );

      if (successLocalizations.length === 0) {
        console.log('❌ 没有成功生成的翻译，跳过保存步骤');
        return '❌ 没有成功生成的翻译，跳过保存步骤';
      }

      return await mcpI18nHandler.saveLocalizations(
        identifier,
        manifest.version,
        successLocalizations,
      );
    });
  },
  {
    flowControl: { key: 'add-mcp-i18n', parallelism: 20, rate: 20 },
  },
);
