import { serve } from '@upstash/workflow/nextjs';
import { MCPManifest } from 'mcp-crawler';

import { workflowService } from '@/server/services/workflow';
import { CrawlRequest, CrawlResult, CrawlerMCPPlugins } from '@/server/workflow/crawlerMCPPlugins';

const crawlerMCPPlugins = new CrawlerMCPPlugins();

export const maxDuration = 90;

export const { POST } = serve(async (context) => {
  // 步骤 1: 解析并验证请求
  const { githubUrls, ownerId } = await context.run('parse-request', async () => {
    const body = context.requestPayload as CrawlRequest;

    if (!body.githubUrls || !Array.isArray(body.githubUrls) || body.githubUrls.length === 0) {
      throw new Error('`githubUrls` 必须是一个非空数组');
    }

    console.log(`收到 ${body.githubUrls.length} 个 GitHub URL，开始处理...`);
    // 在这里可以设置一个默认的 owner ID
    const targetOwnerId = body.ownerId || 1;
    console.log(`使用 owner ID: ${targetOwnerId}`);

    return { githubUrls: body.githubUrls, ownerId: targetOwnerId };
  });

  // 步骤 2: 并行抓取所有 URL (Fan-Out/Fan-in)
  const results: CrawlResult[] = await Promise.all(
    githubUrls.map((url) =>
      context.run<CrawlResult>(
        `crawl:${new URL(url).pathname.slice(1).replaceAll(/[./]/g, '-')}`,
        () => crawlerMCPPlugins.crawlSingleUrl(url),
      ),
    ),
  );

  // 步骤 3: 并行验证所有有效的 manifest
  const validatedResults = await Promise.all(
    results
      .filter((r) => r.success)
      .map(({ manifest }) =>
        context.run<CrawlResult['manifest']>(
          `validate:${manifest.identifier.replaceAll(/[./]/g, '-')}`,
          async () => {
            const result = await crawlerMCPPlugins.validatePlugin(manifest);
            if (result.success && !result.skipped) return result.manifest;

            return manifest;
          },
        ),
      ),
  );

  // 步骤 4: 并行生成 Overview 概述
  const manifestsWithOverview = await Promise.all(
    validatedResults.map((manifest) =>
      context.run<MCPManifest>(`overview:${manifest.identifier.replaceAll(/[./]/g, '-')}`, () => {
        if (manifest.isValidated) return crawlerMCPPlugins.generateOverview(manifest);

        return manifest;
      }),
    ),
  );

  // 步骤 5: 通过 API 导入插件数据
  await context.run('import-plugins-via-api', async () => {
    const successResults = manifestsWithOverview.filter(Boolean);
    const failureResults = results.filter((r) => !r.success);

    console.log(`抓取完成: 成功 ${successResults.length} 个，失败 ${failureResults.length} 个`);

    if (successResults.length === 0) {
      console.log('没有成功抓取的插件，跳过导入步骤');
      return '没有成功抓取的插件，跳过导入步骤';
    }

    try {
      await crawlerMCPPlugins.importPluginsViaApi(successResults, ownerId);
    } catch (importError) {
      console.error('导入插件时出错:', importError);
    }

    return `导入完成: 成功 ${successResults.length} 个，失败 ${failureResults.length} 个`;
  });

  // 步骤 6: 触发插件的 i18n
  await Promise.all(
    manifestsWithOverview.map((m) =>
      context.run(`add-i18n:${m.identifier}`, () =>
        workflowService.trigger('add-mcp-i18n', { identifier: m.identifier }),
      ),
    ),
  );
});
