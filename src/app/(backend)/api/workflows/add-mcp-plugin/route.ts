import { serve } from '@upstash/workflow/nextjs';
import { MCPManifest } from 'mcp-crawler';

import { workflowService } from '@/server/services/workflow';
import { CrawlResult, CrawlerMCPPlugins } from '@/server/workflow/crawlerMCPPlugins';

const crawlerMCPPlugins = new CrawlerMCPPlugins();

export const maxDuration = 90;

export const { POST } = serve(
  async (context) => {
    // 步骤 1: 解析并验证请求
    const { githubUrl, ownerId, isDuplicate } = await context.run('parse-request', async () => {
      const body = context.requestPayload as { githubUrl: string };

      // 在这里可以设置一个默认的 owner ID
      const targetOwnerId = 1;

      const isDuplicate = await crawlerMCPPlugins.checkIsDuplicate(body.githubUrl);

      return { githubUrl: body.githubUrl, isDuplicate, ownerId: targetOwnerId };
    });

    if (isDuplicate) return '已收录';

    // 步骤 2: 抓取 URL (Fan-Out/Fan-in)
    const results: CrawlResult = await context.run<CrawlResult>(
      `crawl:${new URL(githubUrl).pathname.slice(1).replaceAll(/[./]/g, '-')}`,
      () => crawlerMCPPlugins.crawlSingleUrl(githubUrl),
    );

    if (!results.success) return results.error;

    // 步骤 3: 验证有效的 manifest
    const validatedResult = await context.run<CrawlResult['manifest']>(
      `validate:${results.manifest.identifier.replaceAll(/[./]/g, '-')}`,
      async () => {
        const result = await crawlerMCPPlugins.validatePlugin(results.manifest);
        if (result.success && !result.skipped) return result.manifest;

        return results.manifest;
      },
    );

    // 步骤 4: 生成 Overview 概述
    const manifestsWithOverview = await context.run<MCPManifest>(
      `overview:${validatedResult.identifier.replaceAll(/[./]/g, '-')}`,
      () => {
        if (validatedResult.isValidated) return crawlerMCPPlugins.generateOverview(validatedResult);

        return validatedResult;
      },
    );

    // 步骤 5: 通过 API 导入插件数据
    await context.run('import-plugins-via-api', async () => {
      try {
        await crawlerMCPPlugins.importPluginsViaApi([manifestsWithOverview], ownerId);
      } catch (importError) {
        console.error('导入插件时出错:', importError);
      }

      return `导入完成`;
    });

    // 步骤 6: 触发插件的 i18n
    await context.run(`add-i18n:${manifestsWithOverview.identifier}`, () =>
      workflowService.trigger('add-mcp-i18n', { identifier: manifestsWithOverview.identifier }),
    );
  },
  {
    flowControl: { key: 'add-mcp-plugin', parallelism: 20, rate: 20 },
  },
);
