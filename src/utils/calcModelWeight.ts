import { ModelInfoItem } from '@/types/litellm';

export const calcModelWeight = (
  data: ModelInfoItem[],
): {
  id: string;
  name: string;
  weight: number;
}[] => {
  if (data.length === 1)
    return [
      {
        id: data[0].model_info.id,
        name: data[0].model_name,
        weight: 1,
      },
    ];

  const hasWeight = data.some((model) => model.litellm_params.weight !== undefined);
  const hasRPM = data.some((model) => model.litellm_params.rpm !== undefined);

  // 计算权重
  const weights = data.map((model) => {
    if (hasWeight) {
      return {
        id: model.model_info.id,
        name: model.model_name,
        weight: model.litellm_params.weight ?? 0, // 如果有weight配置,没配的默认为0
      };
    } else if (hasRPM) {
      return {
        id: model.model_info.id,
        name: model.model_name,
        weight: model.litellm_params.rpm ?? 0,
      };
    } else if (model.litellm_params.tpm !== undefined) {
      return {
        id: model.model_info.id,
        name: model.model_name,
        weight: model.litellm_params.tpm,
      };
    } else {
      return {
        id: model.model_info.id,
        name: model.model_name,
        weight: 1,
      };
    }
  });

  // 计算总权重
  const totalWeight = weights.reduce((sum, weight) => sum + weight.weight, 0);

  // 计算权重百分比
  return weights.map((item) => ({
    ...item,
    weight: Math.round((totalWeight > 0 ? item.weight / totalWeight : 0) * 100) / 100,
  }));
};
