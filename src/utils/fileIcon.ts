import { ReactElement } from 'react';

import {
  FILE_DEFAULT_ICON,
  FILE_TYPE_ICON_MAP,
} from '@/app/[variants]/(main)/users/[slug]/features/Statistics/features/FileListDrawer/constants';

/**
 * 从文件名中提取扩展名
 * @param filename 文件名
 * @returns 小写的文件扩展名（包含点号）
 */
export const getFileExtension = (filename: string): string => {
  const lastDotIndex = filename.lastIndexOf('.');
  if (lastDotIndex === -1 || lastDotIndex === filename.length - 1) {
    return '';
  }
  return filename.slice(lastDotIndex).toLowerCase();
};

/**
 * 根据文件名获取对应的图标
 * @param filename 文件名
 * @returns 对应的图标 JSX 元素
 */
export const getFileIcon = (filename: string): ReactElement => {
  const extension = getFileExtension(filename);

  if (!extension) {
    return FILE_DEFAULT_ICON;
  }

  // 在 FILE_TYPE_ICON_MAP 中查找匹配的图标
  for (const [extensions, icon] of Object.entries(FILE_TYPE_ICON_MAP)) {
    // 将扩展名字符串分割成数组，并检查是否包含当前文件的扩展名
    const extensionList = extensions.split('、').map((ext) => ext.trim().toLowerCase());
    if (extensionList.includes(extension)) {
      return icon;
    }
  }

  // 如果没有找到匹配的图标，返回默认图标
  return FILE_DEFAULT_ICON;
};
