export function calculateAverage(numbers: number[]) {
  if (numbers.length === 0) return 0;

  const sum = numbers.reduce((acc, num) => acc + num, 0);
  return sum / numbers.length;
}

export function calculateMedian(numbers: number[]) {
  if (numbers.length === 0) return 0;

  const sorted = numbers.slice().sort((a, b) => a - b);
  const middle = Math.floor(sorted.length / 2);

  if (sorted.length % 2 === 0) {
    return (sorted[middle - 1] + sorted[middle]) / 2;
  } else {
    return sorted[middle];
  }
}

export function calculateMax(numbers: number[]): number {
  if (numbers.length === 0) return 0; // 如果数组为空,返回null

  return Math.max(...numbers);
}

export function calculatePercentile(numbers: number[], percentile: 80 | 90 | number): number {
  if (numbers.length === 0) return 0;
  if (percentile < 0 || percentile > 100) throw new Error('Percentile must be between 0 and 100');

  // 先对数组进行排序
  const sorted = numbers.slice().sort((a, b) => a - b);

  // 计算位置
  const index = (percentile / 100) * (sorted.length - 1);

  // 如果index是整数,直接返回该位置的值
  if (Number.isInteger(index)) {
    return sorted[index];
  } else {
    // 如果index不是整数,需要进行插值
    const lowerIndex = Math.floor(index);
    const upperIndex = Math.ceil(index);
    const lowerValue = sorted[lowerIndex];
    const upperValue = sorted[upperIndex];

    // 线性插值
    const fraction = index - lowerIndex;
    return lowerValue + (upperValue - lowerValue) * fraction;
  }
}
