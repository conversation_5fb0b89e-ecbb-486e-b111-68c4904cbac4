import { createEnv } from '@t3-oss/env-nextjs';
import { z } from 'zod';

const extractConfigId = (url?: string) => {
  if (!url) return;
  const match = url.match(/ecfg_[\dA-Za-z]+/);
  return match ? match[0] : undefined;
};

export const getVercelConfig = () => {
  return createEnv({
    runtimeEnv: {
      VERCEL_ACCESS_TOKEN: process.env.VERCEL_ACCESS_TOKEN,
      VERCEL_EDGE_CONFIG_ID: extractConfigId(process.env.EDGE_CONFIG),
      VERCEL_TEAM_ID: process.env.VERCEL_TEAM_ID,
    },
    server: {
      VERCEL_ACCESS_TOKEN: z.string().optional(),
      VERCEL_EDGE_CONFIG_ID: z.string().optional(),
      VERCEL_TEAM_ID: z.string().optional(),
    },
  });
};

export const vercelEnv = getVercelConfig();
export const checkVercelEnv = () =>
  Boolean(vercelEnv.VERCEL_ACCESS_TOKEN && vercelEnv.VERCEL_EDGE_CONFIG_ID);
