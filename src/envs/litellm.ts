import { createEnv } from '@t3-oss/env-nextjs';
import { z } from 'zod';

export const getLiteLLMConfig = () => {
  return createEnv({
    runtimeEnv: {
      CHAT_LITE_LLM_BASE_URL: process.env.CHAT_LITE_LLM_BASE_URL,
      CHAT_LITE_LLM_DATABASE_URL: process.env.CHAT_LITE_LLM_DATABASE_URL,
      CHAT_LITE_LLM_MASTER_KEY: process.env.CHAT_LITE_LLM_MASTER_KEY,
    },
    server: {
      CHAT_LITE_LLM_BASE_URL: z.string().url(),
      CHAT_LITE_LLM_DATABASE_URL: z.string(),
      CHAT_LITE_LLM_MASTER_KEY: z.string(),
    },
  });
};

export const liteLLMEnv = getLiteLLMConfig();
