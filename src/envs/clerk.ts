import { createEnv } from '@t3-oss/env-nextjs';
import { z } from 'zod';

export const getClerkConfig = () => {
  return createEnv({
    runtimeEnv: {
      CHAT_CLERK_APPLICATION_ID: process.env.CHAT_CLERK_APPLICATION_ID,
      CHAT_CLERK_INSTANCE_ID: process.env.CHAT_CLERK_INSTANCE_ID,
      CHAT_CLERK_SECRET_KEY: process.env.CLERK_SECRET_KEY,
    },
    server: {
      CHAT_CLERK_APPLICATION_ID: z.string().optional(),
      CHAT_CLERK_INSTANCE_ID: z.string().optional(),
      CHAT_CLERK_SECRET_KEY: z.string().optional(),
    },
  });
};

export const clerkEnv = getClerkConfig();
