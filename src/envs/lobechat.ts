import { createEnv } from '@t3-oss/env-nextjs';
import { z } from 'zod';

export const getLobeChatDBConfig = () => {
  return createEnv({
    runtimeEnv: {
      CHAT_CLOUD_DATABASE_URL: process.env.CHAT_CLOUD_DATABASE_URL,
      CHAT_DATABASE_DRIVER: process.env.CHAT_DATABASE_DRIVER || 'neon',
      CHAT_DATABASE_TEST_URL: process.env.CHAT_DATABASE_TEST_URL,
      CHAT_DATABASE_URL: process.env.CHAT_DATABASE_URL,
      CHAT_KEY_VAULTS_SECRET: process.env.CHAT_KEY_VAULTS_SECRET,
    },
    server: {
      CHAT_CLOUD_DATABASE_URL: z.string(),
      CHAT_DATABASE_DRIVER: z.enum(['neon', 'node']),
      CHAT_DATABASE_TEST_URL: z.string().optional(),
      CHAT_DATABASE_URL: z.string(),
      CHAT_KEY_VAULTS_SECRET: z.string(),
    },
  });
};

export const lobechatDBEnv = getLobeChatDBConfig();
