import { createEnv } from '@t3-oss/env-nextjs';
import { z } from 'zod';

export const getLobeConsoleEnv = () => {
  return createEnv({
    runtimeEnv: {
      LOBE_CONSOLE_URL: process.env.LOBE_CONSOLE_URL,
      LOBE_LICENSE: process.env.LOBE_LICENSE,
    },
    server: {
      LOBE_CONSOLE_URL: z.string(),
      LOBE_LICENSE: z.string(),
    },
  });
};

export const lobeConsoleEnv = getLobeConsoleEnv();
