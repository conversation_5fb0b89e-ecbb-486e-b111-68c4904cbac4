import { createEnv } from '@t3-oss/env-nextjs';
import { z } from 'zod';

export const getQStashConfig = () => {
  return createEnv({
    runtimeEnv: {
      QSTASH_CURRENT_SIGNING_KEY: process.env.QSTASH_CURRENT_SIGNING_KEY,
      QSTASH_NEXT_SIGNING_KEY: process.env.QSTASH_NEXT_SIGNING_KEY,
      QSTASH_TOKEN: process.env.QSTASH_TOKEN,
    },
    server: {
      QSTASH_CURRENT_SIGNING_KEY: z.string().optional(),
      QSTASH_NEXT_SIGNING_KEY: z.string().optional(),
      QSTASH_TOKEN: z.string().optional(),
    },
  });
};

export const qstashEnv = getQStashConfig();

export const checkQStashEnv = () =>
  Boolean(
    qstashEnv.QSTASH_TOKEN &&
      qstashEnv.QSTASH_CURRENT_SIGNING_KEY &&
      qstashEnv.QSTASH_NEXT_SIGNING_KEY,
  );
