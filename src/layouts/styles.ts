import { createStyles } from 'antd-style';

export const useStyles = createStyles(({ responsive, css, token, isDarkMode, prefixCls }) => ({
  container: css`
    position: relative;
    width: 100%;
    height: 100%;

    &.${prefixCls}-pro-layout {
      &.${prefixCls}-design-pro {
        width: 100%;
        height: 100%;
      }

      .${prefixCls}-layout-sider-children {
        li[role='separator'] {
          display: none;
        }
      }

      .${prefixCls}-pro-sider-logo {
        border: none;

        > a > img {
          height: unset;
        }
      }

      .${prefixCls}-pro-layout-bg-list {
        background: ${token.colorBgLayout};
      }

      .${prefixCls}-pro-layout-content {
        height: 100vh;
        padding: 4px;

        ${responsive.mobile} {
          padding: 0;
        }
      }

      .${prefixCls}-pro-sider .${prefixCls}-layout-sider-children {
        border-inline-end: 0;
      }

      .${prefixCls}-menu-item-selected {
        background: ${isDarkMode ? token.colorFillSecondary : token.colorFillTertiary};
      }

      .${prefixCls}-pro-global-header-header-actions-avatar {
        ${responsive.mobile} {
          padding: 0;
        }
      }

      .${prefixCls}-pro-page-container {
        display: flex;
        flex: 1;
        flex-direction: column;
      }
    }
  `,

  content: css`
    position: relative;

    overflow: hidden auto;

    height: 100%;
    border-radius: 8px;

    background: ${token.colorBgContainer};
    box-shadow: ${token.boxShadowTertiary};

    ${responsive.mobile} {
      border-radius: unset;
      background: ${token.colorBgLayout};
      box-shadow: none;

      .${prefixCls}-page-header {
        padding-inline: 24px;
      }
    }

    .${prefixCls}-pro-grid-content,
      .${prefixCls}-pro-grid-content-children,
      .${prefixCls}-pro-page-container-children-container {
      display: flex;
      flex: 1;
      flex-direction: column;
    }

    .${prefixCls}-pro-page-container-children-container {
      min-height: unset;
      padding-block-end: 0;
      ${responsive.mobile} {
        padding-inline: 0;
      }
    }
  `,
}));
