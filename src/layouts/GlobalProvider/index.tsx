import { ReactNode } from 'react';

import { getServerConfig } from '@/config/serverConfig';
import QueryProvider from '@/layouts/GlobalProvider/Query';
import { ServerConfigStoreProvider } from '@/store/serverConfig/Provider';
import { FeatureFlagProvider } from '@/store/featureFlag/Provider';
import { getAntdLocale } from '@/utils/locale';

import AppTheme from './AppTheme';
import Locale from './Locale';
import StyleRegistry from './StyleRegistry';

interface GlobalLayoutProps {
  appearance: string;
  children: ReactNode;
  isMobile: boolean;
  locale: string;
}

const GlobalLayout = async ({
  children,
  locale: userLocale,
  appearance,
  isMobile,
}: GlobalLayoutProps) => {
  const antdLocale = await getAntdLocale(userLocale);

  // get default feature flags to use with ssr
  const serverConfig = getServerConfig();

  return (
    <StyleRegistry>
      <Locale antdLocale={antdLocale} defaultLang={userLocale}>
        <AppTheme defaultAppearance={appearance}>
          <ServerConfigStoreProvider isMobile={isMobile} serverConfig={serverConfig}>
            <FeatureFlagProvider>
              <QueryProvider>{children}</QueryProvider>
            </FeatureFlagProvider>
          </ServerConfigStoreProvider>
        </AppTheme>
      </Locale>
    </StyleRegistry>
  );
};

export default GlobalLayout;
