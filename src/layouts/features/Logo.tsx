import { useResponsive, useTheme } from 'antd-style';
import Link from 'next/link';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';

import { ProductLogo } from '@/components/Branding';
import { BRANDING_NAME } from '@/const/branding';

const Logo = memo<{ collapsed?: boolean }>(({ collapsed }) => {
  const theme = useTheme();
  const { t } = useTranslation('common');
  const { mobile } = useResponsive();
  return (
    <Link
      href="/"
      style={{ color: theme.colorText }}
      title={t('admin.title', { appName: BRANDING_NAME })}
    >
      <ProductLogo
        extra={collapsed ? undefined : 'Admin'}
        size={28}
        style={{ flex: 'none' }}
        type={collapsed || mobile ? '3d' : 'combine'}
      />
    </Link>
  );
});

export default Logo;
