import { useResponsive, useTheme } from 'antd-style';
import { memo } from 'react';
import { Flexbox } from 'react-layout-kit';

import UserAvatar from '@/features/User/UserAvatar';
import UserInfo from '@/features/User/UserInfo';
import UserPanel from '@/features/User/UserPanel';

const Avatar = memo<{ collapsed?: boolean }>(({ collapsed }) => {
  const theme = useTheme();
  const { mobile } = useResponsive();

  return (
    <Flexbox padding={4}>
      <UserPanel>
        {collapsed || mobile ? (
          <UserAvatar background={theme.colorFill} size={28} />
        ) : (
          <UserInfo size={'small'} />
        )}
      </UserPanel>
    </Flexbox>
  );
});

export default Avatar;
