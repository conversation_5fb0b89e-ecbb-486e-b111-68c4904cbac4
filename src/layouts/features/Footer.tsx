import { createStyles, useResponsive, useTheme } from 'antd-style';
import { memo } from 'react';
import { Flexbox } from 'react-layout-kit';
import urlJoin from 'url-join';

import BrandWatermark from '@/components/BrandWatermark';
import { STATUS_URL } from '@/const/url';
import { serverConfigSelectors, useServerConfigStore } from '@/store/serverConfig';

const useStyles = createStyles(({ css, token }) => {
  return {
    description: css`
      color: ${token.colorTextDescription};
    `,
    logo: css`
      display: flex;
      flex: none;
      align-items: center;
      color: ${token.colorText};
    `,
    status: css`
      border: none !important;
      color-scheme: none;
      background: transparent;
    `,
  };
});

const Footer = memo(() => {
  const { styles } = useStyles();
  const { isDarkMode } = useTheme();
  const { mobile } = useResponsive();
  const { isCustomORG } = useServerConfigStore(serverConfigSelectors.config);

  return (
    <Flexbox
      align={'center'}
      gap={16}
      horizontal
      justify={mobile ? 'center' : 'flex-start'}
      paddingBlock={24}
      paddingInline={mobile ? 24 : 40}
      style={{
        marginInline: mobile ? 0 : -40,
        marginTop: mobile ? 0 : 32,
      }}
      wrap={'wrap'}
    >
      <BrandWatermark />
      {!mobile && !isCustomORG && (
        <iframe
          className={styles.status}
          height="30"
          loading={'lazy'}
          scrolling="no"
          src={urlJoin(STATUS_URL, `badge?theme=${isDarkMode ? 'dark' : 'light'}`)}
          width="250"
        />
      )}
    </Flexbox>
  );
});

export default Footer;
