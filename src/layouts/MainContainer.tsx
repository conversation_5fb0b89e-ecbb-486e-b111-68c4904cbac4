'use client';

import { PageContainer, type PageContainerProps } from '@ant-design/pro-components';
import { PropsWithChildren, memo } from 'react';
import { Flexbox, FlexboxProps } from 'react-layout-kit';

import Footer from '@/layouts/features/Footer';

import { useStyles } from './styles';

const MainContainer = memo<PropsWithChildren<PageContainerProps & Pick<FlexboxProps, 'gap'>>>(
  ({ children, className, gap = 24, ...rest }) => {
    const { cx, styles } = useStyles();

    return (
      <PageContainer className={cx(styles.content, className)} {...rest}>
        <Flexbox flex={1} gap={gap}>
          {children}
        </Flexbox>
        <Footer />
      </PageContainer>
    );
  },
);

export default MainContainer;
