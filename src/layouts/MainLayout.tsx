'use client';

import type { LocaleType } from '@ant-design/pro-layout/es/locales';
import { useThemeMode } from 'antd-style';
import dynamic from 'next/dynamic';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { PropsWithChildren, memo } from 'react';
import { useTranslation } from 'react-i18next';

import LayoutLoading from '@/components/LayoutLoading';
import LangButton from '@/features/User/UserPanel/LangButton';
import ThemeButton from '@/features/User/UserPanel/ThemeButton';
import { normalizeLocale } from '@/locales/resources';
import { useRoutes } from '@/routes';
import { serverConfigSelectors, useServerConfigStore } from '@/store/serverConfig';
import { useUserStore } from '@/store/user';
import { userProfileSelectors } from '@/store/user/selectors';

import Avatar from './features/Avatar';
import Logo from './features/Logo';
import { useStyles } from './styles';

const ProLayout = dynamic(() => import('@ant-design/pro-components').then((mod) => mod.ProLayout), {
  loading: LayoutLoading,
  ssr: false,
});

const MainLayout = memo<PropsWithChildren>(({ children }) => {
  const routers = useRoutes();
  const { styles } = useStyles();
  const { t, i18n } = useTranslation('common');
  const { appearance } = useThemeMode();
  const pathname = usePathname();
  const { brandingName } = useServerConfigStore(serverConfigSelectors.config);
  const user = useUserStore(userProfileSelectors.userProfile);
  const clerkOpenUserProfile = useUserStore((s) => s.clerkOpenUserProfile);

  let normalLang = normalizeLocale(i18n.language);
  if (normalLang === 'ar') normalLang = 'ar-EG';

  return (
    <ProLayout
      actionsRender={() => [
        <LangButton key={'lang'} type={'icon'} />,
        <ThemeButton key={'theme'} type={'icon'} />,
      ]}
      avatarProps={{
        onClick: () => {
          clerkOpenUserProfile?.();
        },
        render: (_, __, { collapsed }) => <Avatar collapsed={collapsed} />,
        title: user?.fullName || user?.username,
      }}
      className={styles.container}
      locale={normalLang as LocaleType}
      location={{ pathname }}
      menu={{
        defaultOpenAll: true,
        ignoreFlatMenu: true,
        locale: false,
      }}
      menuHeaderRender={(_, __, { collapsed } = {}) => <Logo collapsed={collapsed} />}
      menuItemRender={(options, element) => <Link href={options.path!}>{element}</Link>}
      route={routers}
      siderMenuType="group"
      siderWidth={280}
      style={{ width: '100%' }}
      theme={appearance as any}
      title={t('admin.title', { appName: brandingName })}
    >
      {children}
    </ProLayout>
  );
});

export default MainLayout;
