import { Avatar } from '@lobehub/ui';
import { useTheme } from 'antd-style';
import Link from 'next/link';
import { ReactNode, memo } from 'react';
import { Flexbox } from 'react-layout-kit';
import urlJoin from 'url-join';

interface UserInTableProps {
  avatar?: string | null;
  email: ReactNode | null;
  extra?: ReactNode;
  id: string;
  navToDetail?: boolean;
  username?: string | null;
}

const UserInTable = memo<UserInTableProps>(
  ({ username, navToDetail = true, extra, email, avatar, id }) => {
    const theme = useTheme();

    const user = (
      <Flexbox align={'center'} gap={8} horizontal>
        <Avatar
          alt={username || id}
          size={24}
          src={avatar || username || id}
          title={avatar || username || id}
        />
        <Flexbox>
          <Flexbox align={'center'} gap={6} horizontal>
            {username} {extra}
          </Flexbox>
          <div style={{ color: theme.colorTextDescription, fontSize: 12, lineHeight: 1.2 }}>
            {email}
          </div>
        </Flexbox>
      </Flexbox>
    );

    return navToDetail ? (
      <Link href={urlJoin('/users', id)} style={{ color: 'inherit' }}>
        {user}
      </Link>
    ) : (
      user
    );
  },
);

export default UserInTable;
