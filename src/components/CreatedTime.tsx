import { Icon } from '@lobehub/ui';
import { useResponsive, useTheme } from 'antd-style';
import { Clock3Icon, ClockArrowUp, LucideIcon } from 'lucide-react';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

export const TimeLabel = memo<{
  date: string;
  icon: LucideIcon;
  title: string;
}>(({ date, icon, title }) => {
  const theme = useTheme();
  return (
    <Flexbox
      align={'center'}
      gap={4}
      horizontal
      style={{
        color: theme.colorTextDescription,
        fontSize: 12,
      }}
    >
      <Icon icon={icon} />
      {title}: <span style={{ fontWeight: 'bold' }}>{date}</span>
    </Flexbox>
  );
});

const CreatedTime = memo<{
  createdAt?: string;
  updatedAt?: string;
}>(({ createdAt, updatedAt }) => {
  const { t } = useTranslation('common');
  const { mobile } = useResponsive();
  return (
    <Flexbox
      align={'center'}
      gap={mobile ? 4 : 16}
      horizontal={!mobile}
      justify={'flex-end'}
      wrap={'wrap'}
    >
      {createdAt && <TimeLabel date={createdAt} icon={Clock3Icon} title={t('time.createdAt')} />}
      {updatedAt && <TimeLabel date={updatedAt} icon={ClockArrowUp} title={t('time.updatedAt')} />}
    </Flexbox>
  );
});

export default CreatedTime;
