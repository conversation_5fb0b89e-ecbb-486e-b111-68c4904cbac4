'use client';

import { Spin, Typography } from 'antd';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Center, Flexbox } from 'react-layout-kit';

const CircleLoading = memo(() => {
  const { t } = useTranslation('common');
  return (
    <Center flex={1} height={'100%'} style={{ minHeight: 240 }} width={'100%'}>
      <Flexbox align={'center'} gap={8}>
        <Spin percent={'auto'} />
        <Typography.Text type={'secondary'}>{t('loading')}</Typography.Text>
      </Flexbox>
    </Center>
  );
});

export default CircleLoading;
