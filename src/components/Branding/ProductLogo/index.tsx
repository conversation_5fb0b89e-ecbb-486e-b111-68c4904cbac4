import { LobeChat, LobeChatProps } from '@lobehub/ui/brand';
import { memo } from 'react';

import { serverConfigSelectors, useServerConfigStore } from '@/store/serverConfig';

import CustomLogo from './Custom';

export const ProductLogo = memo<LobeChatProps>((props) => {
  const { isCustomBranding } = useServerConfigStore(serverConfigSelectors.config);

  if (isCustomBranding) {
    return <CustomLogo {...props} />;
  }

  return <LobeChat {...props} />;
});
