'use client';

import { LobeHub, type LobeHubProps } from '@lobehub/ui/brand';
import { memo } from 'react';

import { ORG_NAME } from '@/const/branding';
import { serverConfigSelectors, useServerConfigStore } from '@/store/serverConfig';

export const OrgBrand = memo<LobeHubProps>((props) => {
  const { isCustomORG } = useServerConfigStore(serverConfigSelectors.config);

  if (isCustomORG) {
    return <span>{ORG_NAME}</span>;
  }

  return <LobeHub {...props} />;
});
