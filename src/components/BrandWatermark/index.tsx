'use client';

import { LobeHub } from '@lobehub/ui/brand';
import { createStyles } from 'antd-style';
import Link from 'next/link';
import { memo } from 'react';
import { Flexbox, FlexboxProps } from 'react-layout-kit';

import { serverConfigSelectors, useServerConfigStore } from '@/store/serverConfig';

const useStyles = createStyles(({ token, css }) => ({
  logoLink: css`
    line-height: 1;
    color: inherit;

    &:hover {
      color: ${token.colorLink};
    }
  `,
}));

const BrandWatermark = memo<Omit<FlexboxProps, 'children'>>(({ style, ...rest }) => {
  const { styles, theme } = useStyles();
  const { isCustomORG, orgName } = useServerConfigStore(serverConfigSelectors.config);
  return (
    <Flexbox
      align={'center'}
      flex={'none'}
      gap={8}
      horizontal
      style={{ color: theme.colorTextSecondary, ...style }}
      {...rest}
    >
      <span>Powered by</span>
      {isCustomORG ? (
        <span>{orgName}</span>
      ) : (
        <Link className={styles.logoLink} href={'https://lobehub.com'} target={'_blank'}>
          <LobeHub size={24} type={'text'} />
        </Link>
      )}
      <span
        style={{
          color: theme.colorTextSecondary,
        }}
      >
        © {new Date().getFullYear()}
      </span>
    </Flexbox>
  );
});

export default BrandWatermark;
