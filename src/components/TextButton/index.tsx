'use client';

import { Icon, IconProps } from '@lobehub/ui';
import { useTheme } from 'antd-style';
import { LucideIcon } from 'lucide-react';
import { memo } from 'react';
import { Flexbox, FlexboxProps } from 'react-layout-kit';

interface TextButtonProps extends FlexboxProps {
  danger?: boolean;
  icon?: LucideIcon;
  iconProps?: Partial<IconProps>;
}

const TextButton = memo<TextButtonProps>(
  ({ danger, children, icon, style, iconProps, ...rest }) => {
    const theme = useTheme();

    return (
      <Flexbox
        align={'center'}
        gap={12}
        horizontal
        style={{
          color: danger ? theme.colorError : undefined,
          cursor: 'pointer',
          fontWeight: 500,
          ...style,
        }}
        {...rest}
      >
        {icon && <Icon icon={icon} {...iconProps} />}
        <div>{children}</div>
      </Flexbox>
    );
  },
);

export default TextButton;
