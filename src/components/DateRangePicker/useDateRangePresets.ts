import { Dayjs } from 'dayjs';
import { useTranslation } from 'react-i18next';

import {
  daysAgo,
  monthsAgo,
  thisMonth,
  thisQuarter,
  thisYear,
  today,
  weeksAgo,
} from '@/utils/time';

export const useDateRangePresets = (): {
  label: string;
  value: [Dayjs, Dayjs];
}[] => {
  const { t } = useTranslation('common');
  return [
    { label: t('time.last7Days'), value: [daysAgo(6), today()] },
    { label: t('time.last4Weeks'), value: [weeksAgo(3), today()] },
    { label: t('time.last3Months'), value: [monthsAgo(2), today()] },
    { label: t('time.last12Months'), value: [monthsAgo(11), today()] },
    { label: t('time.thisMonth'), value: [thisMonth(), today()] },
    {
      label: t('time.prevMonth'),
      value: [monthsAgo(1).startOf('month'), monthsAgo(1).endOf('month')],
    },
    { label: t('time.thisQuarter'), value: [thisQuarter(), today()] },
    { label: t('time.thisYear'), value: [thisYear(), today()] },
  ];
};
