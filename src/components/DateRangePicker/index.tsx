import { DatePicker, TimeRangePickerProps } from 'antd';
import { createStyles, useResponsive } from 'antd-style';
import { Dayjs } from 'dayjs';
import { memo, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';
import useMergeState from 'use-merge-value';

import { useDateRangePresets } from './useDateRangePresets';

const { RangePicker } = DatePicker;

const useStyles = createStyles(({ css, token, prefixCls }) => ({
  picker: css`
    .${prefixCls}-picker-cell-range-end, .${prefixCls}-picker-cell-range-start {
      &.${prefixCls}-picker-cell-in-view {
        .${prefixCls}-picker-cell-inner {
          color: ${token.colorBgLayout} !important;
        }
      }
    }
  `,
}));

export interface DateRangePickerProps extends Omit<TimeRangePickerProps, 'onChange'> {
  defaultValue?: [Dayjs, Dayjs];
  onRangeChange?: (range: [Dayjs, Dayjs]) => void;
  showLabel?: boolean;
  value?: [Dayjs, Dayjs];
}

const DateRangePicker = memo<DateRangePickerProps>(
  ({
    value,
    defaultValue,
    showLabel,
    format = 'YYYY-MM-DD',
    onRangeChange,
    popupClassName,
    ...rest
  }) => {
    const { t } = useTranslation('common');
    const { styles, cx, theme } = useStyles();
    const { mobile } = useResponsive();
    const defaultPresets = useDateRangePresets() || [];
    const [range, setRange] = useMergeState<[Dayjs, Dayjs]>(defaultPresets[0].value, {
      defaultValue,
      onChange: onRangeChange,
      value,
    });

    const rangeLabel = useMemo(() => {
      let label = t('time.custom');
      defaultPresets.forEach((item: any) => {
        if (
          item.value[0].format(format) === range[0].format(format as any) &&
          item.value[1].format(format) === range[1].format(format as any)
        )
          label = item.label;
      });
      return label;
    }, [t, range, format]);

    const picker = (
      <RangePicker
        classNames={{
          popup: {
            root: cx(styles.picker, popupClassName),
          },
        }}
        defaultValue={
          defaultValue || (defaultPresets[0].value as TimeRangePickerProps['defaultValue'])
        }
        format={format}
        onChange={(d) => {
          setRange?.(d as [Dayjs, Dayjs]);
        }}
        presets={defaultPresets}
        value={range}
        variant={theme.isDarkMode ? 'filled' : 'outlined'}
        {...rest}
      />
    );

    if (!showLabel || mobile) return picker;

    return (
      <Flexbox align={'center'} gap={12} horizontal>
        <span>{rangeLabel}</span>
        {picker}
      </Flexbox>
    );
  },
);

export default DateRangePicker;

export { useDateRangePresets } from './useDateRangePresets';
