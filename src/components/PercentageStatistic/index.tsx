import { StatisticCard } from '@ant-design/pro-components';
import { memo } from 'react';

import { formatPercentage } from '@/utils/format';

const PercentageStatistic = memo<{ title?: string; value?: number }>(({ title, value }) => {
  const percentage = formatPercentage(value);
  return (
    <StatisticCard.Statistic title={title} trend={percentage.trend} value={percentage.value} />
  );
});

export default PercentageStatistic;
