'use client';

import { Skeleton } from 'antd';
import { createStyles } from 'antd-style';
import { Center, Flexbox } from 'react-layout-kit';

import Loading from '@/components/Loading';

const useStyles = createStyles(({ css, token }) => ({
  aside: css`
    width: 280px;
    min-width: 280px;
    padding-inline: 8px;
  `,
  container: css`
    border-radius: 8px;
    background: ${token.colorBgContainer};
    box-shadow: ${token.boxShadowTertiary};
  `,
  main: css`
    width: 100%;
    height: 100vh;
    padding: 4px;
  `,
}));

const LayoutLoading = () => {
  const { styles } = useStyles();
  return (
    <Flexbox horizontal width={'100%'}>
      <Flexbox as={'aside'} className={styles.aside} gap={12}>
        <Flexbox paddingBlock={16} paddingInline={24}>
          <Skeleton.Button active block size={'small'} />
        </Flexbox>
        <Flexbox gap={8}>
          {Array.from({ length: 12 })
            .fill('')
            .map((value, index) => (
              <Flexbox height={45} key={`${index}_${value}`} paddingInline={24}>
                <Skeleton.Button
                  active
                  size={'small'}
                  style={{ height: 22, width: 80 + (index % 3) * 20 }}
                />
              </Flexbox>
            ))}
        </Flexbox>
      </Flexbox>
      <main className={styles.main}>
        <Center className={styles.container} height={'100%'}>
          <Loading />
        </Center>
      </main>
    </Flexbox>
  );
};

export default LayoutLoading;
