import { Tag } from '@lobehub/ui';
import { memo } from 'react';
import { Flexbox, FlexboxProps } from 'react-layout-kit';

const LabelWithKey = memo<
  FlexboxProps & {
    tag: string;
    title: string;
  }
>(({ title, tag, ...rest }) => {
  return (
    <Flexbox align={'center'} gap={8} horizontal {...rest}>
      {title}
      <Tag>{tag}</Tag>
    </Flexbox>
  );
});

export default LabelWithKey;
