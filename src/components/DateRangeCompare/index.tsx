import { Select } from '@lobehub/ui';
import { Dayjs } from 'dayjs';
import { CSSProperties, memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';
import useMergeState from 'use-merge-value';

import { thisMonth, today } from '@/utils/time';

import DateRangePicker from '../DateRangePicker';
import { getPrevRange } from './getPrevRange';
import { CompareType, DisplayType } from './type';

export type DateRangeCompareValue = {
  compare: CompareType;
  display: DisplayType;
  prevRange?: [Dayjs, Dayjs];
  range: [Dayjs, Dayjs];
};

export type DateRangeCompareProps = {
  defaultValue?: DateRangeCompareValue;
  onChange?: (value: DateRangeCompareValue) => void;
  showLabel?: boolean;
  style?: CSSProperties;
  value?: DateRangeCompareValue;
};

export const defaultDateRangeCompareValue: DateRangeCompareValue = {
  compare: CompareType.Prev,
  display: DisplayType.EveryDay,
  prevRange: getPrevRange([thisMonth(), today()]),
  range: [thisMonth(), today()],
};

const DateRangeCompare = memo<DateRangeCompareProps>(
  ({ value, onChange, defaultValue, showLabel, style }) => {
    const { t } = useTranslation('common');
    const [compareValue, setCompareValue] = useMergeState<DateRangeCompareValue>(
      defaultDateRangeCompareValue,
      {
        defaultValue,
        onChange,
        value,
      },
    );

    return (
      <Flexbox
        align={'center'}
        gap={8}
        horizontal
        justify={'flex-start'}
        style={style}
        wrap={'wrap'}
      >
        <DateRangePicker
          defaultValue={compareValue.range}
          onRangeChange={(v) =>
            setCompareValue({
              ...compareValue,
              prevRange: getPrevRange(v as [Dayjs, Dayjs]),
              range: v as [Dayjs, Dayjs],
            })
          }
          showLabel={showLabel}
        />
        <div>{t('time.compare.title')}</div>
        <Select
          onChange={(v) => {
            switch (v) {
              case CompareType.Disabled: {
                setCompareValue({
                  ...compareValue,
                  compare: v as CompareType,
                  prevRange: undefined,
                });
                break;
              }
              case CompareType.Custom: {
                setCompareValue({ ...compareValue, compare: v as CompareType });
                break;
              }
              case CompareType.Prev: {
                setCompareValue({
                  ...compareValue,
                  compare: v as CompareType,
                  prevRange: getPrevRange(compareValue.range),
                });
                break;
              }
            }
          }}
          options={[
            {
              label: t('time.compare.prev'),
              value: CompareType.Prev,
            },
            {
              label: t('time.compare.custom'),
              value: CompareType.Custom,
            },
            {
              label: t('time.compare.disabled'),
              value: CompareType.Disabled,
            },
          ]}
          value={compareValue.compare}
        />
        {compareValue.compare === CompareType.Custom && (
          <DateRangePicker
            defaultValue={compareValue.prevRange}
            onRangeChange={(v) => setCompareValue({ ...compareValue, prevRange: v })}
            value={compareValue.prevRange}
          />
        )}
        <Select
          onChange={(v) => setCompareValue({ ...compareValue, display: v as DisplayType })}
          options={[
            {
              label: t('time.compare.everyDay'),
              value: DisplayType.EveryDay,
            },
            {
              label: t('time.compare.everyWeek'),
              value: DisplayType.EveryWeek,
            },
            {
              label: t('time.compare.everyMonth'),
              value: DisplayType.EveryMonth,
            },
          ]}
          value={compareValue.display}
        />
      </Flexbox>
    );
  },
);

export default DateRangeCompare;

export { getPrevRange } from './getPrevRange';
