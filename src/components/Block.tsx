import { But<PERSON>, Icon } from '@lobehub/ui';
import { createStyles } from 'antd-style';
import { ChevronRight } from 'lucide-react';
import Link from 'next/link';
import { ReactNode, memo } from 'react';
import { Flexbox, FlexboxProps } from 'react-layout-kit';

const useStyles = createStyles(({ css, token, responsive }) => ({
  cardBody: css`
    overflow: hidden;

    padding: 16px;
    border-radius: ${token.borderRadiusLG}px;

    background: ${token.colorBgContainer};
    box-shadow: ${token.boxShadowTertiary};

    ${responsive.mobile} {
      border-radius: 0;
      background: ${token.colorFillQuaternary};
    }
  `,
  cardContainer: css`
    position: relative;

    overflow: hidden;

    width: 100%;
    padding: 4px;
    border-radius: ${token.borderRadiusLG}px;

    background: ${token.colorFillTertiary};

    ${responsive.mobile} {
      padding: 0;
      border-radius: 0;
      background: ${token.colorBgContainer};
    }
  `,
  cardHeader: css`
    padding-block: 12px 16px !important;
    padding-inline: 16px !important;
  `,
  desc: css`
    color: ${token.colorTextSecondary};
  `,
  header: css`
    padding-block-start: 8px;

    ${responsive.mobile} {
      padding-inline: 24px;
    }
  `,
  more: css`
    display: flex;
    align-items: center;
    color: ${token.colorTextSecondary};
  `,
  title: css`
    margin-block: 4px;
    font-size: 20px;
    font-weight: 600;
    line-height: 1.2;
    ${responsive.mobile} {
      font-size: 16px;
    }
  `,
}));

interface BlockProps extends Omit<FlexboxProps, 'title'> {
  desc?: ReactNode;
  extra?: ReactNode;
  fontSize?: number;
  icon?: ReactNode;
  more?: string;
  moreLink?: string;
  title?: ReactNode;
  variant?: 'default' | 'card';
}

const Block = memo<BlockProps>(
  ({
    className,
    variant = 'default',
    fontSize,
    title,
    more,
    moreLink,
    icon,
    children,
    desc,
    extra,
    ...rest
  }) => {
    const { cx, styles } = useStyles();
    const isCard = variant === 'card';

    if (!title) return <Flexbox {...rest}>{children}</Flexbox>;

    return (
      <Flexbox
        className={cx(isCard && styles.cardContainer)}
        gap={isCard ? 0 : 16}
        style={{ position: 'relative' }}
        width={'100%'}
      >
        <Flexbox
          align={'center'}
          className={cx(isCard && styles.cardHeader, styles.header)}
          gap={16}
          horizontal
          justify={'space-between'}
          width={'100%'}
        >
          <Flexbox gap={12}>
            {icon}
            <Flexbox gap={4}>
              <h2 className={styles.title} style={{ fontSize }}>
                {title}
              </h2>
              <div className={styles.desc}>{desc}</div>
            </Flexbox>
          </Flexbox>
          {extra && (
            <Flexbox align={'center'} gap={8} horizontal>
              {extra}
            </Flexbox>
          )}
          {moreLink && (
            <Link href={moreLink} target={moreLink.startsWith('http') ? '_blank' : undefined}>
              <Button className={styles.more} type={'text'}>
                <span>{more}</span>
                <Icon icon={ChevronRight} />
              </Button>
            </Link>
          )}
        </Flexbox>
        <Flexbox className={cx(isCard && styles.cardBody, className)} {...rest}>
          {children}
        </Flexbox>
      </Flexbox>
    );
  },
);

export default Block;
