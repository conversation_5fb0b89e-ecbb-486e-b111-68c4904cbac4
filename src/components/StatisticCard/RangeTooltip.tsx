import { ChartTooltipFrame, ChartTooltipRow } from '@lobehub/charts';
import type { CustomTooltipProps } from '@lobehub/charts/es/common/CustomTooltipProps';
import { Tag } from '@lobehub/ui';
import { Divider } from 'antd';
import { useTheme } from 'antd-style';
import { CSSProperties } from 'react';
import { Flexbox } from 'react-layout-kit';

import { calcGrowthPercentage } from '@/utils/growthPercentage';

const RangeTooltip = ({
  payload,
  active,
  valueFormatter,
  inverseColor,
}: CustomTooltipProps & { inverseColor?: boolean }) => {
  const theme = useTheme();
  if (!active || !payload) return null;
  const count = payload?.[1]?.payload?.count;
  const prevCount = payload?.[0]?.payload?.prevCount;
  const percentage = calcGrowthPercentage(count || 0, prevCount || 0);

  const upStyle: CSSProperties = {
    background: theme.colorSuccessBg,
    borderColor: theme.colorSuccessBorder,
    color: theme.green10,
  };

  const downStyle: CSSProperties = {
    backgroundColor: theme.colorWarningBg,
    borderColor: theme.colorWarningBorder,
    color: theme.orange10,
  };

  return (
    <ChartTooltipFrame>
      {count && prevCount && percentage && percentage !== 0 ? (
        <>
          <Flexbox align={'flex-end'} paddingBlock={8} paddingInline={16} width={'100%'}>
            <Tag
              style={{
                margin: 0,
                ...(inverseColor
                  ? percentage > 0
                    ? downStyle
                    : upStyle
                  : percentage > 0
                    ? upStyle
                    : downStyle),
              }}
            >
              {percentage > 0 ? '+' : ''}
              {percentage.toFixed(1)}%
            </Tag>
          </Flexbox>
          <Divider style={{ margin: 0 }} />
        </>
      ) : null}
      <Flexbox
        gap={4}
        paddingBlock={8}
        paddingInline={16}
        style={{ flexDirection: 'column-reverse', marginTop: 4 }}
      >
        {payload.map((category: any, index: number) => (
          <ChartTooltipRow
            color={category.color}
            key={`id-${index}`}
            name={index > 0 ? category.payload.date : category.payload.prevDate}
            value={(valueFormatter as any)?.(category.value)}
          />
        ))}
      </Flexbox>
    </ChartTooltipFrame>
  );
};

export default RangeTooltip;
