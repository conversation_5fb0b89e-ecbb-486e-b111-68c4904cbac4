import dayjs from 'dayjs';

import { DisplayType } from '@/components/DateRangeCompare/type';

export const labelFormatterByDisplayType = (v: number | string, display: DisplayType) => {
  switch (display) {
    case DisplayType.EveryDay: {
      return dayjs(v).format('MM-DD');
    }
    case DisplayType.EveryWeek: {
      return dayjs(v).format('MM-DD');
    }
    case DisplayType.EveryMonth: {
      return dayjs(v).format('MMM');
    }
  }
};
