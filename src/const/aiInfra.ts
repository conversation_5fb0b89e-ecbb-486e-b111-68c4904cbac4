/**
 * 基础模型类型
 */
export const INFRA_MODEL_TYPES = [
  { label: '大语言模型', value: 'chat' },
  { label: '图片生成模型', value: 'image' },
  { label: 'embedding模型', value: 'embedding' },
  { label: 'rerank模型', value: 'rerank' },
  { label: '视频生成', value: 'text2video' },
  { label: 'ASR模型', value: 'stt' },
  { label: 'TTS模型', value: 'tts' },
];

/**
 * 渠道类型
 */
export const INFRA_PROVIDER_TYPES = [
  { label: 'Bedrock', value: 'bedrock' },
  { label: 'Azure OpenAI', value: 'azure_ai' },
  { label: 'Gemini', value: 'gemini' },
  { label: 'Claude', value: 'claude' },
  { label: 'OpenAI', value: 'openai' },
  { label: 'Vertex', value: 'vertex_ai' },
];

/**
 * 模型能力与 LiteLLM 配置字段映射关系
 */
export const INFRA_MODEL_ABILITIES: {
  abilityKey: string;
  label: string;
  litellmKey: string;
}[] = [
  { abilityKey: 'functionCall', label: '工具使用', litellmKey: 'supports_function_calling' },
  { abilityKey: 'vision', label: '视觉识别', litellmKey: 'supports_vision' },
  { abilityKey: 'reasoning', label: '深度思考', litellmKey: 'supports_reasoning' },
  { abilityKey: 'search', label: '联网搜索', litellmKey: 'supports_web_search' },
  { abilityKey: 'imageOutput', label: '图片生成/输出', litellmKey: 'supports_reasoning' },
];
