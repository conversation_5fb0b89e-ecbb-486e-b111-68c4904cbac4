import urlJoin from 'url-join';

import { Plans, SubscriptionPlan } from '@/types/subscription';

// 1$ = 1M Credit
export const CREDIT_UNIT = 1000 * 1000;

// 1MB = 1024 * 1024 Byte
export const STORAGE_UNIT = 1024 * 1024;

export enum SubscriptionStatus {
  Active,
  Cancelled,
  Inactive,
}

export const subscriptionPlan: Record<string, SubscriptionPlan> = {
  [Plans.Free]: {
    id: Plans.Free,
    limit: {
      credit: 0.03,
      embeddingStorage: 100,
      fileStorage: 10 * STORAGE_UNIT,
    },
    price: {
      monthly: 0,
      yearly: 0,
    },
  },
  [Plans.Hobby]: {
    id: Plans.Hobby,
    limit: {
      credit: 0,
      embeddingStorage: 1000,
      fileStorage: 512 * STORAGE_UNIT,
    },
    price: {
      monthly: 7.9,
      yearly: 4.9,
    },
  },
  [Plans.Starter]: {
    id: Plans.Starter,
    limit: {
      credit: 5,
      embeddingStorage: 5000,
      fileStorage: 1024 * STORAGE_UNIT,
    },
    price: {
      monthly: 12.9,
      yearly: 9.9,
    },
  },
  [Plans.Premium]: {
    id: Plans.Premium,
    limit: {
      credit: 15,
      embeddingStorage: 10_000,
      fileStorage: 2 * 1024 * STORAGE_UNIT,
    },
    price: {
      monthly: 24.9,
      yearly: 19.9,
    },
  },
  [Plans.Ultimate]: {
    id: Plans.Ultimate,
    limit: {
      credit: 35,
      embeddingStorage: 20_000,
      fileStorage: 4 * 1024 * STORAGE_UNIT,
    },
    price: {
      monthly: 49.9,
      yearly: 39.9,
    },
  },
};

export const NORMAL_LLM = [
  'GPT-3.5 Turbo',
  'Claude 3 Sonnet',
  'Claude 3 Haiku',
  'Gemini 1.5 Flash',
];
export const PRO_LLM = ['GPT-4o', 'GPT-4 Turbo', 'Claude 3 Opus', 'Gemini 1.5 Pro'];

export const TOKEN_PER_MESSAGE = 500;

const BASE_DOC_URL = 'https://lobehub.com/docs';
export const SUBSCRIPTION_DOC = urlJoin(
  BASE_DOC_URL,
  'usage/subscription/how-to-subscription-plan',
);
export const SUBSCRIPTION_DOC_CREDIT = urlJoin(BASE_DOC_URL, 'usage/subscription/model-pricing');
export const SUBSCRIPTION_DOC_PROVIDER = urlJoin(BASE_DOC_URL, 'usage/providers/lobehub');
