/* eslint-disable typescript-sort-keys/string-enum  */

export enum FeatureFlag {
  // ======= 总览模块 =======
  Overview = 'Overview',

  // ======= 用户模块 =======
  User = 'User',

  // ======= 市场相关模块 =======

  // 市场功能父菜单
  Market = 'Market',

  // 市场概览
  MarketDashboard = 'MarketDashboard',

  // 插件管理
  MarketPlugin = 'MarketPlugin',

  // ======= 订阅相关模块 =======

  // 订阅功能父菜单
  Subscription = 'Subscription',

  // 订单管理
  SubscriptionOrders = 'SubscriptionOrders',

  // 预算管理
  SubscriptionBudgets = 'SubscriptionBudgets',

  // ======= 渠道相关模块 =======

  // 渠道功能父菜单
  Channel = 'Channel',

  // 渠道管理
  ChannelManagement = 'ChannelManagement',

  // 模型管理
  ModelManagement = 'ModelManagement',

  // ======= 控制台相关模块 =======

  // 控制台功能父菜单
  Console = 'Console',

  // Webhook
  ConsoleWebhook = 'ConsoleWebhook',

  // 数据迁移
  ConsoleMigration = 'ConsoleMigration',

  // ======= 其他模块 =======

  // 其他功能父菜单
  Others = 'Others',

  // 角色权限管理
  OthersRbac = 'OthersRbac',
}
