export enum FeatureFlag {
  /**
   * 订阅功能
   */
  Subscription = 'subscription',

  // 主要页面功能开关
  /**
   * 概览页面
   */
  Overview = 'overview',

  /**
   * 用户管理模块
   */
  Users = 'users',
  UsersManagement = 'users-management',
  UsersRestriction = 'users-restriction',
  UsersProfile = 'users-profile',

  /**
   * 市场模块
   */
  Market = 'market',
  MarketDashboard = 'market-dashboard',
  MarketPlugins = 'market-plugins',
  MarketPluginDetail = 'market-plugin-detail',
  MarketPluginVersion = 'market-plugin-version',
  MarketPluginConfig = 'market-plugin-config',
  MarketUsers = 'market-users',
  MarketOrganizations = 'market-organizations',
  MarketReview = 'market-review',
  MarketSettings = 'market-settings',

  /**
   * 订阅模块
   */
  SubscriptionOrders = 'subscription-orders',
  SubscriptionBudgets = 'subscription-budgets',
  SubscriptionMeters = 'subscription-meters',
  SubscriptionPlans = 'subscription-plans',

  /**
   * 渠道管理模块
   */
  Channel = 'channel',
  ChannelManagement = 'channel-management',
  Models = 'models',

  /**
   * 报告模块
   */
  Reporting = 'reporting',
  ReportingProduct = 'reporting-product',
  ReportingRevenue = 'reporting-revenue',

  /**
   * 运营模块
   */
  Operation = 'operation',
  OperationFeedback = 'operation-feedback',
  OperationSegmentation = 'operation-segmentation',

  /**
   * 控制台模块
   */
  Console = 'console',
  ConsoleFeatureFlag = 'console-feature-flag',
  ConsoleMigration = 'console-migration',
  ConsoleStatus = 'console-status',
  ConsoleWebhook = 'console-webhook',

  /**
   * 其他模块
   */
  Others = 'others',
  OthersRbac = 'others-rbac',

  /**
   * 测试模块（影和测试）
   */
  YingheTest = 'yinghe-test',
  ChannelTest = 'channel-test',
  ModelManagementList = 'model-management-list',
  ModelManagementDetail = 'model-management-detail',
  Roles = 'roles',
}
