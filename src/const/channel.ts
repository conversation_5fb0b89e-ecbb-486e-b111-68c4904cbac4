import { ModelProvider } from '@lobehub/icons';

export const channelAvatar: { [key: string]: string } = {
  'aihubmix.com': 'https://github.com/user-attachments/assets/74aef5ee-b8dd-4308-a0c3-5f427955d313',
  'api.302.ai': 'https://github.com/user-attachments/assets/9e10cd73-7872-41aa-92ec-11f554a1a96f',
  'api.anthropic.com': ModelProvider.Anthropic,
  'api.deepseek.com': ModelProvider.DeepSeek,
  'api.moonshot.cn': ModelProvider.Moonshot,
  'api.openai-up.com':
    'https://github.com/user-attachments/assets/593206cf-8212-4c6b-997d-e95fed015ac2',
  'api.openai.com': ModelProvider.OpenAI,
  'gptapi.us': 'https://github.com/user-attachments/assets/a5230de1-7abd-4989-a776-025a11611ed2',
  'neurality.openai.azure.com': ModelProvider.Azure,
  'openrouter.ai': ModelProvider.OpenRouter,
};

Object.values(ModelProvider).forEach((provider) => {
  channelAvatar[provider] = provider;
});
