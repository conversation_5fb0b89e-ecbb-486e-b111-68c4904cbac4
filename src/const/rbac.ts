/**
 * RBAC 相关常量定义
 */

// ==================== LiteLLM 相关常量 ====================

/**
 * LiteLLM 团队 ID 前缀
 * 用于生成角色对应的团队 ID，格式：role_{roleId}
 */
export const TEAM_ID_PREFIX = 'role_';

/**
 * LiteLLM 元数据中的创建者标识
 * 用于标识由 RBAC 系统创建的资源
 */
export const RBAC_SYSTEM_CREATOR = 'rbac_system';

/**
 * 团队成员的默认角色
 * LiteLLM 中用户在团队中的角色类型
 */
export const DEFAULT_TEAM_MEMBER_ROLE = 'user';

/**
 * API Key 的默认模型权限
 * 表示用户可以使用团队中所有可用的模型
 */
export const ALL_TEAM_MODELS = 'all-team-models';

/**
 * LiteLLM 默认的代理模型权限
 * 当团队没有设置具体模型时的默认权限，需要过滤掉
 */
export const ALL_PROXY_MODELS = 'all-proxy-models';

// ==================== 错误消息常量 ====================

/**
 * 角色相关错误消息
 */
export const ERROR_MESSAGES = {
  // 表单验证
  DISPLAY_NAME_REQUIRED: '显示名称不能为空',
  
  // 模型权限管理
  MODEL_ADD_FAILED: '添加角色模型权限失败',
  MODEL_GET_FAILED: '获取团队信息失败',
  MODEL_REMOVE_FAILED: '移除角色模型权限失败',
  
  // 配额管理
  QUOTA_GET_FAILED: '获取角色配额信息失败',
  
  // 角色管理
  ROLE_CREATE_FAILED: '创建角色失败',
  ROLE_DELETE_FAILED: '删除角色失败',
  ROLE_DELETE_RESTRICTED: '只有禁用状态且无关联用户的角色才能被删除',
  ROLE_NAME_REQUIRED: '角色名称不能为空',
  ROLE_NOT_FOUND: '未找到角色',
  ROLE_TOGGLE_FAILED: '切换角色状态失败',
  ROLE_UPDATE_FAILED: '更新角色失败',
  
  // 用户管理
  USER_ADD_TO_ROLE_FAILED: '添加用户到角色失败',
  USER_REMOVE_FROM_ROLE_FAILED: '从角色中移除用户失败',
} as const;

// ==================== 日志消息常量 ====================

/**
 * 日志消息模板
 */
export const LOG_MESSAGES = {
  LITELLM_OPERATIONS_FAILED: 'LiteLLM operations failed for user',
  LITELLM_TEAM_CREATE_FAILED: 'LiteLLM team creation failed:',
  LITELLM_TEAM_DELETE_FAILED: 'LiteLLM team deletion failed:',
  ROLE_QUOTA_CREATE_FAILED: 'Role quota creation failed:',
  ROLE_QUOTA_DELETE_FAILED: 'Role quota deletion failed:',
} as const;

// ==================== API Key 相关常量 ====================

/**
 * API Key 别名生成模板
 * 格式：{userId}_{teamId}
 */
export const API_KEY_ALIAS_SEPARATOR = '_';

/**
 * API Key 配置
 */
export const API_KEY_CONFIG = {
  SEND_INVITE_EMAIL: false, // 是否发送邀请邮件
} as const;

// ==================== 团队配置常量 ====================

/**
 * 新建团队的默认配置
 */
export const DEFAULT_TEAM_CONFIG = {
  MODELS: [], // 默认模型列表为空
} as const;

// ==================== 工具函数 ====================

/**
 * 生成团队 ID
 * @param roleId 角色 ID
 * @returns 团队 ID，格式：role_{roleId}
 */
export const generateTeamId = (roleId: number | string): string => {
  return `${TEAM_ID_PREFIX}${roleId}`;
};

/**
 * 生成 API Key 别名
 * @param userId 用户 ID
 * @param teamId 团队 ID
 * @returns API Key 别名，格式：{userId}_{teamId}
 */
export const generateApiKeyAlias = (userId: string, teamId: string): string => {
  return `${userId}${API_KEY_ALIAS_SEPARATOR}${teamId}`;
};