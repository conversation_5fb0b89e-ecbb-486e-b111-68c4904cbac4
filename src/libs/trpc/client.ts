import { createTRPCClient, httpBatchLink } from '@trpc/client';
import { createTRPCReact } from '@trpc/react-query';
import superjson from 'superjson';

import { fetchErrorNotification } from '@/components/Error/fetchErrorNotification';
import { loginRequired } from '@/components/Error/loginRequiredNotification';
import type { AppRouter } from '@/server/routers';

const links = [
  httpBatchLink({
    fetch: async (input, init) => {
      const response = await fetch(input, init);
      if (response.ok) return response;

      const errorRes: ErrorResponse = await response.clone().json();

      errorRes.forEach((item) => {
        const errorData = item.error.json;

        const status = errorData.data.httpStatus;
        switch (status) {
          case 401: {
            loginRequired.redirect();
            break;
          }
          default: {
            fetchErrorNotification.error({ errorMessage: errorData.message, status });
          }
        }
      });

      return response;
    },
    transformer: superjson,
    url: '/api/trpc',
  }),
];
export const trpcClient = createTRPCClient<AppRouter>({ links });

export const trpcQuery = createTRPCReact<AppRouter>();

export const trpcQueryClient = trpcQuery.createClient({ links });
export type ErrorResponse = ErrorItem[];

export interface ErrorItem {
  error: {
    json: {
      code: number;
      data: Data;
      message: string;
    };
  };
}

export interface Data {
  code: string;
  httpStatus: number;
  path: string;
  stack: string;
}
