import { NextRequest, NextResponse } from 'next/server';
import { UAParser } from 'ua-parser-js';

import { LOBE_LOCALE_COOKIE } from '@/const/locale';
import { LOBE_THEME_APPEARANCE } from '@/const/theme';
import { getConsoleService } from '@/server/services/console';
import type { LicenseValidationResult } from '@/types/license';
import { parseBrowserLanguage } from '@/utils/locale';
import { RouteVariants } from '@/utils/server/routeVariants';

// License 验证缓存，缓存 30 分钟
const licenseCache = new Map<string, { result: LicenseValidationResult; timestamp: number }>();
const CACHE_DURATION = 30 * 60 * 1000; // 30分钟缓存

export function createLicenseInvalidResponse(
  request: NextRequest,
  result: LicenseValidationResult
): NextResponse {
  const url = new URL(request.url);

  // 构建重定向路径，保持路由变体结构
  const device = new UAParser(request.headers.get('user-agent') || '').getDevice();
  const browserLanguage = parseBrowserLanguage(request.headers);
  const locale = request.cookies.get(LOBE_LOCALE_COOKIE)?.value || browserLanguage;
  const theme = request.cookies.get(LOBE_THEME_APPEARANCE)?.value || 'light';

  const route = RouteVariants.serializeVariants({
    isMobile: device.type === 'mobile',
    locale,
    theme,
  });

  // 正确设置路径和查询参数
  url.pathname = `/${route}/license-invalid`;
  url.search = ''; // 清空现有查询参数

  // 设置新的查询参数
  url.searchParams.set('status', result.status);
  url.searchParams.set('message', result.message);

  if (result.expiresIn !== undefined) {
    url.searchParams.set('expiresIn', result.expiresIn.toString());
  }

  return NextResponse.redirect(url);
}

export async function checkLicenseValidity(request: NextRequest): Promise<NextResponse | null> {
  // 获取 console 服务实例
  const consoleService = getConsoleService();

  // 如果没有配置 console 服务，跳过验证
  if (!consoleService) {
    return null;
  }

  const cacheKey = 'license_validation';
  const cached = licenseCache.get(cacheKey);

  // 检查缓存是否有效（只缓存验证成功的结果）
  if (cached && (Date.now() - cached.timestamp) < CACHE_DURATION && cached.result.isValid) {
    return null; // 验证通过，继续执行
  }

  try {
    const result = await consoleService.validateLicense();

    // 只缓存验证成功的结果
    if (result.isValid) {
      licenseCache.set(cacheKey, {
        result,
        timestamp: Date.now()
      });
      return null; // 验证通过，继续执行
    } else {
      // 验证失败时不缓存，直接返回错误响应
      return createLicenseInvalidResponse(request, result);
    }
  } catch (error) {
    // 验证异常时，不缓存错误结果，每次都重新尝试验证
    const errorResult: LicenseValidationResult = {
      isValid: false,
      status: 'error' as any,
      message: error instanceof Error ? error.message : '验证异常'
    };

    return createLicenseInvalidResponse(request, errorResult);
  }
}
