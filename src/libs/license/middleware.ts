import { NextRequest, NextResponse } from 'next/server';
import { UAParser } from 'ua-parser-js';

import { LOBE_LOCALE_COOKIE } from '@/const/locale';
import { LOBE_THEME_APPEARANCE } from '@/const/theme';
import { parseBrowserLanguage } from '@/utils/locale';
import { RouteVariants } from '@/utils/server/routeVariants';

import { validateLicense } from './validator';

export function createLicenseInvalidResponse(request: NextRequest, result: any): NextResponse {
  const url = new URL(request.url);

  // 构建重定向路径，保持路由变体结构
  const device = new UAParser(request.headers.get('user-agent') || '').getDevice();
  const browserLanguage = parseBrowserLanguage(request.headers);
  const locale = request.cookies.get(LOBE_LOCALE_COOKIE)?.value || browserLanguage;
  const theme = request.cookies.get(LOBE_THEME_APPEARANCE)?.value || 'light';
  
  const route = RouteVariants.serializeVariants({
    isMobile: device.type === 'mobile',
    locale,
    theme,
  });

  // 正确设置路径和查询参数
  url.pathname = `/${route}/license-invalid`;
  url.search = ''; // 清空现有查询参数
  
  // 设置新的查询参数
  url.searchParams.set('status', result.status);
  url.searchParams.set('message', result.message);
  
  if (result.expiresIn !== undefined) {
    url.searchParams.set('expiresIn', result.expiresIn.toString());
  }
  
  return NextResponse.redirect(url);
}

export async function checkLicenseValidity(request: NextRequest): Promise<NextResponse | null> {
  const consoleUrl = process.env.LOBE_CONSOLE_URL;
  const license = process.env.LOBE_LICENSE;

  // 如果没有配置 license 相关环境变量，跳过验证
  if (!consoleUrl || !license) {
    return null;
  }

  try {
    const result = await validateLicense(consoleUrl, license);
    
    if (!result.isValid) {
      return createLicenseInvalidResponse(request, result);
    }
    
    return null; // 验证通过，继续执行
  } catch (error) {
    // 验证异常时，可以选择阻止访问或允许继续（根据业务需求决定）
    const errorResult = {
      isValid: false,
      status: 'error',
      message: error instanceof Error ? error.message : '验证异常'
    };
    return createLicenseInvalidResponse(request, errorResult);
  }
}