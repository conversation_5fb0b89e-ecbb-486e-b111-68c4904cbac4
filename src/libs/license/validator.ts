import type { LicenseValidationResponse, LicenseValidationResult, LicenseStatus } from '@/types/license';

export async function validateLicense(
  consoleUrl: string,
  license: string
): Promise<LicenseValidationResult> {
  try {
    const url = `${consoleUrl}/api/v1/license/${license}/validate`;
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
      },
      method: 'GET',
      // 设置 5 秒超时
      signal: AbortSignal.timeout(5000),
    });

    if (!response.ok) {
      return {
        isValid: false,
        message: `HTTP ${response.status}: ${response.statusText}`,
        status: 'error' as LicenseStatus,
      };
    }

    const data: LicenseValidationResponse = await response.json();
    
    if (!data.success) {
      return {
        expiresIn: data.data?.expiresIn,
        isValid: false,
        message: data.data?.message || '验证失败',
        status: data.data?.status || ('error' as LicenseStatus),
      };
    }

    return {
      expiresIn: data.data.expiresIn,
      isValid: data.data.isValid,
      message: data.data.message,
      status: data.data.status,
    };
  } catch (error) {
    // 网络错误或超时
    if (error instanceof Error) {
      return {
        isValid: false,
        message: `验证失败: ${error.message}`,
        status: 'error' as LicenseStatus,
      };
    }
    
    return {
      isValid: false,
      message: '许可证验证过程中发生未知错误',
      status: 'error' as LicenseStatus,
    };
  }
}