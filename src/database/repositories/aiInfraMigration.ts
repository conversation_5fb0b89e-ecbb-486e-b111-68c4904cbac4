import { and, count, eq, inArray, isNotNull } from 'drizzle-orm';
import { uniq } from 'lodash';
import { isEmpty } from 'lodash-es';

import { lobechatDB } from '@/database/instance/lobechatDB';
import { userSettings, users } from '@/database/instance/lobechatDB/schemas';
import { aiModels, aiProviders } from '@/database/instance/lobechatDB/schemas/aiInfra';
import { genTable } from '@/database/utils/genTable';
import { genWhere } from '@/database/utils/genWhere';
import { KeyVaultsGateKeeper } from '@/server/modules/KeyVaultsEncrypt';
import {
  ProviderMigration,
  ProviderMigrationDetail,
  ProviderMigrationQuery,
} from '@/types/migration';

import { genFuzzySearchUserWhere } from '../helper/fuzzySearch';

const maskString = (str: string): string => {
  if (!str) return str;

  // 保留开头 10 个字符和结尾 6 个字符
  if (str.length <= 16) return str;
  return str.slice(0, 10) + '******' + str.slice(-6);
};

export class AiInfraMigrationRepos {
  async getMigrationProviders(keyVaultsSecret: string, params: ProviderMigrationQuery = {}) {
    const { emailOrUsernameOrUserId, pageSize = 20, current = 1 } = params;
    const { offset, limit } = genTable({ current, pageSize });

    const total = await lobechatDB
      .select({ count: count(userSettings.id) })
      .from(userSettings)
      .where(isNotNull(userSettings.keyVaults));

    const result = await lobechatDB
      .select({
        avatar: users.avatar,
        email: users.email,
        id: userSettings.id,
        keyVaults: userSettings.keyVaults,
        languageModel: userSettings.languageModel,
        username: users.username,
      })
      .from(userSettings)
      .leftJoin(users, eq(users.id, userSettings.id))
      .where(
        genWhere([
          isNotNull(userSettings.keyVaults),
          emailOrUsernameOrUserId ? genFuzzySearchUserWhere(emailOrUsernameOrUserId) : undefined,
        ]),
      )
      .offset(offset)
      .limit(limit);

    const userIds = result.map((item) => item.id);

    const userProviders = await lobechatDB
      .select({
        id: aiProviders.id,
        userId: aiProviders.userId,
      })
      .from(aiProviders)
      .where(inArray(aiProviders.userId, userIds));

    const data = await Promise.all(
      result.map(async ({ keyVaults, languageModel, ...item }) => {
        const data = await KeyVaultsGateKeeper.getUserKeyVaults(
          keyVaultsSecret,
          keyVaults,
          item.id,
        );

        const beforeProviders = uniq(
          Object.entries(data)
            .filter(([key, value]) => !isEmpty(value) && key !== 'password')
            .map(([key]) => key)
            .concat(Object.keys(languageModel || {})),
        );

        const afterProviders = userProviders
          .filter((provider) => provider.userId === item.id)
          .map((item) => item.id);

        return {
          ...item,
          afterProviders,
          beforeProviders,
          status:
            afterProviders.length === 0 && beforeProviders.length > 0
              ? 'waiting'
              : afterProviders.length >= beforeProviders.length
                ? 'success'
                : 'error',
        } as ProviderMigration;
      }),
    );

    return {
      data,
      total: total[0].count,
    };
  }

  async getUserConfigAiInfra(userId: string, keyVaultsSecret: string) {
    const [settings] = await lobechatDB
      .select({
        id: userSettings.id,
        keyVaults: userSettings.keyVaults,
        languageModel: userSettings.languageModel,
      })
      .from(userSettings)
      .where(eq(userSettings.id, userId));

    const providers = await lobechatDB
      .select({
        enabled: aiProviders.enabled,
        fetchOnClient: aiProviders.fetchOnClient,
        id: aiProviders.id,
        keyVaults: aiProviders.keyVaults,
        userId: aiProviders.userId,
      })
      .from(aiProviders)
      .where(eq(aiProviders.userId, userId));

    const newModels = await lobechatDB
      .select({
        enabled: aiModels.enabled,
        id: aiModels.id,
        providerId: aiModels.providerId,
        source: aiModels.source,
        userId: aiModels.userId,
      })
      .from(aiModels)
      .where(eq(aiModels.userId, userId));

    const afterProviders = await Promise.all(
      providers.map(async (item) => {
        const result = await KeyVaultsGateKeeper.getUserKeyVaults(
          keyVaultsSecret,
          item.keyVaults,
          item.userId,
        );

        const modelsInProvider = newModels.filter((model) => model.providerId === item.id);

        return {
          ...item,
          customModels: modelsInProvider.filter((model) => model.source === 'custom').length,
          enabledModels: modelsInProvider.filter((model) => model.enabled).length,
          keyVaults: this.maskSensitiveData(result),
          remoteModels: modelsInProvider.filter((model) => model.source === 'remote').length,
        };
      }),
    );

    // 将 keyVaults 解密后和 language 合并在一起

    const keyVaults = await KeyVaultsGateKeeper.getUserKeyVaults(
      keyVaultsSecret,
      settings.keyVaults,
      userId,
    );

    const languageModel: any = settings.languageModel || {};
    const providerKeys = uniq(Object.keys(keyVaults).concat(Object.keys(languageModel))).filter(
      (key) => key !== 'password',
    );

    return providerKeys.map((key): ProviderMigrationDetail => {
      const models = languageModel[key];

      const after = afterProviders.find((item) => item.id === key);

      return {
        after: after
          ? {
              enabled: after.enabled,
              fetchOnClient: after.fetchOnClient,
              keyVaults: after.keyVaults,
            }
          : undefined,
        afterModels: {
          customModels: after?.customModels || 0,
          enabledModels: after?.enabledModels || 0,
          remoteModels: after?.remoteModels || 0,
        },
        before: {
          enabled: models?.enabled,
          fetchOnClient: models?.fetchOnClient,
          keyVaults: this.maskSensitiveData(keyVaults[key]),
        },
        beforeModels: {
          customModels: models?.customModelCards?.length || 0,
          enabledModels: models?.enabledModels?.length || 0,
          remoteModels: models?.remoteModelCards?.length || 0,
        },
        id: key,
      };
    });
  }

  async getNeedMigrationUsers() {
    const results = await lobechatDB
      .select({ id: userSettings.id })
      .from(userSettings)
      .where(isNotNull(userSettings.keyVaults));

    return results.map((user) => user.id);
  }

  /**
   * 对对象中的敏感数据进行脱敏
   */
  private maskSensitiveData(obj: Record<string, any>): typeof obj {
    const result = { ...obj };

    Object.entries(result).forEach(([key, value]) => {
      if (typeof value === 'object' && value !== null) {
        result[key] = this.maskSensitiveData(value);
      } else if (typeof value === 'string') {
        result[key] = maskString(value);
      }
    });

    return result;
  }

  async getModelsDetail({ userId, providerId }: { providerId: string; userId: string }) {
    const [settings] = await lobechatDB
      .select({
        id: userSettings.id,
        languageModel: userSettings.languageModel,
      })
      .from(userSettings)
      .where(eq(userSettings.id, userId));

    const newModels = await lobechatDB
      .select({
        abilities: aiModels.abilities,
        config: aiModels.config,
        contextWindowTokens: aiModels.contextWindowTokens,
        displayName: aiModels.displayName,
        enabled: aiModels.enabled,
        id: aiModels.id,
        providerId: aiModels.providerId,
        source: aiModels.source,
        userId: aiModels.userId,
      })
      .from(aiModels)
      .where(and(eq(aiModels.userId, userId), eq(aiModels.providerId, providerId)));

    // @ts-expect-error
    const providerModels = settings.languageModel?.[providerId] || {};

    const modelIds = uniq(
      newModels
        .map((item) => item.id)
        .concat(providerModels.customModelCards?.map((item: any) => item.id))
        .concat(providerModels.remoteModelCards?.map((item: any) => item.id))
        .concat(providerModels.enabledModels)
        .filter((m) => !!m),
    );

    return modelIds.map((id) => {
      const after = newModels.find((item) => item.id === id);

      let before = providerModels.customModelCards?.find((item: any) => item.id === id);
      if (!before) {
        before = providerModels.remoteModelCards?.find((item: any) => item.id === id);
      }
      if (!before) {
        before = providerModels.enabledModels?.some((item: string) => item === id)
          ? { id }
          : undefined;
      }

      return {
        after: after
          ? {
              abilities: after.abilities,
              config: providerId === 'azure' ? after.config : undefined,
              contextWindowTokens: after.contextWindowTokens,
              displayName: after.displayName,
              enabled: after.enabled,
              id: after.id,
              source: after.source,
            }
          : undefined,
        before,
        id: id,
      };
    });
  }

  async cleanMigratedUsersConfig(userIds: string[]) {
    return lobechatDB
      .update(userSettings)
      .set({ keyVaults: null, languageModel: null })
      .where(inArray(userSettings.id, userIds));
  }
}
