/* eslint-disable sort-keys-fix/sort-keys-fix  */
import { boolean, jsonb, pgTable, text } from 'drizzle-orm/pg-core';

import { timestamps } from '@/database/helper/schema';
import { ModelReasoning } from '@/types/message';

export const messages = pgTable('messages', {
  id: text('id').primaryKey(),

  role: text('role', { enum: ['user', 'system', 'assistant', 'tool'] }).notNull(),
  content: text('content'),
  reasoning: jsonb('reasoning').$type<ModelReasoning>(),

  model: text('model'),
  provider: text('provider'),

  favorite: boolean('favorite').default(false),
  error: jsonb('error'),

  tools: jsonb('tools'),

  traceId: text('trace_id'),
  observationId: text('observation_id'),

  clientId: text('client_id'),

  // foreign keys
  userId: text('user_id').notNull(),
  sessionId: text('session_id'),
  topicId: text('topic_id'),
  threadId: text('thread_id'),
  // @ts-ignore
  parentId: text('parent_id'),
  quotaId: text('quota_id'),

  // used for group chat
  agentId: text('agent_id'),

  ...timestamps,
});
