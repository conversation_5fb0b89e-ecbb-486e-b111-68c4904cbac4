/* eslint-disable sort-keys-fix/sort-keys-fix  */
import { boolean, pgTable, text } from 'drizzle-orm/pg-core';

import { createdAt, updatedAt } from '@/database/helper/schema';

export const topics = pgTable('topics', {
  clientId: text('client_id'),
  createdAt: createdAt(),
  favorite: boolean('favorite').default(false),
  id: text('id').primaryKey(),
  sessionId: text('session_id'),
  title: text('title'),
  updatedAt: updatedAt(),
  userId: text('user_id').notNull(),
});
