/* eslint-disable sort-keys-fix/sort-keys-fix  */
import { jsonb, pgTable, text } from 'drizzle-orm/pg-core';

import { createdAt, updatedAt } from '@/database/helper/schema';

export const agents = pgTable('agents', {
  id: text('id').primaryKey(),
  title: text('title'),
  description: text('description'),
  tags: jsonb('tags').$type<string[]>().default([]),
  avatar: text('avatar'),
  backgroundColor: text('background_color'),
  plugins: jsonb('plugins').$type<string[]>().default([]),
  userId: text('user_id').notNull(),
  fewShots: jsonb('few_shots'),
  model: text('model'),
  params: jsonb('params').default({}),
  provider: text('provider'),
  systemRole: text('system_role'),
  createdAt: createdAt(),
  updatedAt: updatedAt(),
});
