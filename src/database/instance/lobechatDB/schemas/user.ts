/* eslint-disable sort-keys-fix/sort-keys-fix  */
import { boolean, jsonb, pgTable, text } from 'drizzle-orm/pg-core';

import { createdAt, timestamptz, updatedAt } from '@/database/helper/schema';

export const users = pgTable('users', {
  // The ID will be the user's ID from Clerk
  id: text('id').primaryKey().notNull(),
  username: text('username').unique(),
  email: text('email'),

  avatar: text('avatar'),
  phone: text('phone'),
  firstName: text('first_name'),
  lastName: text('last_name'),

  isOnboarded: boolean('is_onboarded').default(false),
  // Time user was created in Clerk
  clerkCreatedAt: timestamptz('clerk_created_at'),

  preference: jsonb('preference'),

  createdAt: createdAt(),
  updatedAt: updatedAt(),
});

export type UserItem = typeof users.$inferSelect;

export const userSettings = pgTable('user_settings', {
  id: text('id')
    .references(() => users.id, { onDelete: 'cascade' })
    .primaryKey(),
  keyVaults: text('key_vaults'),
  languageModel: jsonb('language_model'),
});

export type UserSettingsItem = typeof userSettings.$inferSelect;
