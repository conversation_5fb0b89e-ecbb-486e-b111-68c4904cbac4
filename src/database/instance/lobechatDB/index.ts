import { Pool as NeonPool } from '@neondatabase/serverless';
import { NeonDatabase, drizzle as neonDrizzle } from 'drizzle-orm/neon-serverless';
import { drizzle as nodeDrizzle } from 'drizzle-orm/node-postgres';
import { Pool as NodePool } from 'pg';

import { lobechatDBEnv } from '@/envs/lobechat';

import * as schema from './schemas';

export const getDBInstance = (): NeonDatabase<typeof schema> => {
  let connectionString = lobechatDBEnv.CHAT_DATABASE_URL;

  if (lobechatDBEnv.CHAT_DATABASE_DRIVER === 'node') {
    const client = new NodePool({ connectionString });
    return nodeDrizzle(client, { schema });
  }

  const client = new NeonPool({ connectionString });
  return neonDrizzle(client, { schema });
};

export const lobechatDB = getDBInstance();
