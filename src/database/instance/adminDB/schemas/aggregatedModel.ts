/* eslint-disable sort-keys-fix/sort-keys-fix  */
import { boolean, index, integer, jsonb, pgTable, text, varchar } from 'drizzle-orm/pg-core';

import { timestamps } from '@/database/helper/schema';

import { adminModels, adminProviders } from './adminInfra';

/**
 * admin 管理的聚合模型
 */
export const aggregatedModel = pgTable(
  'aggregated_model',
  {
    id: varchar('id', { length: 150 }).primaryKey().notNull(), // 主键
    logo: text('logo'), // 模型logo
    displayName: varchar('display_name', { length: 200 }), // 模型显示名称
    description: text('description'), // 模型描述
    enabled: boolean('enabled').notNull().default(true), // 是否启用
    type: varchar('type', { length: 20 }).default('chat').notNull(), // 模型类型
    fallbackModelId: text('fallback_model_id'), // Fallback 模型 ID（保留字段）
    pricing: jsonb('pricing').notNull(), // 定价信息（JSON）
    abilities: jsonb('abilities').default({}), // 能力（JSON）
    contextWindowTokens: integer('context_window_tokens'), // 上下文窗口令牌数
    ...timestamps,
  },
  (table) => [index('uq_model_id').on(table.id)], // 模型ID索引
);

/**
 * admin 中的聚合模型与 litellm 中模型 ID 的关联表
 */
export const aggregatedModelLitellmModel = pgTable(
  'aggregated_model_litellm_model',
  {
    aggregatedModelId: text('aggregated_model_id')
      .notNull()
      .references(() => aggregatedModel.id, { onDelete: 'cascade' }), // 聚合模型ID，级联删除
    litellmModelId: text('litellm_model_id').notNull(), // litellm 中模型 ID
    infraProviderId: text('infra_provider_id')
      .notNull()
      .references(() => adminProviders.id, { onDelete: 'cascade' }), // Admin 中的基础设施提供者ID
    infraModelId: text('infra_model_id')
      .notNull()
      .references(() => adminModels.id, { onDelete: 'cascade' }), // Admin 中的基础设施模型ID
    litellmParams: jsonb('litellm_params').default({}), // LiteLLM 模型参数（JSON）
  },
  (table) => [
    // litellm_model_id 的唯一索引，用于加速查询模型信息
    index('uq_litellm_model_id').on(table.litellmModelId),
    // model_id 和 litellm_model_id 的联合索引，用于加速查询模型信息
    index('uq_aggregated_model_id_litellm_model_id').on(
      table.aggregatedModelId,
      table.litellmModelId,
    ),
    // model_id 和 infra_provider_id 和 infra_model_id 的联合唯一索引，用于确保聚合模型下只有一个相同的记录
    index('uq_aggregated_model_id_infra_provider_id_infra_model_id').on(
      table.aggregatedModelId,
      table.infraProviderId,
      table.infraModelId,
    ),
  ],
);
