/* eslint-disable sort-keys-fix/sort-keys-fix  */
import { boolean, integer, jsonb, pgTable, text, uniqueIndex, varchar } from 'drizzle-orm/pg-core';

import { timestamps } from '@/database/helper/schema';

export const adminProviders = pgTable('providers', {
  id: varchar('id', { length: 64 }).primaryKey().notNull(),
  name: text('name'),

  enabled: boolean('enabled'),
  logo: text('logo'),
  description: text('description'),
  sort: integer('sort'),

  // need to be encrypted
  keyVaults: text('key_vaults'),
  source: varchar('source', { enum: ['builtin', 'custom'], length: 20 }),
  settings: jsonb('settings').default({}),

  // 关联的 LiteLLM 的凭证信息
  litellmCredential: text('litellm_credential').notNull(),

  ...timestamps,
});

export type NewAdminProviderItem = typeof adminProviders.$inferInsert;

export const adminModels = pgTable(
  'models',
  {
    id: varchar('id', { length: 150 }).notNull(),
    displayName: varchar('display_name', { length: 200 }),
    description: text('description'),
    enabled: boolean('enabled'),
    providerId: varchar('provider_id', { length: 64 })
      .notNull()
      .references(() => adminProviders.id, { onDelete: 'cascade' }),
    type: varchar('type', { length: 20 }).default('chat').notNull(),
    sort: integer('sort'),

    pricing: jsonb('pricing'),
    parameters: jsonb('parameters').default({}),
    config: jsonb('config'),
    abilities: jsonb('abilities').default({}),
    contextWindowTokens: integer('context_window_tokens'),
    source: varchar('source', { enum: ['remote', 'custom', 'builtin'], length: 20 }),
    releasedAt: varchar('released_at', { length: 10 }),

    ...timestamps,
  },
  (table) => [uniqueIndex('uq_provider_id_id').on(table.providerId, table.id)],
);

export type NewAdminModelItem = typeof adminModels.$inferInsert;
export type AdminModelSelectItem = typeof adminModels.$inferSelect;
