/* eslint-disable sort-keys-fix/sort-keys-fix  */
import { boolean, integer, jsonb, pgTable, text } from 'drizzle-orm/pg-core';

import { createdAt, updatedAt } from '@/database/helper/schema';

export const userSubscriptions = pgTable('user_subscriptions', {
  id: text('id').primaryKey().notNull(),
  userId: text('user_id').notNull(),
  stripeId: text('stripe_id'),

  currency: text('currency'),
  pricing: integer('pricing'),
  billingPaidAt: integer('billing_paid_at'),
  billingCycleStart: integer('billing_cycle_start'),
  billingCycleEnd: integer('billing_cycle_end'),

  cancelAtPeriodEnd: boolean('cancel_at_period_end'),
  cancelAt: integer('cancel_at'),

  nextBilling: jsonb('next_billing'),

  mode: text('mode'),
  plan: text('plan'),
  recurring: text('recurring'),
  quantity: integer('quantity'),

  storageLimit: integer('storage_limit'),

  status: integer('status'),
  createdAt: createdAt(),
  updatedAt: updatedAt(),
});

export type NewUserSubscription = typeof userSubscriptions.$inferInsert;
export type UserSubscriptionItem = typeof userSubscriptions.$inferSelect;

export const userBudgets = pgTable('user_budgets', {
  id: text('id').primaryKey().notNull(),

  freeBudgetId: text('free_budget_id'),
  freeBudgetKey: text('free_budget_key'),

  subscriptionBudgetId: text('subscription_budget_id'),
  subscriptionBudgetKey: text('subscription_budget_key'),

  packageBudgetId: text('package_budget_id'),
  packageBudgetKey: text('package_budget_key'),

  createdAt: createdAt(),
  updatedAt: updatedAt(),
});

export type NewUserBudgets = typeof userBudgets.$inferInsert;
export type UserBudgetItem = typeof userBudgets.$inferSelect;

export const userCredits = pgTable('user_credits', {
  id: text('id').primaryKey().notNull(),
  userId: text('user_id').notNull(),
  stripeId: text('stripe_id'),

  credits: integer('credits'),

  currency: text('currency'),
  pricing: integer('pricing'),
  billingPaidAt: integer('billing_paid_at'),

  plan: text('plan'),
  quantity: integer('quantity'),
  createdAt: createdAt(),
  updatedAt: updatedAt(),
});

export type NewUserCredit = typeof userCredits.$inferInsert;
export type UserCreditItem = typeof userCredits.$inferSelect;
