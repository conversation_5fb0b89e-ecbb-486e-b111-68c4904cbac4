/* eslint-disable sort-keys-fix/sort-keys-fix  */
import {
  bigint,
  boolean,
  date,
  doublePrecision,
  integer,
  jsonb,
  pgTable,
  pgView,
  text,
  timestamp,
} from 'drizzle-orm/pg-core';

export const LiteLLM_BudgetTable = pgTable('LiteLLM_BudgetTable', {
  budget_id: text('budget_id').primaryKey().notNull(),
  max_budget: doublePrecision('max_budget'),
  soft_budget: doublePrecision('soft_budget'),
  max_parallel_requests: integer('max_parallel_requests'),
  // You can use { mode: "bigint" } if numbers are exceeding js number limitations
  tpm_limit: bigint('tpm_limit', { mode: 'number' }),
  // You can use { mode: "bigint" } if numbers are exceeding js number limitations
  rpm_limit: bigint('rpm_limit', { mode: 'number' }),
  model_max_budget: jsonb('model_max_budget'),
  budget_duration: text('budget_duration'),
  budget_reset_at: timestamp('budget_reset_at', { precision: 3, mode: 'string' }),
  created_at: timestamp('created_at', { precision: 3, mode: 'string' }).defaultNow().notNull(),
  created_by: text('created_by').notNull(),
  updated_at: timestamp('updated_at', { precision: 3, mode: 'string' }).defaultNow().notNull(),
  updated_by: text('updated_by').notNull(),
});

export const LiteLLM_VerificationToken = pgTable('LiteLLM_VerificationToken', {
  token: text('token').primaryKey().notNull(),
  key_name: text('key_name'),
  key_alias: text('key_alias'),
  soft_budget_cooldown: boolean('soft_budget_cooldown').default(false).notNull(),
  blocked: boolean('blocked').default(false).notNull(),
  spend: doublePrecision('spend').notNull().default(0),
  expires: timestamp('expires', { precision: 3, mode: 'string' }),
  models: text('models').array(),
  aliases: jsonb('aliases').default({}).notNull(),
  config: jsonb('config').default({}).notNull(),
  user_id: text('user_id'),
  team_id: text('team_id'),
  permissions: jsonb('permissions').default({}).notNull(),
  max_parallel_requests: integer('max_parallel_requests'),
  metadata: jsonb('metadata').default({}).notNull(),
  // You can use { mode: "bigint" } if numbers are exceeding js number limitations
  tpm_limit: bigint('tpm_limit', { mode: 'number' }),
  // You can use { mode: "bigint" } if numbers are exceeding js number limitations
  rpm_limit: bigint('rpm_limit', { mode: 'number' }),
  max_budget: doublePrecision('max_budget'),
  budget_duration: text('budget_duration'),
  budget_reset_at: timestamp('budget_reset_at', { precision: 3, mode: 'string' }),
  allowed_cache_controls: text('allowed_cache_controls').array(),
  model_spend: jsonb('model_spend').default({}).notNull(),
  model_max_budget: jsonb('model_max_budget').default({}).notNull(),
  budget_id: text('budget_id'),
  created_at: timestamp('created_at', { precision: 3, mode: 'string' }).defaultNow().notNull(),
  updated_at: timestamp('updated_at', { precision: 3, mode: 'string' }).defaultNow().notNull(),
});

export const LiteLLM_SpendLogs = pgTable('LiteLLM_SpendLogs', {
  request_id: text('request_id').primaryKey().notNull(),
  call_type: text('call_type'),
  api_key: text('api_key'),
  spend: doublePrecision('spend'),
  total_tokens: integer('total_tokens'),
  prompt_tokens: integer('prompt_tokens'),
  completion_tokens: integer('completion_tokens'),
  startTime: timestamp('startTime', { precision: 3, mode: 'string' }),
  endTime: timestamp('endTime', { precision: 3, mode: 'string' }),
  completionStartTime: timestamp('completionStartTime', { precision: 3, mode: 'string' }),
  model: text('model'),
  model_id: text('model_id'),
  model_group: text('model_group'),
  api_base: text('api_base'),
  user: text('user'),
  metadata: jsonb('metadata'),
  cache_hit: text('cache_hit'),
  cache_key: text('cache_key'),
  request_tags: jsonb('request_tags'),
  team_id: text('team_id'),
  end_user: text('end_user'),
  requester_ip_address: text('requester_ip_address'),
});

export const LiteLLM_MonthlyGlobalSpend = pgView('MonthlyGlobalSpend', {
  date: date('date').primaryKey(),
  spend: doublePrecision('spend'),
}).existing();
