import dayjs, { Dayjs } from 'dayjs';
import { SQL, and, gte, lte } from 'drizzle-orm';

export const genWhere = (sqls: (SQL<any> | undefined)[]): SQL<any> | undefined => {
  const where = sqls.filter(Boolean);
  if (where.length > 1) return and(...where);
  return where[0];
};

export const genStartDateWhere = (
  date: string | undefined,
  key: any,
  format: (date: Dayjs) => any,
): SQL | undefined => {
  if (!date) return;
  return gte(key, format(dayjs(new Date(date))));
};

export const genEndDateWhere = (
  date: string | undefined,
  key: any,
  format: (date: Dayjs) => any,
): SQL | undefined => {
  if (!date) return;
  return lte(key, format(dayjs(new Date(date)).add(1, 'day')));
};

export const genRangeWhere = (
  range: [string, string] | undefined,
  key: any,
  format: (date: Dayjs) => any,
): SQL | undefined => {
  if (!range) return;
  return and(genStartDateWhere(range[0], key, format), genEndDateWhere(range[1], key, format));
};
