import dayjs, { Dayjs } from 'dayjs';
import { sql } from 'drizzle-orm';

import { DisplayType } from '@/components/DateRangeCompare/type';
import { today } from '@/utils/time';

export const genAllDates = ({
  startDate,
  endDate,
  dateFormat,
  dateIncrement,
}: {
  dateFormat: string;
  dateIncrement: 'day' | 'week' | 'month';
  endDate: Dayjs;
  startDate: Dayjs;
}) => {
  const allDates = [];
  let currentDate = startDate.clone();
  if (dateIncrement === 'week') currentDate = currentDate.endOf('week');
  while (currentDate.isBefore(endDate)) {
    allDates.push(currentDate.format(dateFormat));
    currentDate = currentDate.add(1, dateIncrement);
    if (dateIncrement === 'week') currentDate = currentDate.endOf('week');
  }
  return allDates;
};

export const genRange = ({
  range,
  display = DisplayType.EveryDay,
  groupKey,
}: {
  display?: DisplayType;
  groupKey?: any;
  range: [string, string];
}) => {
  const [startDate, endDate] = [dayjs(new Date(range[0])), dayjs(new Date(range[1])).add(1, 'day')];

  let groupByClause;
  let dateFormat;
  let dateIncrement: 'day' | 'week' | 'month';

  switch (display) {
    case DisplayType.EveryDay: {
      groupByClause = sql`DATE(${groupKey})`;
      dateFormat = 'YYYY-MM-DD';
      dateIncrement = 'day';
      break;
    }
    case DisplayType.EveryWeek: {
      groupByClause = sql`DATE_TRUNC('week', ${groupKey})`;
      dateFormat = 'YYYY-MM-DD';
      dateIncrement = 'week';
      break;
    }
    case DisplayType.EveryMonth: {
      groupByClause = sql`DATE_TRUNC('month', ${groupKey})`;
      dateFormat = 'YYYY-MM';
      dateIncrement = 'month';
      break;
    }
  }

  const allDates = genAllDates({ dateFormat, dateIncrement, endDate, startDate });

  return {
    allDates,
    dateFormat,
    dateIncrement,
    formatAllDates: (result: any) => {
      const resultMap = result.reduce(
        (acc: any, item: any) => {
          let date = dayjs(item.date as string);
          if (dateIncrement === 'week') date = date.endOf('week');
          acc[date.format(dateFormat)] = item;
          return acc;
        },
        {} as Record<string, any>,
      );

      return allDates.map((date) => {
        let count = resultMap[date]?.count;
        if (count) {
          count = Number(count);
        } else if (!count && !dayjs(date as string).isAfter(today())) {
          count = 0;
        }
        return {
          ...resultMap[date],
          count,
          date,
        };
      });
    },
    groupByClause,
  };
};

export const genRangeDiffDay = (range: [string, string]) => {
  return dayjs(range[1]).diff(dayjs(range[0]), 'day') + 1;
};
