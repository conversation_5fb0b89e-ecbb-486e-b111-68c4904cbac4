import { SQL, asc, desc } from 'drizzle-orm';
import type { AnyColumn, SQLWrapper } from 'drizzle-orm';

import { SortEnum } from '@/types/query';

export const genOrder = (sorts: [SortEnum | undefined, AnyColumn | SQLWrapper][]): SQL[] => {
  const result = sorts
    .map(([sort, field]) => {
      return sort && (sort === SortEnum.Asc ? asc(field) : desc(field));
    })
    .filter(Boolean) as SQL[];

  if (result.length === 0) {
    const field = sorts?.[0][1];
    if (!field) return [];
    return [desc(field)] as SQL[];
  }
  return result;
};
