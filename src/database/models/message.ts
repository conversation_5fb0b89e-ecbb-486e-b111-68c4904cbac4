import { count, eq, like, ne } from 'drizzle-orm';

import { lobechatDB } from '@/database/instance/lobechatDB';
import { messages } from '@/database/instance/lobechatDB/schemas';
import { genOrder } from '@/database/utils/genOrder';
import { genTable } from '@/database/utils/genTable';
import { genEndDateWhere, genRangeWhere, genWhere } from '@/database/utils/genWhere';
import { MessageItem, MessageQuery, MessageRoleType } from '@/types/message';
import { SortEnum } from '@/types/query';

export class MessageModel {
  getMessagesList = async (
    params: MessageQuery = {},
    sorts: {
      createdAt?: SortEnum;
    } = {},
  ): Promise<{
    data: MessageItem[];
    total: number;
  }> => {
    const { userId, current = 1, pageSize = 20, role, provider, model, range, topicId } = params;
    const { limit, offset } = genTable({ current, pageSize });
    const result = await lobechatDB
      .select({
        content: messages.content,
        createdAt: messages.createdAt,
        id: messages.id,
        model: messages.model,
        provider: messages.provider,
        role: messages.role,
        updatedAt: messages.updatedAt,
        userId: messages.userId,
      })
      .from(messages)
      .where(
        genWhere([
          genRangeWhere(range, messages.createdAt, (date) => date.toDate()),
          userId ? eq(messages.userId, userId) : undefined,
          role ? eq(messages.role, role) : undefined,
          provider ? like(messages.provider, `%${provider}%`) : undefined,
          model ? like(messages.model, `%${model}%`) : undefined,
          topicId ? eq(messages.topicId, topicId) : undefined,
        ]),
      )
      .orderBy(...genOrder([[sorts?.createdAt, messages.createdAt]]))
      .limit(limit)
      .offset(offset);

    return {
      data: result,
      total: await this.countTotalMessages({ topicId, userId }),
    };
  };

  countTotalMessages = async (props?: {
    customProvider?: boolean;
    endDate?: string;
    role?: MessageRoleType;
    topicId?: string;
    userId?: string;
  }): Promise<number> => {
    const result = await lobechatDB
      .select({
        total: count(messages.id),
      })
      .from(messages)
      .where(
        genWhere([
          props?.role ? eq(messages.role, props.role) : undefined,
          props?.customProvider ? ne(messages.provider, 'lobehub') : undefined,
          props?.userId ? eq(messages.userId, props.userId) : undefined,
          props?.topicId ? eq(messages.topicId, props.topicId) : undefined,
          props?.endDate
            ? genEndDateWhere(props.endDate, messages.createdAt, (date) => date.toDate())
            : undefined,
        ]),
      );

    return result[0]?.total ?? 0;
  };
}
