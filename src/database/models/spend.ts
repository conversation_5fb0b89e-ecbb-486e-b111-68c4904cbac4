import dayjs from 'dayjs';
import { count, desc, eq, inArray, isNotNull, like, not, sum } from 'drizzle-orm';
import { uniq } from 'lodash';

import { DisplayType } from '@/components/DateRangeCompare/type';
import { CREDIT_UNIT } from '@/const/subscription';
import { fuzzySearchUserIdList } from '@/database/helper/fuzzySearch';
import { litellmDB } from '@/database/instance/litellmDB';
import { LiteLLM_SpendLogs } from '@/database/instance/litellmDB/schema';
import { lobechatDB } from '@/database/instance/lobechatDB';
import { users } from '@/database/instance/lobechatDB/schemas';
import { genOrder } from '@/database/utils/genOrder';
import { genRange } from '@/database/utils/genRange';
import { genTable } from '@/database/utils/genTable';
import { genUsername } from '@/database/utils/genUsername';
import {
  genEndDateWhere,
  genRangeWhere,
  genStartDateWhere,
  genWhere,
} from '@/database/utils/genWhere';
import { SortEnum } from '@/types/query';
import { SpendItem, SpendQuery } from '@/types/spend';
import { formatApiBase } from '@/utils/format';

export class SpendModel {
  private formatSpendResult = (result: any[], userList?: any[]) => {
    return result.map(({ userId, ip, ...item }) => {
      const data = {
        ...item,
        TTFT: dayjs(item.completionStartTime).diff(item.startTime, 'ms'),
        duration: dayjs(item.endTime).diff(item.completionStartTime, 'ms'),
        ip: ((ip || '') as string).split(',')[0],
        latency: dayjs(item.endTime).diff(item.startTime, 'ms'),
        spend: Math.round(item.spend! * CREDIT_UNIT),
      };

      if (!userList) return data;

      const user = userList.find((user) => user.id === userId);
      return {
        ...data,
        user: {
          avatar: user?.avatar,
          email: user?.email,
          id: userId,
          name: genUsername(user),
        },
      };
    });
  };

  getUserSpendList = async (
    params: SpendQuery = {},
    sorts: {
      spend?: SortEnum;
      startTime?: SortEnum;
      totalTokens?: SortEnum;
    } = {},
  ): Promise<{
    data: SpendItem[];
    total: number;
  }> => {
    const { type, current = 1, pageSize = 20, model, range, emailOrUsernameOrUserId } = params;
    const { limit, offset } = genTable({ current, pageSize });
    const userIdList = await fuzzySearchUserIdList(emailOrUsernameOrUserId, range);

    const where = genWhere([
      genRangeWhere(range, LiteLLM_SpendLogs.startTime, (date) => date.toISOString()),
      isNotNull(LiteLLM_SpendLogs.user),
      not(inArray(LiteLLM_SpendLogs.user, ['None', 'default_user_id'])),
      userIdList && userIdList.length > 0 ? inArray(LiteLLM_SpendLogs.user, userIdList) : undefined,
      type ? eq(LiteLLM_SpendLogs.call_type, type) : undefined,
      model ? like(LiteLLM_SpendLogs.model, `%${model}%`) : undefined,
    ]);

    const result = await litellmDB
      .select({
        completionStartTime: LiteLLM_SpendLogs.completionStartTime,
        completionTokens: LiteLLM_SpendLogs.completion_tokens,
        endTime: LiteLLM_SpendLogs.endTime,
        id: LiteLLM_SpendLogs.request_id,
        ip: LiteLLM_SpendLogs.requester_ip_address,
        model: LiteLLM_SpendLogs.model,
        promptTokens: LiteLLM_SpendLogs.prompt_tokens,
        spend: LiteLLM_SpendLogs.spend,
        startTime: LiteLLM_SpendLogs.startTime,
        totalTokens: LiteLLM_SpendLogs.total_tokens,
        type: LiteLLM_SpendLogs.call_type,
        userId: LiteLLM_SpendLogs.user,
      })
      .from(LiteLLM_SpendLogs)
      .where(where)
      .orderBy(
        ...genOrder([
          [sorts?.startTime, LiteLLM_SpendLogs.startTime],
          [sorts?.totalTokens, LiteLLM_SpendLogs.total_tokens],
          [sorts?.spend, LiteLLM_SpendLogs.spend],
        ]),
      )
      .limit(limit)
      .offset(offset);

    const userIds = uniq(result.map((item) => item.userId).filter(Boolean)) as string[];

    const userList = await lobechatDB
      .select({
        avatar: users.avatar,
        email: users.email,
        firstName: users.firstName,
        id: users.id,
        lastName: users.lastName,
        username: users.username,
      })
      .from(users)
      .where(inArray(users.id, userIds));

    // 获取总记录数
    const totalCount = await litellmDB
      .select({ count: count(LiteLLM_SpendLogs.request_id) })
      .from(LiteLLM_SpendLogs)
      .where(where);

    return {
      data: this.formatSpendResult(result, userList),
      total: totalCount[0].count,
    };
  };

  getRangeSpend = async ({
    display,
    range,
  }: {
    display: DisplayType;
    range: [string, string];
  }): Promise<{ count: number; date: string }[]> => {
    const { formatAllDates, groupByClause } = genRange({
      display,
      groupKey: LiteLLM_SpendLogs.startTime,
      range,
    });
    const result = await litellmDB
      .select({ count: sum(LiteLLM_SpendLogs.spend), date: groupByClause })
      .from(LiteLLM_SpendLogs)
      .where(genRangeWhere(range, LiteLLM_SpendLogs.startTime, (date) => date.toISOString()))
      .groupBy(groupByClause)
      .orderBy(groupByClause);

    return formatAllDates(result);
  };

  getRangeSpendDetails = async ({
    range,
  }: {
    range: [string, string];
  }): Promise<{ name: string; value: number }[]> => {
    const result = await litellmDB
      .select({
        name: LiteLLM_SpendLogs.api_base,
        value: sum(LiteLLM_SpendLogs.spend).as('value'),
      })
      .from(LiteLLM_SpendLogs)
      .where(genRangeWhere(range, LiteLLM_SpendLogs.startTime, (date) => date.toISOString()))
      .groupBy(LiteLLM_SpendLogs.api_base)
      .orderBy((expr) => desc(expr.value));

    const merged: { name: string; value: number }[] = result.reduce(
      (acc: any, curr) => {
        const name = curr.name ? formatApiBase(curr.name) : 'Unknown';
        if (!acc[name]) {
          acc[name] = {
            name,
            value: Number(curr.value),
          };
        } else {
          acc[name].value += Number(curr.value);
        }
        return acc;
      },
      {} as Record<
        string,
        {
          name: string;
          value: number;
        }
      >,
    );

    return Object.values(merged).sort((a, b) => b.value - a.value);
  };

  getRangeToken = async ({
    display,
    range,
  }: {
    display: DisplayType;
    range: [string, string];
  }): Promise<{ [key: string]: number | string; count: number; date: string }[]> => {
    const { allDates, groupByClause, dateFormat, dateIncrement } = genRange({
      display,
      groupKey: LiteLLM_SpendLogs.startTime,
      range,
    });

    const result = await litellmDB
      .select({
        count: sum(LiteLLM_SpendLogs.total_tokens),
        date: groupByClause,
        model: LiteLLM_SpendLogs.model,
      })
      .from(LiteLLM_SpendLogs)
      .where(genRangeWhere(range, LiteLLM_SpendLogs.startTime, (date) => date.toISOString()))
      .groupBy(groupByClause, LiteLLM_SpendLogs.model)
      .orderBy(groupByClause, LiteLLM_SpendLogs.model);

    return allDates.map((date) => {
      const entry: any = { date };
      const planData = result.filter((r) => {
        let d = dayjs(r.date as string);
        if (dateIncrement === 'week') d = d.endOf('week');
        return d.format(dateFormat) === date;
      });
      planData.forEach((item) => {
        if (!item.model) return;
        entry[item.model] = Number(item.count);
      });
      return {
        ...entry,
        count: Object.values(entry).reduce((sum: number, item) => {
          if (typeof item === 'number' && !isNaN(item)) {
            return sum + item;
          }
          return sum;
        }, 0),
      };
    });
  };

  getRangeModelCalls = async ({
    display,
    range,
  }: {
    display: DisplayType;
    range: [string, string];
  }): Promise<{ count: number; date: string }[]> => {
    const { formatAllDates, groupByClause } = genRange({
      display,
      groupKey: LiteLLM_SpendLogs.startTime,
      range,
    });

    const result = await litellmDB
      .select({
        count: count(),
        date: groupByClause,
      })
      .from(LiteLLM_SpendLogs)
      .where(genRangeWhere(range, LiteLLM_SpendLogs.startTime, (date) => date.toISOString()))
      .groupBy(groupByClause)
      .orderBy(groupByClause);

    return formatAllDates(result);
  };

  getRangeModelCallsDetails = async ({
    range,
  }: {
    range: [string, string];
  }): Promise<{ name: string; value: number }[]> => {
    const result = await litellmDB
      .select({
        name: LiteLLM_SpendLogs.model,
        value: count().as('value'),
      })
      .from(LiteLLM_SpendLogs)
      .where(genRangeWhere(range, LiteLLM_SpendLogs.startTime, (date) => date.toISOString()))
      .groupBy(LiteLLM_SpendLogs.model)
      .orderBy((expr) => desc(expr.value));

    return result.map(({ name, value }) => ({
      name: String(name),
      value: Number(value),
    }));
  };

  getTotalModelCalls = async (props?: {
    endDate?: string;
    range?: [string, string];
    startDate?: string;
  }): Promise<number> => {
    const result = await litellmDB
      .select({ total: count() })
      .from(LiteLLM_SpendLogs)
      .where(
        genWhere([
          props?.startDate
            ? genStartDateWhere(props.startDate, LiteLLM_SpendLogs.startTime, (date) =>
                date.toISOString(),
              )
            : undefined,
          props?.endDate
            ? genEndDateWhere(props.endDate, LiteLLM_SpendLogs.startTime, (date) =>
                date.toISOString(),
              )
            : undefined,
          props?.range
            ? genRangeWhere(props.range, LiteLLM_SpendLogs.startTime, (date) => date.toISOString())
            : undefined,
        ]),
      );
    return result[0]?.total ?? 0;
  };

  getTotalSpend = async (props?: {
    endDate?: string;
    range?: [string, string];
    startDate?: string;
    userId?: string;
  }): Promise<number> => {
    const result = await litellmDB
      .select({ total: sum(LiteLLM_SpendLogs.spend) })
      .from(LiteLLM_SpendLogs)
      .where(
        genWhere([
          props?.userId ? eq(LiteLLM_SpendLogs.user, props.userId) : undefined,
          props?.startDate
            ? genStartDateWhere(props.startDate, LiteLLM_SpendLogs.startTime, (date) =>
                date.toISOString(),
              )
            : undefined,
          props?.endDate
            ? genEndDateWhere(props.endDate, LiteLLM_SpendLogs.startTime, (date) =>
                date.toISOString(),
              )
            : undefined,
          props?.range
            ? genRangeWhere(props.range, LiteLLM_SpendLogs.startTime, (date) => date.toISOString())
            : undefined,
        ]),
      );
    return Number(result[0]?.total) ?? 0;
  };

  /**
   * 根据模型ID列表和时间范围查询消费记录汇总
   */
  getSpendByModelIds = async (props: {
    endDate?: string;
    modelIds: string[];
    range?: [string, string];
    startDate?: string;
  }): Promise<{
    modelStats: Array<{
      calls: number;
      inputTokens: number;
      model: string;
      modelId: string;
      outputTokens: number;
      spend: number;
      totalTokens: number;
    }>;
    totalCalls: number;
    totalInputTokens: number;
    totalOutputTokens: number;
    totalSpend: number;
    totalTokens: number;
  }> => {
    const { modelIds, startDate, endDate, range } = props;

    if (modelIds.length === 0) {
      return {
        modelStats: [],
        totalCalls: 0,
        totalInputTokens: 0,
        totalOutputTokens: 0,
        totalSpend: 0,
        totalTokens: 0,
      };
    }

    const whereConditions = genWhere([
      inArray(LiteLLM_SpendLogs.model_id, modelIds),
      startDate
        ? genStartDateWhere(startDate, LiteLLM_SpendLogs.startTime, (date) => date.toISOString())
        : undefined,
      endDate
        ? genEndDateWhere(endDate, LiteLLM_SpendLogs.startTime, (date) => date.toISOString())
        : undefined,
      range
        ? genRangeWhere(range, LiteLLM_SpendLogs.startTime, (date) => date.toISOString())
        : undefined,
    ]);

    // 获取总体统计
    const totalResult = await litellmDB
      .select({
        totalCalls: count(LiteLLM_SpendLogs.request_id),
        totalInputTokens: sum(LiteLLM_SpendLogs.prompt_tokens),
        totalOutputTokens: sum(LiteLLM_SpendLogs.completion_tokens),
        totalSpend: sum(LiteLLM_SpendLogs.spend),
        totalTokens: sum(LiteLLM_SpendLogs.total_tokens),
      })
      .from(LiteLLM_SpendLogs)
      .where(whereConditions);

    // 获取按模型分组的统计
    const modelStatsResult = await litellmDB
      .select({
        calls: count(LiteLLM_SpendLogs.request_id),
        inputTokens: sum(LiteLLM_SpendLogs.prompt_tokens),
        model: LiteLLM_SpendLogs.model,
        modelId: LiteLLM_SpendLogs.model_id,
        outputTokens: sum(LiteLLM_SpendLogs.completion_tokens),
        spend: sum(LiteLLM_SpendLogs.spend),
        totalTokens: sum(LiteLLM_SpendLogs.total_tokens),
      })
      .from(LiteLLM_SpendLogs)
      .where(whereConditions)
      .groupBy(LiteLLM_SpendLogs.model_id, LiteLLM_SpendLogs.model);

    return {
      modelStats: modelStatsResult.map((item) => ({
        calls: Number(item.calls) ?? 0,
        inputTokens: Number(item.inputTokens) ?? 0,
        model: item.model || '',
        modelId: item.modelId || '',
        outputTokens: Number(item.outputTokens) ?? 0,
        spend: Number(item.spend) ?? 0,
        totalTokens: Number(item.totalTokens) ?? 0,
      })),
      totalCalls: Number(totalResult[0]?.totalCalls) ?? 0,
      totalInputTokens: Number(totalResult[0]?.totalInputTokens) ?? 0,
      totalOutputTokens: Number(totalResult[0]?.totalOutputTokens) ?? 0,
      totalSpend: Number(totalResult[0]?.totalSpend) ?? 0,
      totalTokens: Number(totalResult[0]?.totalTokens) ?? 0,
    };
  };
}
