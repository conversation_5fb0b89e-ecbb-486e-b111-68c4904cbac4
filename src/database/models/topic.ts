import { count, eq } from 'drizzle-orm';

import { lobechatDB } from '@/database/instance/lobechatDB';
import { topics } from '@/database/instance/lobechatDB/schemas';
import { genOrder } from '@/database/utils/genOrder';
import { genTable } from '@/database/utils/genTable';
import { genEndDateWhere, genWhere } from '@/database/utils/genWhere';
import { SortQuery } from '@/types/query';

export interface TopicListItem {
  clientId: string | null;
  createdAt: Date;
  favorite: boolean | null;
  id: string;
  sessionId: string | null;
  title: string | null;
  updatedAt: Date;
  userId: string;
}

export class TopicModel {
  getTotalTopics = async (props?: { endDate?: string; userId?: string }): Promise<number> => {
    const result = await lobechatDB
      .select({
        total: count(topics.id),
      })
      .from(topics)
      .where(
        genWhere([
          genEndDateWhere(props?.endDate, topics.createdAt, (date) => date.toDate()),
          props?.userId ? eq(topics.userId, props.userId) : undefined,
        ]),
      );

    return result[0]?.total ?? 0;
  };

  getUserTopicsList = async (
    params: {
      current?: number;
      pageSize?: number;
      userId: string;
    },
    sorts: SortQuery = {},
  ): Promise<{
    data: TopicListItem[];
    total: number;
  }> => {
    const { userId, pageSize = 5, current = 1 } = params;
    const { offset, limit } = genTable({ current, pageSize });

    const result = await lobechatDB
      .select({
        clientId: topics.clientId,
        createdAt: topics.createdAt,
        favorite: topics.favorite,
        id: topics.id,
        sessionId: topics.sessionId,
        title: topics.title,
        updatedAt: topics.updatedAt,
        userId: topics.userId,
      })
      .from(topics)
      .where(eq(topics.userId, userId))
      .orderBy(
        ...genOrder([
          [sorts?.createdAt, topics.createdAt],
          [sorts?.updatedAt, topics.updatedAt],
        ]),
      )
      .limit(limit)
      .offset(offset);

    const totalResult = await lobechatDB
      .select({ total: count(topics.id) })
      .from(topics)
      .where(eq(topics.userId, userId));

    return {
      data: result,
      total: totalResult[0]?.total ?? 0,
    };
  };
}
