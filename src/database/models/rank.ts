import { count, desc, eq, sql } from 'drizzle-orm';

import { lobechatDB } from '@/database/instance/lobechatDB';
import { agents, messages, topics, users } from '@/database/instance/lobechatDB/schemas';
import { genRangeDiffDay } from '@/database/utils/genRange';
import { genUsername } from '@/database/utils/genUsername';
import { genRangeWhere } from '@/database/utils/genWhere';

export interface RankItem {
  avatar: string;
  count: number;
  id: string;
  username: string;
}

export class RankModel {
  getTopicRank = async (params: {
    limit?: number;
    range: [string, string];
  }): Promise<RankItem[]> => {
    const result = await lobechatDB
      .select({
        avatar: users.avatar,
        count: count(topics.id).as('count'),
        firstName: users.firstName,
        id: users.id,
        lastName: users.lastName,
        username: users.username,
      })
      .from(users)
      .leftJoin(topics, eq(users.id, topics.userId))
      .where(genRangeWhere(params.range, topics.createdAt, (date) => date.toDate()))
      .groupBy(users.id)
      .orderBy(desc(sql`count`))
      .limit(params?.limit || 10);

    return this.buildRank(result);
  };

  getAgentRank = async (params: {
    limit?: number;
    range: [string, string];
  }): Promise<RankItem[]> => {
    const result = await lobechatDB
      .select({
        avatar: users.avatar,
        count: count(agents.id).as('count'),
        firstName: users.firstName,
        id: users.id,
        lastName: users.lastName,
        username: users.username,
      })
      .from(users)
      .leftJoin(agents, eq(users.id, agents.userId))
      .where(genRangeWhere(params.range, agents.createdAt, (date) => date.toDate()))
      .groupBy(users.id, users.username, users.avatar)
      .orderBy(desc(sql`count`))
      .limit(params?.limit || 10);

    return this.buildRank(result);
  };

  getMessageRank = async (params: {
    limit?: number;
    range: [string, string];
  }): Promise<RankItem[]> => {
    const result = await lobechatDB
      .select({
        avatar: users.avatar,
        count: count(messages.id).as('count'),
        firstName: users.firstName,
        id: users.id,
        lastName: users.lastName,
        username: users.username,
      })
      .from(users)
      .leftJoin(messages, eq(users.id, messages.userId))
      .where(genRangeWhere(params.range, messages.createdAt, (date) => date.toDate()))
      .groupBy(users.id, users.username, users.avatar)
      .orderBy(desc(sql`count`))
      .limit(params?.limit || 10);

    return this.buildRank(result);
  };

  getWeeklyActiveRank = async (params: {
    limit?: number;
    range: [string, string];
  }): Promise<RankItem[]> => {
    const result = await lobechatDB
      .select({
        avatar: users.avatar,
        count: sql<number>`COUNT(DISTINCT DATE(${messages.createdAt}))`.as('count'),
        firstName: users.firstName,
        id: users.id,
        lastName: users.lastName,
        username: users.username,
      })
      .from(users)
      .leftJoin(messages, eq(users.id, messages.userId))
      .where(genRangeWhere(params.range, messages.createdAt, (date) => date.toDate()))
      .groupBy(users.id, users.username, users.avatar)
      .orderBy(desc(sql`count`))
      .limit(params?.limit || 10);

    const diffDate = genRangeDiffDay(params.range);

    return this.buildRank(result, (count) => (count / diffDate) * 7);
  };

  private buildRank = (result: any[], formatCount?: (count: number) => number): RankItem[] => {
    return result.map((item) => ({
      avatar: item.avatar ?? '',
      count: formatCount ? formatCount(Number(item.count)) : Number(item.count),
      id: item.id,
      username: genUsername(item),
    }));
  };
}
