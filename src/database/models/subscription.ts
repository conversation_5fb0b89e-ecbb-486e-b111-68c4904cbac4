import dayjs from 'dayjs';
import isoWeek from 'dayjs/plugin/isoWeek';
import utc from 'dayjs/plugin/utc';
import {
  and,
  asc,
  count,
  desc,
  eq,
  gt,
  gte,
  inArray,
  isNotNull,
  isNull,
  lt,
  ne,
  or,
  sql,
} from 'drizzle-orm';

import { DisplayType } from '@/components/DateRangeCompare/type';
import { SubscriptionStatus } from '@/const/subscription';
import { cloudDB } from '@/database/instance/cloudDB';
import { userSubscriptions } from '@/database/instance/cloudDB/schema';
import { lobechatDB } from '@/database/instance/lobechatDB';
import { users } from '@/database/instance/lobechatDB/schemas';
import { genRange } from '@/database/utils/genRange';
import { genTable } from '@/database/utils/genTable';
import { genUsername } from '@/database/utils/genUsername';
import {
  genEndDateWhere,
  genRangeWhere,
  genStartDateWhere,
  genWhere,
} from '@/database/utils/genWhere';
import { CommonQuery } from '@/types/query';
import { BillingMode, Plans, Recurring, Status } from '@/types/subscription';
import { today } from '@/utils/time';

dayjs.extend(isoWeek);
dayjs.extend(utc);

export class SubscriptionModel {
  getTotalStatusCount = async (): Promise<{
    total: number;
    [Status.ActiveNotCancelling]: number;
    [Status.ActiveFutureCancellation]: number;
    [Status.Cancelled]: number;
  }> => {
    const activeNotCancellingCount = await cloudDB
      .select({ count: count(userSubscriptions.id) })
      .from(userSubscriptions)
      .where(
        and(
          eq(userSubscriptions.status, SubscriptionStatus.Active),
          or(isNull(userSubscriptions.cancelAt), lt(userSubscriptions.cancelAt, today().unix())),
        ),
      );

    const activeFutureCancellationCount = await cloudDB
      .select({ count: count(userSubscriptions.id) })
      .from(userSubscriptions)
      .where(
        and(
          eq(userSubscriptions.status, SubscriptionStatus.Active),
          gte(userSubscriptions.cancelAt, today().unix()),
        ),
      );

    const cancelledCount = await cloudDB
      .select({ count: count(userSubscriptions.id) })
      .from(userSubscriptions)
      .where(eq(userSubscriptions.status, SubscriptionStatus.Cancelled));

    const data = {
      total: 0,
      [Status.ActiveNotCancelling]: Number(activeNotCancellingCount[0].count),
      [Status.ActiveFutureCancellation]: Number(activeFutureCancellationCount[0].count),
      [Status.Cancelled]: Number(cancelledCount[0].count),
    };

    data.total = Object.values(data).reduce((acc, count) => acc + count, 0) - data.total;

    return data;
  };

  getTotalModeCount = async (): Promise<{
    total: number;
    [BillingMode.Payment]: number;
    [BillingMode.Subscription]: number;
  }> => {
    const result = await cloudDB
      .select({
        count: count(userSubscriptions.id),
        mode: userSubscriptions.mode,
      })
      .from(userSubscriptions)
      .where(ne(userSubscriptions.status, 1))
      .groupBy(userSubscriptions.mode);

    return result.reduce(
      (acc, item) => {
        const count = Number(item.count);
        // @ts-ignore
        acc[item.mode] = count;
        acc.total += count;
        return acc;
      },
      {
        total: 0,
        [BillingMode.Payment]: 0,
        [BillingMode.Subscription]: 0,
      },
    );
  };

  getTotalRecurringCount = async (): Promise<{
    total: number;
    [Recurring.Monthly]: number;
    [Recurring.Yearly]: number;
  }> => {
    const result = await cloudDB
      .select({
        count: count(userSubscriptions.id),
        recurring: userSubscriptions.recurring,
      })
      .from(userSubscriptions)
      .where(ne(userSubscriptions.status, 1))
      .groupBy(userSubscriptions.recurring);

    return result.reduce(
      (acc, item) => {
        const count = Number(item.count);
        // @ts-ignore
        acc[item.recurring] = count;
        acc.total += count;
        return acc;
      },
      {
        total: 0,
        [Recurring.Monthly]: 0,
        [Recurring.Yearly]: 0,
      },
    );
  };

  getTotalPlanCount = async (): Promise<{
    total: number;
    [Plans.Hobby]: number;
    [Plans.Starter]: number;
    [Plans.Premium]: number;
    [Plans.Ultimate]: number;
  }> => {
    const result = await cloudDB
      .select({
        count: count(userSubscriptions.id),
        plan: userSubscriptions.plan,
      })
      .from(userSubscriptions)
      .where(ne(userSubscriptions.status, 1))
      .groupBy(userSubscriptions.plan);

    return result.reduce(
      (acc, item) => {
        const count = Number(item.count);
        // @ts-ignore
        acc[item.plan] = count;
        acc.total += count;
        return acc;
      },
      {
        total: 0,
        [Plans.Hobby]: 0,
        [Plans.Starter]: 0,
        [Plans.Premium]: 0,
        [Plans.Ultimate]: 0,
      },
    );
  };

  getTotalFutureCancelUsers = async (): Promise<number> => {
    const result = await cloudDB
      .select({
        count: count(),
      })
      .from(userSubscriptions)
      .where(
        and(isNotNull(userSubscriptions.cancelAt), gt(userSubscriptions.cancelAt, today().unix())),
      );

    return result[0]?.count ?? 0;
  };

  getFutureCancelUsers = async ({ current = 1, pageSize = 10 }: CommonQuery = {}): Promise<
    {
      avatar: string;
      cancelAt: string;
      id: string;
      mode: BillingMode;
      plan: Plans;
      recurring: Recurring;
      username: string;
    }[]
  > => {
    const { limit, offset } = genTable({ current, pageSize });
    const result = await cloudDB
      .select({
        cancelAt: userSubscriptions.cancelAt,
        mode: userSubscriptions.mode,
        plan: userSubscriptions.plan,
        recurring: userSubscriptions.recurring,
        userId: userSubscriptions.userId,
      })
      .from(userSubscriptions)
      .where(
        and(isNotNull(userSubscriptions.cancelAt), gt(userSubscriptions.cancelAt, dayjs().unix())),
      )
      .orderBy(asc(userSubscriptions.cancelAt))
      .limit(limit)
      .offset(offset);

    const userIds = result.map((cancel) => cancel.userId);
    const usersInfo = await lobechatDB
      .select({
        avatar: users.avatar,
        firstName: users.firstName,
        id: users.id,
        lastName: users.lastName,
        username: users.username,
      })
      .from(users)
      .where(inArray(users.id, userIds));

    const userInfoMap = new Map(usersInfo.map((user) => [user.id, user]));

    return result.map((item) => {
      const userItem = userInfoMap.get(item.userId);
      return {
        avatar: userInfoMap.get(item.userId)?.avatar ?? '',
        cancelAt: dayjs.unix(item.cancelAt as any).format('YYYY-MM-DD HH:mm:ss'),
        id: item.userId,
        mode: item.mode as BillingMode,
        plan: item.plan as Plans,
        recurring: item.recurring as Recurring,
        username: genUsername(userItem),
      };
    });
  };

  getTotalRecentSubscriptUsers = async (): Promise<number> => {
    const result = await cloudDB
      .select({
        count: count(userSubscriptions.id),
      })
      .from(userSubscriptions);

    return result[0]?.count ?? 0;
  };

  getRecentSubscriptUsers = async ({ current = 1, pageSize = 10 }: CommonQuery = {}): Promise<
    {
      avatar: string;
      createdAt: string;
      id: string;
      mode: BillingMode;
      plan: Plans;
      recurring: Recurring;
      username: string;
    }[]
  > => {
    const { limit, offset } = genTable({ current, pageSize });

    const result = await cloudDB
      .select({
        createdAt: userSubscriptions.createdAt,
        mode: userSubscriptions.mode,
        plan: userSubscriptions.plan,
        recurring: userSubscriptions.recurring,
        userId: userSubscriptions.userId,
      })
      .from(userSubscriptions)
      .orderBy(desc(userSubscriptions.createdAt))
      .limit(limit)
      .offset(offset);

    const userIds = result.map((sub) => sub.userId);
    const usersInfo = await lobechatDB
      .select({
        avatar: users.avatar,
        firstName: users.firstName,
        id: users.id,
        lastName: users.lastName,
        username: users.username,
      })
      .from(users)
      .where(inArray(users.id, userIds));

    const userInfoMap = new Map(usersInfo.map((user) => [user.id, user]));

    return result.map((item) => {
      const userItem = userInfoMap.get(item.userId);
      return {
        avatar: userInfoMap.get(item.userId)?.avatar ?? '',
        createdAt: dayjs(item.createdAt).format('YYYY-MM-DD HH:mm:ss'),
        id: item.userId,
        mode: item.mode as BillingMode,
        plan: item.plan as Plans,
        recurring: item.recurring as Recurring,
        username: genUsername(userItem),
      };
    });
  };

  getTotalSubscriptions = async (props?: {
    endDate?: string;
    plan?: Plans;
    range?: [string, string];
    startDate?: string;
  }): Promise<number> => {
    const result = await cloudDB
      .select({
        total: count(userSubscriptions.id),
      })
      .from(userSubscriptions)
      .where(
        genWhere([
          ne(userSubscriptions.status, 1),
          props?.plan ? eq(userSubscriptions.plan, props.plan) : undefined,
          props?.startDate
            ? genStartDateWhere(props.startDate, userSubscriptions.createdAt, (date) =>
                date.toDate(),
              )
            : undefined,
          props?.endDate
            ? genEndDateWhere(props.endDate, userSubscriptions.createdAt, (date) => date.toDate())
            : undefined,
          props?.range
            ? genRangeWhere(props.range, userSubscriptions.createdAt, (date) => date.toDate())
            : undefined,
        ]),
      );

    return result[0]?.total ?? 0;
  };

  getRangeSubscriptions = async ({
    display,
    range,
  }: {
    display: DisplayType;
    range: [string, string];
  }): Promise<{ count: number; date: string }[]> => {
    const { groupByClause, formatAllDates } = genRange({
      display,
      groupKey: userSubscriptions.createdAt,
      range,
    });

    const result = await cloudDB
      .select({
        count: count(userSubscriptions.id),
        date: groupByClause,
      })
      .from(userSubscriptions)
      .where(genRangeWhere(range, userSubscriptions.createdAt, (date) => date.toDate()))
      .groupBy(groupByClause)
      .orderBy(groupByClause);

    return formatAllDates(result);
  };

  getRangeCancel = async ({
    display,
    range,
  }: {
    display: DisplayType;
    range: [string, string];
  }): Promise<{ count: number; date: string }[]> => {
    const { groupByClause, formatAllDates } = genRange({
      display,
      groupKey: sql`to_timestamp(${userSubscriptions.cancelAt})::date`,
      range,
    });

    const result = await cloudDB
      .select({
        count: count(userSubscriptions.id),
        date: groupByClause,
      })
      .from(userSubscriptions)
      .where(
        and(
          isNotNull(userSubscriptions.cancelAt),
          genRangeWhere(range, userSubscriptions.cancelAt, (date) => date.unix()),
        ),
      )
      .groupBy(groupByClause)
      .orderBy(groupByClause);

    return formatAllDates(result);
  };

  getRangePlans = async ({
    display,
    range,
  }: {
    display: DisplayType;
    range: [string, string];
  }): Promise<
    {
      date: number;
      [Plans.Hobby]: number;
      [Plans.Starter]: number;
      [Plans.Premium]: number;
      [Plans.Ultimate]: number;
    }[]
  > => {
    const { groupByClause, allDates, dateFormat, dateIncrement } = genRange({
      display,
      groupKey: userSubscriptions.createdAt,
      range,
    });

    const result = await cloudDB
      .select({
        count: count(userSubscriptions.id),
        date: groupByClause,
        plan: userSubscriptions.plan,
      })
      .from(userSubscriptions)
      .where(genRangeWhere(range, userSubscriptions.createdAt, (date) => date.toDate()))
      .groupBy(groupByClause, userSubscriptions.plan)
      .orderBy(groupByClause, userSubscriptions.plan);

    return allDates.map((date) => {
      const entry: any = { date };
      Object.values(Plans).forEach((plan) => {
        if (plan === Plans.Free) return;
        const planData = result.find((r) => {
          let d = dayjs(r.date as string);
          if (dateIncrement === 'week') d = d.endOf('week');
          return d.format(dateFormat) === date && r.plan === plan;
        });
        entry[plan] = planData ? planData.count : 0;
      });
      return entry;
    });
  };

  getUserSubscriptionById = async (id: string) => {
    const result = await cloudDB.query.userSubscriptions.findMany({
      where: eq(userSubscriptions.userId, id),
    });
    return result.sort((a, b) => {
      return dayjs(b.billingPaidAt).diff(dayjs(a.billingPaidAt));
    });
  };

  getUserStripIdById = async (id: string) => {
    const result = await cloudDB.query.userSubscriptions.findFirst({
      where: eq(userSubscriptions.userId, id),
    });
    if (!result) return;
    return result?.stripeId;
  };

  getUserCurrentPlanById = async (id: string): Promise<Plans> => {
    const result = await cloudDB.query.userSubscriptions.findFirst({
      where: and(eq(userSubscriptions.userId, id), eq(userSubscriptions.status, 0)),
    });
    return (result?.plan as Plans) || Plans.Free;
  };

  // 取消用户订阅
  cancelUserSubscriptionById = async (subscriptionId: string) => {
    const now = dayjs().unix();

    // 获取当前订阅信息
    const subscription = await cloudDB.query.userSubscriptions.findFirst({
      where: eq(userSubscriptions.id, subscriptionId),
    });

    if (!subscription) {
      throw new Error('Subscription not found');
    }

    // 更新订阅状态为到期后取消
    const result = await cloudDB
      .update(userSubscriptions)
      .set({
        cancelAt: now,
        status: 1, // 取消
        updatedAt: new Date(),
      })
      .where(eq(userSubscriptions.id, subscriptionId))
      .returning();

    return result[0];
  };
}
