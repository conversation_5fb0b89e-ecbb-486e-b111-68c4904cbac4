import dayjs from 'dayjs';
import isoWeek from 'dayjs/plugin/isoWeek';
import utc from 'dayjs/plugin/utc';
import { count, eq, inArray } from 'drizzle-orm';
import { uniq } from 'lodash';

import { fuzzySearchUserIdList } from '@/database/helper/fuzzySearch';
import { cloudDB } from '@/database/instance/cloudDB';
import { userSubscriptions } from '@/database/instance/cloudDB/schema';
import { lobechatDB } from '@/database/instance/lobechatDB';
import { users } from '@/database/instance/lobechatDB/schemas';
import { genOrder } from '@/database/utils/genOrder';
import { genTable } from '@/database/utils/genTable';
import { genRangeWhere, genWhere } from '@/database/utils/genWhere';
import { SortEnum } from '@/types/query';
import { OrderItem, OrderQuery } from '@/types/subscription';

dayjs.extend(isoWeek);
dayjs.extend(utc);

export class OrderModel {
  getOrders = async (
    params: OrderQuery = {},
    sorts: {
      billingPaidAt?: SortEnum;
      createdAt?: SortEnum;
      pricing?: SortEnum;
    } = {},
  ): Promise<{
    data: OrderItem[];
    total: number;
  }> => {
    const {
      current = 1,
      pageSize = 20,
      range,
      emailOrUsernameOrUserId,
      recurring,
      plan,
      mode,
      status,
    } = params;
    const { offset, limit } = genTable({ current, pageSize });
    const userIdList = await fuzzySearchUserIdList(emailOrUsernameOrUserId, range);

    const where = genWhere([
      genRangeWhere(range, userSubscriptions.createdAt, (date) => date.toDate()),
      userIdList && userIdList.length > 0
        ? inArray(userSubscriptions.userId, userIdList)
        : undefined,
      plan ? eq(userSubscriptions.plan, plan) : undefined,
      mode ? eq(userSubscriptions.mode, mode) : undefined,
      recurring ? eq(userSubscriptions.recurring, recurring) : undefined,
      status ? eq(userSubscriptions.status, Number(status)) : undefined,
    ]);

    const result = await cloudDB
      .select({
        billingCycleEnd: userSubscriptions.billingCycleEnd,
        billingCycleStart: userSubscriptions.billingCycleStart,
        billingPaidAt: userSubscriptions.billingPaidAt,
        cancelAt: userSubscriptions.cancelAt,
        cancelAtPeriodEnd: userSubscriptions.cancelAtPeriodEnd,
        createdAt: userSubscriptions.createdAt,
        currency: userSubscriptions.currency,
        id: userSubscriptions.id,
        mode: userSubscriptions.mode,
        plan: userSubscriptions.plan,
        pricing: userSubscriptions.pricing,
        quantity: userSubscriptions.quantity,
        recurring: userSubscriptions.recurring,
        status: userSubscriptions.status,
        stripeId: userSubscriptions.stripeId,
        updatedAt: userSubscriptions.updatedAt,
        userId: userSubscriptions.userId,
      })
      .from(userSubscriptions)
      .where(where)
      .orderBy(
        ...genOrder([
          [sorts?.createdAt, userSubscriptions.createdAt],
          [sorts?.pricing, userSubscriptions.pricing],
          [sorts?.billingPaidAt, userSubscriptions.billingPaidAt],
        ]),
      )
      .limit(limit)
      .offset(offset);

    const userIds = uniq(result.map((item) => item.userId).filter(Boolean)) as string[];

    const userList = await lobechatDB
      .select({
        avatar: users.avatar,
        email: users.email,
        firstName: users.firstName,
        id: users.id,
        lastName: users.lastName,
        username: users.username,
      })
      .from(users)
      .where(inArray(users.id, userIds));

    // 获取总记录数
    const totalCount = await cloudDB
      .select({ count: count(userSubscriptions.id) })
      .from(userSubscriptions)
      .where(where);

    return {
      data: this.formatOrderResult(result, userList),
      total: totalCount[0].count,
    };
  };

  private formatOrderResult = (result: any[], userList: any[]): OrderItem[] => {
    return result.map((item) => {
      const user = userList.find((u) => u.id === item.userId);
      const { userId, ...rest } = item;
      return {
        ...rest,
        user: {
          avatar: user?.avatar,
          email: user?.email,
          id: userId,
          name:
            user?.firstName && user?.lastName
              ? `${user?.firstName} ${user?.lastName}`
              : user?.username,
        },
      };
    });
  };
}
