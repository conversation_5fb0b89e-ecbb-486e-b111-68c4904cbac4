import { and, asc, count, desc, eq, inArray } from 'drizzle-orm';

import { adminDB } from '@/database/instance/adminDB';
import {
  AdminModelSelectItem,
  adminModels,
  aggregatedModel,
  aggregatedModelLitellmModel,
} from '@/database/instance/adminDB/schemas';
import {
  AggregatedModelAssociationQuery,
  AggregatedModelItem,
  AggregatedModelLitellmItem,
  AggregatedModelQuery,
  CreateAggregatedModelData,
  CreateAggregatedModelLitellmData,
  ModelPricing,
  UpdateAggregatedModelData,
} from '@/types/aggregatedModel';
import { ModelAbilities } from '@/types/aiModel';
import { CreateModelParams } from '@/types/litellm';

export class AggregatedModelModel {
  /**
   * 获取所有聚合模型
   * @param query 查询参数
   * @returns 聚合模型列表
   */
  getAllModels = async (query?: AggregatedModelQuery): Promise<AggregatedModelItem[]> => {
    const whereConditions = [];

    if (query?.enabled !== undefined) {
      whereConditions.push(eq(aggregatedModel.enabled, query.enabled));
    }

    if (query?.type) {
      whereConditions.push(eq(aggregatedModel.type, query.type));
    }

    // 查询聚合模型及其关联模型数量
    const result = await adminDB
      .select({
        abilities: aggregatedModel.abilities as ModelAbilities,
        accessedAt: aggregatedModel.accessedAt,
        associatedModelsCount: count(aggregatedModelLitellmModel.litellmModelId),
        contextWindowTokens: aggregatedModel.contextWindowTokens,
        createdAt: aggregatedModel.createdAt,
        description: aggregatedModel.description,
        displayName: aggregatedModel.displayName,
        enabled: aggregatedModel.enabled,
        fallbackModelId: aggregatedModel.fallbackModelId,
        id: aggregatedModel.id,
        logo: aggregatedModel.logo,
        pricing: aggregatedModel.pricing as ModelPricing,
        type: aggregatedModel.type,
        updatedAt: aggregatedModel.updatedAt,
      })
      .from(aggregatedModel)
      .leftJoin(
        aggregatedModelLitellmModel,
        eq(aggregatedModel.id, aggregatedModelLitellmModel.aggregatedModelId),
      )
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
      .groupBy(aggregatedModel.id)
      .orderBy(desc(aggregatedModel.createdAt))
      .limit(query?.limit || 1000)
      .offset(query?.offset || 0);

    return result;
  };

  /**
   * 根据 ID 获取聚合模型
   * @param id 模型 ID
   * @returns 聚合模型信息或 null
   */
  getModelById = async (id: string): Promise<AggregatedModelItem | null> => {
    const model = await adminDB.query.aggregatedModel.findFirst({
      where: eq(aggregatedModel.id, id),
    });

    return model as AggregatedModelItem | null;
  };

  /**
   * 创建聚合模型
   * @param data 创建数据
   * @returns 创建的聚合模型信息
   */
  createModel = async (data: CreateAggregatedModelData): Promise<AggregatedModelItem> => {
    const result = await adminDB
      .insert(aggregatedModel)
      .values({
        abilities: data.abilities ?? {},
        contextWindowTokens: data.contextWindowTokens,
        description: data.description,
        displayName: data.displayName,
        enabled: data.enabled ?? true,
        fallbackModelId: data.fallbackModelId,
        id: data.id,
        logo: data.logo,
        pricing: data.pricing,
        type: data.type ?? 'chat',
      })
      .returning();

    return result[0] as AggregatedModelItem;
  };

  /**
   * 更新聚合模型
   * @param id 模型 ID
   * @param updateData 更新数据
   * @returns 更新后的聚合模型信息或 null
   */
  updateModel = async (
    id: string,
    updateData: UpdateAggregatedModelData,
  ): Promise<AggregatedModelItem | null> => {
    const result = await adminDB
      .update(aggregatedModel)
      .set({
        abilities: updateData.abilities,
        contextWindowTokens: updateData.contextWindowTokens,
        description: updateData.description,
        displayName: updateData.displayName,
        enabled: updateData.enabled,
        fallbackModelId: updateData.fallbackModelId,
        logo: updateData.logo,
        pricing: updateData.pricing,
        type: updateData.type,
      })
      .where(eq(aggregatedModel.id, id))
      .returning();

    return result.length > 0 ? (result[0] as AggregatedModelItem) : null;
  };

  /**
   * 删除聚合模型
   * @param id 模型 ID
   * @returns 是否删除成功
   */
  deleteModel = async (id: string): Promise<boolean> => {
    const result = await adminDB
      .delete(aggregatedModel)
      .where(eq(aggregatedModel.id, id))
      .returning();

    return result.length > 0;
  };

  /**
   * 获取聚合模型关联的模型信息
   */
  getModelsByAggregatedModelId = async (
    aggregatedModelId: string,
  ): Promise<AdminModelSelectItem[]> => {
    const associations = await this.getAssociationsByAggregatedModelId({
      aggregatedModelId,
    });

    const providerIds = associations.map((association) => association.infraProviderId);
    const modelIds = associations.map((association) => association.infraModelId);

    const models = await adminDB.query.adminModels.findMany({
      where: and(inArray(adminModels.id, modelIds), inArray(adminModels.providerId, providerIds)),
    });

    return models;
  };

  /**
   * 获取启用的聚合模型列表
   * @param query 查询参数
   * @returns 启用的聚合模型列表
   */
  getEnabledModels = async (
    query?: Omit<AggregatedModelQuery, 'enabled'>,
  ): Promise<AggregatedModelItem[]> => {
    const whereConditions = [eq(aggregatedModel.enabled, true)];

    if (query?.type) {
      whereConditions.push(eq(aggregatedModel.type, query.type));
    }

    const result = await adminDB.query.aggregatedModel.findMany({
      limit: query?.limit,
      offset: query?.offset,
      orderBy: asc(aggregatedModel.displayName),
      where: and(...whereConditions),
    });

    return result as AggregatedModelItem[];
  };

  /**
   * 切换模型启用状态
   * @param id 模型 ID
   * @param enabled 启用状态
   * @returns 更新后的聚合模型信息或 null
   */
  toggleModelStatus = async (id: string, enabled: boolean): Promise<AggregatedModelItem | null> => {
    const result = await adminDB
      .update(aggregatedModel)
      .set({
        enabled,
      })
      .where(eq(aggregatedModel.id, id))
      .returning();

    return result.length > 0 ? (result[0] as AggregatedModelItem) : null;
  };

  // =============== 聚合模型与LiteLLM模型关联 ===============

  /**
   * 判断模型是否可以被关联，同一个聚合模型下，不能有相同的渠道+模型
   * @param aggregatedModelId 聚合模型 ID
   * @param infraProviderId 基础设施提供者 ID
   * @param infraModelId 基础设施模型 ID
   * @returns 是否可以被关联
   */
  canInfraModelBeAssociated = async (
    aggregatedModelId: string,
    infraProviderId: string,
    infraModelId: string,
  ): Promise<boolean> => {
    const result = await adminDB.query.aggregatedModelLitellmModel.findMany({
      where: and(
        eq(aggregatedModelLitellmModel.aggregatedModelId, aggregatedModelId),
        eq(aggregatedModelLitellmModel.infraProviderId, infraProviderId),
        eq(aggregatedModelLitellmModel.infraModelId, infraModelId),
      ),
    });

    return result.length === 0;
  };

  /**
   * 根据聚合模型 ID 获取关联记录
   * @param query 查询参数
   * @returns 关联记录列表
   */
  getAssociationsByAggregatedModelId = async (
    query: AggregatedModelAssociationQuery,
  ): Promise<AggregatedModelLitellmItem[]> => {
    const { aggregatedModelId, infraProviderId, infraModelId, litellmModelId } = query;
    const whereConditions = [];

    if (aggregatedModelId) {
      whereConditions.push(eq(aggregatedModelLitellmModel.aggregatedModelId, aggregatedModelId));
    }

    if (infraProviderId) {
      whereConditions.push(eq(aggregatedModelLitellmModel.infraProviderId, infraProviderId));
    }

    if (infraModelId) {
      whereConditions.push(eq(aggregatedModelLitellmModel.infraModelId, infraModelId));
    }

    if (litellmModelId) {
      whereConditions.push(eq(aggregatedModelLitellmModel.litellmModelId, litellmModelId));
    }

    const result = await adminDB.query.aggregatedModelLitellmModel.findMany({
      where: and(...whereConditions),
    });

    // 将 unknown 类型的 litellmParams 转换为期望的类型
    return result.map((item) => ({
      ...item,
      litellmParams: item.litellmParams as Partial<CreateModelParams['litellm_params']>,
    }));
  };

  /**
   * 创建聚合模型与 LiteLLM 模型关联记录
   * @param data 创建数据
   * @returns 创建的关联记录
   */
  createAssociation = async (
    data: CreateAggregatedModelLitellmData,
  ): Promise<AggregatedModelLitellmItem> => {
    const result = await adminDB
      .insert(aggregatedModelLitellmModel)
      .values({
        aggregatedModelId: data.aggregatedModelId,
        infraModelId: data.infraModelId,
        infraProviderId: data.infraProviderId,
        litellmModelId: data.litellmModelId,
        litellmParams: data.litellmParams,
      })
      .returning();

    return result[0] as AggregatedModelLitellmItem;
  };

  /**
   * 更新关联记录的LiteLLM参数
   * @param litellmModelId LiteLLM 模型 ID
   * @param litellmParams 更新的参数
   * @returns 是否更新成功
   */
  updateAssociationParams = async (
    litellmModelId: string,
    litellmParams: Record<string, any>,
  ): Promise<boolean> => {
    const result = await adminDB
      .update(aggregatedModelLitellmModel)
      .set({
        litellmParams,
      })
      .where(eq(aggregatedModelLitellmModel.litellmModelId, litellmModelId))
      .returning();

    return result.length > 0;
  };

  /**
   * 删除关联记录
   * @param aggregatedModelId 聚合模型 ID
   * @param litellmModelId LiteLLM 模型 ID
   * @returns 是否删除成功
   */
  deleteAssociation = async (litellmModelId: string): Promise<boolean> => {
    const result = await adminDB
      .delete(aggregatedModelLitellmModel)
      .where(eq(aggregatedModelLitellmModel.litellmModelId, litellmModelId))
      .returning();

    return result.length > 0;
  };
}
