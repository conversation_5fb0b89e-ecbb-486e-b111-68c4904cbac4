import { and, eq } from 'drizzle-orm';

import { adminDB } from '@/database/instance/adminDB';
import { userVirtualKeys } from '@/database/instance/adminDB/schemas';
import { KeyVaultsGateKeeper } from '@/server/modules/KeyVaultsEncrypt';
import type {
  BatchCreateUserVirtualKeyData,
  CreateUserVirtualKeyData,
  UserVirtualKeyDetails,
  VirtualKeyInfo,
} from '@/types/rbac';

export class VirtualKeyModel {
  /**
   * 创建用户 Virtual key 记录
   * @param data 创建数据
   * @returns 创建的 Virtual key 信息
   */
  createVirtualKey = async (
    data: CreateUserVirtualKeyData,
    gateKeeper: KeyVaultsGateKeeper,
  ): Promise<UserVirtualKeyDetails> => {
    // 加密 Virtual key 信息
    const encryptedKeyVaults = await gateKeeper.encrypt(data.virtualKeyInfo);

    const result = await adminDB
      .insert(userVirtualKeys)
      .values({
        keyVaults: encryptedKeyVaults,
        roleId: data.roleId,
        userId: data.userId,
      })
      .returning();

    const createdVirtualKey = result[0];
    return {
      roleId: createdVirtualKey.roleId,
      userId: createdVirtualKey.userId,
      virtualKeyInfo: data.virtualKeyInfo,
    };
  };

  /**
   * 根据用户ID和角色ID获取 Virtual key
   * @param userId 用户ID
   * @param roleId 角色ID
   * @param gateKeeper 加密管理器
   * @returns Virtual key 信息或 null
   */
  getVirtualKeyByUserAndRole = async (
    userId: string,
    roleId: string,
    gateKeeper: KeyVaultsGateKeeper,
  ): Promise<UserVirtualKeyDetails | null> => {
    const apiKey = await adminDB.query.userVirtualKeys.findFirst({
      where: and(eq(userVirtualKeys.userId, userId), eq(userVirtualKeys.roleId, roleId)),
    });

    if (!apiKey) {
      return null;
    }

    // 解密 Virtual key 信息
    let decryptedVirtualKeyInfo: VirtualKeyInfo;
    try {
      const decryptResult = await gateKeeper.decrypt(apiKey.keyVaults);
      if (decryptResult.wasAuthentic && decryptResult.plaintext) {
        decryptedVirtualKeyInfo = JSON.parse(decryptResult.plaintext);
      } else {
        console.warn('Failed to decrypt API key: authentication failed');
        return null;
      }
    } catch (error) {
      console.warn('Failed to decrypt API key:', error);
      return null;
    }

    return {
      roleId: apiKey.roleId,
      userId: apiKey.userId,
      virtualKeyInfo: decryptedVirtualKeyInfo,
    };
  };

  /**
   * 根据 Virtual Key ID 获取 Virtual key 信息
   * @param virtualKeyId Virtual Key ID
   * @param gateKeeper 加密管理器
   * @returns Virtual key 信息或 null
   */
  getVirtualKeyByVirtualKeyId = async (
    virtualKeyId: string,
    gateKeeper: KeyVaultsGateKeeper,
  ): Promise<UserVirtualKeyDetails | null> => {
    // 获取所有 Virtual key 记录并解密查找匹配的 virtualKeyId
    const apiKeys = await adminDB.query.userVirtualKeys.findMany();

    for (const apiKey of apiKeys) {
      try {
        const decryptResult = await gateKeeper.decrypt(apiKey.keyVaults);

        if (decryptResult.wasAuthentic && decryptResult.plaintext) {
          const virtualKeyInfo: VirtualKeyInfo = JSON.parse(decryptResult.plaintext);
          if (virtualKeyInfo.virtualKeyId === virtualKeyId) {
            return {
              roleId: apiKey.roleId,
              userId: apiKey.userId,
              virtualKeyInfo,
            };
          }
        }
      } catch {
        // 忽略解密失败的记录
        continue;
      }
    }

    return null;
  };

  /**
   * 更新 Virtual key 信息
   * @param userId 用户ID
   * @param roleId 角色ID
   * @param newVirtualKeyInfo 新的 Virtual key 信息
   * @param gateKeeper 加密管理器
   * @returns 更新后的 Virtual key 信息
   */
  updateVirtualKey = async (
    userId: string,
    roleId: string,
    newVirtualKeyInfo: VirtualKeyInfo,
    gateKeeper: KeyVaultsGateKeeper,
  ): Promise<UserVirtualKeyDetails | null> => {
    // 加密新的 Virtual key 信息
    const encryptedKeyVaults = await gateKeeper.encrypt(newVirtualKeyInfo);

    const result = await adminDB
      .update(userVirtualKeys)
      .set({ keyVaults: encryptedKeyVaults })
      .where(and(eq(userVirtualKeys.userId, userId), eq(userVirtualKeys.roleId, roleId)))
      .returning();

    if (result.length === 0) {
      return null;
    }

    const updatedVirtualKey = result[0];
    return {
      roleId: updatedVirtualKey.roleId,
      userId: updatedVirtualKey.userId,
      virtualKeyInfo: newVirtualKeyInfo,
    };
  };

  /**
   * 删除 Virtual key 记录
   * @param userId 用户ID
   * @param roleId 角色ID
   * @returns 是否删除成功
   */
  deleteVirtualKey = async (userId: string, roleId: string): Promise<boolean> => {
    const result = await adminDB
      .delete(userVirtualKeys)
      .where(and(eq(userVirtualKeys.userId, userId), eq(userVirtualKeys.roleId, roleId)))
      .returning();

    return result.length > 0;
  };

  /**
   * 根据 Virtual Key ID 删除记录
   * @param virtualKeyId Virtual Key ID
   * @param gateKeeper 加密管理器
   * @returns 是否删除成功
   */
  deleteVirtualKeyByVirtualKeyId = async (
    virtualKeyId: string,
    gateKeeper: KeyVaultsGateKeeper,
  ): Promise<boolean> => {
    // 先查找匹配的记录
    const apiKeyDetails = await this.getVirtualKeyByVirtualKeyId(virtualKeyId, gateKeeper);
    if (!apiKeyDetails) {
      return false;
    }

    // 删除记录
    const result = await adminDB
      .delete(userVirtualKeys)
      .where(
        and(
          eq(userVirtualKeys.userId, apiKeyDetails.userId),
          eq(userVirtualKeys.roleId, apiKeyDetails.roleId),
        ),
      )
      .returning();

    return result.length > 0;
  };

  /**
   * 检查 Virtual key 是否存在
   * @param userId 用户ID
   * @param roleId 角色ID
   * @returns 是否存在
   */
  apiKeyExists = async (userId: string, roleId: string): Promise<boolean> => {
    const result = await adminDB.query.userVirtualKeys.findFirst({
      where: and(eq(userVirtualKeys.userId, userId), eq(userVirtualKeys.roleId, roleId)),
    });

    return !!result;
  };

  /**
   * 批量创建 Virtual key
   * @param dataList 创建数据列表
   * @param gateKeeper 加密管理器
   * @returns 创建的 Virtual key 列表
   */
  batchCreateVirtualKeys = async (
    dataList: BatchCreateUserVirtualKeyData[],
    gateKeeper: KeyVaultsGateKeeper,
  ): Promise<UserVirtualKeyDetails[]> => {
    // 批量加密 Virtual key
    const encryptedDataList = await Promise.all(
      dataList.map(async (data) => {
        const encryptedKeyVaults = await gateKeeper.encrypt(data.virtualKeyInfo);
        return {
          keyVaults: encryptedKeyVaults,
          roleId: data.roleId,
          userId: data.userId,
        };
      }),
    );

    const result = await adminDB.insert(userVirtualKeys).values(encryptedDataList).returning();

    return result.map((apiKey, index) => ({
      roleId: apiKey.roleId,
      userId: apiKey.userId,
      virtualKeyInfo: dataList[index].virtualKeyInfo,
    }));
  };

  /**
   * 删除用户的所有 Virtual key 记录
   * @param userId 用户ID
   * @returns 是否删除成功
   */
  deleteAllUserVirtualKeys = async (userId: string): Promise<boolean> => {
    try {
      const result = await adminDB
        .delete(userVirtualKeys)
        .where(eq(userVirtualKeys.userId, userId))
        .returning();

      return result.length > 0;
    } catch (error) {
      console.error('Failed to delete user virtual keys:', error);
      return false;
    }
  };
}
