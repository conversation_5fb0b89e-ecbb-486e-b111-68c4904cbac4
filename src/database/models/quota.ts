import { eq } from 'drizzle-orm';

import { adminDB } from '@/database/instance/adminDB';
import { roleQuotas } from '@/database/instance/adminDB/schemas';
import { CreateRoleQuotaData, RoleQuotaItem, UpdateRoleQuotaData } from '@/types/quota';

export class QuotaModel {
  /**
   * 获取角色关联的限额信息
   * @param roleId 角色ID
   * @returns 角色配额信息或 null
   */
  getRoleQuotas = async (roleId: string): Promise<RoleQuotaItem | null> => {
    const quota = await adminDB.query.roleQuotas.findFirst({
      where: eq(roleQuotas.roleId, roleId),
    });

    if (!quota) {
      return null;
    }

    return {
      accessedAt: quota.accessedAt,
      createdAt: quota.createdAt,
      fileMb: quota.fileMb,
      id: quota.id,
      roleId: quota.roleId,
      tokenBudget: quota.tokenBudget,
      updatedAt: quota.updatedAt,
      vectorCount: quota.vectorCount,
    };
  };

  /**
   * 更新角色的限额信息，如果不存在则创建
   * @param roleId 角色ID
   * @param quotaData 要更新的配额数据
   * @returns 更新后的角色配额信息
   */
  updateRoleQuotas = async (
    roleId: string,
    quotaData: UpdateRoleQuotaData,
  ): Promise<RoleQuotaItem> => {
    // 首先检查配额记录是否存在
    const existingQuota = await this.getRoleQuotas(roleId);

    if (!existingQuota) {
      // 如果不存在，创建一个新的配额记录，使用提供的数据或默认值
      const createData: CreateRoleQuotaData = {
        fileMb: quotaData.fileMb ?? null,
        roleId: roleId,
        tokenBudget: quotaData.tokenBudget ?? null,
        vectorCount: quotaData.vectorCount ?? null,
      };
      return await this.createRoleQuotas(createData);
    }

    // 如果存在，执行更新操作
    const updateData: Partial<typeof roleQuotas.$inferInsert> = {
      updatedAt: new Date(),
    };

    // 只更新提供的字段
    if (quotaData.tokenBudget !== undefined) {
      updateData.tokenBudget = quotaData.tokenBudget;
    }
    if (quotaData.fileMb !== undefined) {
      updateData.fileMb = quotaData.fileMb;
    }
    if (quotaData.vectorCount !== undefined) {
      updateData.vectorCount = quotaData.vectorCount;
    }

    const result = await adminDB
      .update(roleQuotas)
      .set(updateData)
      .where(eq(roleQuotas.roleId, roleId))
      .returning();

    if (result.length === 0) {
      throw new Error(`Role quota update failed for roleId: ${roleId}`);
    }

    const updatedQuota = result[0];
    return {
      accessedAt: updatedQuota.accessedAt,
      createdAt: updatedQuota.createdAt,
      fileMb: updatedQuota.fileMb,
      id: updatedQuota.id,
      roleId: updatedQuota.roleId,
      tokenBudget: updatedQuota.tokenBudget,
      updatedAt: updatedQuota.updatedAt,
      vectorCount: updatedQuota.vectorCount,
    };
  };

  /**
   * 创建角色配额记录
   * @param quotaData 配额数据
   * @returns 创建的角色配额信息
   */
  createRoleQuotas = async (quotaData: CreateRoleQuotaData): Promise<RoleQuotaItem> => {
    const result = await adminDB
      .insert(roleQuotas)
      .values({
        fileMb: quotaData.fileMb,
        roleId: quotaData.roleId,
        tokenBudget: quotaData.tokenBudget,
        vectorCount: quotaData.vectorCount,
      })
      .returning();

    const createdQuota = result[0];

    return {
      accessedAt: createdQuota.accessedAt,
      createdAt: createdQuota.createdAt,
      fileMb: createdQuota.fileMb,
      id: createdQuota.id,
      roleId: createdQuota.roleId,
      tokenBudget: createdQuota.tokenBudget,
      updatedAt: createdQuota.updatedAt,
      vectorCount: createdQuota.vectorCount,
    };
  };

  /**
   * 删除角色配额记录
   * @param roleId 角色ID
   * @returns 是否删除成功
   */
  deleteRoleQuotas = async (roleId: string): Promise<boolean> => {
    const result = await adminDB
      .delete(roleQuotas)
      .where(eq(roleQuotas.roleId, roleId))
      .returning();

    return result.length > 0;
  };

  /**
   * 检查角色配额是否存在
   * @param roleId 角色ID
   * @returns 是否存在
   */
  roleQuotaExists = async (roleId: string): Promise<boolean> => {
    const result = await adminDB.query.roleQuotas.findFirst({
      where: eq(roleQuotas.roleId, roleId),
    });

    return !!result;
  };
}
