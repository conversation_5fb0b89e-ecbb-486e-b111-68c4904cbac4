import dayjs from 'dayjs';
import isoWeek from 'dayjs/plugin/isoWeek';
import { and, count, desc, eq, gt, gte, inArray, ne, notExists, sum } from 'drizzle-orm';

import { DisplayType } from '@/components/DateRangeCompare/type';
import { genFuzzySearchUserWhere } from '@/database/helper/fuzzySearch';
import { cloudDB } from '@/database/instance/cloudDB';
import {
  UserBudgetItem,
  UserSubscriptionItem,
  userBudgets,
  userSubscriptions,
} from '@/database/instance/cloudDB/schema';
import { litellmDB } from '@/database/instance/litellmDB';
import { LiteLLM_VerificationToken } from '@/database/instance/litellmDB/schema';
import { lobechatDB } from '@/database/instance/lobechatDB';
import { genOrder } from '@/database/utils/genOrder';
import { genRange, genRangeDiffDay } from '@/database/utils/genRange';
import { genTable } from '@/database/utils/genTable';
import { genUsername } from '@/database/utils/genUsername';
import {
  genEndDateWhere,
  genRangeWhere,
  genStartDateWhere,
  genWhere,
} from '@/database/utils/genWhere';
import { KeyVaultsGateKeeper } from '@/server/modules/KeyVaultsEncrypt';
import { clerkServices } from '@/server/services/clerk';
import { EndUser, EndUserType, UserQuery } from '@/types/endUsers';
import { SortEnum } from '@/types/query';
import { Plans } from '@/types/subscription';

import { messages, users } from '../instance/lobechatDB/schemas';

dayjs.extend(isoWeek);

interface UserSelectType {
  avatar: string | null;
  createdAt: Date;
  email: string | null;
  id: string;
  isOnboarded: boolean | null;
  plan?: string | null;
  username: string | null;
}

interface TokenSelectType {
  expires: string | null;
  id: string;
  limit: number | null;
  spend: number;
}

const getUsagePercent = (item: TokenSelectType | undefined): number => {
  if (item && item.spend && item.limit) return (item.spend / item.limit) * 100;
  return 0;
};

export class UserModel {
  clerkServices = new clerkServices();

  getTotalUsers = async (props?: {
    endDate?: string;
    range?: [string, string];
    startDate?: string;
  }): Promise<number> => {
    const result = await lobechatDB
      .select({ total: count(users.id) })
      .from(users)
      .where(
        genWhere([
          props?.startDate
            ? genStartDateWhere(props.startDate, users.createdAt, (date) => date.toDate())
            : undefined,
          props?.endDate
            ? genEndDateWhere(props.endDate, users.createdAt, (date) => date.toDate())
            : undefined,
          props?.range
            ? genRangeWhere(props.range, users.createdAt, (date) => date.toDate())
            : undefined,
        ]),
      );
    return result[0]?.total ?? 0;
  };

  getRangeUsers = async ({
    display,
    range,
  }: {
    display: DisplayType;
    range: [string, string];
  }): Promise<{ count: number; date: string }[]> => {
    const { groupByClause, formatAllDates } = genRange({
      display,
      groupKey: users.createdAt,
      range,
    });

    const result = await lobechatDB
      .select({
        count: count(),
        date: groupByClause,
      })
      .from(users)
      .where(genRangeWhere(range, users.createdAt, (date) => date.toDate()))
      .groupBy(groupByClause)
      .orderBy(groupByClause);

    return formatAllDates(result);
  };

  getUserInfoById = async (
    id: string,
  ): Promise<
    | {
        avatar: string | null;
        clerkCreatedAt: Date | null;
        createdAt: Date;
        email: string | null;
        firstName: string | null;
        id: string;
        isOnboarded: boolean | null;
        lastName: string | null;
        phone: string | null;
        username: string | null;
      }
    | undefined
  > => {
    return lobechatDB.query.users.findFirst({ where: eq(users.id, id) });
  };

  getUserApiKey = async (userId: string, token: string): Promise<string | undefined> => {
    const item = await cloudDB.query.userBudgets.findFirst({ where: eq(users.id, userId) });
    if (!item) return;

    const maps = [
      [item.freeBudgetId, item.freeBudgetKey],
      [item.subscriptionBudgetId, item.subscriptionBudgetKey],
      [item.packageBudgetId, item.packageBudgetKey],
    ];

    const budget = maps.find((item) => item[0] === token);
    if (!budget || !budget[1]) return;

    return await KeyVaultsGateKeeper.getUserBudgetKey(budget[1]);
  };

  getUserTypes = async (): Promise<{
    active: number;
    all: number;
    banned: number;
    bug: number;
    inactive: number;
    paid: number;
    risk: number;
  }> => {
    // 并发执行所有异步操作
    const [total, inactiveResult, activeTokenResult, riskUsers, paidUsers, bugUsers, bannedUsers] =
      await Promise.all([
        this.getTotalUsers(),
        this.getInactiveUsers(),
        this.getActiveUsers(),
        this.getRiskUsers(),
        this.getSubscriptionUsers(),
        this.getBugUsers(),
        this.clerkServices.countBannedUser(),
      ]);

    // 返回结果
    return {
      active: activeTokenResult.length,
      all: total,
      banned: bannedUsers,
      bug: bugUsers.length,
      inactive: inactiveResult.length,
      paid: paidUsers.length,
      risk: riskUsers.length,
    };
  };

  private getUserTypeIds = async (type?: EndUserType): Promise<string[] | undefined> => {
    switch (type) {
      case EndUserType.Active: {
        return this.getActiveUsers();
      }

      case EndUserType.Bug: {
        return this.getBugUsers();
      }
      case EndUserType.Inactive: {
        return this.getInactiveUsers();
      }
      case EndUserType.Paid: {
        return this.getSubscriptionUsers();
      }
      case EndUserType.Risk: {
        return this.getRiskUsers();
      }
      case EndUserType.Banned: {
        return this.getBannedUsers();
      }
      default:
      case EndUserType.All: {
        return [];
      }
    }
  };

  getUserList = async (
    params: UserQuery = {},
    sorts: {
      createdAt?: SortEnum;
    } = {},
  ): Promise<{
    data: EndUser[];
    total: number;
  }> => {
    const { range, userType, plan, emailOrUsernameOrUserId, pageSize = 20, current = 1 } = params;
    const { offset, limit } = genTable({ current, pageSize });

    const userTypeUserIds = await this.getUserTypeIds(userType);

    const result = await lobechatDB
      .select({
        avatar: users.avatar,
        createdAt: users.createdAt,
        email: users.email,
        firstName: users.firstName,
        id: users.id,
        isOnboarded: users.isOnboarded,
        lastName: users.lastName,
        username: users.username,
      })
      .from(users)
      .orderBy(...genOrder([[sorts.createdAt, users.createdAt]]))
      .where(
        genWhere([
          genRangeWhere(range, users.createdAt, (date) => date.toDate()),
          emailOrUsernameOrUserId ? genFuzzySearchUserWhere(emailOrUsernameOrUserId) : undefined,
          userTypeUserIds && userTypeUserIds?.length > 0
            ? inArray(users.id, userTypeUserIds)
            : undefined,
          plan && plan !== 'free'
            ? inArray(users.id, await this.getSubscriptionUsers(range, plan))
            : undefined,
          plan && plan === 'free' ? inArray(users.id, await this.getFreeUsers(range)) : undefined,
        ]),
      )
      .limit(limit)
      .offset(offset);

    if (result.length === 0) return { data: [], total: 0 };

    const userIds = result.map((item) => item.id);

    return {
      data: this.mapUserData({
        budgets: await cloudDB.query.userBudgets.findMany({
          where: inArray(userBudgets.id, userIds),
        }),
        messages: await lobechatDB
          .select({ count: count(messages.id), id: users.id })
          .from(users)
          .leftJoin(messages, eq(messages.userId, users.id))
          .where(inArray(users.id, userIds))
          .groupBy(users.id),
        spends: await litellmDB
          .select({
            expires: LiteLLM_VerificationToken.expires,
            id: LiteLLM_VerificationToken.token,
            limit: LiteLLM_VerificationToken.max_budget,
            spend: LiteLLM_VerificationToken.spend,
            userId: LiteLLM_VerificationToken.user_id,
          })
          .from(LiteLLM_VerificationToken)
          .where(inArray(LiteLLM_VerificationToken.user_id, userIds)),
        subscriptions: await cloudDB.query.userSubscriptions.findMany({
          orderBy: [desc(userSubscriptions.createdAt)],
          where: genWhere([
            inArray(userSubscriptions.userId, userIds),
            eq(userSubscriptions.status, 0),
            plan ? eq(userSubscriptions.plan, plan) : undefined,
          ]),
        }),
        users: result.map((item) => {
          return {
            ...item,
            username: genUsername(item),
          };
        }),
      }),
      total: userTypeUserIds?.length || (await this.getTotalUsers({ range })),
    };
  };

  private mapUserData({
    users,
    budgets,
    messages,
    spends,
    subscriptions,
  }: {
    budgets: UserBudgetItem[];
    messages: { count: number | null; id: string }[];
    spends: TokenSelectType[];
    subscriptions: UserSubscriptionItem[];
    users: UserSelectType[];
  }): EndUser[] {
    return users.map<EndUser>((item) => {
      const userBudget = budgets.find((budget) => budget.id === item.id);
      const userSubscription = subscriptions.find(
        (subscription) => subscription.userId === item.id && subscription.status !== 1,
      );

      const freeItem = spends.find((token) => token.id === userBudget?.freeBudgetId);

      const subscriptionItem = spends.find(
        (token) => token.id === userBudget?.subscriptionBudgetId,
      );

      const message = messages.find((message) => message.id === item.id);

      const hasRisk = message?.count === 0 && freeItem && freeItem?.spend > 0;

      return {
        ...item,
        credits: {
          freeExpire: freeItem?.expires,
          freePercent: getUsagePercent(freeItem),
          freeUsed: freeItem?.spend,
          subscriptionExpire: subscriptionItem?.expires,
          subscriptionPercent: getUsagePercent(subscriptionItem),
          subscriptionUsed: subscriptionItem?.spend,
        },
        freeCredit: getUsagePercent(freeItem),
        hasRisk,
        messageCount: message?.count,
        // 使用拼接后的姓名
        name: item.username,
        plan: (userSubscription?.plan as Plans) || Plans.Free,
        subscriptionCredit: getUsagePercent(subscriptionItem),
      };
    });
  }

  getActiveVsInactiveUsers = async (params: {
    range: [string, string];
  }): Promise<{
    active: number;
    inactive: number;
    normal: number;
    total: number;
  }> => {
    const { range } = params;
    const allUsers = await this.getAllUsers(range);
    const activeUsers = await this.getActiveUsers(range);
    const inactiveUsers = await this.getInactiveUsers(range);

    const otherUsers = allUsers.filter(
      (id) => !activeUsers.includes(id) && !inactiveUsers.includes(id),
    );

    return {
      active: activeUsers.length,
      inactive: inactiveUsers.length,
      normal: otherUsers.length,
      total: allUsers.length,
    };
  };

  getFreeVsSubscriptionUsers = async (params: {
    range: [string, string];
    type: 'total' | 'effective' | 'active';
  }): Promise<{
    free: number;
    subscription: number;
    total: number;
  }> => {
    const { type = 'total', range } = params;

    const allUsers = await this.getAllUsers(range);
    const freeUsers = await this.getFreeUsers(range);
    const subscriptionUser = await this.getSubscriptionUsers(range);

    switch (type) {
      case 'total': {
        return {
          free: freeUsers.length,
          subscription: subscriptionUser.length,
          total: allUsers.length,
        };
      }
      case 'effective': {
        const effectiveUsers = await this.getEffectiveUsers(range);
        const effectiveUsersSet = new Set(effectiveUsers);
        const effectiveSubscriptionUser = subscriptionUser.filter((id) =>
          effectiveUsersSet.has(id),
        );
        return {
          free: effectiveUsers.length - effectiveSubscriptionUser.length,
          subscription: effectiveSubscriptionUser.length,
          total: effectiveUsers.length,
        };
      }
      case 'active': {
        const acitveUsers = await this.getActiveUsers(range);
        const acitveUsersSet = new Set(acitveUsers);
        const activeSubscriptionUser = subscriptionUser.filter((id) => acitveUsersSet.has(id));
        return {
          free: acitveUsers.length - activeSubscriptionUser.length,
          subscription: activeSubscriptionUser.length,
          total: acitveUsers.length,
        };
      }
    }
  };

  getAllUsers = async (range?: [string, string]): Promise<string[]> => {
    const result = await lobechatDB
      .select({ id: users.id })
      .from(users)
      .where(
        genWhere([
          range ? genRangeWhere(range, users.createdAt, (date) => date.toDate()) : undefined,
        ]),
      );
    return result.map((item) => item.id as string) || [];
  };

  getSubscriptionUsers = async (range?: [string, string], plan?: Plans): Promise<string[]> => {
    const result = await cloudDB
      .select({ id: userSubscriptions.userId })
      .from(userSubscriptions)
      .where(
        genWhere([
          ne(userSubscriptions.status, 1),
          plan ? eq(userSubscriptions.plan, plan) : undefined,
          range ? inArray(userSubscriptions.userId, await this.getAllUsers(range)) : undefined,
        ]),
      );
    return result.map((item) => item.id as string) || [];
  };

  getFreeUsers = async (range?: [string, string]): Promise<string[]> => {
    const allUsers = await this.getAllUsers(range);
    const subscriptionUsers = await this.getSubscriptionUsers(range);
    const subscriptionUsersSet = new Set(subscriptionUsers);
    return allUsers.filter((userId) => !subscriptionUsersSet.has(userId)) || [];
  };

  getEffectiveUsers = async (range?: [string, string]): Promise<string[]> => {
    const allUsers = await this.getAllUsers(range);
    const inactiveUsers = await this.getInactiveUsers(range);
    const inactiveUsersSet = new Set(inactiveUsers);
    return allUsers.filter((userId) => !inactiveUsersSet.has(userId)) || [];
  };

  getActiveUsers = async (range?: [string, string]): Promise<string[]> => {
    const activeTokenResult = await lobechatDB
      .select({ id: users.id })
      .from(users)
      .leftJoin(messages, eq(messages.userId, users.id))
      .groupBy(users.id)
      .where(
        genWhere([
          range ? genRangeWhere(range, users.createdAt, (date) => date.toDate()) : undefined,
        ]),
      )
      .having(gte(count(messages.id), range ? genRangeDiffDay(range) : 100));

    return activeTokenResult.map((item) => item.id as string) || [];
  };

  getInactiveUsers = async (range?: [string, string]): Promise<string[]> => {
    // 构建基本查询条件
    const conditions = [];

    if (range) {
      conditions.push(genRangeWhere(range, users.createdAt, (date) => date.toDate()));
    }

    // 使用 NOT EXISTS 子查询，这通常比 LEFT JOIN + HAVING 更高效
    const inactiveUserResult = await lobechatDB
      .select({ id: users.id })
      .from(users)
      .where(
        and(
          ...conditions,
          notExists(
            lobechatDB
              .select({ id: messages.id })
              .from(messages)
              .where(eq(messages.userId, users.id))
              .limit(1),
          ),
        ),
      );

    return inactiveUserResult.map((item) => item.id as string) || [];
  };

  getBugUsers = async (range?: [string, string]): Promise<string[]> => {
    const subscriptionUsersResult = await cloudDB
      .select({ id: userSubscriptions.userId })
      .from(userSubscriptions)
      .where(
        genWhere([
          ne(userSubscriptions.status, 1),
          ne(userSubscriptions.plan, Plans.Hobby),
          range ? inArray(userSubscriptions.userId, await this.getAllUsers(range)) : undefined,
        ]),
      );

    const subscriptionUsers = subscriptionUsersResult.map((item) => item.id as string) || [];

    const results = await litellmDB
      .select({ id: LiteLLM_VerificationToken.user_id })
      .from(LiteLLM_VerificationToken)
      .groupBy(LiteLLM_VerificationToken.user_id)
      .where(
        and(
          inArray(LiteLLM_VerificationToken.user_id, subscriptionUsers),
          gt(LiteLLM_VerificationToken.expires, dayjs().toISOString()),
        ),
      );

    const skipUsers = results.map((item) => item.id as string) || [];

    return subscriptionUsers.filter((item) => !skipUsers.includes(item)) || [];
  };

  getRiskUsers = async (range?: [string, string]): Promise<string[]> => {
    const nonMessagesUserIds = await this.getInactiveUsers(range);

    const results = await litellmDB
      .select({ id: LiteLLM_VerificationToken.user_id })
      .from(LiteLLM_VerificationToken)
      .groupBy(LiteLLM_VerificationToken.user_id)
      .where(genWhere([inArray(LiteLLM_VerificationToken.user_id, nonMessagesUserIds)]))
      .having(gt(sum(LiteLLM_VerificationToken.spend), 0));

    return results.map((item) => item.id as string) || [];
  };

  getBannedUsers = async (): Promise<string[]> => {
    const result = await this.clerkServices.getBannedUserList({ pageSize: 500 });
    return result.data.map((item) => item.id);
  };

  deleteUserData = async (userId: string): Promise<boolean> => {
    try {
      await lobechatDB.delete(users).where(eq(users.id, userId));
      return true;
    } catch (error) {
      console.error('Failed to delete user data:', error);
      return false;
    }
  };
}
