import { count, eq } from 'drizzle-orm';

import { lobechatDB } from '@/database/instance/lobechatDB';
import { agents } from '@/database/instance/lobechatDB/schemas';
import { genOrder } from '@/database/utils/genOrder';
import { genTable } from '@/database/utils/genTable';
import { genEndDateWhere, genWhere } from '@/database/utils/genWhere';
import { SortQuery } from '@/types/query';

interface AgentListItem {
  avatar: string | null;
  createdAt: Date;
  description: string | null;
  id: string;
  title: string | null;
  updatedAt: Date;
  userId: string;
}

export class AgentModel {
  countTotalAgents = async (props?: { endDate?: string; userId?: string }): Promise<number> => {
    const result = await lobechatDB
      .select({
        total: count(agents.id),
      })
      .from(agents)
      .where(
        genWhere([
          props?.userId ? eq(agents.userId, props.userId) : undefined,
          props?.endDate
            ? genEndDateWhere(props.endDate, agents.createdAt, (date) => date.toDate())
            : undefined,
        ]),
      );

    return result[0]?.total ?? 0;
  };

  getUserAgentsList = async (
    params: {
      current?: number;
      pageSize?: number;
      userId: string;
    },
    sorts: SortQuery = {},
  ): Promise<{
    data: AgentListItem[];
    total: number;
  }> => {
    const { userId, pageSize = 20, current = 1 } = params;
    const { offset, limit } = genTable({ current, pageSize });

    const result = await lobechatDB
      .select({
        avatar: agents.avatar,
        createdAt: agents.createdAt,
        description: agents.description,
        id: agents.id,
        title: agents.title,
        updatedAt: agents.updatedAt,
        userId: agents.userId,
      })
      .from(agents)
      .where(eq(agents.userId, userId))
      .orderBy(
        ...genOrder([
          [sorts?.createdAt, agents.createdAt],
          [sorts?.updatedAt, agents.updatedAt],
        ]),
      )
      .limit(limit)
      .offset(offset);

    const totalResult = await lobechatDB
      .select({ total: count(agents.id) })
      .from(agents)
      .where(eq(agents.userId, userId));

    return {
      data: result,
      total: totalResult[0]?.total ?? 0,
    };
  };
}
