import { count, inArray, sql } from 'drizzle-orm';

import { lobechatDB } from '@/database/instance/lobechatDB';
import { agents, messages, topics } from '@/database/instance/lobechatDB/schemas';
import { genRangeDiffDay } from '@/database/utils/genRange';
import { genRangeWhere, genWhere } from '@/database/utils/genWhere';
import {
  calculateAverage,
  calculateMax,
  calculateMedian,
  calculatePercentile,
} from '@/utils/calculate';

export interface DistributionResult {
  average: number;
  max: number;
  median: number;
  per80: number;
  per90: number;
}

export class DistributionModel {
  getAgentDistribution = async (params: {
    range: [string, string];
    userIds?: string[];
  }): Promise<DistributionResult> => {
    const result = await lobechatDB
      .select({
        count: count(agents.id),
      })
      .from(agents)
      .where(
        genWhere([
          genRangeWhere(params.range, agents.createdAt, (date) => date.toDate()),
          params?.userIds ? inArray(agents.userId, params.userIds) : undefined,
        ]),
      )
      .groupBy(agents.userId);

    const counts = result.map((item) => Number(item.count));

    return this.buildDistribution(counts);
  };

  getTopicDistribution = async (params: {
    range: [string, string];
    userIds?: string[];
  }): Promise<DistributionResult> => {
    const result = await lobechatDB
      .select({
        count: count(topics.id),
      })
      .from(topics)
      .where(
        genWhere([
          genRangeWhere(params.range, topics.createdAt, (date) => date.toDate()),
          params?.userIds ? inArray(topics.userId, params.userIds) : undefined,
        ]),
      )
      .groupBy(topics.userId);

    const counts = result.map((item) => Number(item.count));

    return this.buildDistribution(counts);
  };

  getWeeklyActiveDistribution = async (params: {
    range: [string, string];
    userIds?: string[];
  }): Promise<DistributionResult> => {
    const result = await lobechatDB
      .select({
        count: sql<number>`COUNT(DISTINCT DATE(${messages.createdAt}))`,
      })
      .from(messages)
      .where(
        genWhere([
          genRangeWhere(params.range, messages.createdAt, (date) => date.toDate()),
          params?.userIds ? inArray(messages.userId, params.userIds) : undefined,
        ]),
      )
      .groupBy(messages.userId);

    const diffDate = genRangeDiffDay(params.range);
    const counts = result.map((item) => (item.count / diffDate) * 7);

    return this.buildDistribution(counts);
  };

  getMessageDistribution = async (params: {
    range: [string, string];
    userIds?: string[];
  }): Promise<DistributionResult> => {
    const result = await lobechatDB
      .select({
        count: count(messages.id),
      })
      .from(messages)
      .where(
        genWhere([
          genRangeWhere(params.range, messages.createdAt, (date) => date.toDate()),
          params?.userIds ? inArray(messages.userId, params.userIds) : undefined,
        ]),
      )
      .groupBy(messages.userId);

    const counts = result.map((item) => Number(item.count));

    return this.buildDistribution(counts);
  };

  private buildDistribution = (counts: number[]): DistributionResult => {
    return {
      average: calculateAverage(counts),
      max: calculateMax(counts),
      median: calculateMedian(counts),
      per80: calculatePercentile(counts, 80),
      per90: calculatePercentile(counts, 90),
    };
  };
}
