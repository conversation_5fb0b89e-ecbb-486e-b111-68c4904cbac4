import { count, eq, inArray, like, sum } from 'drizzle-orm';

import { lobechatDB } from '@/database/instance/lobechatDB';
import {
  chunks,
  files,
  knowledgeBaseFiles,
  knowledgeBases,
} from '@/database/instance/lobechatDB/schemas';
import { genOrder } from '@/database/utils/genOrder';
import { genTable } from '@/database/utils/genTable';
import { genEndDateWhere, genRangeWhere, genWhere } from '@/database/utils/genWhere';
import { FileItem, FileQuery } from '@/types/file';
import { SortEnum } from '@/types/query';

export class FileModel {
  countTotalFileSize = async (props?: { endDate?: string; userId?: string }): Promise<number> => {
    const result = await lobechatDB
      .select({
        total: sum(files.size),
      })
      .from(files)
      .where(
        genWhere([
          props?.userId ? eq(files.userId, props.userId) : undefined,
          props?.endDate
            ? genEndDateWhere(props.endDate, files.createdAt, (date) => date.toDate())
            : undefined,
        ]),
      );

    return Number(result[0]?.total) ?? 0;
  };

  countTotalFiles = async (props?: { endDate?: string; userId?: string }): Promise<number> => {
    const result = await lobechatDB
      .select({
        total: count(files.id),
      })
      .from(files)
      .where(
        genWhere([
          props?.userId ? eq(files.userId, props.userId) : undefined,
          props?.endDate
            ? genEndDateWhere(props.endDate, files.createdAt, (date) => date.toDate())
            : undefined,
        ]),
      );

    return Number(result[0]?.total) ?? 0;
  };

  getFilesList = async (
    params: FileQuery = {},
    sorts: {
      createdAt?: SortEnum;
      size?: SortEnum;
      updatedAt?: SortEnum;
    } = {},
  ): Promise<{
    data: FileItem[];
    total: number;
  }> => {
    const { userId, current = 1, pageSize = 20, fileType, name, range } = params;
    const { limit, offset } = genTable({ current, pageSize });

    // 首先获取基本文件信息
    const result = await lobechatDB
      .select({
        accessedAt: files.accessedAt,
        clientId: files.clientId,
        createdAt: files.createdAt,
        fileHash: files.fileHash,
        fileType: files.fileType,
        id: files.id,
        name: files.name,
        size: files.size,
        updatedAt: files.updatedAt,
        url: files.url,
        userId: files.userId,
      })
      .from(files)
      .where(
        genWhere([
          userId ? eq(files.userId, userId) : undefined,
          fileType ? eq(files.fileType, fileType) : undefined,
          name ? like(files.name, `%${name}%`) : undefined,
          range ? genRangeWhere(range, files.createdAt, (date) => date.toDate()) : undefined,
        ]),
      )
      .orderBy(
        ...genOrder([
          [sorts?.createdAt, files.createdAt],
          [sorts?.updatedAt, files.updatedAt],
          [sorts?.size, files.size],
        ]),
      )
      .limit(limit)
      .offset(offset);

    if (result.length === 0) {
      return {
        data: [],
        total: await this.countTotalFiles({ userId }),
      };
    }

    const fileIds = result.map((item) => item.id);

    // 查询文件关联的知识库信息
    const knowledgeBaseInfo = await lobechatDB
      .select({
        fileId: knowledgeBaseFiles.fileId,
        knowledgeBaseId: knowledgeBases.id,
        knowledgeBaseName: knowledgeBases.name,
      })
      .from(knowledgeBaseFiles)
      .leftJoin(knowledgeBases, eq(knowledgeBaseFiles.knowledgeBaseId, knowledgeBases.id))
      .where(inArray(knowledgeBaseFiles.fileId, fileIds));

    // 查询文件的分块数量（通过 clientId 关联）
    const chunkCounts = await this.getFileChunkCounts(result);

    // 组装最终结果
    const data: FileItem[] = result.map((file) => {
      // 获取该文件关联的知识库
      const fileKnowledgeBases = knowledgeBaseInfo
        .filter((kb) => kb.fileId === file.id)
        .map((kb) => ({
          id: kb.knowledgeBaseId!,
          name: kb.knowledgeBaseName!,
        }))
        .filter((kb) => kb.id && kb.name);

      return {
        ...file,
        chunkCount: chunkCounts[file.id] || 0,
        knowledgeBases: fileKnowledgeBases,
      };
    });

    return {
      data,
      total: await this.countTotalFiles({ userId }),
    };
  };

  // 获取文件的分块数量（通过 clientId 关联）
  private async getFileChunkCounts(fileList: any[]): Promise<Record<string, number>> {
    const chunkCounts: Record<string, number> = {};

    // 获取有 clientId 的文件
    const filesWithClientId = fileList.filter((file) => file.clientId);

    if (filesWithClientId.length === 0) {
      // 如果没有文件有 clientId，返回空的统计
      fileList.forEach((file) => {
        chunkCounts[file.id] = 0;
      });
      return chunkCounts;
    }

    const clientIds = filesWithClientId.map((file) => file.clientId);

    // 通过 clientId 查询分块数量
    const chunkResult = await lobechatDB
      .select({
        clientId: chunks.clientId,
        count: count(chunks.id),
      })
      .from(chunks)
      .where(inArray(chunks.clientId, clientIds))
      .groupBy(chunks.clientId);

    // 创建 clientId 到分块数量的映射
    const clientIdToChunkCount: Record<string, number> = {};
    chunkResult.forEach((result) => {
      if (result.clientId) {
        clientIdToChunkCount[result.clientId] = result.count;
      }
    });

    // 将结果映射到文件ID
    fileList.forEach((file) => {
      if (file.clientId && clientIdToChunkCount[file.clientId]) {
        chunkCounts[file.id] = clientIdToChunkCount[file.clientId];
      } else {
        chunkCounts[file.id] = 0;
      }
    });

    return chunkCounts;
  }
}
