import { count, eq } from 'drizzle-orm';

import { lobechatDB } from '@/database/instance/lobechatDB';
import { embeddings } from '@/database/instance/lobechatDB/schemas';
import { genWhere } from '@/database/utils/genWhere';

export class EmbeddingModel {
  countTotalEmbeddings = async (props?: { userId?: string }): Promise<number> => {
    const result = await lobechatDB
      .select({ total: count(embeddings.id) })
      .from(embeddings)
      .where(genWhere([props?.userId ? eq(embeddings.userId, props.userId) : undefined]));

    return result[0]?.total ?? 0;
  };
}
