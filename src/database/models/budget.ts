import dayjs from 'dayjs';
import { count, desc, eq, gt, inArray, like, or } from 'drizzle-orm';
import { uniq } from 'lodash';

import { CREDIT_UNIT } from '@/const/subscription';
import { fuzzySearchUserIdList } from '@/database/helper/fuzzySearch';
import { cloudDB } from '@/database/instance/cloudDB';
import { userBudgets } from '@/database/instance/cloudDB/schema';
import { litellmDB } from '@/database/instance/litellmDB';
import { LiteLLM_VerificationToken } from '@/database/instance/litellmDB/schema';
import { lobechatDB } from '@/database/instance/lobechatDB';
import { users } from '@/database/instance/lobechatDB/schemas';
import { genOrder } from '@/database/utils/genOrder';
import { genTable } from '@/database/utils/genTable';
import { genRangeWhere, genWhere } from '@/database/utils/genWhere';
import { SortEnum } from '@/types/query';
import { BudgetItem, BudgetQuery, CreditUsage } from '@/types/subscription';

export class BudgetModel {
  getBudgeUsageById = async (userId?: string | null): Promise<CreditUsage | undefined> => {
    if (!userId) return undefined;

    const [item] = await litellmDB
      .select({
        expiredAt: LiteLLM_VerificationToken.expires,
        limit: LiteLLM_VerificationToken.max_budget,
        resetAt: LiteLLM_VerificationToken.budget_reset_at,
        spend: LiteLLM_VerificationToken.spend,
      })
      .from(LiteLLM_VerificationToken)
      .where(eq(LiteLLM_VerificationToken.token, userId));

    if (!item) return undefined;

    const date = dayjs.utc(item.resetAt);
    const expiredAt = dayjs.utc(item.expiredAt);

    return {
      expiredAt: item.expiredAt ? expiredAt.toDate() : null,
      limit: (item.limit || 0) * CREDIT_UNIT,
      resetAt: item.resetAt ? date.toDate() : null,
      spend: (item.spend || 0) * CREDIT_UNIT,
    };
  };

  getBudgets = async (
    params: BudgetQuery = {},
    sorts: {
      budgetResetAt?: SortEnum;
      createdAt?: SortEnum;
      expires?: SortEnum;
    } = {},
  ): Promise<{
    data: BudgetItem[];
    total: number;
  }> => {
    const { current = 1, pageSize = 20, range, id, key, token, emailOrUsernameOrUserId } = params;
    const { offset, limit } = genTable({ current, pageSize });
    const userIdList: string[] = await fuzzySearchUserIdList(emailOrUsernameOrUserId, range);

    const where = genWhere([
      gt(LiteLLM_VerificationToken.max_budget, 0),
      genRangeWhere(range, LiteLLM_VerificationToken.created_at, (date) => date.toISOString()),
      userIdList && userIdList.length > 0
        ? or(...userIdList.map((i) => like(LiteLLM_VerificationToken.key_alias, `%${i}%`)))
        : undefined,
      token ? eq(LiteLLM_VerificationToken.token, token) : undefined,
      id ? eq(LiteLLM_VerificationToken.budget_id, id) : undefined,
      key ? eq(LiteLLM_VerificationToken.key_alias, key) : undefined,
    ]);

    const result = await litellmDB
      .select({
        budgetDuration: LiteLLM_VerificationToken.budget_duration,
        budgetResetAt: LiteLLM_VerificationToken.budget_reset_at,
        createdAt: LiteLLM_VerificationToken.created_at,
        expires: LiteLLM_VerificationToken.expires,
        id: LiteLLM_VerificationToken.budget_id,
        key: LiteLLM_VerificationToken.key_alias,
        maxBudget: LiteLLM_VerificationToken.max_budget,
        metadata: LiteLLM_VerificationToken.metadata,
        models: LiteLLM_VerificationToken.models,
        rpmLimit: LiteLLM_VerificationToken.rpm_limit,
        spend: LiteLLM_VerificationToken.spend,
        token: LiteLLM_VerificationToken.token,
        tpmLimit: LiteLLM_VerificationToken.tpm_limit,
        updatedAt: LiteLLM_VerificationToken.updated_at,
        userId: LiteLLM_VerificationToken.user_id,
      })
      .from(LiteLLM_VerificationToken)
      .where(where)
      .groupBy(LiteLLM_VerificationToken.token)
      .orderBy(
        ...genOrder([
          [sorts?.createdAt, LiteLLM_VerificationToken.created_at],
          [sorts?.budgetResetAt, LiteLLM_VerificationToken.budget_reset_at],
          [sorts?.expires, LiteLLM_VerificationToken.expires],
        ]),
      )
      .limit(limit)
      .offset(offset);

    const userIds = uniq(result.map((item) => item.userId).filter(Boolean)) as string[];

    const userList = await lobechatDB
      .select({
        avatar: users.avatar,
        email: users.email,
        firstName: users.firstName,
        id: users.id,
        lastName: users.lastName,
        username: users.username,
      })
      .from(users)
      .where(inArray(users.id, userIds));

    // 获取总记录数
    const totalCount = await litellmDB
      .select({ count: count(LiteLLM_VerificationToken.token) })
      .from(LiteLLM_VerificationToken)
      .where(where);

    return {
      data: this.formatBudgetResult(result, userList),
      total: totalCount[0].count,
    };
  };

  private formatBudgetResult = (result: any[], userList: any[]): BudgetItem[] => {
    return result.map((item) => {
      const user = userList.find((u) => item.key.includes(u.id));
      return {
        ...item,
        budgetResetAt: item.budgetResetAt ? dayjs.utc(item.budgetResetAt).toDate() : null,
        createdAt: item.createdAt ? dayjs.utc(item.createdAt).toDate() : null,
        expires: item.expires ? dayjs.utc(item.expires).toDate() : null,
        user: {
          avatar: user?.avatar,
          email: user?.email,
          id: user?.id,
          name:
            user?.firstName && user?.lastName
              ? `${user?.firstName} ${user?.lastName}`
              : user?.username,
        },
      };
    });
  };

  getUserBudgetById = async (
    id: string,
  ): Promise<
    | {
        freeBudgetId: string | null;
        id: string;
        subscriptionBudgetId: string | null;
      }
    | undefined
  > => {
    return cloudDB.query.userBudgets.findFirst({ where: eq(userBudgets.id, id) });
  };

  getUserBudgetDetailById = async (id: string) => {
    const data = await litellmDB
      .select({
        blocked: LiteLLM_VerificationToken.blocked,
        budgetDuration: LiteLLM_VerificationToken.budget_duration,
        budgetResetAt: LiteLLM_VerificationToken.budget_reset_at,
        createdAt: LiteLLM_VerificationToken.created_at,
        expires: LiteLLM_VerificationToken.expires,
        id: LiteLLM_VerificationToken.budget_id,
        key: LiteLLM_VerificationToken.key_alias,
        maxBudget: LiteLLM_VerificationToken.max_budget,
        metadata: LiteLLM_VerificationToken.metadata,
        models: LiteLLM_VerificationToken.models,
        rpmLimit: LiteLLM_VerificationToken.rpm_limit,
        spend: LiteLLM_VerificationToken.spend,
        token: LiteLLM_VerificationToken.token,
        tpmLimit: LiteLLM_VerificationToken.tpm_limit,
        updatedAt: LiteLLM_VerificationToken.updated_at,
      })
      .from(LiteLLM_VerificationToken)
      .where(eq(LiteLLM_VerificationToken.user_id, id))
      .groupBy(LiteLLM_VerificationToken.token)
      .orderBy(desc(LiteLLM_VerificationToken.budget_reset_at))
      .having(gt(LiteLLM_VerificationToken.max_budget, 0));

    return data.map((item) => ({
      ...item,
      budgetResetAt: !!item.budgetResetAt ? dayjs.utc(item.budgetResetAt).toDate() : undefined,
      createdAt: !!item.createdAt ? dayjs.utc(item.createdAt).toDate() : undefined,
      expires: !!item.expires ? dayjs.utc(item.expires).toDate() : undefined,
      updatedAt: !!item.updatedAt ? dayjs.utc(item.updatedAt).toDate() : undefined,
    }));
  };

  async updateBudgetKey(token: string, param: { budgetResetAt?: string; expires?: string }) {
    return litellmDB
      .update(LiteLLM_VerificationToken)
      .set({
        budget_reset_at: param.budgetResetAt,
        expires: param.expires,
      })
      .where(eq(LiteLLM_VerificationToken.token, token));
  }
}
