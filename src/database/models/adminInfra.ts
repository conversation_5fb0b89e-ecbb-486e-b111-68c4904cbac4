import { and, count, eq } from 'drizzle-orm';

import { adminDB } from '@/database/instance/adminDB';
import {
  AdminModelSelectItem,
  NewAdminProviderItem,
  adminModels,
  adminProviders,
  aggregatedModelLitellmModel,
} from '@/database/instance/adminDB/schemas';
import { KeyVaultsGateKeeper } from '@/server/modules/KeyVaultsEncrypt';
import {
  AdminProviderSelectItem,
  CreateAiModelData,
  CreateAiProviderData,
  UpdateAiModelData,
  UpdateAiProviderData,
} from '@/types/adminInfra';

export class AdminInfraModel {
  // ==================== Provider 相关操作 ====================

  /**
   * 从 Admin 数据库中获取所有的 provider 列表
   * @param gateKeeper 加密管理器
   * @returns provider 列表
   */
  getAllInfraProviders = async (
    gateKeeper: KeyVaultsGateKeeper,
  ): Promise<AdminProviderSelectItem[]> => {
    const result = await adminDB.query.adminProviders.findMany({
      orderBy: [adminProviders.sort, adminProviders.id],
    });

    // 为每个 provider 解密 keyVaults 数据并获取模型数量
    const processedProviders: AdminProviderSelectItem[] = await Promise.all(
      result.map(async (provider) => {
        let decryptedKeyVaults = {};
        let processedSettings = provider.settings || {};

        if (provider.keyVaults) {
          try {
            // 解密 keyVaults 数据
            const decryptResult = await gateKeeper.decrypt(provider.keyVaults);
            if (decryptResult.wasAuthentic) {
              decryptedKeyVaults = JSON.parse(decryptResult.plaintext);
            } else {
              // 解密失败，使用空对象
              console.warn(`解密 keyVaults 失败，使用空对象: ${provider.id}`);
              decryptedKeyVaults = {};
            }
          } catch (error) {
            // 解密失败，使用空对象
            console.warn(
              `解密 keyVaults 失败: ${error instanceof Error ? error.message : '未知错误'}`,
            );
            decryptedKeyVaults = {};
          }
        }

        // 获取该 provider 下的模型数量
        const modelCountResult = await adminDB
          .select({ count: count() })
          .from(adminModels)
          .where(eq(adminModels.providerId, provider.id));

        const modelCount = modelCountResult[0]?.count || 0;

        return {
          ...provider,
          keyVaults: decryptedKeyVaults,
          modelCount,
          settings: processedSettings,
        } as AdminProviderSelectItem;
      }),
    );

    return processedProviders;
  };

  /**
   * 根据 Provider ID 查询 provider 详情
   * @param providerId Provider ID
   * @param gateKeeper 加密管理器
   * @returns provider 详情或 null
   */
  getInfraProviderById = async (
    providerId: string,
    gateKeeper: KeyVaultsGateKeeper,
  ): Promise<AdminProviderSelectItem | null> => {
    const provider = await adminDB.query.adminProviders.findFirst({
      where: eq(adminProviders.id, providerId),
    });

    if (!provider) {
      return null;
    }

    // 如果有 keyVaults 数据，需要解密
    let decryptedKeyVaults = {};
    let processedSettings = provider.settings || {};

    if (provider.keyVaults) {
      try {
        // 解密 keyVaults 数据
        const decryptResult = await gateKeeper.decrypt(provider.keyVaults);
        if (decryptResult.wasAuthentic) {
          decryptedKeyVaults = JSON.parse(decryptResult.plaintext);
        } else {
          // 解密失败，使用空对象
          console.warn(`解密 keyVaults 失败，使用空对象: ${providerId}`);
          decryptedKeyVaults = {};
        }
      } catch (error) {
        // 解密失败，使用空对象
        console.warn(`解密 keyVaults 失败: ${error instanceof Error ? error.message : '未知错误'}`);
        decryptedKeyVaults = {};
      }
    }

    return {
      ...provider,
      keyVaults: decryptedKeyVaults,
      settings: processedSettings,
    } as AdminProviderSelectItem;
  };

  /**
   * 创建渠道信息
   * @param data 创建数据
   * @param gateKeeper 加密管理器
   * @returns 创建的 provider 信息
   */
  createInfraProvider = async (
    data: CreateAiProviderData,
    gateKeeper: KeyVaultsGateKeeper,
  ): Promise<AdminProviderSelectItem> => {
    const { keyVaults, ...rest } = data;

    // 如果有 keyVaults 数据，需要加密
    let processedData: NewAdminProviderItem = {
      ...rest,
      keyVaults: '',
    };

    if (keyVaults) {
      try {
        // 加密 keyVaults 数据
        const encryptedKeyVaults = await gateKeeper.encrypt(keyVaults);
        processedData.keyVaults = encryptedKeyVaults;
      } catch (error) {
        throw new Error(
          `加密 keyVaults 失败: ${error instanceof Error ? error.message : '未知错误'}`,
        );
      }
    }

    const result = await adminDB.insert(adminProviders).values(processedData).returning();
    const createdProvider = result[0];

    // 返回解密后的 keyVaults 数据
    let decryptedKeyVaults = {};
    if (createdProvider.keyVaults) {
      try {
        const decryptResult = await gateKeeper.decrypt(createdProvider.keyVaults);
        if (decryptResult.wasAuthentic) {
          decryptedKeyVaults = JSON.parse(decryptResult.plaintext);
        }
      } catch (error) {
        console.warn(`解密 keyVaults 失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    }

    return {
      ...createdProvider,
      keyVaults: decryptedKeyVaults,
      settings: createdProvider.settings || {},
    } as AdminProviderSelectItem;
  };

  /**
   * 删除渠道信息
   * @param providerId Provider ID
   * @returns 是否删除成功
   * @description 由于数据库设置了级联删除，删除渠道时会自动删除关联的模型
   */
  deleteInfraProvider = async (providerId: string): Promise<boolean> => {
    // 由于设置了外键约束和级联删除，删除渠道时会自动删除关联的模型
    const result = await adminDB
      .delete(adminProviders)
      .where(eq(adminProviders.id, providerId))
      .returning();

    return result.length > 0;
  };

  /**
   * 更新渠道信息
   * @param providerId Provider ID
   * @param data 更新数据
   * @param gateKeeper 加密管理器
   * @returns 更新后的 provider 信息或 null
   */
  updateInfraProvider = async (
    providerId: string,
    data: UpdateAiProviderData,
    gateKeeper: KeyVaultsGateKeeper,
  ): Promise<AdminProviderSelectItem | null> => {
    const { keyVaults, ...rest } = data;

    let processedData: Partial<NewAdminProviderItem> = rest;

    if (keyVaults) {
      try {
        // 加密 keyVaults 数据
        const encryptedKeyVaults = await gateKeeper.encrypt(keyVaults);
        processedData.keyVaults = encryptedKeyVaults;
      } catch (error) {
        throw new Error(
          `加密 keyVaults 失败: ${error instanceof Error ? error.message : '未知错误'}`,
        );
      }
    }

    const result = await adminDB
      .update(adminProviders)
      .set(processedData)
      .where(eq(adminProviders.id, providerId))
      .returning();

    if (result.length === 0) {
      return null;
    }

    const updatedProvider = result[0];

    // 返回解密后的 keyVaults 数据
    let decryptedKeyVaults = {};
    if (updatedProvider.keyVaults) {
      try {
        const decryptResult = await gateKeeper.decrypt(updatedProvider.keyVaults);
        if (decryptResult.wasAuthentic) {
          decryptedKeyVaults = JSON.parse(decryptResult.plaintext);
        }
      } catch (error) {
        console.warn(`解密 keyVaults 失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    }

    return {
      ...updatedProvider,
      keyVaults: decryptedKeyVaults,
      settings: updatedProvider.settings || {},
    } as AdminProviderSelectItem;
  };

  /**
   * 切换渠道的启用状态
   * @param providerId Provider ID
   * @param enabled 启用状态
   * @returns 更新后的 provider 信息或 null
   */
  toggleInfraProviderStatus = async (
    providerId: string,
    enabled: boolean,
  ): Promise<AdminProviderSelectItem | null> => {
    const result = await adminDB
      .update(adminProviders)
      .set({ enabled })
      .where(eq(adminProviders.id, providerId))
      .returning();

    if (result.length === 0) {
      return null;
    }

    const updatedProvider = result[0];

    // 返回正确的类型格式
    return {
      ...updatedProvider,
      keyVaults: updatedProvider.keyVaults || {},
      settings: updatedProvider.settings || {},
    } as AdminProviderSelectItem;
  };

  // ==================== Model 相关操作 ====================

  /**
   * 根据 Provider ID 从 models 中查询所有的模型
   * @param providerId Provider ID
   * @returns 模型列表
   */
  getInfraModelsByProviderId = async (providerId: string): Promise<AdminModelSelectItem[]> => {
    const result = await adminDB.query.adminModels.findMany({
      orderBy: [adminModels.sort, adminModels.id],
      where: eq(adminModels.providerId, providerId),
    });

    return result;
  };

  /**
   * 根据模型 ID 和 Provider ID 查询模型详情
   * @param providerId Provider ID
   * @param modelId 模型 ID
   * @returns 模型详情或 null
   */
  getInfraModelById = async (
    providerId: string,
    modelId: string,
  ): Promise<AdminModelSelectItem | null> => {
    const model = await adminDB.query.adminModels.findFirst({
      where: and(eq(adminModels.providerId, providerId), eq(adminModels.id, modelId)),
    });

    return model || null;
  };

  /**
   * 创建模型信息
   * @param data 创建数据
   * @returns 创建的 model 信息
   */
  createInfraModel = async (data: CreateAiModelData): Promise<AdminModelSelectItem> => {
    const result = await adminDB.insert(adminModels).values(data).returning();

    return result[0];
  };

  /**
   * 更新模型信息
   * @param providerId Provider ID
   * @param modelId 模型 ID
   * @param data 更新数据
   * @returns 更新后的 model 信息或 null
   */
  updateInfraModel = async (
    providerId: string,
    modelId: string,
    data: UpdateAiModelData,
  ): Promise<AdminModelSelectItem | null> => {
    const result = await adminDB
      .update(adminModels)
      .set(data)
      .where(and(eq(adminModels.providerId, providerId), eq(adminModels.id, modelId)))
      .returning();

    return result.length > 0 ? result[0] : null;
  };

  /**
   * 删除模型
   * @param providerId Provider ID
   * @param modelId 模型 ID
   * @returns 是否删除成功
   */
  deleteInfraModel = async (providerId: string, modelId: string): Promise<boolean> => {
    const result = await adminDB
      .delete(adminModels)
      .where(and(eq(adminModels.providerId, providerId), eq(adminModels.id, modelId)))
      .returning();

    return result.length > 0;
  };

  /**
   * 切换模型的激活状态
   * @param providerId Provider ID
   * @param modelId 模型 ID
   * @param enabled 启用状态
   * @returns 更新后的 model 信息或 null
   */
  toggleInfraModelStatus = async (
    providerId: string,
    modelId: string,
    enabled: boolean,
  ): Promise<AdminModelSelectItem | null> => {
    const result = await adminDB
      .update(adminModels)
      .set({ enabled })
      .where(and(eq(adminModels.providerId, providerId), eq(adminModels.id, modelId)))
      .returning();

    return result.length > 0 ? result[0] : null;
  };

  // ==================== 与聚合模型关联的操作 ====================

  /**
   * 根据渠道 ID 从聚合模型关联表中查询对应的 LiteLLM 模型列表
   * @param infraProviderId 基础设施提供者 ID
   * @returns LiteLLM 模型 ID 列表
   */
  getLitellmModelIdsByInfraProviderId = async (infraProviderId: string): Promise<string[]> => {
    const result = await adminDB.query.aggregatedModelLitellmModel.findMany({
      columns: {
        litellmModelId: true,
      },
      where: eq(aggregatedModelLitellmModel.infraProviderId, infraProviderId),
    });

    return result.map((item) => item.litellmModelId);
  };

  /**
   * 根据渠道 ID 和模型 ID 从聚合模型关联表中查询对应的 LiteLLM 模型信息
   * @param infraProviderId 基础设施提供者 ID
   * @param infraModelId 基础设施模型 ID
   * @returns LiteLLM 模型 ID 列表
   */
  getLitellmModelIdsByInfraProviderAndModel = async (
    infraProviderId: string,
    infraModelId: string,
  ): Promise<string[]> => {
    const result = await adminDB.query.aggregatedModelLitellmModel.findMany({
      columns: {
        litellmModelId: true,
      },
      where: and(
        eq(aggregatedModelLitellmModel.infraProviderId, infraProviderId),
        eq(aggregatedModelLitellmModel.infraModelId, infraModelId),
      ),
    });

    return result.map((item) => item.litellmModelId);
  };

  /**
   * 删除聚合模型关联表中对应的关联关系（根据渠道 ID）
   * @param infraProviderId 基础设施提供者 ID
   * @returns 删除的记录数
   */
  deleteAssociationsByInfraProviderId = async (infraProviderId: string): Promise<number> => {
    const result = await adminDB
      .delete(aggregatedModelLitellmModel)
      .where(eq(aggregatedModelLitellmModel.infraProviderId, infraProviderId))
      .returning();

    return result.length;
  };

  /**
   * 删除聚合模型关联表中对应的关联关系（根据渠道 ID 和模型 ID）
   * @param infraProviderId 基础设施提供者 ID
   * @param infraModelId 基础设施模型 ID
   * @returns 删除的记录数
   */
  deleteAssociationsByInfraProviderAndModel = async (
    infraProviderId: string,
    infraModelId: string,
  ): Promise<number> => {
    const result = await adminDB
      .delete(aggregatedModelLitellmModel)
      .where(
        and(
          eq(aggregatedModelLitellmModel.infraProviderId, infraProviderId),
          eq(aggregatedModelLitellmModel.infraModelId, infraModelId),
        ),
      )
      .returning();

    return result.length;
  };

  /**
   * 检查模型是否存在
   * @param providerId Provider ID
   * @param modelId 模型 ID
   * @returns 是否存在
   */
  isInfraModelExists = async (providerId: string, modelId: string): Promise<boolean> => {
    const model = await adminDB.query.adminModels.findFirst({
      where: and(eq(adminModels.providerId, providerId), eq(adminModels.id, modelId)),
    });

    return !!model;
  };

  /**
   * 批量创建模型信息（过滤已存在的模型）
   * @param models 模型数据数组
   * @returns 创建的 model 信息列表
   */
  batchCreateInfraModels = async (models: CreateAiModelData[]): Promise<AdminModelSelectItem[]> => {
    if (models.length === 0) return [];

    // 过滤掉已存在的模型
    const modelsToCreate: CreateAiModelData[] = [];
    for (const model of models) {
      const exists = await this.isInfraModelExists(model.providerId, model.id);
      if (!exists) {
        modelsToCreate.push(model);
      }
    }

    if (modelsToCreate.length === 0) return [];

    const result = await adminDB.insert(adminModels).values(modelsToCreate).returning();

    return result;
  };

  /**
   * 根据渠道 ID 删除所有模型
   * @param providerId Provider ID
   * @returns 删除的记录数
   */
  deleteAllModelsByProviderId = async (providerId: string): Promise<number> => {
    const result = await adminDB
      .delete(adminModels)
      .where(eq(adminModels.providerId, providerId))
      .returning();

    return result.length;
  };
}
