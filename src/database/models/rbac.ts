import { and, count, eq, inArray } from 'drizzle-orm';

import { lobechatDB } from '@/database/instance/lobechatDB';
import {
  NewRole,
  NewUserRole,
  RoleItem,
  rolePermissions,
  roles,
  userRoles,
} from '@/database/instance/lobechatDB/schemas/rbac';
import { QuotaModel } from '@/database/models/quota';
import type { RoleUserInfo, RoleWithStats } from '@/types/rbac';

export class RbacModel {
  private quotaModel = new QuotaModel();

  // 获取系统中所有角色列表
  getAllRoles = async (): Promise<RoleWithStats[]> => {
    const roleList = await lobechatDB.query.roles.findMany({
      orderBy: (roles, { desc }) => [desc(roles.createdAt)],
      with: {
        rolePermissions: true,
        userRoles: true,
      },
    });

    // 获取每个角色的配额信息
    const roleListWithQuota = await Promise.all(
      roleList.map(async (role) => {
        const quota = await this.quotaModel.getRoleQuotas(role.id.toString());
        return {
          ...role,
          permissionCount: role.rolePermissions?.length || 0,
          quota,
          userCount: role.userRoles?.length || 0,
        };
      }),
    );

    return roleListWithQuota;
  };

  getRoleById = async (roleId: number): Promise<RoleItem | null> => {
    const role = await lobechatDB.query.roles.findFirst({
      where: eq(roles.id, roleId),
    });

    if (!role) {
      return null;
    }

    return role;
  };

  // 创建角色
  createRole = async (roleData: Omit<NewRole, 'id'>): Promise<RoleItem> => {
    const [newRole] = await lobechatDB.insert(roles).values(roleData).returning();
    return newRole;
  };

  // 更新角色信息
  updateRole = async (
    roleId: number,
    roleData: Partial<Omit<NewRole, 'id'>>,
  ): Promise<RoleItem> => {
    const [updatedRole] = await lobechatDB
      .update(roles)
      .set({ ...roleData, updatedAt: new Date() })
      .where(eq(roles.id, roleId))
      .returning();
    return updatedRole;
  };

  // 启用角色
  enableRole = async (roleId: number): Promise<RoleItem> => {
    return this.updateRole(roleId, { isActive: true });
  };

  // 禁用角色
  disableRole = async (roleId: number): Promise<RoleItem> => {
    return this.updateRole(roleId, { isActive: false });
  };

  // 删除角色
  deleteRole = async (roleId: number): Promise<void> => {
    // 首先删除角色相关的权限关联
    await lobechatDB.delete(rolePermissions).where(eq(rolePermissions.roleId, roleId));

    // 删除角色相关的用户关联
    await lobechatDB.delete(userRoles).where(eq(userRoles.roleId, roleId));

    // 最后删除角色
    await lobechatDB.delete(roles).where(eq(roles.id, roleId));
  };

  // 获取角色对应的用户列表
  getRoleUsers = async (roleId: number): Promise<RoleUserInfo[]> => {
    const result = await lobechatDB.query.userRoles.findMany({
      orderBy: (userRoles, { asc }) => [asc(userRoles.createdAt)],
      where: eq(userRoles.roleId, roleId),
      with: {
        user: {
          columns: {
            email: true,
            id: true,
            isOnboarded: true,
            username: true,
          },
        },
      },
    });

    return result.map((userRole) => ({
      createdAt: userRole.createdAt,
      email: userRole.user.email,
      expiresAt: userRole.expiresAt,
      id: userRole.user.id,
      isOnboarded: userRole.user.isOnboarded,
      username: userRole.user.username,
    }));
  };

  // 在某个角色中新增一个用户
  addRoleUser = async (roleId: number, userId: string, expiresAt?: Date): Promise<void> => {
    const newUserRole: NewUserRole = {
      expiresAt: expiresAt || null,
      roleId,
      userId,
    };

    await lobechatDB.insert(userRoles).values(newUserRole);
  };

  // 在某个角色中新增多个用户
  addRoleUsers = async (roleId: number, userIds: string[], expiresAt?: Date): Promise<void> => {
    const newUserRoles: NewUserRole[] = userIds.map((userId) => ({
      expiresAt: expiresAt || null,
      roleId,
      userId,
    }));

    await lobechatDB.insert(userRoles).values(newUserRoles);
  };

  // 删除角色中的某一个用户
  removeRoleUser = async (roleId: number, userId: string): Promise<void> => {
    await lobechatDB
      .delete(userRoles)
      .where(and(eq(userRoles.roleId, roleId), eq(userRoles.userId, userId)));
  };

  // 删除角色中的多个用户
  removeRoleUsers = async (roleId: number, userIds: string[]): Promise<void> => {
    await lobechatDB
      .delete(userRoles)
      .where(and(eq(userRoles.roleId, roleId), inArray(userRoles.userId, userIds)));
  };

  // 检查角色是否可以删除（没有关联用户且未启用）
  canDeleteRole = async (roleId: number): Promise<boolean> => {
    const role = await lobechatDB.query.roles.findFirst({
      where: eq(roles.id, roleId),
    });

    if (!role || role.isActive) {
      return false;
    }

    const userCount = await lobechatDB
      .select({ count: count() })
      .from(userRoles)
      .where(eq(userRoles.roleId, roleId));

    return userCount[0]?.count === 0;
  };
}
