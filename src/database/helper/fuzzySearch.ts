import { eq, like, or } from 'drizzle-orm';

import { lobechatDB } from '@/database/instance/lobechatDB';
import { users } from '@/database/instance/lobechatDB/schemas';
import { genRangeWhere, genWhere } from '@/database/utils/genWhere';

export const genFuzzySearchUserWhere = (emailOrUsernameOrUserId: string) =>
  or(
    like(users.email, `%${emailOrUsernameOrUserId}%`),
    like(users.username, `%${emailOrUsernameOrUserId}%`),
    eq(users.id, emailOrUsernameOrUserId),
  );

export const fuzzySearchUserIdList = async (
  emailOrUsernameOrUserId?: string,
  range?: [string, string],
): Promise<string[]> => {
  if (!emailOrUsernameOrUserId) return [];
  if (emailOrUsernameOrUserId.startsWith('user_')) return [emailOrUsernameOrUserId];

  const result = await lobechatDB
    .select({ id: users.id })
    .from(users)
    .where(
      genWhere([
        genFuzzySearchUserWhere(emailOrUsernameOrUserId),
        range ? genRangeWhere(range, users.createdAt, (date) => date.toDate()) : undefined,
      ]),
    );

  return result.map((item) => item.id);
};
