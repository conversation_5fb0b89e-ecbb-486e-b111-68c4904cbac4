<div align="center"><a name="readme-top"></a>

<img height="120" src="https://registry.npmmirror.com/@lobehub/assets-logo/1.0.0/files/assets/logo-3d.webp">
<img height="120" src="https://gw.alipayobjects.com/zos/kitchen/qJ3l3EPsdW/split.svg">
<img height="120" src="https://registry.npmmirror.com/@lobehub/fluent-emoji-3d/latest/files/assets/1f60e.webp">

<h1>Lobe Chat Admin</h1>

Your All-in-One Management Solution

[Changelog](./CHANGELOG.md) · [Report Bug][github-issues-link] · [Request Feature][github-issues-link]

![](https://raw.githubusercontent.com/andreasbm/readme/master/assets/lines/rainbow.png)

</div>

## ✨ Features

Introducing the LobeChat Admin, a comprehensive tool designed to streamline your chat management experience. With our all-in-one platform, you can effortlessly monitor data, manage users, and oversee channels, all from a single interface.

- [x] 🌟 **All-in-One Management**: The LobeChat Admin Console combines multiple functionalities into a single platform, allowing you to manage everything seamlessly without the need for multiple tools;
- [x] 📊 **Data Monitoring**: Stay informed with real-time analytics and reporting tools that provide valuable insights into user engagement and chat performance, empowering you to make data-driven decisions;
- [x] 👥 **User Management**: Effortlessly manage user accounts, roles, and permissions, ensuring a secure and organized environment tailored to your team's needs;
- [x] 📡 **Channel Management**: Control and configure multiple chat channels with ease, enabling you to implement customized communication strategies that align with your objectives;

Elevate your chat management experience with the LobeChat Admin Console, where efficiency meets innovation.

<div align="right">

[![][back-to-top]](#readme-top)

</div>

## ⌨️ Local Development

You can use Github Codespaces for online development:

[![][github-codespace-shield]][github-codespace-link]

Or clone it for local development:

[![][bun-shield]][bun-link]

```bash
$ git clone https://github.com/lobehub/lobe-admin.git
$ cd lobe-admin
$ bun install
$ bun dev
```

<div align="right">

[![][back-to-top]](#readme-top)

</div>

## 📦 Environment Variables

### `1` Admin Env

| Category      | Environment Variables               | Example                              |
| ------------- | ----------------------------------- | ------------------------------------ |
| ADMIN         | `BASE_URL`                          | <https://cloud-admin.lobehub-inc.cn> |
| Clerk         | `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY` | pk\_**\*\*\***                       |
| Clerk         | `CLERK_SECRET_KEY`                  | sk\_**\*\*\***                       |
| Server Config | `BRANDING_NAME`                     | LobeChat                             |
| Server Config | `BRANDING_LOGO_URL`                 | https\://**\*\*\***                  |
| Server Config | `ORG_NAME`                          | LobeHub                              |

### `2` Chat Env

| Category | Environment Variables        | Example                 |
| -------- | ---------------------------- | ----------------------- |
| ChatDB   | `CHAT_DATABASE_DRIVER`       | `neno` \| `node`        |
| ChatDB   | `CHAT_DATABASE_URL`          | postgresql://**\*\*\*** |
| ChatDB   | `CHAT_DATABASE_TEST_URL`     | postgresql://**\*\*\*** |
| ChatDB   | `CHAT_CLOUD_DATABASE_URL`    | postgresql://**\*\*\*** |
| LiteLLM  | `CHAT_LITE_LLM_DATABASE_URL` | postgresql://**\*\*\*** |
| LiteLLM  | `CHAT_LITE_LLM_BASE_URL`     | https\://**\*\*\***     |
| LiteLLM  | `CHAT_LITE_LLM_MASTER_KEY`   | sk-**\*\*\***           |
| LiteLLM  | `CHAT_LITE_LLM_CRON_SECRET`  |                         |
| Stripe   | `CHAT_ENABLE_STRIPE`         | 1 \| 0                  |
| Clerk    | `CHAT_CLERK_APPLICATION_ID`  | app\_**\*\*\***         |
| Clerk    | `CHAT_CLERK_INSTANCE_ID`     | ins\_**\*\*\***         |
| Clerk    | `CHAT_CLERK_SECRET_KEY`      | sk\_**\*\*\***          |

### `3` Vercel Env

> `EDGE_CONFIG`: This environment variable connects this project to an Edge Config

| Category | Environment Variables | Example          |
| -------- | --------------------- | ---------------- |
| Vercel   | `VERCEL_TEAM_ID`      | lobe-hub-landing |
| Vercel   | `VERCEL_ACCESS_TOKEN` | \*\*\*           |
| Vercel   | `CRON_SECRET`         | \*\*\*           |

<div align="right">

[![][back-to-top]](#readme-top)

</div>

---

#### 📝 License

Copyright © 2024 [LobeHub][profile-link].

[back-to-top]: https://img.shields.io/badge/-BACK_TO_TOP-black?style=flat-square
[bun-link]: https://bun.sh
[bun-shield]: https://img.shields.io/badge/-speedup%20with%20bun-black?logo=bun&style=for-the-badge
[github-codespace-link]: https://codespaces.new/lobehub/lobe-admin
[github-codespace-shield]: https://github.com/codespaces/badge.svg
[github-issues-link]: https://github.com/lobehub/lobe-admin/issues
[profile-link]: https://github.com/lobehub
