{"name": "mcp-crawler", "version": "1.0.0", "description": "MCP服务器 GitHub 仓库解析流水线", "keywords": ["mcp", "crawler", "github", "manifest"], "license": "ISC", "author": "", "main": "src/index.ts", "scripts": {"build": "tsc", "test": "vitest"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.11.3", "@types/node": "^22.15.3", "@types/uuid": "^10.0.0", "debug": "^4.4.1", "dotenv": "^16.5.0", "octokit": "^3.2.1", "openai": "^4.96.0", "p-map": "^7.0.3", "typescript": "^5.8.3", "uuid": "^11.1.0", "winston": "^3.17.0"}, "devDependencies": {"@types/debug": "^4.1.12"}}