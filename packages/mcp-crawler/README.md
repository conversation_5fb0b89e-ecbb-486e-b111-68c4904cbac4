# MCP-Crawler

MCP-Crawler 是一个强大的工具，用于解析 GitHub 仓库并生成符合 MCP Market Manifest Schema 的清单文件。它通过分析仓库的代码、配置和文档，自动检测和生成 MCP 服务器的配置信息。

## 功能特点

- 支持多种 GitHub 仓库 URL 格式 (HTTPS, SSH, 简短格式)
- 自动检测和解析 package.json、Dockerfile 和 README
- 智能识别不同类型的 MCP 服务器 (npm, docker 等)
- 递归遍历目录结构寻找关键文件
- 支持 OpenAI 增强解析，从 README 中提取更精确的元数据
- 智能合并和补充信息，生成完整的 Manifest
- **GitHub 搜索功能**: 自动搜索和发现 GitHub 上的 MCP Server 项目
- **智能过滤**: 自动识别真正的 MCP Server，过滤模板和示例项目
- **批量验证**: 批量验证搜索结果是否为有效的 MCP Server
- 提供命令行界面和编程接口
- 详细的日志输出

## 安装

### 直接使用

```bash
# 克隆仓库
git clone https://github.com/yourusername/mcp-crawler.git
cd mcp-crawler

# 安装依赖
pnpm install

# 构建项目
pnpm build
```

### 作为依赖安装

```bash
pnpm add mcp-crawler
```

## 使用方法

### 命令行使用

```bash
# 设置GitHub令牌（推荐：避免API请求限制）
export GITHUB_TOKEN=your_github_token_here

# OpenAI功能（可选，用于增强解析）
export OPENAI_API_KEY=your_openai_api_key_here

# 或者在项目根目录创建.env文件,添加:
# GITHUB_TOKEN=your_github_token_here
# OPENAI_API_KEY=your_openai_api_key_here

# 爬取指定仓库
pnpm crawl https://github.com/owner/repo

# 指定输出文件
pnpm crawl https://github.com/owner/repo ./output.json

# 使用简短格式
pnpm crawl owner/repo
```

### 编程接口使用

#### 爬取单个仓库

```typescript
import * as fs from 'fs';
import { MCPCrawler } from 'mcp-crawler';

async function main() {
  // 创建爬虫实例
  const crawler = new MCPCrawler('https://github.com/owner/repo');

  // 解析仓库生成Manifest
  const manifest = await crawler.crawl();

  // 保存为文件
  fs.writeFileSync('manifest.json', JSON.stringify(manifest, null, 2));
  console.log('Manifest已生成!');
}

main().catch(console.error);
```

#### 搜索 GitHub MCP Server 项目

```typescript
import { MCPSearcher } from 'mcp-crawler';

async function searchMCPServers() {
  const searcher = new MCPSearcher({ maxPages: 3 });

  // 基础搜索
  const results = await searcher.search();

  // 按关键词搜索
  const keywordResults = await searcher.searchByKeywords(['redis', 'database']);

  // 获取搜索统计
  const stats = searcher.getSearchStats(results);

  console.log(`找到 ${results.length} 个 MCP Server 项目`);
  console.log(`平均 Stars: ${stats.avgStars}`);
  console.log(`主要语言:`, stats.languages);

  results.forEach((repo) => {
    console.log(`${repo.name}: ${repo.url} (${repo.stars} stars)`);
  });

  // 导出结果
  const jsonData = searcher.exportResults(results);
  console.log('搜索结果已导出');
}

searchMCPServers().catch(console.error);
```

## 流水线步骤

MCP-Crawler 按照以下步骤工作:

1. **解析仓库 URL**: 从 GitHub URL 中提取所有者、仓库名和分支信息
2. **获取仓库元数据**: 通过 GitHub API 获取仓库的基本信息
3. **查找关键文件**:
   - 查找 MCP Manifest 文件 (如已存在)
   - 查找 package.json (Node.js 项目)
   - 查找 Dockerfile (Docker 项目)
   - 查找 README.md (项目文档)
4. **解析文件**:
   - 从 package.json 解析包名、版本、描述、作者等信息
   - 从 Dockerfile 解析容器配置
   - 从 README 提取说明、标签等元信息
     - 使用正则表达式提取基本信息
     - 使用 OpenAI 增强解析（如启用）
5. **整合信息**: 将所有解析结果智能合并，构建完整的 Manifest
6. **生成最终 Manifest**: 补充缺失字段，生成符合规范的完整清单

## OpenAI 增强解析

MCP-Crawler 支持使用 OpenAI API 进行更精确的元数据提取。这个功能可以帮助:

- 从复杂的 README 中识别出更多信息
- 提取人类容易理解但正则表达式难以捕获的模式
- 更准确地推断连接类型和安装方法

### 启用 OpenAI 解析

1. 设置 OPENAI_API_KEY 环境变量
2. 解析时将自动尝试使用 OpenAI 增强分析
3. 正则表达式和 OpenAI 解析的结果会智能合并

### 单独使用 OpenAI 解析器

```typescript
import { OpenAIParser } from 'mcp-crawler';

async function analyzeReadme() {
  const parser = new OpenAIParser();

  // 检查 OpenAI 解析器是否可用
  if (!parser.isAvailable()) {
    console.log('请设置 OPENAI_API_KEY 环境变量');
    return;
  }

  // 使用 OpenAI 解析
  const readme = '# 项目名称\n这是一个示例项目...(README内容)';
  const result = await parser.parseReadme(readme, 'owner/repo');

  console.log(result);
}
```

## 调试

为了便于调试，可以设置环境变量调整日志级别:

```bash
# 在.env文件中设置，或通过环境变量:
LOG_LEVEL=debug # 可选值: debug, info, warn, error
```

## 高级配置

### 自定义仓库分支

```typescript
import { MCPCrawler, parseGitHubUrl } from 'mcp-crawler';

async function customBranch() {
  // 手动设置仓库信息和分支
  const repoInfo = parseGitHubUrl('https://github.com/owner/repo');
  repoInfo.branch = 'develop';

  // 创建爬虫
  const crawler = new MCPCrawler(
    `https://github.com/${repoInfo.owner}/${repoInfo.repo}/tree/${repoInfo.branch}`,
  );
  const manifest = await crawler.crawl();

  console.log(manifest);
}
```

数据源：

- 官方 MCP Server 373 个 <https://github.com/modelcontextprotocol/registry/blob/main/data/seed.json>
- glama AI 1000 个 MCP <https://glama.ai/mcp/servers.json>
- smithery 6434 个 MCP

## 贡献指南

欢迎贡献代码和提出改进建议！

1. Fork 仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 提交 Pull Request

## 许可证

MIT
