import debug from 'debug';

import { MCPManifest, McpPrompt, McpResource } from '../types';
import { logger } from '../utils';
import { MCPClient } from './index';
import { MCPClientParams, ValidationResult, ValidatorOptions } from './types';

const log = debug('mcp:validator');

/**
 * MCP客户端校验器
 * 用于校验MCP Manifest中的连接配置是否有效
 */
export const MCPValidator = {
  /**
   * 从MCP Manifest创建连接参数
   */
  createClientParamsFromManifest(manifest: MCPManifest): MCPClientParams | null {
    try {
      // 找到推荐的部署选项，如果没有则使用第一个
      const deploymentOption =
        manifest.deploymentOptions.find((option) => option.isRecommended) ||
        manifest.deploymentOptions[0];

      if (!deploymentOption) {
        log('No deployment options found in manifest');
        return null;
      }

      const connection = deploymentOption.connection;

      // 基础连接参数
      const params: MCPClientParams = {
        id: manifest.identifier,
        name: manifest.name,
        type: connection.type,
      };

      // 根据连接类型设置特定参数
      if (connection.type === 'http' && connection.url) {
        params.url = connection.url;
      } else if (connection.type === 'stdio' && connection.command) {
        params.command = connection.command;
        params.args = connection.args || [];
      } else {
        log('Invalid connection configuration in manifest');
        return null;
      }

      // 添加环境变量支持
      if (connection.configSchema?.properties) {
        params.env = {};
        Object.keys(connection.configSchema.properties).forEach((key) => {
          if (process.env[key]) {
            // @ts-ignore
            params.env[key] = process.env[key]!;
          }
        });
      }

      return params;
    } catch (error) {
      log('Error creating client params from manifest: %O', error);
      return null;
    }
  },

  /**
   * 验证MCP连接
   * @param params MCP客户端连接参数
   * @param options 校验选项
   */
  async validate(
    params: MCPClientParams | MCPManifest,
    options: ValidatorOptions = {},
  ): Promise<ValidationResult> {
    const { timeout = 60_000, onProgress } = options;

    // 如果输入是Manifest，则转换为连接参数
    let manifest: MCPManifest | null = null;
    if ('deploymentOptions' in params) {
      manifest = params;
      // eslint-disable-next-line no-param-reassign
      params = this.createClientParamsFromManifest(params) || { type: 'http', url: '' };
    }

    const clientParams = params as MCPClientParams;

    if (
      !clientParams ||
      (!clientParams.url && clientParams.type === 'http') ||
      (!clientParams.command && clientParams.type === 'stdio')
    ) {
      return {
        connected: false,
        error: 'Failed to create valid MCP client params',
      };
    }

    // 记录验证开始
    const startTime = Date.now();
    const connectionInfo =
      clientParams.type === 'http'
        ? `HTTP: ${clientParams.url}`
        : `STDIO: ${clientParams.command} ${clientParams.args?.join(' ')}`;

    log('Starting validation for %s: %s', clientParams.name || clientParams.id, connectionInfo);
    logger.info(
      `🔍 开始验证MCP客户端: ${clientParams.name || clientParams.id} (${clientParams.type})`,
    );

    // 创建客户端
    const client = new MCPClient(clientParams);
    const result: ValidationResult = { connected: false };

    try {
      // 设置超时
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Connection timeout')), timeout);
      });

      // 初始化连接
      await Promise.race([client.initialize({ onProgress }), timeoutPromise]);

      result.connected = true;

      // 获取工具列表
      try {
        log('Fetching tools...');
        const tools = await client.listTools();
        result.tools = tools;
        result.capabilities = result.capabilities || {};
        result.capabilities.tools = tools.length > 0;
      } catch (error) {
        log('Error fetching tools: %O', error);
        // 工具功能可能未实现，继续检查其他功能
      }

      // 获取提示词列表
      try {
        log('Fetching prompts...');
        const promptsResult = await (client as any).mcp.listPrompts();
        if (promptsResult && promptsResult.prompts) {
          result.prompts = promptsResult.prompts as McpPrompt[];
          result.capabilities = result.capabilities || {};
          result.capabilities.prompts = result.prompts.length > 0;
        }
      } catch (error) {
        log('Error fetching prompts: %O', error);
        // 提示词功能可能未实现，继续检查其他功能
      }

      // 获取资源列表
      try {
        log('Fetching resources...');
        const resourcesResult = await (client as any).mcp.listResources();
        if (resourcesResult && resourcesResult.resources) {
          result.resources = resourcesResult.resources as McpResource[];
          result.capabilities = result.capabilities || {};
          result.capabilities.resources = result.resources.length > 0;
        }
      } catch (error) {
        log('Error fetching resources: %O', error);
        // 资源功能可能未实现
      }

      // 设置已验证标记和时间
      result.isValidated = true;
      result.validatedAt = new Date().toISOString();

      // 如果有manifest，更新其验证的能力信息
      if (manifest) {
        manifest.capabilities = result.capabilities;

        // 更新manifest的实际数据
        if (result.tools) manifest.tools = result.tools;
        if (result.prompts) manifest.prompts = result.prompts;
        if (result.resources) manifest.resources = result.resources;

        // 设置已验证标记和时间
        manifest.isValidated = true;
        manifest.validatedAt = result.validatedAt;

        result.manifest = manifest;
      }

      // 计算处理时间
      const processingTime = (Date.now() - startTime) / 1000;

      // 构建功能摘要
      const capabilitySummary = [];
      if (result.capabilities?.tools) capabilitySummary.push(`工具(${result.tools?.length || 0})`);
      if (result.capabilities?.prompts)
        capabilitySummary.push(`提示词(${result.prompts?.length || 0})`);
      if (result.capabilities?.resources)
        capabilitySummary.push(`资源(${result.resources?.length || 0})`);

      logger.success(
        `✅ MCP客户端验证成功! 支持功能: ${capabilitySummary.join(', ')}, 耗时: ${processingTime.toFixed(2)}秒`,
      );
    } catch (error) {
      // 记录错误
      const errorMessage = (error as Error).message || String(error);
      result.error = errorMessage;
      log('Validation failed: %O', error);
      logger.error(`❌ MCP客户端验证失败: ${errorMessage}`);
    } finally {
      // 断开连接
      try {
        await client.disconnect();
      } catch (error) {
        log('Error during disconnect: %O', error);
      }
    }

    return result;
  },
};
