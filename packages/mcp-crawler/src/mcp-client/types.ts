import { McpTool } from '../types';

/**
 * MCP客户端连接参数
 */
export interface MCPClientParams {
  /** stdio连接参数 */
  args?: string[];
  /** stdio连接命令 */
  command?: string;
  /** 环境变量 */
  env?: Record<string, string>;
  /** 连接ID */
  id?: string;
  /** 连接名称 */
  name?: string;
  /** 连接类型：HTTP或stdio */
  type: 'http' | 'stdio';
  /** HTTP连接URL */
  url?: string;
}

/**
 * 校验器选项
 */
export interface ValidatorOptions {
  /** 进度回调 */
  onProgress?: (progress: any) => void;
  /** 超时时间(毫秒) */
  timeout?: number;
}

/**
 * 校验结果
 */
export interface ValidationResult {
  /** MCP功能验证结果 */
  capabilities?: {
    prompts?: boolean;
    resources?: boolean;
    tools?: boolean;
  };

  /** 是否连接成功 */
  connected: boolean;

  /** 错误信息 */
  error?: string;

  /** 是否已验证 */
  isValidated?: boolean;

  /** 更新后的Manifest（如果从Manifest验证） */
  manifest?: any;

  /** 可用提示词列表 */
  prompts?: any[];

  /** 可用资源列表 */
  resources?: any[];

  /** 测试结果 */
  testResults?: {
    error?: string;
    result?: any;
    success: boolean;
    toolName: string;
  }[];

  /** 可用工具列表 */
  tools?: McpTool[];

  /** 验证时间 */
  validatedAt?: string;
}
