import winston from 'winston';

// 扩展 <PERSON> Logger 类型以包含自定义方法
declare module 'winston' {
  interface Logger {
    progress: (current: number, total: number, message: string) => void;
    success: (message: string) => void;
  }
}

/**
 * 专业的日志工具 - 使用 Winston
 */
export const logger = winston.createLogger({
  format: winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    winston.format.errors({ stack: true }),
    winston.format.colorize({ all: true }),
    winston.format.printf(({ timestamp, level, message, stack }) => {
      return `${timestamp} [${level}]: ${message}${stack ? '\n' + stack : ''}`;
    }),
  ),
  level: process.env.NODE_ENV === 'development' ? 'debug' : 'info',
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize({ all: true }),
        winston.format.printf(({ level, message, stack }) => {
          return `[${level}] ${message}${stack ? '\n' + stack : ''}`;
        }),
      ),
    }),
  ],
});

// 添加自定义的进度和成功方法
logger.progress = (current: number, total: number, message: string) => {
  const percentage = Math.floor((current / total) * 100);
  const bar = '█'.repeat(Math.floor(percentage / 5)) + '░'.repeat(20 - Math.floor(percentage / 5));
  logger.info(`${bar} ${percentage}% - ${message}`);
};

logger.success = (message: string) => {
  logger.info(`✅ ${message}`);
};
