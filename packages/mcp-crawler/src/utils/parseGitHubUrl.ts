import { GitHubRepo } from '../types';

/**
 * 解析GitHub URL获取仓库信息
 */
export function parseGitHubUrl(url: string): GitHubRepo {
  try {
    // 规范化URL
    const normalizedUrl = url.trim().replace(/\.git$/, '');

    // 尝试从URL中解析
    const urlRegex = /github\.com\/([^/]+)\/([^/]+)(?:\/tree\/([^/]+))?/;
    const match = normalizedUrl.match(urlRegex);

    if (match) {
      const [, owner, repo, branch] = match;
      return { branch, owner, repo };
    }

    // 如果URL格式不正确，尝试从'owner/repo'格式解析
    const simpleRegex = /^([^/]+)\/([^/]+)$/;
    const simpleMatch = normalizedUrl.match(simpleRegex);

    if (simpleMatch) {
      const [, owner, repo] = simpleMatch;
      return { owner, repo };
    }

    throw new Error(`无法解析GitHub URL: ${url}`);
  } catch (error) {
    throw new Error(
      `GitHub URL解析失败: ${error instanceof Error ? error.message : String(error)}`,
    );
  }
}

/**
 * 构建GitHub API URL
 */
export function buildGitHubApiUrl(repo: GitHubRepo, path?: string): string {
  const { owner, repo: repoName, branch = 'main' } = repo;

  if (path) {
    return `https://api.github.com/repos/${owner}/${repoName}/contents/${path}?ref=${branch}`;
  }

  return `https://api.github.com/repos/${owner}/${repoName}`;
}

/**
 * 构建GitHub网页URL
 */
export function buildGitHubWebUrl(repo: GitHubRepo): string {
  const { owner, repo: repoName, branch } = repo;

  if (branch && ['main', 'master'].every((item) => item !== branch)) {
    return `https://github.com/${owner}/${repoName}/tree/${branch}`;
  }

  return `https://github.com/${owner}/${repoName}`;
}
