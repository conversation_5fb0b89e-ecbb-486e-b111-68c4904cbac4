/**
 * GitHub仓库信息
 */
export interface GitHubRepo {
  branch?: string;
  owner: string;
  repo: string;
}

/**
 * GitHub文件内容
 */
export interface GitHubFile {
  content: string;
  path: string;
}

/**
 * MCP Tool 定义
 */
export interface McpTool {
  description: string;
  name: string;
  parameters?: {
    properties?: Record<string, any>;
    required?: string[];
    type: string;
  };
  returns?: {
    properties?: Record<string, any>;
    type: string;
  };
}

/**
 * MCP资源定义
 */
export interface McpResource {
  data: any;
  description: string;
  id: string;
  name: string;
  type: string;
}

/**
 * MCP提示词定义
 */
export interface McpPrompt {
  description: string;
  id: string;
  name: string;
  text: string;
  variables?: string[];
}

/**
 * MCP Manifest类型
 * 基于prompt.ts中定义的JSON Schema
 */
export interface MCPManifest {
  // 构建产物信息
  artifacts?: {
    docker?: {
      imageName?: string;
      tag?: string;
    };
    npm?: {
      packageName?: string;
      version?: string;
    };
    pypi?: {
      packageName?: string;
      version?: string;
    };
  };
  author?: {
    name: string | null;
    url: string | null;
  };
  // 功能描述 (来自抓取分析)
  capabilities?: {
    prompts?: boolean;
    resources?: boolean;
    tools?: boolean;
  };
  category?:
    | 'gaming-entertainment'
    | 'lifestyle'
    | 'media-generate'
    | 'science-education'
    | 'social'
    | 'stocks-finance'
    | 'tools'
    | 'web-search'
    | 'productivity'
    | 'developer'
    | 'business'
    | 'health-wellness'
    | 'travel-transport'
    | 'news'
    | 'weather';
  // 部署选项
  deploymentOptions: Array<{
    connection: {
      args?: string[] | null;
      command?: string | null;
      configSchema?: {
        properties: Record<
          string,
          {
            description?: string;
            type: string;
          }
        >;
        required?: string[];
        type: string;
      } | null;
      type: 'stdio' | 'http';
      url?: string | null;
    };
    installationDetails?: {
      buildCommand?: string | null;
      buildContext?: string | null;
      buildRequired?: boolean | null;
      fileName?: string | null;
      // git 安装详情
      gitRepo?: string;
      // docker 安装详情
      imageName?: string;
      // npm 安装详情
      packageName?: string;
      // go 安装详情
      remotePackage?: string;
      runCommandExample?: string | null;
      // 二进制下载详情
      url?: string;
    } | null;
    installationMethod:
      | 'npm'
      | 'docker'
      | 'python'
      | 'go'
      | 'git'
      | 'binaryUrl'
      | 'none'
      | 'manual';
    isRecommended?: boolean | null;
    notes?: string | null;
    systemDependencies?: Array<{
      checkCommand: string;
      installInstructions: {
        linux_debian?: string | null;
        macos?: string | null;
        manual: string;
        windows?: string | null;
      };
      name: string;
      notes?: string | null;
      requiredVersion?: string | null;
      type: 'executable' | 'library' | 'runtime';
      versionParsingRequired: boolean;
    }>;
  }>;
  description: string;
  // GitHub 相关信息
  github?: {
    language?: string;
    license?: string;
    stars?: number;
    url: string;
  };
  homepage?: string | null;
  icon: string;

  // 基本信息
  identifier: string;
  // 是否已经验证过
  isValidated?: boolean;
  name: string;

  overview?: {
    readme: string;
    summary?: string;
  };

  // 实际提示词列表（来自实际连接后的listPrompts）
  prompts?: McpPrompt[];

  // 实际资源列表（来自实际连接后的listResources）
  resources?: McpResource[];

  tags?: string[];

  // 实际工具列表（来自实际连接后的listTools）
  tools?: McpTool[];

  // 验证时间
  validatedAt?: string;

  // 实际MCP功能信息（来自实际连接验证）
  verifiedCapabilities?: {
    prompts?: boolean;
    resources?: boolean;
    tools?: boolean;
  };

  version: string;
}

/**
 * 爬虫选项
 */
export interface CrawlerOptions {
  /** OpenAI API密钥 */
  apiKey?: string;
  /** OpenAI API基础URL */
  baseUrl?: string;
  /** GitHub令牌 */
  githubToken?: string;
  /** OpenAI模型名称 */
  model?: string;
}

/**
 * Plugin localization data
 */
export interface PluginLocalization {
  /** Description of the plugin in specific locale */
  description: string;
  /** Locale code (e.g., 'zh-CN', 'ja-JP') */
  locale: string;
  /** Name of the plugin in specific locale */
  name: string;
  /** Overview summary in specific locale */
  summary?: string;
  /** Tags in specific locale */
  tags?: string[];
}

/**
 * Localization generation options
 */
export interface LocalizationOptions {
  /** OpenAI API密钥 */
  openaiApiKey?: string;
  /** OpenAI API基础URL */
  openaiBaseUrl?: string;
  /** OpenAI模型名称 */
  openaiModel?: string;
  /** 目标语言列表 */
  targetLocales?: string[];
}
