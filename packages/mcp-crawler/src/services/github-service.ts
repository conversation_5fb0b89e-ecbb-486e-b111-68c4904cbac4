import * as dotenv from 'dotenv';
import { Octokit } from 'octokit';

import { GitHubFile, GitHubRepo } from '../types';
import { buildGitHubApiUrl, logger } from '../utils';

dotenv.config();

export interface GitHubSearchResult {
  description: string;
  language: string;
  name: string;
  size: number;
  stars: number;
  updatedAt: string;
  url: string;
}

export interface SearchOptions {
  maxPages?: number;
  order?: 'asc' | 'desc';
  perPage?: number;
  query?: string;
  sort?: 'stars' | 'updated';
}

/**
 * GitHub服务类，负责从GitHub获取仓库内容
 */
export class GitHubService {
  private token: string | undefined;
  private octokit: Octokit;

  constructor(token?: string) {
    this.token = token || process.env.GITHUB_TOKEN;
    this.octokit = new Octokit({
      auth: this.token,
    });
  }

  /**
   * 创建请求头
   */
  private createHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      Accept: 'application/vnd.github.v3+json',
    };

    if (this.token) {
      headers['Authorization'] = `token ${this.token}`;
    }

    return headers;
  }

  /**
   * 获取仓库基本信息
   */
  async getRepoInfo(repo: GitHubRepo): Promise<any> {
    try {
      const url = buildGitHubApiUrl(repo);
      const response = await fetch(url, { headers: this.createHeaders() });

      if (!response.ok) {
        throw new Error(`获取仓库信息失败: ${response.status} ${response.statusText}`);
      }

      const data = (await response.json()) as any;

      // 如果未指定分支，使用仓库的默认分支
      if (!repo.branch) {
        repo.branch = data.default_branch;
      }

      return data;
    } catch (error) {
      logger.error(`获取仓库信息失败: ${repo.owner}/${repo.repo}`, error);
      throw error;
    }
  }

  /**
   * 读取文件内容
   */
  async readFile(repo: GitHubRepo, path: string): Promise<string> {
    try {
      // 常见文本文件，可以直接从 raw.githubusercontent.com 获取
      const commonTextFiles = [
        'README.md',
        'readme.md',
        'package.json',
        'pyproject.toml',
        'Dockerfile',
      ];

      if (commonTextFiles.includes(path)) {
        const branch = repo.branch || 'main';
        const rawUrl = `https://raw.githubusercontent.com/${repo.owner}/${repo.repo}/${branch}/${path}`;

        const response = await fetch(rawUrl);

        if (!response.ok) {
          // 文件不存在返回空字符串
          if (response.status === 404) {
            return '';
          }
          throw new Error(`读取文件失败: ${response.status} ${response.statusText}`);
        }

        return await response.text();
      } else {
        return this.fetchWithApi(repo, path);
      }
    } catch (error) {
      logger.error(`读取文件失败: ${repo.owner}/${repo.repo}/${path}`, error);
      return '';
    }
  }

  /**
   * 一次性读取多个文件
   */
  async readFiles(repo: GitHubRepo, paths: string[]): Promise<GitHubFile[]> {
    // 使用Promise.all并行读取所有文件
    const contents = await Promise.all(
      paths.map(async (path) => {
        const content = await this.readFile(repo, path);
        return { content, path };
      }),
    );

    // 过滤掉读取失败的文件（内容为空）
    return contents.filter((file) => file.content !== '');
  }

  /**
   * 读取存储库的关键文件（README, package.json等）
   */
  async readRepoKeyFiles(repo: GitHubRepo): Promise<GitHubFile[]> {
    // 获取仓库基本信息，确保分支信息正确
    await this.getRepoInfo(repo);

    // 定义可能存在的关键文件路径
    const keyFilePaths = ['README.md', 'readme.md', 'package.json', 'pyproject.toml', 'Dockerfile'];

    return this.readFiles(repo, keyFilePaths);
  }

  /**
   * 搜索 MCP Server 仓库
   */
  async searchMCPServers(options: SearchOptions = {}): Promise<GitHubSearchResult[]> {
    const {
      query = 'mcp server OR mcp-server OR "model context protocol"',
      sort = 'updated',
      order = 'desc',
      perPage = 100,
      maxPages = 3,
    } = options;

    const results: GitHubSearchResult[] = [];
    let page = 1;

    try {
      while (page <= maxPages) {
        logger.info(`🔍 搜索第 ${page} 页...`);

        const response = await this.octokit.rest.search.repos({
          order,
          page,
          per_page: perPage,
          q: query,
          sort,
        });

        if (response.data.items.length === 0) {
          logger.info(`第 ${page} 页没有更多结果，停止搜索`);
          break;
        }

        const pageResults = response.data.items
          .filter((item) => this.isMCPServerRepo(item))
          .map((item) => ({
            description: item.description || '',
            language: item.language || 'Unknown',
            name: item.full_name,
            size: item.size,
            stars: item.stargazers_count,
            updatedAt: item.updated_at,
            url: item.html_url,
          }));

        results.push(...pageResults);
        logger.info(`第 ${page} 页找到 ${pageResults.length} 个 MCP Server 仓库`);

        // 如果当前页结果少于请求数量，说明没有更多结果了
        if (response.data.items.length < perPage) {
          break;
        }

        page++;

        // 避免触发 GitHub API 限制，添加延迟
        if (page <= maxPages) {
          await this.delay(1000);
        }
      }

      logger.success(`🎉 搜索完成，总共找到 ${results.length} 个 MCP Server 仓库`);
      return results;
    } catch (error) {
      logger.error('GitHub 搜索失败:', error);
      return results;
    }
  }

  /**
   * 搜索特定关键词的仓库
   */
  async searchByKeywords(
    keywords: string[],
    options: SearchOptions = {},
  ): Promise<GitHubSearchResult[]> {
    const searchQueries = [
      // 基础 MCP 相关搜索
      'mcp server OR mcp-server',
      '"model context protocol"',
      'mcp plugin OR mcp-plugin',

      // 结合特定关键词
      ...keywords.map((keyword) => `"${keyword}" mcp server`),
      ...keywords.map((keyword) => `"${keyword}" "model context protocol"`),
    ];

    const allResults: GitHubSearchResult[] = [];
    const seenUrls = new Set<string>();

    for (const query of searchQueries) {
      try {
        logger.info(`🔍 搜索关键词: ${query}`);
        const results = await this.searchMCPServers({ ...options, query });

        // 去重
        const newResults = results.filter((result) => {
          if (seenUrls.has(result.url)) {
            return false;
          }
          seenUrls.add(result.url);
          return true;
        });

        allResults.push(...newResults);
        logger.info(`找到 ${newResults.length} 个新仓库`);

        // 添加延迟避免触发限制
        await this.delay(2000);
      } catch (error) {
        logger.error(`搜索关键词 "${query}" 失败:`, error);
        // 继续搜索其他关键词
      }
    }

    return allResults;
  }

  /**
   * 判断仓库是否是 MCP Server
   */
  private isMCPServerRepo(repo: any): boolean {
    const name = (repo.name || '').toLowerCase();
    const description = (repo.description || '').toLowerCase();
    const fullName = (repo.full_name || '').toLowerCase();

    // 关键词匹配
    const mcpKeywords = [
      'mcp',
      'model context protocol',
      'mcp-server',
      'mcp server',
      'mcp-plugin',
      'mcp plugin',
    ];

    // 排除关键词 - 避免误判
    const excludeKeywords = [
      'template',
      'example',
      'demo',
      'tutorial',
      'awesome',
      'list',
      'collection',
    ];

    const text = `${name} ${description} ${fullName}`;

    // 检查是否包含 MCP 相关关键词
    const hasMCPKeyword = mcpKeywords.some((keyword) => text.includes(keyword));

    // 检查是否是模板或示例项目
    const isTemplate = excludeKeywords.some((keyword) => text.includes(keyword));

    return hasMCPKeyword && !isTemplate;
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(resolve, ms);
    });
  }

  private async fetchWithApi(repo: GitHubRepo, path: string): Promise<string> {
    const url = buildGitHubApiUrl(repo, path);
    const response = await fetch(url, { headers: this.createHeaders() });

    if (!response.ok) {
      // 文件不存在返回空字符串
      if (response.status === 404) {
        return '';
      }

      throw new Error(`读取文件失败: ${response.status} ${response.statusText}`);
    }

    const data = (await response.json()) as any;

    // GitHub API返回的是Base64编码的内容
    return Buffer.from(data.content, 'base64').toString('utf8');
  }
}
