import * as dotenv from 'dotenv';
import OpenAI from 'openai';

import { logger } from '../../utils';

// 加载环境变量
dotenv.config();

export interface AIServiceOptions {
  apiKey?: string;
  baseUrl?: string;
  model?: string;
}

export interface ChatCompletionOptions {
  maxTokens?: number;
  messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[];
  model?: string;
  responseFormat?: { type: 'json_object' } | { type: 'text' };
  systemMessage?: string;
  temperature?: number;
}

/**
 * 通用AI服务，封装OpenAI API调用逻辑
 */
export class AIService {
  private openai: OpenAI;
  private isInitialized: boolean = false;
  private options: AIServiceOptions;

  constructor(options: AIServiceOptions = {}) {
    this.options = options;

    // 初始化OpenAI客户端
    const apiKey = options.apiKey || process.env.OPENAI_API_KEY;

    if (apiKey) {
      this.openai = new OpenAI({
        apiKey,
        baseURL: options.baseUrl || process.env.OPENAI_PROXY_URL,
      });
      this.isInitialized = true;
    } else {
      logger.warn('未设置OpenAI API密钥，无法使用AI服务');
      this.openai = {} as OpenAI;
    }
  }

  /**
   * 检查AI服务是否已初始化
   */
  isReady(): boolean {
    return this.isInitialized;
  }

  /**
   * 统一的聊天完成API调用
   */
  async chatCompletion(options: ChatCompletionOptions): Promise<string | null> {
    if (!this.isInitialized) {
      logger.error('未初始化OpenAI客户端，无法调用AI服务');
      return null;
    }

    try {
      const messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [];

      // 添加系统消息（如果提供）
      if (options.systemMessage) {
        messages.push({
          content: options.systemMessage,
          role: 'system',
        });
      }

      // 添加用户消息
      messages.push(...options.messages);

      // 构建API调用参数
      const apiOptions: OpenAI.Chat.Completions.ChatCompletionCreateParams = {
        messages,
        model: options.model || this.options.model || 'gpt-4.1-mini',
        temperature: options.temperature ?? 0,
      };

      // 添加响应格式（如果指定）
      if (options.responseFormat) {
        apiOptions.response_format = options.responseFormat;
      }

      // 添加最大token数（如果指定）
      if (options.maxTokens) {
        apiOptions.max_completion_tokens = options.maxTokens;
      }

      // 调用OpenAI API
      const response = await this.openai.chat.completions.create(apiOptions);

      // 获取响应内容
      const content = response.choices[0].message.content?.trim() || '';

      if (!content) {
        logger.warn('AI服务返回空响应');
        return null;
      }

      return content;
    } catch (error) {
      logger.error('AI服务调用失败', error);
      return null;
    }
  }

  /**
   * JSON格式的聊天完成调用，自动解析JSON响应
   */
  async chatCompletionJSON<T = any>(
    options: Omit<ChatCompletionOptions, 'responseFormat'>,
  ): Promise<T | null> {
    const response = await this.chatCompletion({
      ...options,
      responseFormat: { type: 'json_object' },
    });

    if (!response) {
      return null;
    }

    try {
      return JSON.parse(response) as T;
    } catch (error) {
      logger.error('解析AI服务返回的JSON失败', error);
      logger.error(`原始返回内容: ${response}`);
      return null;
    }
  }

  /**
   * 文本格式的聊天完成调用
   */
  async chatCompletionText(
    options: Omit<ChatCompletionOptions, 'responseFormat'>,
  ): Promise<string | null> {
    return this.chatCompletion({
      ...options,
      responseFormat: { type: 'text' },
    });
  }

  /**
   * 批量处理，带有并发控制和错误处理
   */
  async batchProcess<T, R>(
    items: T[],
    processor: (item: T) => Promise<R | null>,
    options: {
      batchSize?: number;
      delayMs?: number;
    } = {},
  ): Promise<R[]> {
    const { batchSize = 5, delayMs = 200 } = options;
    const results: R[] = [];

    logger.info(`🔄 开始批量处理 ${items.length} 个项目...`);

    // 分批处理以避免API限制
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      const batchPromises = batch.map(processor);

      try {
        const batchResults = await Promise.all(batchPromises);

        // 过滤掉失败的结果
        for (const result of batchResults) {
          if (result !== null) {
            results.push(result);
          }
        }

        // 在批次之间添加短暂延迟
        if (i + batchSize < items.length && delayMs > 0) {
          await new Promise((resolve) => {
            setTimeout(resolve, delayMs);
          });
        }
      } catch (error) {
        logger.error(`批量处理时发生错误: ${error}`);
      }
    }

    logger.success(`✅ 批量处理完成，成功处理 ${results.length}/${items.length} 个项目`);
    return results;
  }
}
