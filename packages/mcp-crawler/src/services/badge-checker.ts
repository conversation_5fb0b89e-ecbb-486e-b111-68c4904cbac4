import { parseGitHubUrl } from '../utils';
import { GitHubService } from './github-service';

export interface BadgeCheckResult {
  error?: string;
  githubUrl?: string;
  hasBadge: boolean;
  hasGitHub: boolean;
  identifier: string;
}

export class BadgeChecker {
  private githubService: GitHubService;

  constructor(githubToken?: string) {
    this.githubService = new GitHubService(githubToken);
  }

  /**
   * 检查 README 内容中是否包含 MCP Badge
   */
  private checkMCPBadge(readmeContent: string, identifier: string): boolean {
    if (!readmeContent) return false;

    // 构建预期的 badge 内容
    const expectedBadge = `[![MCP Badge](https://lobehub.com/badge/mcp-full/${identifier})](https://lobehub.com/mcp/${identifier})`;

    // 检查是否包含完整的 badge
    if (readmeContent.includes(expectedBadge)) {
      return true;
    }

    // 检查是否包含部分 badge 内容 (更宽松的匹配)
    const badgePattern = new RegExp(
      `lobehub\\.com/badge/mcp-full/${identifier.replaceAll(/[$()*+.?[\\\]^{|}]/g, '\\$&')}`,
      'i',
    );
    return badgePattern.test(readmeContent);
  }

  /**
   * 检查单个插件的 MCP Badge
   */
  async checkPluginBadge(
    identifier: string,
    githubUrl?: string,
    homepage?: string,
  ): Promise<BadgeCheckResult> {
    try {
      console.log(`🔍 检查插件 "${identifier}" 的 MCP Badge...`);

      // 获取插件的 GitHub URL
      const targetUrl = githubUrl || homepage;

      if (!targetUrl || !targetUrl.includes('github.com')) {
        console.warn(`⚠️  插件 "${identifier}" 没有 GitHub URL，跳过检查`);
        return {
          hasBadge: false,
          hasGitHub: false,
          identifier,
        };
      }

      // 解析 GitHub URL 并读取 README
      const repoInfo = parseGitHubUrl(targetUrl);

      await this.githubService.getRepoInfo(repoInfo);

      const readmeContent = await this.githubService.readFile(repoInfo, 'README.md');

      if (!readmeContent) {
        console.warn(`⚠️  无法读取插件 "${identifier}" 的 README`);
        return {
          githubUrl: targetUrl,
          hasBadge: false,
          hasGitHub: true,
          identifier,
        };
      }

      // 检查 MCP Badge
      const hasBadge = this.checkMCPBadge(readmeContent, identifier);

      if (hasBadge) {
        console.log(`✅ 插件 "${identifier}" 已包含 MCP Badge`);
      } else {
        console.log(`❌ 插件 "${identifier}" 缺少 MCP Badge`);
        console.log(
          `   期望: [![MCP Badge](https://lobehub.com/badge/mcp-full/${identifier})](https://lobehub.com/mcp/${identifier})`,
        );
      }

      return {
        githubUrl: targetUrl,
        hasBadge,
        hasGitHub: true,
        identifier,
      };
    } catch (error) {
      console.error(`❌ 检查插件 "${identifier}" 失败:`, error);
      return {
        error: error instanceof Error ? error.message : '未知错误',
        hasBadge: false,
        hasGitHub: false,
        identifier,
      };
    }
  }
}
