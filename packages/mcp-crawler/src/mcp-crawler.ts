import { ManifestGenerator } from './generators/manifest';
import { GitHubService } from './services/github-service';
import { CrawlerOptions, GitHubRepo, MCPManifest } from './types';
import { logger, parseGitHubUrl } from './utils';

export class MCPCrawler {
  private githubService: GitHubService;
  private manifestGenerator: ManifestGenerator;
  private repoInfo: GitHubRepo;

  /**
   * 创建MCP爬虫
   * @param repositoryUrl GitHub仓库URL或"owner/repo"格式的字符串
   * @param options 爬虫选项
   */
  constructor(repositoryUrl: string, options: CrawlerOptions = {}) {
    // 解析仓库URL
    this.repoInfo = parseGitHubUrl(repositoryUrl);

    // 初始化服务
    this.githubService = new GitHubService(options.githubToken);
    this.manifestGenerator = new ManifestGenerator(options);
  }

  /**
   * 爬取GitHub仓库并生成MCP Manifest
   */
  async crawl(): Promise<MCPManifest | null> {
    try {
      // 记录开始时间
      const startTime = Date.now();

      logger.info(`🚀 开始爬取仓库 ${this.repoInfo.owner}/${this.repoInfo.repo}`);
      const repoURL = `https://github.com/${this.repoInfo.owner}/${this.repoInfo.repo}`;

      // 读取仓库关键文件
      const files = await this.githubService.readRepoKeyFiles(this.repoInfo);

      if (files.length === 0) {
        logger.error(`⚠️ 未找到有效的仓库文件，无法继续处理 ${repoURL}`);
        return null;
      }

      // 显示找到的文件列表（简洁模式）
      const fileNames = files.map((file) => file.path);
      logger.info(
        `📄 仓库地址: ${repoURL}, 获取到 ${files.length} 个文件: ${fileNames.slice(0, 5).join(', ')}${fileNames.length > 5 ? ` 等${fileNames.length}个文件` : ''}`,
      );

      // 生成Manifest
      logger.info(`🧠 开始分析仓库内容并生成 Manifest...`);
      const manifest = await this.manifestGenerator.generateManifest(this.repoInfo, files);

      // 计算处理时间
      const processingTime = (Date.now() - startTime) / 1000;

      if (manifest) {
        // 获取连接类型信息（从第一个或推荐的部署选项中获取）
        let connectionType = 'unknown';
        const recommendedOption = manifest.deploymentOptions.find((option) => option.isRecommended);
        const firstOption = manifest.deploymentOptions[0];
        const optionToLog = recommendedOption || firstOption;

        if (optionToLog) {
          connectionType = optionToLog.connection.type;
        }

        logger.success(
          `🎉 成功生成 ${this.repoInfo.owner}/${this.repoInfo.repo} 的 MCP Manifest ${manifest.name} (${connectionType}), ⏱️ 耗时: ${processingTime.toFixed(2)}秒`,
        );
      } else {
        // 检查是否是因为不是 MCP Server 而跳过
        const isSkipped =
          files.some((file) => file.path.toLowerCase() === 'readme.md') &&
          !this.manifestGenerator.isMCPServerFromReadme(
            files.find((file) => file.path.toLowerCase() === 'readme.md')?.content || '',
          );

        if (isSkipped) {
          logger.info(`⏭️ 跳过非 MCP Server 仓库，耗时: ${processingTime.toFixed(2)}秒`);
        } else {
          logger.error(`❌ 生成Manifest失败，耗时: ${processingTime.toFixed(2)}秒`);
          logger.info(`💡 可能原因: 该仓库不是MCP Server或没有足够的信息生成Manifest`);
        }
      }

      return manifest;
    } catch (error) {
      logger.error(`❌ 爬取失败`, error);
      return null;
    }
  }

  /**
   * 获取解析后的仓库信息
   */
  getRepoInfo(): GitHubRepo {
    return this.repoInfo;
  }
}
