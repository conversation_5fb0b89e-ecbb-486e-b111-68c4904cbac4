import { GitHubSearchResult, GitHubService, SearchOptions } from './services/github-service';
import { logger } from './utils';

export interface SearcherOptions {
  delayBetweenRequests?: number;
  githubToken?: string;
  maxPages?: number;
}

/**
 * MCP Server 搜索器
 * 专门用于搜索和发现 GitHub 上的 MCP Server 项目
 */
export class MCPSearcher {
  private githubService: GitHubService;
  private options: SearcherOptions;

  constructor(options: SearcherOptions = {}) {
    this.options = {
      delayBetweenRequests: 1000,
      maxPages: 5,
      ...options,
    };

    this.githubService = new GitHubService(options.githubToken);
  }

  /**
   * 基础搜索 - 使用默认关键词搜索 MCP Server
   */
  async search(options: SearchOptions = {}): Promise<GitHubSearchResult[]> {
    const searchOptions = {
      maxPages: this.options.maxPages,
      ...options,
    };

    logger.info('🚀 开始搜索 MCP Server 项目...');
    return await this.githubService.searchMCPServers(searchOptions);
  }

  /**
   * 关键词搜索 - 按特定关键词搜索 MCP Server
   */
  async searchByKeywords(
    keywords: string[],
    options: SearchOptions = {},
  ): Promise<GitHubSearchResult[]> {
    if (keywords.length === 0) {
      logger.warn('⚠️ 未提供关键词，使用基础搜索');
      return this.search(options);
    }

    const searchOptions = {
      maxPages: this.options.maxPages,
      ...options,
    };

    logger.info(`🚀 开始按关键词搜索 MCP Server: ${keywords.join(', ')}`);
    return await this.githubService.searchByKeywords(keywords, searchOptions);
  }

  /**
   * 获取搜索结果的统计信息
   */
  getSearchStats(results: GitHubSearchResult[]) {
    const languages = results.reduce(
      (acc, repo) => {
        acc[repo.language] = (acc[repo.language] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    const totalStars = results.reduce((sum, repo) => sum + repo.stars, 0);
    const avgStars = results.length > 0 ? Math.round(totalStars / results.length) : 0;

    return {
      avgStars,
      languages,
      topRepos: results
        .sort((a, b) => b.stars - a.stars)
        .slice(0, 5)
        .map((repo) => ({ name: repo.name, stars: repo.stars })),
      total: results.length,
      totalStars,
    };
  }

  /**
   * 导出搜索结果为 JSON
   */
  exportResults(results: GitHubSearchResult[], filename?: string): string {
    const stats = this.getSearchStats(results);
    const exportData = {
      results,
      searchTime: new Date().toISOString(),
      stats,
    };

    const jsonData = JSON.stringify(exportData, null, 2);

    if (filename) {
      // 这里可以根据需要添加文件保存逻辑
      logger.info(`📄 搜索结果已准备导出到: ${filename}`);
    }

    return jsonData;
  }
}
