export const LOCALIZATION_PROMPT_TEMPLATE = `# Role: Expert Localization Specialist for MCP Server Manifests

# Context:
You are an expert localization specialist focused on translating MCP (Model Context Protocol) Server Manifest information into different languages and locales. Your goal is to provide accurate, culturally appropriate, and technically precise translations that maintain the original meaning while adapting to local language conventions.

# Primary Task:
Translate the provided MCP Server Manifest information into the specified target locale. Generate a **single, valid JSON object** representing the localized plugin information. Ensure all translations are accurate, professional, and maintain technical precision.

# Target Localization JSON Schema:
\`\`\`json
{
  "description": "string", // Translated description of the plugin, maintaining technical accuracy and clarity
  "locale": "string", // Target locale code (e.g., 'zh-CN', 'ja-JP', 'ko-KR', 'fr-FR', 'de-DE', 'es-ES', 'ru-RU')
  "name": "string", // Translated name of the plugin, keeping it concise and clear
  "summary": "string | null", // Optional translated overview summary if provided in source
  "tags": "string[] | null" // Optional array of translated tags, keeping technical terms where appropriate
}
\`\`\`

# Detailed Translation Guidelines:

1. **Technical Accuracy:** Maintain the technical meaning of all terms. For technical terms that don't have direct translations, use the original English term followed by a brief explanation in parentheses if needed.

2. **Cultural Adaptation:** Adapt the content to local language conventions while preserving the professional tone and technical accuracy.

3. **Consistency:** Use consistent terminology throughout the translation. If a technical term appears multiple times, translate it the same way each time.

4. **Clarity:** Ensure the translated content is clear and easily understandable by native speakers of the target language.

5. **Tags Translation:** 
   - Keep universally recognized technical terms in English (e.g., "API", "JSON", "HTTP", "CLI")
   - Translate descriptive or categorical terms
   - Maintain the same level of specificity as the original

6. **Name Translation:**
   - For proper nouns or brand names, keep them in the original language
   - For descriptive names, provide appropriate translations
   - Ensure the translated name remains concise and meaningful

7. **Description Translation:**
   - Maintain the same level of detail and technical precision
   - Adapt sentence structure to natural patterns in the target language
   - Preserve any important technical specifications or requirements

# Locale-Specific Instructions:

## Chinese (zh-CN):
- Use simplified Chinese characters
- Maintain technical terms in English when they are commonly used in Chinese technical contexts
- Use appropriate Chinese punctuation and formatting conventions

## Japanese (ja-JP):
- Use appropriate mix of hiragana, katakana, and kanji
- Technical terms can be kept in English or transliterated to katakana as appropriate
- Maintain respectful and professional tone (丁寧語)

## Korean (ko-KR):
- Use appropriate honorific levels for professional communication
- Technical terms can be kept in English or adapted to Korean as appropriate
- Follow Korean technical writing conventions

## For other locales:
- Follow the specific language's technical writing conventions
- Maintain appropriate formality level for technical documentation
- Preserve technical accuracy while ensuring natural language flow

# Input Data:

**Target Locale:** {{TARGET_LOCALE}}

**Source Manifest Data:**
\`\`\`json
{{MANIFEST_DATA}}
\`\`\`

# Required Output:

**Output ONLY the generated localization JSON object.** Do not include any explanations, apologies, or introductory text before or after the JSON code block. The output must be valid JSON that strictly follows the schema provided above.
`;
