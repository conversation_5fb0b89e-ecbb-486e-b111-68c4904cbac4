import { cleanObject } from '@/utils/object';

import { AIService } from '../../services/ai-service';
import { LocalizationOptions, MCPManifest, PluginLocalization } from '../../types';
import { logger } from '../../utils';
import { LOCALIZATION_PROMPT_TEMPLATE } from './prompt';

/**
 * Localization生成器，使用AI服务从MCP Manifest生成多语言i18n翻译
 */
export class LocalizationGenerator {
  private aiService: AIService;
  private options: LocalizationOptions;

  constructor(options: LocalizationOptions = {}) {
    this.options = options;
    this.aiService = new AIService({
      apiKey: options.openaiApiKey,
      baseUrl: options.openaiBaseUrl,
      model: options.openaiModel,
    });
  }

  /**
   * 为指定的语言区域生成本地化翻译
   */
  async generateLocalization(
    manifest: MCPManifest,
    targetLocale: string,
  ): Promise<PluginLocalization | null> {
    if (!this.aiService.isReady()) {
      logger.error('AI服务未初始化，无法生成本地化翻译');
      return null;
    }

    try {
      // 提取需要翻译的源数据
      const sourceData = {
        description: manifest.description,
        name: manifest.name,
        summary: manifest.overview?.summary,
        tags: manifest.tags,
      };

      // 构建提示词
      const prompt = this.buildPrompt(sourceData, targetLocale);

      logger.info(`🌍 开始为语言区域 "${targetLocale}" 生成本地化翻译...`);

      // 调用AI服务
      const localization = await this.aiService.chatCompletionJSON<PluginLocalization>({
        messages: [{ content: prompt, role: 'user' }],
        model: this.options.openaiModel,
        systemMessage:
          'You are an expert localization specialist focused on technical documentation translation.',
        temperature: 0.3, // 稍微提高创造性以获得更自然的翻译
      });

      if (!localization) {
        logger.error('AI服务生成本地化翻译失败');
        return null;
      }

      // 验证返回的数据
      if (!localization.name || !localization.description || !localization.locale) {
        logger.error('生成的本地化数据缺少必要字段');
        return null;
      }

      // 确保locale字段正确
      localization.locale = targetLocale;

      logger.success(`✅ 为语言区域 "${targetLocale}" 生成本地化翻译成功`);

      return cleanObject(localization);
    } catch (error) {
      logger.error(`生成语言区域 "${targetLocale}" 的本地化翻译失败`, error);
      return null;
    }
  }

  /**
   * 批量生成多个语言区域的本地化翻译
   */
  async generateBatchLocalizations(
    manifest: MCPManifest,
    targetLocales: string[],
  ): Promise<PluginLocalization[]> {
    if (!this.aiService.isReady()) {
      logger.error('AI服务未初始化，无法生成本地化翻译');
      return [];
    }

    // 使用AI服务的批量处理功能
    return this.aiService.batchProcess(
      targetLocales,
      (locale) => this.generateLocalization(manifest, locale),
      {
        batchSize: 5,
        delayMs: 200,
      },
    );
  }

  /**
   * 使用默认的常用语言区域列表生成本地化翻译
   */
  async generateCommonLocalizations(manifest: MCPManifest): Promise<PluginLocalization[]> {
    const defaultLocales = this.options.targetLocales || [
      'zh-CN', // 简体中文
      'zh-TW', // 繁体中文
      'ja-JP', // 日语
      'ko-KR', // 韩语
      'fr-FR', // 法语
      'de-DE', // 德语
      'es-ES', // 西班牙语
      'ru-RU', // 俄语
      'pt-BR', // 巴西葡萄牙语
      'it-IT', // 意大利语
    ];

    return this.generateBatchLocalizations(manifest, defaultLocales);
  }

  /**
   * 构建本地化提示词
   */
  private buildPrompt(sourceData: any, targetLocale: string): string {
    // 使用提示词模板
    let prompt = LOCALIZATION_PROMPT_TEMPLATE;

    // 替换目标语言区域
    prompt = prompt.replace('{{TARGET_LOCALE}}', targetLocale);

    // 替换源数据
    const manifestDataJson = JSON.stringify(sourceData, null, 2);
    prompt = prompt.replace('{{MANIFEST_DATA}}', manifestDataJson);

    return prompt;
  }

  /**
   * 获取支持的语言区域列表
   */
  getSupportedLocales(): string[] {
    return [
      'zh-CN', // 简体中文
      'zh-TW', // 繁体中文
      'ja-JP', // 日语
      'ko-KR', // 韩语
      'fr-FR', // 法语
      'de-DE', // 德语
      'es-ES', // 西班牙语
      'ru-RU', // 俄语
      'pt-BR', // 巴西葡萄牙语
      'it-IT', // 意大利语
      'nl-NL', // 荷兰语
      'sv-SE', // 瑞典语
      'da-DK', // 丹麦语
      'no-NO', // 挪威语
      'fi-FI', // 芬兰语
      'pl-PL', // 波兰语
      'tr-TR', // 土耳其语
      'ar-SA', // 阿拉伯语
      'hi-IN', // 印地语
      'th-TH', // 泰语
      'vi-VN', // 越南语
    ];
  }

  /**
   * 验证语言区域代码是否有效
   */
  isValidLocale(locale: string): boolean {
    const supportedLocales = this.getSupportedLocales();
    return supportedLocales.includes(locale);
  }
}
