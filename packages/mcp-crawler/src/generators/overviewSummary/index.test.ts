import { beforeEach, describe, expect, it, vi } from 'vitest';

import { MCPManifest } from '../../types';
import { logger } from '../../utils';
import { OverviewGenerator } from './index';

// Mock logger to avoid console output during tests
vi.mock('../../utils', () => ({
  logger: {
    error: vi.fn(),
    info: vi.fn(),
    success: vi.fn(),
    warn: vi.fn(),
  },
}));

// Mock AIService
vi.mock('../../services/ai-service', () => ({
  AIService: vi.fn().mockImplementation(() => ({
    chatCompletionText: vi.fn(),
    isReady: vi.fn().mockReturnValue(true),
  })),
}));

describe('OverviewGenerator', () => {
  let generator: OverviewGenerator;
  let mockAIService: any;

  beforeEach(() => {
    // Clear all mocks before each test
    vi.clearAllMocks();

    // Create generator with mock options
    generator = new OverviewGenerator({
      apiKey: 'test-api-key',
    });

    // Get the mocked AIService instance
    mockAIService = (generator as any).aiService;
  });

  describe('generateOverview', () => {
    it('应该在 AI 服务未初始化时返回 null', async () => {
      // Mock isReady 返回 false
      mockAIService.isReady.mockReturnValue(false);

      const manifest: MCPManifest = {
        deploymentOptions: [],
        description: 'A test server',
        icon: 'test-icon',
        identifier: 'test-not-ready',
        isValidated: true,
        name: 'Test Not Ready Server',
        overview: { readme: 'test readme' },
        tools: [{ description: 'A test tool', name: 'test_tool' }],
        version: '1.0.0',
      };

      const result = await generator.generateOverview(manifest);

      expect(result).toBeNull();
      expect(logger.error).toHaveBeenCalledWith('AI服务未初始化，无法生成Overview');

      // 重置 mock 为默认状态
      mockAIService.isReady.mockReturnValue(true);
    });

    it('应该对未验证的 Manifest 返回 null', async () => {
      const unvalidatedManifest: MCPManifest = {
        deploymentOptions: [],
        description: 'A test server that is not validated',
        icon: 'test-icon',
        identifier: 'test-unvalidated',
        isValidated: false, // 关键：未验证
        name: 'Test Unvalidated Server',
        overview: { readme: 'test readme' },
        version: '1.0.0',
      };

      const result = await generator.generateOverview(unvalidatedManifest);

      expect(result).toBeNull();
      expect(logger.warn).toHaveBeenCalledWith(
        'Manifest "Test Unvalidated Server" 尚未验证，无法生成Overview',
      );
    });

    it('应该对已验证但无功能数据的 Manifest 返回 null', async () => {
      const emptyManifest: MCPManifest = {
        deploymentOptions: [],
        description: 'A test server with no tools/resources/prompts',
        icon: 'test-icon',
        identifier: 'test-empty',
        isValidated: true, // 已验证
        name: 'Test Empty Server',
        overview: { readme: 'test readme' },

        // 空数组
        prompts: [],

        // 空数组
        resources: [],
        tools: [], // 空数组
        version: '1.0.0',
      };

      const result = await generator.generateOverview(emptyManifest);

      expect(result).toBeNull();
      expect(logger.warn).toHaveBeenCalledWith(
        'Manifest "Test Empty Server" 没有可用的功能数据（tools/resources/prompts），无法生成Overview',
      );
    });

    it('应该对已验证且有工具数据的 Manifest 生成概述', async () => {
      const validManifest: MCPManifest = {
        deploymentOptions: [],
        description: 'A comprehensive test server with multiple capabilities',
        icon: 'test-icon',
        identifier: 'test-valid',
        isValidated: true, // 已验证
        name: 'Test Valid Server',
        overview: { readme: 'test readme' },
        tools: [
          {
            description: 'Create a new file with specified content',
            name: 'create_file',
            parameters: {
              properties: {
                content: { type: 'string' },
                path: { type: 'string' },
              },
              required: ['path', 'content'],
              type: 'object',
            },
          },
        ],
        version: '1.0.0',
      };

      const expectedOverview =
        'The Test Valid Server MCP Server is a comprehensive test server. You can:\n\n- File Management: Create files with specified content';

      mockAIService.chatCompletionText.mockResolvedValue(expectedOverview);

      const result = await generator.generateOverview(validManifest);

      expect(result).toBe(expectedOverview);
      expect(logger.info).toHaveBeenCalledWith('🎯 开始为 "Test Valid Server" 生成功能概述...');
      expect(logger.success).toHaveBeenCalledWith('✅ 为 "Test Valid Server" 生成功能概述成功');
    });

    it('应该对已验证且有资源数据的 Manifest 生成概述', async () => {
      const validManifest: MCPManifest = {
        deploymentOptions: [],
        description: 'A server with resources',
        icon: 'test-icon',
        identifier: 'test-resources',
        isValidated: true,
        name: 'Test Resources Server',
        overview: { readme: 'test readme' },
        resources: [
          {
            data: {},
            description: 'Current server configuration settings',
            id: 'config',
            name: 'Server Configuration',
            type: 'json',
          },
        ],
        version: '1.0.0',
      };

      const expectedOverview =
        'The Test Resources Server MCP Server provides configuration management. You can:\n\n- Configuration: Access server configuration settings';

      mockAIService.chatCompletionText.mockResolvedValue(expectedOverview);

      const result = await generator.generateOverview(validManifest);

      expect(result).toBe(expectedOverview);
    });

    it('应该对已验证且有提示词数据的 Manifest 生成概述', async () => {
      const validManifest: MCPManifest = {
        deploymentOptions: [],
        description: 'A server with prompts',
        icon: 'test-icon',
        identifier: 'test-prompts',
        isValidated: true,
        name: 'Test Prompts Server',
        overview: { readme: 'test readme' },
        prompts: [
          {
            description: 'Get help information about available commands',
            id: 'help',
            name: 'Help Prompt',
            text: 'What would you like help with?',
            variables: ['topic'],
          },
        ],
        version: '1.0.0',
      };

      const expectedOverview =
        'The Test Prompts Server MCP Server provides help functionality. You can:\n\n- Help System: Get information about available commands';

      mockAIService.chatCompletionText.mockResolvedValue(expectedOverview);

      const result = await generator.generateOverview(validManifest);

      expect(result).toBe(expectedOverview);
    });

    it('应该在 AI服务 调用失败时返回 null', async () => {
      const validManifest: MCPManifest = {
        deploymentOptions: [],
        description: 'A test server',
        icon: 'test-icon',
        identifier: 'test-api-error',
        isValidated: true,
        name: 'Test API Error Server',
        overview: { readme: 'test readme' },
        tools: [
          {
            description: 'A test tool',
            name: 'test_tool',
          },
        ],
        version: '1.0.0',
      };

      const error = new Error('API Error');
      mockAIService.chatCompletionText.mockRejectedValue(error);

      const result = await generator.generateOverview(validManifest);

      expect(result).toBeNull();
      expect(logger.error).toHaveBeenCalledWith(
        '生成 "Test API Error Server" 的功能概述失败',
        error,
      );
    });

    it('应该在 AI服务 返回空内容时返回 null', async () => {
      const validManifest: MCPManifest = {
        deploymentOptions: [],
        description: 'A test server',
        icon: 'test-icon',
        identifier: 'test-empty-response',
        isValidated: true,
        name: 'Test Empty Response Server',
        overview: { readme: 'test readme' },
        tools: [
          {
            description: 'A test tool',
            name: 'test_tool',
          },
        ],
        version: '1.0.0',
      };

      mockAIService.chatCompletionText.mockResolvedValue(null);

      const result = await generator.generateOverview(validManifest);

      expect(result).toBeNull();
      expect(logger.error).toHaveBeenCalledWith('AI服务生成概述失败');
    });
  });

  describe('updateManifestOverview', () => {
    it('应该成功更新 Manifest 的 overview.summary 字段', async () => {
      const validManifest: MCPManifest = {
        deploymentOptions: [],
        description: 'A comprehensive test server',
        icon: 'test-icon',
        identifier: 'test-update',
        isValidated: true,
        name: 'Test Update Server',
        overview: { readme: 'test readme' },
        tools: [
          {
            description: 'A test tool',
            name: 'test_tool',
          },
        ],
        version: '1.0.0',
      };

      const generatedOverview = 'The Test Update Server MCP Server provides testing functionality.';

      mockAIService.chatCompletionText.mockResolvedValue(generatedOverview);

      const result = await generator.updateManifestOverview(validManifest);

      expect(result).not.toBeNull();
      expect(result?.overview?.summary).toBe(generatedOverview);
      expect(result?.overview?.readme).toBe('test readme'); // 应该保留原有的 readme
    });

    it('应该在生成概述失败时返回 null', async () => {
      const invalidManifest: MCPManifest = {
        deploymentOptions: [],
        description: 'An invalid test server',
        icon: 'test-icon',
        identifier: 'test-update-fail',
        isValidated: false, // 未验证
        name: 'Test Update Fail Server',
        overview: { readme: 'test readme' },
        version: '1.0.0',
      };

      const result = await generator.updateManifestOverview(invalidManifest);

      expect(result).toBeNull();
    });

    it('应该为没有 overview 字段的 Manifest 创建 overview 对象', async () => {
      const manifestWithoutOverview: MCPManifest = {
        deploymentOptions: [],
        description: 'A test server without overview',
        icon: 'test-icon',
        identifier: 'test-no-overview',
        isValidated: true,
        name: 'Test No Overview Server',
        // 注意：没有 overview 字段
        tools: [
          {
            description: 'A test tool',
            name: 'test_tool',
          },
        ],
        version: '1.0.0',
      } as any;

      const generatedOverview =
        'The Test No Overview Server MCP Server provides testing functionality.';

      mockAIService.chatCompletionText.mockResolvedValue(generatedOverview);

      const result = await generator.updateManifestOverview(manifestWithoutOverview);

      expect(result).not.toBeNull();
      expect(result?.overview).toBeDefined();
      expect(result?.overview?.summary).toBe(generatedOverview);
      expect(result?.overview?.readme).toBe(''); // 应该设置为空字符串
    });
  });

  describe('constructor', () => {
    it('应该在没有 API 密钥时正确初始化', () => {
      const generatorWithoutKey = new OverviewGenerator({});

      // 验证初始化成功
      expect(generatorWithoutKey).toBeInstanceOf(OverviewGenerator);
    });

    it('应该在有 API 密钥时正确初始化', () => {
      const generatorWithKey = new OverviewGenerator({
        apiKey: 'test-key',
      });

      // 验证初始化成功（通过检查是否能调用方法而不抛出错误）
      expect(generatorWithKey).toBeInstanceOf(OverviewGenerator);
    });
  });

  describe('extractRelevantData', () => {
    it('应该正确提取相关数据', async () => {
      const manifest: MCPManifest = {
        deploymentOptions: [],
        description: 'Test description',
        icon: 'test-icon',
        identifier: 'test-extract',
        isValidated: true,
        name: 'Test Extract Server',
        overview: { readme: 'test readme' },
        prompts: [
          {
            description: 'Prompt description',
            id: 'test_prompt',
            name: 'Test Prompt',
            text: 'Test prompt text',
            variables: ['var1', 'var2'],
          },
        ],
        resources: [
          {
            data: {},
            description: 'Resource description',
            id: 'test_resource',
            name: 'Test Resource',
            type: 'json',
          },
        ],
        tools: [
          {
            description: 'Tool description',
            name: 'test_tool',
            parameters: {
              properties: {
                param1: { type: 'string' },
                param2: { type: 'number' },
              },
              required: ['param1'],
              type: 'object',
            },
          },
        ],
        version: '1.0.0',
      };

      const expectedOverview = 'Test overview';

      mockAIService.chatCompletionText.mockResolvedValue(expectedOverview);

      await generator.generateOverview(manifest);

      // 验证 AIService 被调用时传入了正确的数据结构
      expect(mockAIService.chatCompletionText).toHaveBeenCalledWith({
        maxTokens: 1500,
        messages: [
          {
            content: expect.stringContaining('"name": "Test Extract Server"'),
            role: 'user',
          },
        ],
        model: undefined,
        systemMessage:
          'You are an expert technical writer specialized in creating clear and concise MCP Server overviews.',
        temperature: 0.3,
      });

      // 检查传入的数据是否包含预期的字段
      const callArgs = vi.mocked(mockAIService.chatCompletionText).mock.calls[0][0];
      const userMessage = callArgs.messages[0].content;

      expect(userMessage).toContain('"description": "Test description"');
      expect(userMessage).toContain('"name": "Test Extract Server"');
      expect(userMessage).toContain('"tools"');
      expect(userMessage).toContain('"resources"');
      expect(userMessage).toContain('"prompts"');
      expect(userMessage).toContain('"test_tool"');
      expect(userMessage).toContain('"Test Resource"');
      expect(userMessage).toContain('"Test Prompt"');
    });
  });
});
