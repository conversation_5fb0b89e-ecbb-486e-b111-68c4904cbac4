export const OVERVIEW_PROMPT_TEMPLATE = `# Role: Expert Overview Generator for MCP Server Descriptions

# Task:
Generate a concise overview text for an MCP Server based on its validated tools, resources, and prompts. Follow the format examples below.

# Format Examples:

**Example 1:**
\`\`\`md
The Webflow MCP Server is a Node.js server enabling AI agents to interact with Webflow APIs to manage websites. You can:

- **Manage Sites**: List sites, get site details, and publish changes
- **Manage Pages**: List pages, access metadata/content, update settings
- **Manage Components**: List, get, and update component content and properties
- **Manage CMS**: Work with collections, create/update/publish collection items
\`\`\`

**Example 2:**
\`\`\`md
The PlayCanvas Editor MCP Server enables automation of the PlayCanvas Editor using an LLM.

- **Entity Management**: Create, modify, duplicate, reparent, delete, and list entities
- **Asset Management**: Create, list, delete, and instantiate various asset types
- **Scene Settings**: Query and modify scene settings, including render and physics configurations
- **Store Interaction**: Search, retrieve, and download assets from the PlayCanvas store
\`\`\`

# Guidelines:

1. **Opening**: Start with "The [Server Name] MCP Server is..." followed by a brief description
2. **Categories**: Group similar tools/resources/prompts into logical categories
3. **Actions**: List what users can accomplish with each category
4. **Concise**: Keep descriptions brief and action-oriented

# Processing:

1. Analyze the available tools, resources, and prompts
2. Group them into 3-5 logical functional categories
3. Write a brief description for each category focusing on user capabilities

# Input Data:

{{MANIFEST_DATA}}

# Output:

Generate only the overview text without any explanations or formatting.`;
