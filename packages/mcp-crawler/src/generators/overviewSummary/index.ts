import { AIService } from '../../services/ai-service';
import { CrawlerOptions, MCPManifest } from '../../types';
import { logger } from '../../utils';
import { OVERVIEW_PROMPT_TEMPLATE } from './prompt';

/**
 * Overview生成器，使用AI服务从MCP Manifest生成功能概述
 */
export class OverviewGenerator {
  private aiService: AIService;
  private options: CrawlerOptions;

  constructor(options: CrawlerOptions = {}) {
    this.options = options;
    this.aiService = new AIService({
      apiKey: options.apiKey,
      baseUrl: options.baseUrl,
      model: options.model,
    });
  }

  /**
   * 为MCP Manifest生成Overview概述
   */
  async generateOverview(manifest: MCPManifest): Promise<string | null> {
    if (!this.aiService.isReady()) {
      logger.error('AI服务未初始化，无法生成Overview');
      return null;
    }

    // 检查manifest是否已经验证过
    if (!manifest.isValidated) {
      logger.warn(`Manifest "${manifest.name}" 尚未验证，无法生成Overview`);
      return null;
    }

    // 检查是否有实际的功能数据
    const hasTools = manifest.tools && manifest.tools.length > 0;
    const hasResources = manifest.resources && manifest.resources.length > 0;
    const hasPrompts = manifest.prompts && manifest.prompts.length > 0;

    if (!hasTools && !hasResources && !hasPrompts) {
      logger.warn(
        `Manifest "${manifest.name}" 没有可用的功能数据（tools/resources/prompts），无法生成Overview`,
      );
      return null;
    }

    try {
      // 提取需要分析的数据
      const manifestData = this.extractRelevantData(manifest);

      // 构建提示词
      const prompt = this.buildPrompt(manifestData);

      logger.info(`🎯 开始为 "${manifest.name}" 生成功能概述...`);

      // 调用AI服务
      const overview = await this.aiService.chatCompletionText({
        // 稍微提高创造性以获得更自然的表达
        maxTokens: 1500,

        messages: [{ content: prompt, role: 'user' }],

        model: this.options.model,

        systemMessage:
          'You are an expert technical writer specialized in creating clear and concise MCP Server overviews.',
        temperature: 0.3, // 限制输出长度，确保概述简洁
      });

      if (!overview) {
        logger.error('AI服务生成概述失败');
        return null;
      }

      logger.success(`✅ 为 "${manifest.name}" 生成功能概述成功`);
      return overview;
    } catch (error) {
      logger.error(`生成 "${manifest.name}" 的功能概述失败`, error);
      return null;
    }
  }

  /**
   * 更新Manifest的overview.summary字段
   */
  async updateManifestOverview(manifest: MCPManifest): Promise<MCPManifest | null> {
    const overview = await this.generateOverview(manifest);

    if (!overview) {
      return null;
    }

    // 更新manifest的overview.summary字段
    const updatedManifest = { ...manifest };
    if (!updatedManifest.overview) {
      updatedManifest.overview = { readme: '' };
    }
    updatedManifest.overview.summary = overview;

    return updatedManifest;
  }

  /**
   * 提取Manifest中与Overview生成相关的数据
   */
  private extractRelevantData(manifest: MCPManifest): any {
    return {
      // 基本信息
      description: manifest.description,
      name: manifest.name,

      prompts: manifest.prompts?.map((prompt) => ({
        description: prompt.description,
        name: prompt.name,
        variables: prompt.variables || [],
      })),

      resources: manifest.resources?.map((resource) => ({
        description: resource.description,
        name: resource.name,
        type: resource.type,
      })),

      // 实际功能列表（已验证的功能数据）
      tools: manifest.tools?.map((tool) => ({
        description: tool.description,
        name: tool.name,
        // 只提取参数结构，不包含具体值
        parameters: tool.parameters
          ? {
              properties: tool.parameters.properties ? Object.keys(tool.parameters.properties) : [],
              required: tool.parameters.required || [],
            }
          : null,
      })),
    };
  }

  /**
   * 构建概述生成提示词
   */
  private buildPrompt(manifestData: any): string {
    // 使用提示词模板
    let prompt = OVERVIEW_PROMPT_TEMPLATE;

    // 替换manifest数据
    const manifestDataJson = JSON.stringify(manifestData, null, 2);
    prompt = prompt.replace('{{MANIFEST_DATA}}', manifestDataJson);

    return prompt;
  }

  /**
   * 验证生成的概述是否符合格式要求
   */
  private validateOverview(overview: string): boolean {
    if (!overview || overview.length < 50) {
      return false;
    }

    // 检查是否包含关键格式元素
    const hasServerName = overview.includes('MCP Server');
    const hasCapabilities = overview.includes(':') || overview.includes('You can');

    return hasServerName && hasCapabilities;
  }
}
