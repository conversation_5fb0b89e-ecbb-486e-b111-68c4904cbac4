import { AIService } from '../../services/ai-service';
import { GitHubService } from '../../services/github-service';
import { CrawlerOptions, GitHubFile, GitHubRepo, MCPManifest } from '../../types';
import { buildGitHubWebUrl, logger } from '../../utils';
import { PROMPT_TEMPLATE } from './prompt';

/**
 * Manifest生成器，使用AI服务从仓库内容生成MCP Manifest
 */
export class ManifestGenerator {
  private aiService: AIService;
  private options: CrawlerOptions;
  private githubService: GitHubService;

  constructor(options: CrawlerOptions = {}) {
    this.options = options;
    this.githubService = new GitHubService(options.githubToken);
    this.aiService = new AIService({
      apiKey: options.apiKey,
      baseUrl: options.baseUrl,
      model: options.model,
    });
  }

  /**
   * 从仓库文件生成MCP Manifest
   */
  async generateManifest(repo: GitHubRepo, files: GitHubFile[]): Promise<MCPManifest | null> {
    if (!this.aiService.isReady()) {
      logger.error('AI服务未初始化，无法生成Manifest');
      return null;
    }

    try {
      // 查找README文件
      const readmeFile = files.find((file) => file.path.toLowerCase() === 'readme.md');
      if (!readmeFile) {
        logger.warn(`[${repo.owner}/${repo.repo}] 未找到README.md文件，无法生成完整的Manifest`);
        return null;
      }

      // 检查是否为MCP Server，先查找README中的mcpServers配置
      const isMCPServer = this.isMCPServerFromReadme(readmeFile.content);

      if (!isMCPServer) {
        logger.warn(`仓库 ${repo.owner}/${repo.repo} 不是 MCP Server，跳过生成`);
        return null;
      }

      logger.info(`✅ 仓库 ${repo.owner}/${repo.repo} 是一个 MCP Server，开始生成 Manifest...`);

      // 查找package.json文件
      const packageJsonFile = files.find((file) => file.path === 'package.json');

      // 查找pyproject.toml文件
      const pyprojectTomlFile = files.find((file) => file.path === 'pyproject.toml');

      // 构建提示词
      const prompt = this.buildPrompt(readmeFile.content, packageJsonFile, pyprojectTomlFile, repo);

      // 调用AI服务
      const manifest = await this.aiService.chatCompletionJSON<MCPManifest>({
        messages: [{ content: prompt, role: 'user' }],
        model: this.options.model,
        systemMessage:
          'You are an expert at extracting MCP Server Manifest information from code repositories.',
        temperature: 0,
      });

      if (!manifest) {
        logger.error('AI服务生成Manifest失败');
        return null;
      }

      try {
        // 新增 overview 字段
        manifest.overview = {
          readme: readmeFile.content,
          // summary,
        };

        // 如果没有identifier，只使用仓库名作为identifier
        if (!manifest.identifier) {
          manifest.identifier = `${repo.repo}`.toLowerCase();
        }
        if (!manifest.icon) {
          manifest.icon = `https://github.com/${repo.owner}.png`;
        }

        // 始终使用仓库URL作为homepage
        manifest.homepage = buildGitHubWebUrl(repo);

        // 如果author.name是"Your Name"，使用仓库所有者名称替换
        if (manifest.author && manifest.author.name === 'Your Name') {
          manifest.author.name = repo.owner;
        }

        // 如果没有author.url，使用GitHub仓库所有者的URL
        if (manifest.author && !manifest.author.url) {
          manifest.author.url = `https://github.com/${repo.owner}`;
        }

        // 添加 GitHub 相关信息
        manifest.github = {
          url: buildGitHubWebUrl(repo),
        };

        // 获取仓库信息，包括 star 数、语言和许可证
        try {
          const repoInfo = await this.githubService.getRepoInfo(repo);
          manifest.github.stars = repoInfo.stargazers_count;
          manifest.github.language = repoInfo.language;
          manifest.github.license = repoInfo.license?.name;
        } catch (error) {
          logger.warn(`获取仓库信息失败: ${error}`);
        }

        // 解析构建产物信息
        manifest.artifacts = {};

        // 解析 npm 包信息
        if (packageJsonFile) {
          try {
            const packageJson = JSON.parse(packageJsonFile.content);
            manifest.artifacts.npm = {
              packageName: packageJson.name,
              version: packageJson.version,
            };
          } catch (error) {
            logger.warn(`解析 package.json 失败: ${error}`);
          }
        }

        // 解析 Python 包信息
        if (pyprojectTomlFile) {
          try {
            const pyprojectContent = pyprojectTomlFile.content;
            // 使用简单的正则表达式匹配包名和版本
            const nameMatch = pyprojectContent.match(/name\s*=\s*["']([^"']+)["']/);
            const versionMatch = pyprojectContent.match(/version\s*=\s*["']([^"']+)["']/);

            if (nameMatch || versionMatch) {
              manifest.artifacts.pypi = {
                packageName: nameMatch?.[1],
                version: versionMatch?.[1],
              };
            }
          } catch (error) {
            logger.warn(`解析 pyproject.toml 失败: ${error}`);
          }
        }

        // 查找 Dockerfile 并解析镜像信息
        const dockerfile = files.find((file) => file.path.toLowerCase() === 'dockerfile');
        if (dockerfile) {
          try {
            const dockerfileContent = dockerfile.content;
            // 查找 FROM 指令中的镜像名和标签
            const fromMatch = dockerfileContent.match(/FROM\s+([^\s:]+)(?::(\S+))?/);
            if (fromMatch) {
              manifest.artifacts.docker = {
                imageName: fromMatch[1],
                tag: fromMatch[2] || 'latest',
              };
            }
          } catch (error) {
            logger.warn(`解析 Dockerfile 失败: ${error}`);
          }
        }

        // 确保deploymentOptions存在
        if (
          !manifest.deploymentOptions ||
          !Array.isArray(manifest.deploymentOptions) ||
          manifest.deploymentOptions.length === 0
        ) {
          manifest.deploymentOptions = [
            {
              connection: {
                args: ['index.js'],
                command: 'node',
                type: 'stdio',
              },
              installationDetails: {
                gitRepo: buildGitHubWebUrl(repo),
              },
              installationMethod: 'git',
            },
          ];
        }

        // 移除所有null字段
        let updatedManifest = this.removeNullFields(manifest);

        logger.success('✅ 生成 MCP Manifest 成功');
        return updatedManifest;
      } catch (error) {
        logger.error('处理生成的Manifest失败', error);
        return null;
      }
    } catch (error) {
      logger.error('生成Manifest失败', error);
      return null;
    }
  }

  /**
   * 检查README中是否包含MCP Server配置
   */
  public isMCPServerFromReadme(readmeContent: string): boolean {
    // 1. 查找类似 mcpServers 的配置块
    const mcpServerConfigRegex = /["']mcpservers["']\s*:\s*{/i;

    return mcpServerConfigRegex.test(readmeContent.toLowerCase());
  }

  /**
   * 构建提示词
   */
  private buildPrompt(
    readmeContent: string,
    packageJsonFile?: GitHubFile,
    pyprojectTomlFile?: GitHubFile,
    repo?: GitHubRepo,
  ): string {
    // 截断README内容，防止token过长
    const truncatedReadme =
      readmeContent.length > 10_000 ? readmeContent.slice(0, 10_000) + '...' : readmeContent;

    // 使用提示词模板
    let prompt = PROMPT_TEMPLATE;

    // 替换README内容
    prompt = prompt.replace('{{README_CONTENT}}', truncatedReadme);

    // 添加repo url（如果有）
    if (repo) {
      const repoUrl = buildGitHubWebUrl(repo);
      prompt = prompt.replace('{{REPO_URL}}', repoUrl);
    } else {
      prompt = prompt.replace('{{REPO_URL}}', '');
    }

    // 添加package.json内容（如果有）
    if (packageJsonFile) {
      prompt = prompt.replace('{{PACKAGE_JSON_CONTENT}}', packageJsonFile.content);
    } else {
      prompt = prompt.replace('```json\n{{PACKAGE_JSON_CONTENT}}\n```', '');
    }

    // 添加pyproject.toml内容（如果有）
    if (pyprojectTomlFile) {
      prompt = prompt.replace('{{PYPROJECT_TOML_CONTENT}}', pyprojectTomlFile.content);
    } else {
      prompt = prompt.replace('```toml\n{{PYPROJECT_TOML_CONTENT}}\n```', '');
    }

    return prompt;
  }

  /**
   * 递归移除对象中的所有null值字段
   */
  private removeNullFields<T>(obj: T): T {
    if (obj === null || obj === undefined) {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map((item) => this.removeNullFields(item)) as unknown as T;
    }

    if (typeof obj === 'object') {
      const newObj = {} as any;

      Object.entries(obj as Record<string, any>).forEach(([key, value]) => {
        // 跳过null或undefined值
        if (value === null || value === undefined) {
          return;
        }

        // 递归处理对象或数组
        if (typeof value === 'object') {
          const cleanValue = this.removeNullFields(value);

          // 如果是空对象，也跳过
          if (Object.keys(cleanValue).length === 0 && !Array.isArray(cleanValue)) {
            return;
          }

          // 如果是空数组，也跳过
          if (Array.isArray(cleanValue) && cleanValue.length === 0) {
            return;
          }

          newObj[key] = cleanValue;
        } else {
          newObj[key] = value;
        }
      });

      return newObj as T;
    }

    return obj;
  }
}
