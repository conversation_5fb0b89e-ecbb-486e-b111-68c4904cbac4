# Gitignore for LobeHub
################################################################

# general
.DS_Store
.idea
.vscode
.history
.temp
.env.local
venv
temp
tmp
.windsurfrules

# dependencies
node_modules
*.log
*.lock
package-lock.json

# ci
coverage
.coverage
.eslintcache
.stylelintcache

# production
dist
es
lib
logs
test-output

# umi
.umi
.umi-production
.umi-test
.dumi/tmp*

# husky
.husky/prepare-commit-msg

# misc
# add other ignore file below

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
.next
.env
public/*.js
public/sitemap.xml
public/sitemap-index.xml
bun.lockb
sitemap*.xml
robots.txt

# Serwist
public/sw*
public/swe-worker*

*.patch
*.pdf
vertex-ai-key.json
.pnpm-store
outputs
server.txt
url.txt
seed.json
mcp_official_test.txt
skipped_urls.json
env.json