const fs = require('node:fs');
const path = require('node:path');

function convertJsonToEnv() {
  // 读取 env.json 文件
  const envData = JSON.parse(fs.readFileSync(path.join(__dirname, 'env.json'), 'utf8'));

  // 创建 .env.example 文件
  const envExampleContent = Object.entries(envData)
    .map(([service, variables]) => {
      const serviceComment = `# ${service}`;
      const variableLines = Object.keys(variables).map((varName) => `${varName}=`);
      return [serviceComment, ...variableLines, ''].join('\n');
    })
    .join('\n');

  fs.writeFileSync(path.join(__dirname, '.env.example'), envExampleContent);
  console.log('已成功生成 .env.example 文件');
}

convertJsonToEnv();
