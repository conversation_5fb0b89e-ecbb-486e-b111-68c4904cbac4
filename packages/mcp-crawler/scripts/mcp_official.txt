https://github.com/21st-dev/magic-mcp
https://github.com/Adfin-Engineering/mcp-server-adfin
https://github.com/tinyfish-io/agentql-mcp
https://github.com/agentrpc/agentrpc
https://github.com/Aiven-Open/mcp-aiven
https://github.com/apache/iotdb-mcp-server
https://github.com/apache/iotdb
https://github.com/apify/actors-mcp-server
https://github.com/apimatic/apimatic-validator-mcp
https://github.com/datastax/astra-db-mcp
https://github.com/atlanhq/agent-toolkit
https://github.com/AudienseCo/mcp-audiense-insights
https://github.com/awslabs/mcp
https://github.com/axiomhq/mcp-server-axiom
https://github.com/Bankless/onchain-mcp
https://github.com/ahnlabio/bicscan-mcp
https://github.com/bitrise-io/bitrise-mcp
https://github.com/box-community/mcp-server-box
https://github.com/browserbase/mcp-server-browserbase
https://github.com/chargebee/agentkit
https://github.com/chroma-core/chroma-mcp
https://github.com/ChronulusAI/chronulus-mcp
https://github.com/CircleCI-Public/mcp-server-circleci
https://github.com/ClickHouse/mcp-clickhouse
https://github.com/cloudflare/mcp-server-cloudflare
https://github.com/codacy/codacy-mcp-server
https://github.com/CodeLogicIncEngineering/codelogic-mcp-server
https://github.com/comet-ml/opik-mcp
https://github.com/comet-ml/opik
https://github.com/its-dart/dart-mcp-server
https://github.com/devhub/devhub-cms-mcp
https://github.com/e2b-dev/mcp-server
https://github.com/EduBase/MCP
https://github.com/elastic/mcp-server-elasticsearch
https://github.com/esignaturescom/mcp-server-esignatures
https://github.com/exa-labs/exa-mcp-server
https://github.com/Fewsats/fewsats-mcp
https://github.com/Fibery-inc/fibery-mcp-server
https://github.com/financial-datasets/mcp-server
https://github.com/mendableai/firecrawl-mcp-server
https://github.com/fireproof-storage/mcp-database-server
https://github.com/oschina/mcp-gitee
https://github.com/gotohuman/gotohuman-mcp-server
https://github.com/grafana/mcp-grafana
https://github.com/graphlit/graphlit-mcp-server
https://github.com/GreptimeTeam/greptimedb-mcp-server
https://github.com/GreptimeTeam/greptimedb
https://github.com/heroku/heroku-mcp-server
https://github.com/aliyun/alibabacloud-hologres-mcp-server
https://github.com/hyperbrowserai/mcp
https://github.com/IBM/wxflows
https://github.com/jamsocket/forevervm
https://github.com/elie222/inbox-zero
https://github.com/inkeep/mcp-server-python
https://github.com/integration-app/mcp-server
https://github.com/JetBrains/mcp-jetbrains
https://github.com/kagisearch/kagimcp
https://github.com/keboola/keboola-mcp-server
https://github.com/translated/lara-mcp
https://github.com/pydantic/logfire-mcp
https://github.com/langfuse/mcp-server-langfuse
https://github.com/lingodotdev/lingo.dev
https://github.com/mailgun/mailgun-mcp-server
https://github.com/integromat/make-mcp-server
https://github.com/meilisearch/meilisearch-mcp
https://github.com/metoro-io/metoro-mcp-server
https://github.com/zilliztech/mcp-server-milvus
https://github.com/momentohq/mcp-momento
https://github.com/rember/rember-mcp
https://github.com/motherduckdb/mcp-server-motherduck
https://github.com/needle-ai/needle-mcp
https://github.com/neo4j-contrib/mcp-neo4j
https://github.com/neondatabase-labs/mcp-server-neon
https://github.com/oceanbase/mcp-oceanbase
https://github.com/OctagonAI/octagon-mcp-server
https://github.com/oxylabs/oxylabs-mcp
https://github.com/riza-io/riza-mcp
https://github.com/PaddleHQ/paddle-mcp-server
https://github.com/ppl-ai/modelcontextprotocol
https://github.com/redis/mcp-redis
https://github.com/redis/mcp-redis-cloud
https://github.com/qdrant/mcp-server-qdrant
https://github.com/ramp-public/ramp_mcp
https://github.com/MindscapeHQ/mcp-server-raygun
https://github.com/fatwang2/search1api-mcp
https://github.com/screenshotone/mcp
https://github.com/semgrep/mcp
https://github.com/singlestore-labs/mcp-server-singlestore
https://github.com/StarRocks/mcp-server-starrocks
https://github.com/stripe/agent-toolkit
https://github.com/tavily-ai/tavily-mcp
https://github.com/thirdweb-dev/ai
https://github.com/tinybirdco/mcp-tinybird
https://github.com/unifai-network/unifai-mcp-server
https://github.com/Unstructured-IO/UNS-MCP
https://github.com/vectorize-io/vectorize-mcp-server
https://github.com/Verodat/verodat-mcp-server
https://github.com/VeyraX/veyrax-mcp
https://github.com/XeroAPI/xero-mcp-server
https://github.com/zenml-io/mcp-zenml
https://github.com/Simon-Kansara/ableton-live-mcp-server
https://github.com/openbnb-org/mcp-server-airbnb
https://github.com/baryhuang/mcp-server-aws-resources-python
https://github.com/AI-Agent-Hub/ai-agent-marketplace-index-mcp
https://github.com/GoPlausible/algorand-mcp
https://github.com/yangkyeongmo/mcp-server-apache-airflow
https://github.com/domdomegg/airtable-mcp-server
https://github.com/felores/airtable-mcp
https://github.com/calvernaz/alphavantage
https://github.com/donghyun-chae/mcp-amadeus
https://github.com/scorzeth/anki-mcp-server
https://github.com/pyroprompts/any-chat-completions-mcp
https://github.com/Omar-V2/mcp-ical
https://github.com/ravenwits/mcp-server-arangodb
https://github.com/vishalmysore/choturobo
https://github.com/sooperset/mcp-atlassian
https://github.com/co-browser/attestable-mcp-server
https://github.com/rishikavikondala/mcp-server-aws
https://github.com/lishenxydlgzs/aws-athena-mcp
https://github.com/aarora79/aws-cost-explorer-mcp-server
https://github.com/whataboutyou-ai/eunomia-mcp-server
https://github.com/aws-samples/sample-mcp-server-s3
https://github.com/pab1it0/adx-mcp-server
https://github.com/Vortiago/mcp-azure-devops
https://github.com/baidubce/app-builder
https://github.com/magnetai/mcp-free-usdc-transfer
https://github.com/basicmachines-co/basic-memory
https://github.com/LucasHild/mcp-server-bigquery
https://github.com/bazinga012/mcp_code_executor
https://github.com/ergut/mcp-bigquery-server
https://github.com/leehanchung/bing-search-mcp
https://github.com/lloydzhou/bitable-mcp
https://github.com/ahujasid/blender-mcp
https://github.com/co-browser/browser-use-mcp-server
https://github.com/TermiX-official/bsc-mcp
https://github.com/githejie/mcp-server-calculator
https://github.com/lenwood/cfbd-mcp-server
https://github.com/reading-plus-ai/mcp-server-data-exploration
https://github.com/AI-QL/chat-mcp
https://github.com/chatmcp/mcp-server-chatsum
https://github.com/pab1it0/chess-mcp
https://github.com/privetin/chroma
https://github.com/ZilongXue/claude-post
https://github.com/felores/cloudinary-mcp-server
https://github.com/stippi/code-assistant
https://github.com/Automata-Labs-team/code-sandbox-mcp
https://github.com/topoteretes/cognee
https://github.com/longmans/coin_api_mcp
https://github.com/ivo-toby/contentful-mcp
https://github.com/kukapay/crypto-feargreed-mcp
https://github.com/kukapay/cryptopanic-mcp-server
https://github.com/DappierAI/dappier-mcp
https://github.com/JordiNeil/mcp-databricks-server
https://github.com/GeLi2001/datadog-mcp-server
https://github.com/privetin/dataset-viewer
https://github.com/bytebase/dbhub
https://github.com/DMontgomery40/deepseek-mcp-server
https://github.com/66julienmartin/MCP-server-Deepseek_R1
https://github.com/ruixingshi/deepseek-thinker-mcp
https://github.com/descope-sample-apps/descope-mcp-server-stdio
https://github.com/kpsunil97/devrev-mcp-server
https://github.com/ChristianHinge/dicom-mcp
https://github.com/YanxingLiu/dify-mcp-server
https://github.com/v-3/discordmcp
https://github.com/SaseQ/discord-mcp
https://github.com/AshDevFr/discourse-mcp-server
https://github.com/ckreiling/mcp-server-docker
https://github.com/Omedia/mcp-server-drupal
https://github.com/kukapay/dune-analytics-mcp
https://github.com/TencentEdgeOne/edgeone-pages-mcp
https://github.com/cr7258/elasticsearch-mcp-server
https://github.com/mamertofabian/elevenlabs-mcp-server
https://github.com/mcpdotdirect/evm-mcp-server
https://github.com/mamertofabian/mcp-everything-search
https://github.com/haris-musa/excel-mcp-server
https://github.com/rishijatia/fantasy-pl-mcp
https://github.com/fastnai/mcp-fastn
https://github.com/zcaceres/fetch-mcp
https://github.com/GLips/Figma-Context-MCP
https://github.com/gannonh/firebase-mcp
https://github.com/mendableai/firecrawl-mcp-server
https://github.com/sunsetcoder/flightradar24-mcp-server
https://github.com/MFYDev/ghost-mcp
https://github.com/ko1ynnky/github-actions-mcp-server
https://github.com/longyi1207/glean-mcp-server
https://github.com/GongRzhe/Gmail-MCP-Server
https://github.com/baryhuang/mcp-headless-gmail
https://github.com/hichana/goalstory-mcp
https://github.com/goat-sdk/goat
https://github.com/Coding-Solo/godot-mcp
https://github.com/mark3labs/mcp-filesystem-server
https://github.com/VectorInstitute/mcp-goodnews
https://github.com/v-3/google-calendar
https://github.com/nspady/google-calendar-mcp
https://github.com/adenot/mcp-google-search
https://github.com/zcaceres/gtasks-mcp
https://github.com/hannesj/mcp-graphql-schema
https://github.com/horizondatawave/hdw-mcp-server
https://github.com/heurist-network/heurist-mesh-mcp-server
https://github.com/heurist-network/heurist-agent-framework
https://github.com/syucream/holaspirit-mcp-server
https://github.com/tevonsb/homeassistant-mcp
https://github.com/voska/hass-mcp
https://github.com/peakmojo/mcp-hubspot
https://github.com/evalstate/mcp-hfspace
https://github.com/mektigboy/server-hyperliquid
https://github.com/iflytek/ifly-workflow-mcp-server
https://github.com/GongRzhe/Image-Generation-MCP-Server
https://github.com/idoru/influxdb-mcp-server
https://github.com/sergehuber/inoyu-mcp-unomi-server
https://github.com/raoulbia-ai/mcp-server-for-intercom
https://github.com/InditexTech/mcp-server-simulator-ios-idb
https://github.com/ferrislucas/iterm-mcp
https://github.com/quarkiverse/quarkus-mcp-servers
https://github.com/GongRzhe/JSON-MCP-Server
https://github.com/lamaalrajih/kicad-mcp
https://github.com/ChristophEnglisch/keycloak-model-context-protocol
https://github.com/kiwamizamurai/mcp-kibela-server
https://github.com/macrat/mcp-server-kintone
https://github.com/Kong/mcp-konnect
https://github.com/Flux159/mcp-server-kubernetes
https://github.com/manusa/kubernetes-mcp-server
https://github.com/GongRzhe/Langflow-DOC-QA-SERVER
https://github.com/syucream/lightdash-mcp-server
https://github.com/jerhadf/linear-mcp-server
https://github.com/geropl/linear-mcp-go
https://github.com/amornpan/py-mcp-line
https://github.com/run-llama/mcp-server-llamacloud
https://github.com/cyberchitta/llm-context.py
https://github.com/carterlasalle/mac_messages_mcp
https://github.com/abel9851/mcp-server-mariadb
https://github.com/maton-ai/agent-toolkit
https://github.com/liuyoshio/mcp-compass
https://github.com/tesla0225/mcp-create
https://github.com/anaisbetts/mcp-installer
https://github.com/strowk/mcp-k8s-go
https://github.com/nkapila6/mcp-local-rag
https://github.com/sparfenyuk/mcp-proxy
https://github.com/mem0ai/mem0-mcp
https://github.com/unibaseio/membase-mcp
https://github.com/smithery-ai/mcp-obsidian
https://github.com/JexinSam/mssql_mcp_server
https://github.com/amornpan/py-mcp-mssql
https://github.com/daobataotie/mssql-mcp
https://github.com/zcaceres/markdownify-mcp
https://github.com/InditexTech/mcp-teams-server
https://github.com/YuChenSSR/mindmap-mcp-server
https://github.com/dmayboroda/minima
https://github.com/mobile-next/mobile-mcp
https://github.com/kiliczsh/mcp-mongo-server
https://github.com/furey/mongodb-lens
https://github.com/sakce/mcp-server-monday
https://github.com/yanmxa/multicluster-mcp-server
https://github.com/benborla/mcp-server-mysql
https://github.com/designcomputer/mysql_mcp_server
https://github.com/leonardsellem/n8n-mcp-server
https://github.com/ProgramComputer/NASA-MCP-server
https://github.com/stefanoamorelli/nasdaq-data-link-mcp
https://github.com/KyrieTangSheng/mcp-server-nationalparks
https://github.com/pfldy2850/py-mcp-naver
https://github.com/r-huijts/ns-mcp-server
https://github.com/da-okazaki/mcp-neo4j-server
https://github.com/bigcodegen/mcp-neovim-server
https://github.com/suekou/mcp-notion-server
https://github.com/v-3/notion-server
https://github.com/teddyzxcv/ntfy-mcp
https://github.com/oatpp/oatpp-mcp
https://github.com/StevenStavrakis/obsidian-mcp
https://github.com/yuanoOo/oceanbase_mcp_server
https://github.com/kapilduraphe/okta-mcp-server
https://github.com/rajvirtual/MCP-Servers
https://github.com/ConechoAI/openai-websearch-mcp
https://github.com/janwilmake/openapi-mcp-server
https://github.com/baryhuang/mcp-server-any-openapi
https://github.com/deepfates/mcp-replicate
https://github.com/hannesj/mcp-openapi-schema
https://github.com/Spathodea-Network/opencti-mcp
https://github.com/asusevski/opendota-mcp-server
https://github.com/shanejonas/openrpc-mpc-server
https://github.com/open-strategy-partners/osp_marketing_tools
https://github.com/vivekVells/mcp-pandoc
https://github.com/hungryrobot1/MCP-PIF
https://github.com/sirmews/mcp-pinecone
https://github.com/felores/placid-mcp-server
https://github.com/executeautomation/mcp-playwright
https://github.com/shannonlal/mcp-postman
https://github.com/kenjihikmatullah/productboard-mcp
https://github.com/pab1it0/prometheus-mcp-server
https://github.com/dogukanakkaya/pulumi-mcp-server
https://github.com/AshikNesin/pushover-mcp
https://github.com/jjsantos01/qgis_mcp
https://github.com/GongRzhe/Quickchart-MCP-Server
https://github.com/66julienmartin/MCP-server-Qwen_Max
https://github.com/kenliao94/mcp-server-rabbitmq
https://github.com/apify/mcp-server-rag-web-browser
https://github.com/dschuler36/reaper-mcp-server
https://github.com/GongRzhe/REDIS-MCP-Server
https://github.com/prajwalnayak7/mcp-server-redis
https://github.com/skydeckai/mcp-server-rememberizer
https://github.com/xxxbrian/mcp-rquest
https://github.com/r-huijts/rijksmuseum-mcp
https://github.com/jifrozen0110/mcp-riot
https://github.com/smn2gnt/MCP-Salesforce
https://github.com/adityak74/mcp-scholarly
https://github.com/cyberchitta/scrapling-fetch-mcp
https://github.com/ihor-sokoliuk/mcp-searxng
https://github.com/stefanoamorelli/sec-edgar-mcp
https://github.com/osomai/servicenow-mcp
https://github.com/GeLi2001/shopify-mcp
https://github.com/dvcrn/mcp-server-siri-shortcuts
https://github.com/isaacwasserman/mcp-snowflake-server
https://github.com/yeonupark/mcp-soccer-data
https://github.com/sendaifun/solana-agent-kit
https://github.com/varunneal/spotify-mcp
https://github.com/starwind-ui/starwind-ui-mcp
https://github.com/atharvagupta2003/mcp-stripe
https://github.com/wilsonchenghy/ShaderToy-MCP
https://github.com/Laksh-star/mcp-server-tmdb
https://github.com/RamXX/mcp-tavily
https://github.com/chigwell/telegram-mcp
https://github.com/chaindead/telegram-mcp
https://github.com/GongRzhe/terminal-controller-mcp
https://github.com/GeLi2001/tft-mcp-server
https://github.com/delorenj/mcp-server-ticketmaster
https://github.com/alexarevalo9/ticktick-mcp-server
https://github.com/abhiz123/todoist-mcp-server
https://github.com/suhail-ak-s/mcp-typesense-server
https://github.com/GongRzhe/TRAVEL-PLANNER-MCP-Server
https://github.com/ognis1205/mcp-server-unitycatalog
https://github.com/CoderGamester/mcp-unity
https://github.com/quazaai/UnityMCPIntegration
https://github.com/isaacwasserman/mcp-vegalite-server
https://github.com/burningion/video-editing-mcp
https://github.com/mfukushim/map-traveler-mcp
https://github.com/dinghuazhou/sample-mcp-server-tos
https://github.com/wanaku-ai/wanaku
https://github.com/kapilduraphe/webflow-mcp-server
https://github.com/kukapay/whale-tracker-mcp
https://github.com/bharathvaj-ganesan/whois-mcp
https://github.com/zzaebok/mcp-wikidata
https://github.com/wildfly-extras/wildfly-mcp
https://github.com/SimonB97/win-cli-mcp-server
https://github.com/ZubeidHendricks/youtube-mcp-server
https://github.com/anshumax/world_bank_mcp_server
https://github.com/EnesCinr/twitter-mcp
https://github.com/vidhupv/x-mcp
https://github.com/ShenghaiWang/xcodebuild
https://github.com/john-zhang-dev/xero-mcp
https://github.com/XGenerationLab/xiyan_mcp_server
https://github.com/apeyroux/mcp-xmind
https://github.com/Klavis-AI/klavis
https://github.com/isdaniel/mcp_weather_server
https://github.com/zcaceres/easy-mcp
https://github.com/tadata-org/fastapi_mcp
https://github.com/punkpeye/fastmcp
https://github.com/strowk/foxy-contexts
https://github.com/alibaba/higress
https://github.com/quarkiverse/quarkus-mcp-server
https://github.com/mcpdotdirect/template-mcp-server
https://github.com/marimo-team/codemirror-mcp
https://github.com/badkk/awesome-crypto-mcp-servers
https://github.com/appcypher/awesome-mcp-servers
https://github.com/punkpeye/awesome-mcp-servers
https://github.com/wong2/awesome-mcp-servers
https://github.com/mcp-router/mcp-router
https://github.com/mcpx-dev/mcp-badges
https://github.com/apappascs/mcp-servers-hub
https://github.com/wong2/mcp-cli
https://github.com/eqtylab/mcp-guardian
https://github.com/pathintegral-institute/mcpm.sh
https://github.com/zueai/mcp-manager
https://github.com/Jeamee/MCPHub-Desktop
https://github.com/chatmcp/mcp-directory