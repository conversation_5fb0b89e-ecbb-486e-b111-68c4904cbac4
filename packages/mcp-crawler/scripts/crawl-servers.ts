import * as dotenv from 'dotenv';
import { access, constants, mkdir, readFile, writeFile } from 'node:fs/promises';
import { join } from 'node:path';
import pMap from 'p-map';

import { MCPCrawler } from '../src';

// 加载环境变量
dotenv.config({ path: '../.env' });

// ===== 配置区域 =====
// URL文件路径配置（可添加多个文件路径）
const URL_FILES = [
  join(__dirname, './mcp_official.txt'),
  // join(__dirname, './mcp_official_test.txt'),
  join(__dirname, './url.txt'),
];
// ======= END =======

const baseOutputDir = join(__dirname, './outputs');
const tmpDir = join(__dirname, './_tmp');
const skipCacheFilePath = join(tmpDir, 'skipped_urls.json');

// 配置并发数量
const CONCURRENT_LIMIT = 5;

/**
 * 缓存已跳过URL的接口
 */
interface SkipCache {
  skippedUrls: Record<string, { reason: string; timestamp: number }>;
}

/**
 * 确保临时目录存在
 */
const ensureTmpDir = async (): Promise<void> => {
  try {
    await access(tmpDir, constants.F_OK);
  } catch {
    await mkdir(tmpDir, { recursive: true });
  }
};

/**
 * 格式化耗时显示
 */
const formatDuration = (ms: number): string => {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);

  if (hours > 0) {
    return `${hours}小时${minutes % 60}分${seconds % 60}秒`;
  }
  if (minutes > 0) {
    return `${minutes}分${seconds % 60}秒`;
  }
  return `${seconds}秒`;
};

/**
 * 从文本文件中读取GitHub URLs，每行一个URL
 */
const readGithubUrlsFromFile = async (filePath: string): Promise<string[]> => {
  try {
    const content = await readFile(filePath, 'utf8');
    // 按行分割，过滤掉空行和注释行
    return content
      .split('\n')
      .map((line) => line.trim())
      .filter((line) => line && !line.startsWith('#'));
  } catch (error) {
    console.error(`读取URL文件 ${filePath} 失败:`, error);
    return [];
  }
};

/**
 * 从多个文件中读取GitHub URLs
 */
const readGithubUrlsFromFiles = async (filePaths: string[]): Promise<string[]> => {
  const allUrls: string[] = [];

  for (const filePath of filePaths) {
    const urls = await readGithubUrlsFromFile(filePath);
    console.log(`从 ${filePath} 读取到 ${urls.length} 个GitHub URLs`);
    allUrls.push(...urls);
  }

  // 去重
  const uniqueUrls = Array.from(new Set(allUrls));
  console.log(`总共读取到 ${uniqueUrls.length} 个唯一GitHub URLs`);
  return uniqueUrls;
};

/**
 * 读取跳过的URL缓存
 */
const readSkipCache = async (): Promise<SkipCache> => {
  try {
    await access(skipCacheFilePath, constants.F_OK);
    const content = await readFile(skipCacheFilePath, 'utf8');
    return JSON.parse(content) as SkipCache;
  } catch {
    // 如果文件不存在或解析失败，返回空缓存
    return { skippedUrls: {} };
  }
};

/**
 * 更新跳过的URL缓存
 */
const updateSkipCache = async (skipCache: SkipCache): Promise<void> => {
  await writeFile(skipCacheFilePath, JSON.stringify(skipCache, null, 2));
};

/**
 * 处理单个GitHub URL的爬取任务
 */
const processGithubUrl = async (
  githubUrl: string,
  currentIndex: number,
  totalUrls: number,
  forceRefresh: boolean,
  skipCache: SkipCache,
): Promise<void> => {
  try {
    // 通过githubUrl推测manifest.id
    const repoPath = new URL(githubUrl).pathname.slice(1); // 移除开头的'/'
    const safeId = repoPath.replaceAll('/', '-');
    // 添加前缀序号到文件名
    const prefixedId = `${String(currentIndex).padStart(2, '0')}-${safeId}`;
    const fileName = `${prefixedId}.json`;
    const outputFilePath = join(baseOutputDir, fileName);

    // 检查URL是否在跳过缓存中
    if (!forceRefresh && skipCache.skippedUrls[githubUrl]) {
      console.log(
        `[${currentIndex}/${totalUrls}] URL ${githubUrl} 已在缓存中标记为跳过（原因: ${skipCache.skippedUrls[githubUrl].reason}），跳过爬取`,
      );
      return;
    }

    // 先检查文件是否已存在
    try {
      await access(outputFilePath, constants.F_OK);
      // 文件已存在
      if (!forceRefresh) {
        console.log(`[${currentIndex}/${totalUrls}] 文件 ${outputFilePath} 已存在，跳过爬取`);
        // 记录到跳过缓存
        skipCache.skippedUrls[githubUrl] = {
          reason: '文件已存在',
          timestamp: Date.now(),
        };
        await updateSkipCache(skipCache);
        return; // 跳过这个URL的后续处理
      }
      console.log(
        `[${currentIndex}/${totalUrls}] 文件已存在，但将更新 ${githubUrl} 的Manifest文件: ${fileName}`,
      );
    } catch {
      // 文件不存在，首次创建
      console.log(`[${currentIndex}/${totalUrls}] 将创建 ${githubUrl} 的Manifest文件: ${fileName}`);
    }

    // 使用MCPCrawler
    const crawler = new MCPCrawler(githubUrl);
    const manifest = await crawler.crawl();

    if (manifest && manifest.identifier) {
      // 将爬取的清单写入.json文件
      await writeFile(outputFilePath, JSON.stringify(manifest, null, 2));
      console.log(
        `[${currentIndex}/${totalUrls}] 为 ${githubUrl} 生成的Manifest文件(ID: ${manifest.identifier})`,
      );

      // 如果之前在跳过缓存中，现在移除
      if (skipCache.skippedUrls[githubUrl]) {
        delete skipCache.skippedUrls[githubUrl];
        await updateSkipCache(skipCache);
      }
    } else {
      console.error(`[${currentIndex}/${totalUrls}] 生成 ${githubUrl} 的Manifest失败或缺少ID字段`);
    }
  } catch (crawlError) {
    console.error(
      `[${currentIndex}/${totalUrls}] 爬取或写入 ${githubUrl} 的清单时出错:`,
      crawlError,
    );
    throw crawlError; // 重新抛出错误，让 p-map 处理
  }
};

async function main() {
  // 记录开始时间
  const startTime = Date.now();

  // 确保临时目录存在
  await ensureTmpDir();

  // 处理命令行参数
  const forceRefresh = process.argv.includes('--force');

  // 使用顶部配置的URL文件路径
  const urlFiles = URL_FILES;

  // 获取GitHub URLs
  const githubUrls = await readGithubUrlsFromFiles(urlFiles);

  if (githubUrls.length === 0) {
    console.error('没有找到任何GitHub URLs。请检查配置的URL文件');
    // eslint-disable-next-line unicorn/no-process-exit
    process.exit(1);
  }

  // 读取跳过缓存
  const skipCache = await readSkipCache();
  console.log(`从缓存中读取到 ${Object.keys(skipCache.skippedUrls).length} 个已跳过的URLs`);

  try {
    // 确保输出目录存在
    await mkdir(baseOutputDir, { recursive: true });

    // 使用 p-map 并发处理所有URL
    await pMap(
      githubUrls,
      async (githubUrl, index) => {
        const currentIndex = index + 1;
        await processGithubUrl(githubUrl, currentIndex, githubUrls.length, forceRefresh, skipCache);
      },
      {
        concurrency: CONCURRENT_LIMIT,
        stopOnError: false, // 即使某个任务失败也继续处理其他任务
      },
    );

    // 计算总耗时
    const endTime = Date.now();
    const totalDuration = endTime - startTime;

    console.log('完成所有仓库的处理。');
    console.log(`总耗时: ${formatDuration(totalDuration)}`);
    console.log(`平均每个仓库耗时: ${formatDuration(totalDuration / githubUrls.length)}`);
  } catch (error) {
    console.error('爬取过程中出错:', error);
  }
}

await main();
