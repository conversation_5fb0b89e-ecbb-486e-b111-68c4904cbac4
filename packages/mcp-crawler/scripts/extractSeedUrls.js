// 读取 seed.json 文件
// 转成 mcp official

const fs = require('node:fs');
const path = require('node:path');

const seedPath = path.join(__dirname, 'seed.json');
const outputPath = path.join(__dirname, './mcp_official.txt');

try {
  // 读取 JSON 文件
  const data = fs.readFileSync(seedPath, 'utf8');
  const repositories = JSON.parse(data);

  // 提取所有仓库 URL
  const urls = repositories
    .filter((repo) => repo.repository && repo.repository.url) // 确保存在 repository.url
    .map((repo) => repo.repository.url); // 提取 URL

  // 将 URL 写入文件，每行一个 URL
  fs.writeFileSync(outputPath, urls.join('\n'), 'utf8');

  console.log(`成功提取 ${urls.length} 个仓库 URL 到 mcp_official.txt 文件`);
} catch (error) {
  console.error('处理过程中出错:', error);
}
