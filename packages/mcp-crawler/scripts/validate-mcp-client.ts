/* eslint-disable */
/**
 * MCP客户端校验器示例
 *
 * 这个示例演示如何验证outputs目录下所有MCP服务器Manifest文件，
 * 并更新它们的capabilities、tools、prompts和resources信息。
 * 已经验证过的文件(isValidated=true)会被跳过，除非使用--force参数。
 */
import { config } from 'dotenv';
import fs from 'node:fs';
import path from 'node:path';

import { MCPManifest, MCPValidator } from '../src';

// 加载环境变量
config();

// 检查缺失的环境变量
function checkMissingEnvVars(manifest: MCPManifest): string[] {
  const missingEnvVars: string[] = [];
  const deploymentOption =
    manifest.deploymentOptions.find((option) => option.isRecommended) ||
    manifest.deploymentOptions[0];

  if (deploymentOption?.connection?.configSchema?.properties) {
    const { properties, required = [] } = deploymentOption.connection.configSchema;

    required.forEach((key) => {
      if (!process.env[key]) {
        missingEnvVars.push(key);
      }
    });
  }

  return missingEnvVars;
}

// 记录缺失的环境变量到文件
function recordMissingEnvVars(missingEnvVarsMap: Record<string, Record<string, string>>) {
  // 如果当前运行没有检测到新的缺失环境变量，则不修改 env.json
  if (Object.keys(missingEnvVarsMap).length === 0) {
    // 可以选择在此处添加逻辑：如果 env.json 存在且所有变量都有值，则打印一条成功消息
    // 但为了保持简单，如果本次没有发现新的缺失项，我们就直接返回。
    return;
  }

  const envFilePath = path.join(process.cwd(), 'env.json');
  let existingEnvVars: Record<string, Record<string, string>> = {};

  if (fs.existsSync(envFilePath)) {
    try {
      const fileContent = fs.readFileSync(envFilePath, 'utf-8');
      const parsedContent = JSON.parse(fileContent);
      if (
        typeof parsedContent === 'object' &&
        parsedContent !== null &&
        !Array.isArray(parsedContent)
      ) {
        existingEnvVars = parsedContent;
      } else {
        console.warn(`⚠️ env.json 文件内容格式不正确，将视其为空对象处理。`);
        existingEnvVars = {}; //确保 existingEnvVars 是一个对象
      }
    } catch (error) {
      console.warn(
        `⚠️ 读取或解析 env.json 文件失败，将基于当前检测到的缺失变量创建或更新该文件: ${(error as Error).message}`,
      );
      existingEnvVars = {}; //确保 existingEnvVars 是一个对象
    }
  }

  // 合并逻辑：以现有的为基础，将新检测到的缺失变量覆盖或添加进去
  const finalEnvVarsMap = { ...existingEnvVars };
  for (const serviceKey in missingEnvVarsMap) {
    finalEnvVarsMap[serviceKey] = {
      ...(existingEnvVars[serviceKey] || {}), // 保留该服务下旧的、可能已填写的变量
      ...missingEnvVarsMap[serviceKey], // 添加/覆盖新的缺失变量（其值应为 ''）
    };
  }

  fs.writeFileSync(envFilePath, JSON.stringify(finalEnvVarsMap, null, 2));

  // 基于最终写入 env.json 的内容计算统计数据
  let servicesWithMissingVarsCount = 0;
  let totalMissingVarsCount = 0;

  for (const serviceKey in finalEnvVarsMap) {
    const serviceVars = finalEnvVarsMap[serviceKey];
    let serviceHasMissing = false;
    if (typeof serviceVars === 'object' && serviceVars !== null) {
      for (const varKey in serviceVars) {
        if (serviceVars[varKey] === '') {
          // 空字符串表示该变量缺失，需要用户填写
          totalMissingVarsCount++;
          serviceHasMissing = true;
        }
      }
    }
    if (serviceHasMissing) {
      servicesWithMissingVarsCount++;
    }
  }

  if (totalMissingVarsCount > 0) {
    console.log(
      `
⚠️ env.json 已更新。共 ${servicesWithMissingVarsCount} 个服务当前记录了 ${totalMissingVarsCount} 个需要补充的环境变量。请检查 env.json 文件并提供这些值。`,
    );
  } else if (Object.keys(finalEnvVarsMap).length > 0) {
    // 虽然本次传入的 missingEnvVarsMap 可能非空，但合并后可能所有变量都有值了
    console.log(
      `
✅ env.json 已更新。所有记录的环境变量似乎都已填写。`,
    );
  }
  // 如果 finalEnvVarsMap 为空 (例如，原 env.json 为空，本次也无新发现)，则不打印消息。
  // (此情况会被顶部的 if (Object.keys(missingEnvVarsMap).length === 0) return; 捕获)
}

async function main() {
  const outputsDir = path.join(__dirname, './outputs');
  console.log(`🔍 读取目录: ${outputsDir}`);

  // 解析命令行参数
  const args = process.argv.slice(2);
  const forceValidate = args.includes('--force');

  try {
    // 加载 env.json 中的环境变量
    const envJsonPath = path.join(process.cwd(), 'env.json');
    if (fs.existsSync(envJsonPath)) {
      try {
        const envJsonContent = fs.readFileSync(envJsonPath, 'utf-8');
        const envVarsFromJson = JSON.parse(envJsonContent) as Record<
          string,
          Record<string, string>
        >;
        let loadedCount = 0;
        for (const serviceIdentifier in envVarsFromJson) {
          const serviceEnvs = envVarsFromJson[serviceIdentifier];
          for (const key in serviceEnvs) {
            if (serviceEnvs[key]) {
              // 只加载有值的环境变量
              process.env[key] = serviceEnvs[key];
              loadedCount++;
            }
          }
        }
        if (loadedCount > 0) {
          console.log(`ℹ️ 已从 env.json 加载 ${loadedCount} 个环境变量`);
        }
      } catch (error) {
        console.warn(`⚠️ 读取或解析 env.json 失败: ${(error as Error).message}`);
      }
    }

    // 读取outputs目录中的所有文件
    const files = fs.readdirSync(outputsDir);

    // 过滤出JSON文件
    const jsonFiles = files.filter((file) => file.endsWith('.json'));

    console.log(`🗂️ 找到 ${jsonFiles.length} 个JSON文件`);

    // 对每个文件进行验证
    let successCount = 0;
    let failCount = 0;
    let skipCount = 0;
    const capabilitiesStats = {
      tools: 0,
      prompts: 0,
      resources: 0,
    };

    // 用于收集所有缺失的环境变量
    const missingEnvVarsMap: Record<string, Record<string, string>> = {};

    for (const [index, file] of jsonFiles.entries()) {
      const filePath = path.join(outputsDir, file);
      console.log(`\n------------------------------`);
      console.log(`🔍 [${index + 1}/${jsonFiles.length}] 检查文件: ${file}`);

      try {
        // 读取Manifest文件
        const manifestContent = fs.readFileSync(filePath, 'utf-8');
        const manifest = JSON.parse(manifestContent) as MCPManifest;

        // 显示基本信息
        console.log(`📄 Manifest信息: ${manifest.name} (${manifest.identifier})`);

        // 检查环境变量
        const missingEnvVars = checkMissingEnvVars(manifest);
        if (missingEnvVars.length > 0) {
          // 记录缺失的环境变量
          missingEnvVarsMap[manifest.identifier] = missingEnvVars.reduce(
            (acc, key) => {
              acc[key] = '';
              return acc;
            },
            {} as Record<string, string>,
          );
          console.log(`  ⚠️ 缺少环境变量: ${missingEnvVars.join(', ')}`);
        }

        // 检查是否已经验证过
        if (manifest.isValidated && !forceValidate) {
          console.log(`  ⏭️ 已验证过 (${manifest.validatedAt}), 跳过验证`);

          // 更新统计信息
          successCount++;
          skipCount++;

          // 统计功能
          const caps = manifest.verifiedCapabilities || {};
          if (caps.tools) capabilitiesStats.tools++;
          if (caps.prompts) capabilitiesStats.prompts++;
          if (caps.resources) capabilitiesStats.resources++;

          continue;
        }

        // 检查安装方式
        const isAutoInstallable = manifest.deploymentOptions?.some(
          (option) =>
            option.installationMethod === 'npm' ||
            (option.installationMethod === 'python' && option.connection?.command === 'uv'),
        );

        if (!isAutoInstallable) {
          console.log(`  ⚠️ 该服务器需要手动安装，跳过验证`);
          skipCount++;
          continue;
        }

        // 如果强制验证，显示提示
        if (forceValidate && manifest.isValidated) {
          console.log(`  🔄 强制重新验证`);
        }

        // 执行验证 - 验证连接和所有功能
        const result = await MCPValidator.validate(manifest, {
          timeout: 60000,
          onProgress: (progress) => {
            // 控制进度输出，减少日志量
            if (progress.step === 'connected' || progress.step === 'complete') {
              console.log(`  📊 ${progress.step} - ${progress.message || ''}`);
            }
          },
        });

        // 输出结果
        if (result.connected) {
          console.log(`  ✅ 连接成功!`);

          // 显示实际验证的功能
          if (result.capabilities) {
            const caps = result.capabilities;
            const capsList = [];

            if (caps.tools) {
              capsList.push(`工具(${result.tools?.length || 0})`);
              capabilitiesStats.tools++;
            }

            if (caps.prompts) {
              capsList.push(`提示词(${result.prompts?.length || 0})`);
              capabilitiesStats.prompts++;
            }

            if (caps.resources) {
              capsList.push(`资源(${result.resources?.length || 0})`);
              capabilitiesStats.resources++;
            }

            console.log(`  🔧 支持功能: ${capsList.join(', ') || '无'}`);
          }

          // 保存更新后的Manifest
          if (result.manifest) {
            fs.writeFileSync(filePath, JSON.stringify(result.manifest, null, 2));
            console.log(`  💾 已更新 ${manifest.name} (${manifest.identifier})`);
          }

          successCount++;
        } else {
          console.error(`  ❌ 连接失败: ${result.error}`);
          failCount++;
        }
      } catch (error) {
        console.error(`  ❌ 验证失败: ${(error as Error).message}`);
        failCount++;
      }
    }

    // 在所有验证完成后，统一写入缺失的环境变量
    recordMissingEnvVars(missingEnvVarsMap);

    // 输出总结
    console.log(`\n------------------------------`);
    console.log(`📊 验证结果总结:`);
    console.log(`  ✅ 成功: ${successCount} 个`);
    console.log(`  ❌ 失败: ${failCount} 个`);
    console.log(`  ⏭️ 跳过: ${skipCount} 个`);
    console.log(`  📝 总计: ${jsonFiles.length} 个`);
    console.log(`\n📊 功能支持统计:`);
    console.log(`  🔧 支持工具的服务: ${capabilitiesStats.tools} 个`);
    console.log(`  📝 支持提示词的服务: ${capabilitiesStats.prompts} 个`);
    console.log(`  🗃️ 支持资源的服务: ${capabilitiesStats.resources} 个`);
  } catch (error) {
    console.error(`❌ 读取目录失败:`, (error as Error).message);
    process.exit(1);
  }
}

main().catch((error) => {
  console.error(`❌ 程序执行失败:`, error);
  process.exit(1);
});
