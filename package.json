{"name": "@lobehub/admin", "version": "1.0.0", "private": true, "description": "Dashboard Admin for LobeChat", "homepage": "https://github.com/lobehub/lobe-admin", "bugs": {"url": "https://github.com/lobehub/lobe-admin/issues/new/choose"}, "repository": {"type": "git", "url": "https://github.com/lobehub/lobe-admin.git"}, "author": "LobeHub <<EMAIL>>", "sideEffects": false, "workspaces": ["packages/*"], "scripts": {"build": "next build --turbopack", "dev": "next dev --turbopack -p 3020", "i18n": "npm run workflow:i18n && lobe-i18n", "lint": "eslint \"{src,tests}/**/*.{js,jsx,ts,tsx}\" --fix", "lint:circular": "dpdm src/**/*.{ts,tsx}  --warning false  --tree false  --exit-code circular:1  -T true", "lint:md": "remark . --quiet --frail --output", "lint:style": "stylelint \"{src,tests}/**/*.{js,jsx,ts,tsx}\" --fix", "openapi:clerk": "openapi-typescript src/server/modules/clerk/openapi.json --output ./src/server/modules/clerk/openapi.d.ts", "openapi:litellm": "openapi-typescript src/server/modules/litellm/openapi.json --output ./src/server/modules/litellm/openapi.d.ts", "prepare": "husky", "prettier": "prettier -c --write \"**/**\"", "qstash-local": "qstash dev", "start": "next start", "type-check": "tsc --noEmit", "workflow:i18n": "tsx scripts/i18nWorkflow/index.ts"}, "lint-staged": {"*.md": ["remark --quiet --output --", "prettier --write --no-error-on-unmatched-pattern"], "*.json": ["prettier --write --no-error-on-unmatched-pattern"], "*.{js,jsx}": ["prettier --write", "stylelint --fix", "eslint --fix"], "*.{ts,tsx}": ["prettier --parser=typescript --write", "stylelint --fix", "eslint --fix"]}, "overrides": {"mdast-util-gfm": "3.0.0"}, "dependencies": {"@ant-design/plots": "^2.3.3", "@ant-design/pro-components": "^2.8.4", "@ant-design/pro-layout": "^7.22.1", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@clerk/nextjs": "^6.10.4", "@clerk/themes": "^2.2.14", "@clerk/types": "^4.44.2", "@icons-pack/react-simple-icons": "^13.2.0", "@lobehub/charts": "^2.0.0", "@lobehub/icons": "^2.0.0", "@lobehub/market-sdk": "^0.22.7", "@lobehub/market-types": "^1.11.3", "@lobehub/ui": "^2.1.6", "@neondatabase/serverless": "^1.0.1", "@serwist/next": "^9.0.11", "@t3-oss/env-nextjs": "^0.13.8", "@tanstack/react-query": "^5.65.1", "@trpc/client": "^11.1.2", "@trpc/next": "^11.1.2", "@trpc/react-query": "^11.1.2", "@trpc/server": "^11.1.2", "@upstash/qstash": "^2.8.1", "@upstash/redis": "^1.35.0", "@upstash/workflow": "^0.2.12", "@vercel/edge-config": "^1.4.0", "@vercel/sdk": "^1.3.1", "ahooks": "^3.8.4", "antd": "^5.25.1", "antd-style": "^3.7.1", "dayjs": "^1.11.13", "discord-api-types": "^0.37.118", "drizzle-orm": "^0.44.2", "drizzle-zod": "^0.8.2", "fast-deep-equal": "^3.1.3", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.0.2", "i18next-resources-to-backend": "^1.2.1", "lodash-es": "^4.17.21", "lucide-react": "latest", "mcp-crawler": "workspace:*", "nanoid": "^5.0.9", "next": "^15.3.2", "nextjs-toploader": "^3.7.15", "numeral": "^2.0.6", "nuqs": "^2.4.3", "openapi-fetch": "^0.13.4", "p-map": "^7.0.3", "pg": "^8.13.1", "pino": "^9.6.0", "polished": "^4.3.1", "query-string": "^9.1.1", "random-words": "^2.0.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-i18next": "^15.4.0", "react-layout-kit": "^1.9.1", "resolve-accept-language": "^3.1.10", "rtl-detect": "^1.1.2", "serwist": "^9.0.11", "shiki": "^3.4.2", "superjson": "^2.2.2", "swr": "^2.3.0", "ua-parser-js": "^1.0.40", "ufo": "^1.5.4", "url-join": "^5.0.0", "use-merge-value": "^1.2.0", "utility-types": "^3.11.0", "uuid": "^11.1.0", "ws": "^8.18.0", "zlib-sync": "^0.1.9", "zod": "^3.24.1", "zustand": "5.0.1", "zustand-utils": "^2.1.0"}, "devDependencies": {"@lobehub/i18n-cli": "^1.25.1", "@lobehub/lint": "^1.25.5", "@next/eslint-plugin-next": "^15.1.6", "@peculiar/webcrypto": "^1.5.0", "@testing-library/react": "^16.2.0", "@trigger.dev/build": "^3.3.17", "@types/lodash": "^4.17.15", "@types/lodash-es": "^4.17.12", "@types/node": "^22.12.0", "@types/numeral": "^2.0.5", "@types/pg": "^8.11.11", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@types/rtl-detect": "^1.0.3", "@types/ua-parser-js": "^0.7.39", "@types/uuid": "^10.0.0", "@types/ws": "^8.5.14", "@upstash/qstash-cli": "^2.22.3", "consola": "^3.4.0", "dotenv": "^16.4.7", "dpdm": "^3.14.0", "eslint": "^8.57.1", "husky": "^9.1.7", "just-diff": "^6.0.2", "lint-staged": "^15.4.3", "lodash": "^4.17.21", "openapi-typescript": "^7.6.0", "prettier": "^3.4.2", "remark": "^15.0.1", "remark-cli": "^12.0.1", "stylelint": "^15.11.0", "tsx": "^4.19.2", "typescript": "^5.7.3", "vitest": "^3.2.4"}, "packageManager": "pnpm@10.2.0", "pnpm": {"onlyBuiltDependencies": ["@upstash/qstash-cli"], "overrides": {"mdast-util-gfm": "3.0.0"}}}