{"duration": {"TPS": "TPS(Token per Second): میزان خروجی Token در هر ثانیه", "TTFT": "TTFT(Time To First Token): تاخیر اولین Token", "completion": "مد<PERSON> زمان خروجی", "latency": "مد<PERSON> زمان", "stage": {"end": "پایان", "fistToken": "اولین <PERSON>", "start": "شروع"}}, "table": {"columns": {"TTFT": {"tooltip": "تاخیر اولین Token (Time To First Token)، واحد: ثانیه"}, "duration": "مد<PERSON> زمان", "emailOrUsernameOrUserId": "ایمیل/نام کاربری/UserId", "model": "مدل", "spend": "امت<PERSON><PERSON>ز", "startTime": "زمان ایجاد", "totalTokens": "م<PERSON><PERSON><PERSON>", "type": {"enums": {"acompletion": "تو<PERSON><PERSON><PERSON> متن", "aembedding": "تجزیه و تحلیل برداری", "aimage_generation": "تولید تصویر از متن"}, "title": "نوع"}, "user": "کاربر"}, "desc": "جزئیات استفاده از امتیاز محاسباتی برای تولید متن، تجزیه و تحلیل برداری، تولید تصویر از متن و غیره", "more": "جزئیات بیشتر", "title": "جزئیات استفاده از امتیاز محاسباتی", "totalToken": {"completion": "تکمیل خروجی", "prompt": "متن ورودی"}}}