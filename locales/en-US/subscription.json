{"billing": {"amount": "Amount", "closed": "Closed", "created": "Payment Date", "draft": "Draft", "draftTooltip": "This draft invoice is generated by a new subscription and will be automatically charged soon", "empty": "No billing history available", "endDate": "End Date", "history": "Billing History", "orderNumber": "Order Number", "paid": "Paid", "pay": "Go to pay", "paymentMethod": "Payment Method", "price": "Price", "startDate": "Start Date", "status": "Status", "subscriptionId": "Subscription ID", "unpaid": "Unpaid", "view": "View"}, "cancelAt": "Cancellation Time", "cancelPlan": {"alert": "You will continue to enjoy the corresponding benefits until the current plan expires ({{date}}). You can resubscribe at any time before expiration.", "desc": "After unsubscribing, you will automatically downgrade to the free version when the current plan expires.", "title": "Unsubscribe"}, "cancelSubscription": {"confirm": "Are you sure you want to cancel this subscription? The user's subscription status will be terminated immediately upon cancellation.", "error": "Failed to cancel the subscription, please try again later.", "success": "Subscription has been successfully cancelled.", "title": "Cancel Subscription"}, "compare": {"hobbyCreditTooltip": "Excludes monthly credit calculation limit, requires manual configuration of provider API", "monthlyCredit": "Monthly credit calculation", "title": "Plan Comparison"}, "compareAllPlans": "Compare All Plans", "comparePlans": "Compare Plans", "createdAt": "Subscription Time", "currentPlan": {"cancelAlert": "Subscription will be canceled after {{canceledAt}}. You can still restore it in 'Manage Subscription' before that", "downgradeAlert": "Will downgrade to {{plan}} after {{downgradedAt}}", "management": "Manage Subscription", "notIncluded": "Not included in the current plan", "paymentExpired": "Your subscription will expire on {{expiredAt}}. Please plan your usage accordingly.", "seeAllFeaturesAndComparePlans": "View all features and compare plans", "title": "Current Plan"}, "discount": {"add": "Add", "maxOff": "Maximum discount of {{percent}}%", "off": "{{percent}}% off", "save": "Off"}, "downgradePlans": {"alert": "You will continue to enjoy the corresponding benefits until the current plan expires ({{date}}). The downgrade plan will take effect after the current plan expires.", "desc": "The downgrade plan will take effect after the current plan expires.", "success": "Subscription canceled successfully", "title": "Downgrade to {{plan}}"}, "header": {"desc": "Usage and Subscription Management", "title": "Billing"}, "limitation": {"chat": {"expired": {"desc": "Your {{plan}} computation credit expired on {{expiredAt}}. Upgrade your plan now to regain access to your computation credit.", "title": "Computation Credit Expired"}, "hobby": {"action": "Configured, continue the conversation", "configAPI": "Go to Configure API", "desc": "Your free computation quota has been exhausted. Please configure a custom provider API to continue using.", "docs": "View configuration documentation", "tip": "Remember to switch to the model with your custom API Key!", "title": "Please configure the custom provider API"}, "limited": {"action": "Upgrade now", "advanceFeature": "Upgrade to enjoy premium features:", "desc": "Your {{plan}} calculation limit has been reached. Upgrade now to get more calculation limits.", "title": "Calculation limit reached"}, "success": {"action": "Continue chatting", "desc": "Your {{plan}} subscription has been successfully upgraded. Enjoy the fun of AI chat. Current plan brings you advanced features:", "title": "Congratulations on the successful upgrade"}}, "providers": {"mask": {"action": "Upgrade Now", "subTitle": "Not supported in the current plan, custom provider API services are only available for paid plans, upgrade now to enjoy global mainstream model services", "title": "Upgrade to a paid plan to use custom provider API services"}, "tooltip": "Custom provider API services are only available for paid plans"}}, "modelPricing": {"button": "View billing details documentation", "desc": "{{name}} uses Credits to measure AI model usage, corresponding to Tokens used by large models. The table below lists the corresponding credit calculations based on 1M Tokens.", "title": "Text Model Pricing"}, "models": {"input": "Input", "intro": "Introduction", "link": "View", "output": "Output", "title": "Models"}, "payDiffPrice": "Pay the Price Difference", "payment": {"error": {"actions": {"billing": "Billing Management", "home": "Back to Homepage"}, "desc": "No subscription number found in the system: {{id}}. Please contact us via email if you have any questions", "title": "Query Failed"}, "success": {"actions": {"startUsing": "Start Using", "viewBill": "View Billing Records"}, "desc": "The plan you subscribed to has been successfully activated", "title": "Subscription Successful"}, "switchSuccess": {"desc": "Your subscribed plan will automatically switch on {{switchAt}}", "title": "Switch Successful"}, "title": "Payment Method", "upgradeFailed": {"alert": {"reason": {"bank3DS": "The issuing bank has 3DS verification, please confirm again", "inefficient": "Insufficient card balance", "security": "Stripe system risk control"}, "title": "Common reasons for failed automatic payment"}, "desc": "Failed to upgrade your subscribed plan, please check and try again", "title": "Upgrade failed"}, "upgradeSuccess": {"desc": "The plan you subscribed to has been successfully upgraded", "title": "Upgrade Successful"}}, "plans": {"btn": {"contact": "Contact Us", "noAction": "Plan Locked", "payment": "Purchase", "paymentDesc": "Supports credit card / Alipay / WeChat Pay", "soon": "Coming Soon"}, "changePlan": "Select Plan", "cloud": {"history": "Unlimited Chat History", "sync": "Global Cloud Sync", "title": "Cloud Services"}, "credit": {"api": "Custom API", "apiDesc": "Requires configuration of your own provider API", "apiProvider": "Supports over 20 mainstream model service providers including OpenAI / Anthropic / OpenRouter", "buy": "Purchase credits", "buyDesc": "Also supports on-demand payment for purchased credits", "none": "No built-in credits", "tip": "Get {{credit}} credits for free every day within {{duration}} days", "title": "Compute Credits", "tooltip": "Monthly model message computation quota"}, "current": "Current Plan", "downgradePlan": "Downgrade Plan", "downgradeTip": "You have switched subscriptions and cannot perform other actions until the subscription switch is complete", "embeddingStorage": {"embeddings": "entry", "title": "Vector Storage", "tooltip": "Approximately 1 entry is generated for a single page document (1000-1500 characters). (Estimated using OpenAI Embeddings; results may vary with different models)"}, "features": {"agents": "Featured Agent Market", "ceAgents": "Community Agent Market", "cePlugins": "Community Plugin Market", "internet": "Intelligent Internet Query", "plugins": "Exclusive Premium Plugins", "showAll": "View All Features", "title": "Advanced Features"}, "fileStorage": {"title": "File Storage", "tooltip": "File storage is used for storing files, images, and other data."}, "free": "Free", "freeTrail": "Register to get {{name}} API calls for free trial, no credit card required", "includes": "Includes:", "includesExtra": "All {{name}} benefits, plus:", "knowledgeBase": {"desc": "Use file and knowledge base features in conversations", "filetype": "Supports multiple file formats including PDF / MD / DOC / XLS / PPT", "title": "Files and Knowledge Base", "tooltip": "Supports file uploads and knowledge base features. You can upload various types of files, including documents, images, audio, and video, as well as create a knowledge base for easier management and retrieval. Additionally, use files and knowledge base features in conversations for a richer dialogue experience."}, "llm": {"customAPI": "Global mainstream model custom API services", "messageRequest": "Unlimited Message Requests", "title": "Model Services", "tooltip": "Add custom provider API and enjoy cloud sync"}, "message": {"count": "Approx {{number}} messages", "more": "See more models in the plan comparison", "normalLLM": "Standard Model", "proLLM": "Advanced Model", "tooltip": "Estimated based on an average consumption of {{number}} Tokens per message"}, "mostPicked": "Most Popular", "navs": {"monthly": "Monthly", "payonce": "One-time payment", "yearly": "Yearly"}, "payonce": {"cancel": "Cancel", "ok": "Confirm Selection", "popconfirm": "After a successful one-time payment purchase, you must wait until the subscription expires before switching subscription plans or modifying the billing cycle. Please confirm your selection is correct.", "tooltip": "You must wait until the subscription expires before switching subscription plans or modifying the billing cycle after a one-time payment."}, "plan": {"enterprise": {"contactSales": "Contact Sales", "title": "Enterprise"}, "free": {"desc": "For first-time users", "title": "Free"}, "hobby": {"desc": "Suitable for users who pay for their own provider API on demand", "title": "<PERSON>bby"}, "premium": {"desc": "Designed for professional users who frequently use AI features", "title": "Premium"}, "starter": {"desc": "Suitable for users who occasionally use AI features", "title": "Starter"}, "ultimate": {"desc": "For heavy users requiring extensive AI complex conversations", "title": "Ultimate"}}, "storage": {"title": "Data Storage"}, "subscribe": "Subscribe", "support": {"hobby": "Community Forum", "premium": "Priority Email Support", "starter": "Email and Community Forum", "title": "Service Support", "ultimate": "Priority Chat and Email Support"}, "target": "Target Plan", "unlimited": "Unlimited"}, "qa": {"desc": "If your question is not answered, you can refer to the <1>product documentation</1> for more common questions. Feel free to contact us.", "detail": "View Details", "list": {"credit": {"a": "Calculating credits is a metric used to measure the usage of AI models when {{cloud}}. The credit consumption varies for different AI models.", "q": "What are calculating credits?"}, "embeddings": {"a": "Vector storage does not equal the original size of the dataset you uploaded or imported; it is calculated based on the pure text content extracted from your files. For example, a 1-page PDF file, when extracted and vectorized into plain text (1000-1500 characters), may only occupy about 1 embedding. You can check your usage under '{{usage}}'.", "q": "How is vector storage calculated?"}, "free": {"a": "{{name}} always adheres to the open-source concept. For professional developers, you can use all open-source capabilities through self-deploying the community edition. In the {{cloud}} version, we provide {{credit}} free calculating credits for all registered users, ready to use without complex configurations. If you need more usage, you can subscribe to {{starter}}, {{premium}}, or {{ultimate}}.", "q": "Can {{name}} be used for free?"}, "limit": {"a": "The {{cloud}} subscription plans are divided into {{starter}}, {{premium}}, and {{ultimate}}, each providing different amounts of calculating credits. If the current plan's calculating credits are insufficient, we recommend considering upgrading the plan. Additionally, you can set up custom provider API keys to use API usage purchased from other channels.", "q": "What to do if calculating credits are insufficient?"}, "management": {"a": "On the {{subscribe}} page, you can 'upgrade/downgrade' the current subscription plan and switch between annual or monthly billing. By clicking '{{usage}}-{{management}}', you can go to Stripe for subscription management and cancel the current subscription at any time. After unsubscribing, it will automatically downgrade to the free version when the current plan expires.", "q": "How to change or cancel a subscription plan?"}}, "support": {"community": "Community Support", "email": "Email Support"}, "title": "Frequently Asked Questions"}, "recurring": {"day": "daily", "fullYear": "Full Year", "month": "{{quantity}} months", "monthly": "Monthly", "oneMonth": "One month", "oneYear": "One year", "payonce": "One-time payment", "perMonth": "Month", "perYear": "Year", "sixMonth": "Six months", "threeMonth": "Three months", "title": "Billing Cycle", "yearly": "Yearly"}, "sessionCard": {"title": "Ready to say goodbye to the free plan? Upgrade to enjoy premium features."}, "status": {"active": "Active Subscription", "activeFutureCancellation": "Active with Future Cancellation", "activeNotCancelling": "Continuously Active", "cancelled": "Cancelled Subscription", "inactive": "Inactive"}, "summary": {"desc": "This amount only includes expenses for subscription services.", "dueBy": "Due on {{date}}", "nextPayment": "Your Next Payment", "paymentInformation": "Billing Information", "title": "Billing Summary", "usageThisMonth": "View your usage this month.", "viewBillingHistory": "View Payment History"}, "switchPlan": "Switch Plan", "switchToMonthly": {"desc": "After switching, the monthly billing plan will take effect after the current annual plan expires.", "title": "Switch to Monthly Billing"}, "switchToYearly": {"desc": "After switching, the annual billing plan will take effect immediately after paying the price difference, and the start date will inherit the previous plan.", "title": "Switch to Yearly Billing"}, "tab": {"billing": "Billing", "funds": "Funds", "plans": "Plans & Pricing", "spend": "Credits Details", "usage": "Usage"}, "upgrade": "Upgrade", "upgradeNow": "Upgrade Now", "upgradePlan": "Upgrade Plan", "upgradePlans": {"desc": "The upgraded plan will take effect immediately after paying the price difference, and the start date will inherit the previous plan.", "title": "Upgrade to {{plan}}"}, "usage": {"credit": {"addon": {"desc": "Effective during the subscription plan duration", "used": "Recharge credits"}, "desc": "Credits used for AI dialogue, image-to-text, and speech synthesis", "detail": "Usage statistics for the past {{day}} days", "free": {"desc": "Reset after {{time}}", "expired": "Expired on {{date}}", "used": "Free Credits"}, "subscription": {"desc": "Credits will reset in {{day}} days", "used": "Subscription Credits"}, "title": "Compute Credit Usage"}, "overview": {"charge": "Cost Settlement", "included": "Plan Usage", "onDemand": "On-demand usage", "product": "Product Items", "title": "Usage Overview"}, "storage": {"desc": "Data storage that can be released through manual cleaning", "embeddings": {"used": "Embedding storage"}, "file": {"used": "File storage"}, "title": "Data Storage"}, "title": "Usage This Month", "used": "Used"}}