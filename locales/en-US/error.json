{"error": {"backHome": "Back to Home", "desc": "Give it a try later, or go back to the known world.", "retry": "Reload", "title": "Oops, something went wrong.."}, "fetchError": {"detail": "Error details", "title": "Request failed"}, "loginRequired": {"desc": "You will be redirected to the login page shortly", "title": "Please log in to use this feature"}, "notFound": {"backHome": "Back to Home", "check": "Please check if your URL is correct.", "desc": "We couldn't find the page you were looking for.", "title": "Entered Unknown Territory?"}, "response": {"400": "Sorry, the server does not understand your request. Please make sure your request parameters are correct.", "401": "Sorry, the server has rejected your request, possibly due to insufficient permissions or invalid authentication.", "403": "Sorry, the server has rejected your request. You do not have permission to access this content.", "404": "Sorry, the server cannot find the page or resource you requested. Please make sure your URL is correct.", "405": "Sorry, the server does not support the request method you are using. Please make sure your request method is correct.", "406": "Sorry, the server cannot complete the request based on the characteristics of the content you requested", "407": "Sorry, you need to authenticate the proxy before continuing with this request", "408": "Sorry, the server timed out while waiting for the request, please check your network connection and try again", "409": "Sorry, the request cannot be processed due to a conflict, possibly because the resource state is incompatible with the request", "410": "Sorry, the resource you requested has been permanently removed and cannot be found", "411": "Sorry, the server cannot process the request without a valid content length", "412": "Sorry, your request does not meet the server's conditions and cannot be completed", "413": "Sorry, your request data is too large for the server to process", "414": "Sorry, the URI of your request is too long for the server to process", "415": "Sorry, the server cannot process the media format attached to the request", "416": "Sorry, the server cannot satisfy the range of your request", "417": "Sorry, the server cannot meet your expectations", "422": "Sorry, your request is in the correct format, but due to semantic errors, it cannot be responded to", "423": "Sorry, the resource you requested is locked", "424": "Sorry, the current request cannot be completed due to a previous request failure", "426": "Sorry, the server requires your client to upgrade to a higher protocol version", "428": "Sorry, the server requires a precondition, and requests that your request contain the correct conditional header", "429": "Sorry, your request is too frequent and the server is a bit tired. Please try again later.", "431": "Sorry, the header fields of your request are too large for the server to process", "451": "Sorry, the server refuses to provide this resource due to legal reasons", "500": "Sorry, the server seems to be experiencing some difficulties and is temporarily unable to complete your request. Please try again later.", "501": "Sorry, the server does not know how to handle this request yet. Please confirm that your operation is correct.", "502": "Sorry, the server seems to be lost and is temporarily unable to provide service. Please try again later.", "503": "Sorry, the server is currently unable to process your request, possibly due to overload or maintenance. Please try again later.", "504": "Sorry, the server did not receive a response from the upstream server. Please try again later.", "505": "Sorry, the server does not support the HTTP version you are using. Please update and try again.", "506": "Sorry, there is a configuration issue with the server. Please contact the administrator for resolution.", "507": "Sorry, the server has insufficient storage space to process your request. Please try again later.", "509": "Sorry, the server's bandwidth has been exhausted. Please try again later.", "510": "Sorry, the server does not support the requested extension. Please contact the administrator.", "524": "Sorry, the server timed out while waiting for a response, possibly due to a slow reply. Please try again later.", "AgentRuntimeError": "Lobe language model runtime execution error. Please troubleshoot or retry based on the following information.", "FreePlanLimit": "You are currently a free user and cannot use this feature. Please upgrade to a paid plan to continue using it.", "InvalidAccessCode": "Invalid access code or empty. Please enter the correct access code or add a custom API Key.", "InvalidBedrockCredentials": "Bedrock authentication failed. Please check the AccessKeyId/SecretAccessKey and retry.", "InvalidClerkUser": "Sorry, you are not currently logged in. Please log in or register an account to continue.", "InvalidGithubToken": "The GitHub Personal Access Token is incorrect or empty. Please check your GitHub Personal Access Token and try again.", "InvalidOllamaArgs": "Invalid Ollama configuration, please check Ollama configuration and try again", "InvalidProviderAPIKey": "{{provider}} API Key is incorrect or empty, please check your {{provider}} API Key and try again", "LocationNotSupportError": "We're sorry, your current location does not support this model service. This may be due to regional restrictions or the service not being available. Please confirm if the current location supports using this service, or try using a different location.", "NoOpenAIAPIKey": "OpenAI API Key is empty, please add a custom OpenAI API Key", "OllamaBizError": "Error requesting Ollama service, please troubleshoot or retry based on the following information", "OllamaServiceUnavailable": "Ollama service is unavailable. Please check if Ollama is running properly or if the cross-origin configuration of Ollama is set correctly.", "OpenAIBizError": "Error requesting OpenAI service, please troubleshoot or retry based on the following information", "PermissionDenied": "Sorry, you do not have permission to access this service. Please check if your key has the necessary access rights.", "PluginApiNotFound": "Sorry, the API does not exist in the plugin's manifest. Please check if your request method matches the plugin manifest API", "PluginApiParamsError": "Sorry, the input parameter validation for the plugin request failed. Please check if the input parameters match the API description", "PluginFailToTransformArguments": "Sorry, the plugin failed to parse the arguments. Please try regenerating the assistant message or switch to a more powerful AI model with Tools Calling capability and try again", "PluginGatewayError": "Sorry, there was an error with the plugin gateway. Please check if the plugin gateway configuration is correct.", "PluginManifestInvalid": "Sorry, the plugin's manifest validation failed. Please check if the manifest format is correct", "PluginManifestNotFound": "Sorry, the server could not find the plugin's manifest file (manifest.json). Please check if the plugin manifest file address is correct", "PluginMarketIndexInvalid": "Sorry, the plugin index validation failed. Please check if the index file format is correct", "PluginMarketIndexNotFound": "Sorry, the server could not find the plugin index. Please check if the index address is correct", "PluginMetaInvalid": "Sorry, the plugin's metadata validation failed. Please check if the plugin metadata format is correct", "PluginMetaNotFound": "Sorry, the plugin was not found in the index. Please check the plugin's configuration information in the index", "PluginOpenApiInitError": "Sorry, the OpenAPI client failed to initialize. Please check if the OpenAPI configuration information is correct.", "PluginServerError": "Plugin server request returned an error. Please check your plugin manifest file, plugin configuration, or server implementation based on the error information below", "PluginSettingsInvalid": "This plugin needs to be correctly configured before it can be used. Please check if your configuration is correct", "ProviderBizError": "Error requesting {{provider}} service, please troubleshoot or retry based on the following information", "QuotaLimitReached": "We apologize, but the current token usage or number of requests has reached the quota limit for this key. Please increase the quota for this key or try again later.", "StreamChunkError": "Error parsing the message chunk of the streaming request. Please check if the current API interface complies with the standard specifications, or contact your API provider for assistance.", "SubscriptionPlanLimit": "Your subscription limit has been reached, and you cannot use this feature. Please upgrade to a higher plan or configure a custom model API to continue using it.", "UnknownChatFetchError": "Sorry, an unknown request error occurred. Please check the information below or try again."}}