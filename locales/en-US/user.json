{"actions": {"ban": "Ban this user", "banConfirm": "Are you sure you want to ban this user?", "clerk": "Clerk Management", "delete": "Delete this user", "deleteConfirm": "Are you sure you want to delete this user?", "lock": "Lock this user", "lockConfirm": "Are you sure you want to lock this user?", "strip": "Stripe Management", "stripSubscriptions": "Manage Stripe Subscriptions", "unban": "Unban this user", "unbanConfirm": "Are you sure you want to unban this user?", "unlock": "Unlock this user", "unlockConfirm": "Are you sure you want to unlock this user?"}, "budget": {"budgetDuration": "Budget Duration", "budgetResetAt": "Reset Date", "createdAt": "Creation Time", "desc": "Manage User Budget", "detail": "View Details", "edit": "Edit Budget", "expires": "Expiration Date", "keyAlias": "<PERSON><PERSON>", "maxBudget": "Maximum Budget", "metadata": "Notes", "models": "Model Limitations", "oneTime": "One-Time", "spend": "Budget Spending", "status": {"active": "Active", "expired": "Expired", "over": "Exhausted", "title": "Current Status"}, "title": "Budget Details", "updateSuccess": "Budget updated successfully", "used": "Used"}, "drawer": {"message": {"columns": {"content": "Content", "createdAt": "Creation Time", "role": "Role"}, "title": "User Messages"}}, "info": {"clerkCreatedAt": "Registration Date", "email": "Email", "firstName": "First Name", "isOnboarded": "Onboarded", "lastActiveAt": "Last Active", "lastName": "Last Name", "phone": "Phone Number", "preference": "Preferences", "title": "User Information", "username": "Username"}, "messages": {"table": {"columns": {"content": "Content", "createdAt": "Creation Time", "modal": "Model", "provider": "Model Provider", "role": "Role"}}}, "refundable": {"form": {"createdAt": "Subscription Time", "currentPlan": "Current Plan", "embeddingUsage": "Vector Storage", "fileUsage": "File Usage", "messages": "Number of Model Responses", "messagesWithCustomProvider": "Custom Model Messages", "spendUsage": "Model Service Costs", "subscriptionHistory": "History of Subscriptions", "subscriptionUsage": "Subscription Points Usage", "suggest": {"allow": "Refundable", "deny": "Non-refundable", "none": "No Refund Needed", "title": "Refund Suggestions"}}, "title": "Refund Detection"}, "restriction": {"actions": {"add": "Add", "batchAdd": "<PERSON><PERSON> Add", "deleteAllowlist": "Delete Allowlist", "deleteBlocklist": "Delete Blocklist", "deleteConfirmAllowlist": "Are you sure you want to delete the allowlist?", "deleteConfirmBlocklist": "Are you sure you want to delete the blocklist?", "disableAllowlist": "Disable Allowlist", "disableBlocklist": "Disable Blocklist", "disableConfirmAllowlist": "Are you sure you want to disable the Allowlist?", "disableConfirmBlocklist": "Are you sure you want to disable the Blocklist?", "enableAllowlist": "Enable Allowlist", "enableBlocklist": "Enable Blocklist", "enableConfirmAllowlist": "Are you sure you want to enable the Allowlist?", "enableConfirmBlocklist": "Are you sure you want to enable the Blocklist?"}, "allowlist": "Allowlist", "blocklist": "Blocklist", "createdAt": "Created At", "email": "Email", "emailOrUsernameOrUserId": "Email/Username/UserId", "management": "Clerk Management", "status": {"off": "Disabled", "on": "Enabled"}, "types": {"allowlist": "Allowlist", "blocklist": "Blocklist"}, "updatedAt": "Updated At"}, "statistics": {"desc": "User usage data", "title": "Statistics"}, "status": {"banned": "Banned", "delete": "Deleted", "locked": "Locked"}, "subscription": {"billingCycleEnd": "Billing Cycle End Date", "billingCycleStart": "Billing Cycle Start Date", "billingPaidAt": "Payment Date", "cancelAt": "Cancel Subscription Date", "cancelAtPeriodEnd": "Cancel Subscription at Period End", "count": "{{count}} subscriptions", "desc": "Manage user's subscription information", "detail": "View Details", "mode": "Payment Mode", "nextBilling": "Update Subscription Information", "nextBillingDate": "Next Billing", "noNextBillingDate": "No upcoming bills", "plan": "Subscription Plan", "pricing": "Payment Amount", "recurring": "Subscription Cycle", "status": {"active": "Active", "inactive": "Cancelled", "title": "Current Status"}, "title": "Subscription Information", "total": "Total Payment"}, "table": {"columns": {"bizData": "Active Metrics", "createdAt": "Creation Time", "creditUsage": "Credit Usage", "emailOrUsernameOrUserId": "Email/Username/UserId", "freeCredit": "Free Credit Usage", "id": "ID", "lastActiveAt": "Last Active", "plan": "Subscription Plan", "registerAt": "Registration Time", "subscriptionCredit": "Subscription Usage", "username": "Username"}, "credit": {"expired": "Expired", "expiredAt": "{{expiredAt}} expired", "used": "Used: ${{used}}"}, "header": {"type": {"active": "Active Users", "all": "All Users", "inactive": "Inactive Users", "risk": "At-Risk Users"}}, "title": "User Management", "user": {"isOnboard": "Onboarding Complete", "messages": "Messages", "risk": "At Risk"}}, "tabs": {"info": {"active": "Users with an average of more than 1 message per day during the period: message count / number of days in the period > 1", "banned": "Banned Users", "bug": "Subscribed users whose usage has expired", "inactive": "Users who have never sent a message", "paid": "All former paid users, including those who have canceled their subscriptions", "risk": "Users who have called the model but have sent 0 messages"}, "type": {"active": "Active Users", "all": "All Users", "banned": "Banned Users", "bug": "Abnormal User", "inactive": "Inactive Users", "paid": "Paid Users", "risk": "At-Risk Users"}}, "usage": {"desc": "Manage user usage information", "expiredAt": "Expiration Date", "resetAt": "Reset Date", "tab": {"budget": "Statistics", "spend": "Details"}, "title": "Usage Information"}}