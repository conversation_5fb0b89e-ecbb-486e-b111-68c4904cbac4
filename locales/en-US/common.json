{"admin": {"title": "{{appName}} Admin"}, "lang": {"ar": "Arabic", "bg-BG": "Bulgarian", "bn": "Bengali", "cs-CZ": "Czech", "da-DK": "Danish", "de-DE": "German", "el-GR": "Greek", "en": "English", "en-US": "English", "es-ES": "Spanish", "fi-FI": "Finnish", "fr-FR": "French", "hi-IN": "Hindi", "hu-HU": "Hungarian", "id-ID": "Indonesian", "it-IT": "Italian", "ja-JP": "Japanese", "ko-KR": "Korean", "nl-NL": "Dutch", "no-NO": "Norwegian", "pl-PL": "Polish", "pt-BR": "Portuguese", "pt-PT": "Portuguese", "ro-RO": "Romanian", "ru-RU": "Russian", "sk-SK": "Slovak", "sr-RS": "Serbian", "sv-SE": "Swedish", "th-TH": "Thai", "tr-TR": "Turkish", "uk-UA": "Ukrainian", "vi-VN": "Vietnamese", "zh": "Simplified Chinese", "zh-CN": "Simplified Chinese", "zh-TW": "Traditional Chinese"}, "loading": "Loading...", "models": {"contentLength": "Maximum Context Length", "functionCall": "Function Call Support", "input": "Input", "output": "Output", "title": "Others", "vision": "Vision Recognition Support", "withKnowledge": "With Knowledge Base", "withTool": "With Plugins"}, "overview": {"tabs": {"channel": "Channel", "overview": "Overview", "subscription": "Subscription", "user": "User"}}, "qoq": {"daily": "Day-on-day", "monthly": "Month-on-month"}, "rank": {"agents": "Number of Assistants", "messages": "Number of Messages", "topics": "Number of Topics", "weeklyActive": "Weekly Active Days Ranking"}, "statisticGroup": {"channelData": "Channel Data", "futureCancelUsers": "Users About to Cancel", "recentSubscriptUsers": "Recently Subscribed Users", "subscriptionData": "Subscription Data", "subscriptionUsers": "Subscribed Users", "userAnalysis": "Behavior Distribution", "userData": "User Data", "userRanking": "User Ranking"}, "statistics": {"active": "Active", "activeUser": "Active Users", "activeUsers": "Active Users", "agentDistribution": "User Assistant Distribution", "agents": "Number of Assistants", "all": "All", "allUser": "All Users", "average": "Average", "cancelSubscriptions": "Number of Cancellations", "channel": "Channel Name", "created": "Creation Time", "detail": "View Details", "effective": "Effective", "effectiveUser": "Effective User", "effectiveUsers": "Number of Effective Users", "forChanel": "Channel", "forDetail": "Details", "forPlan": "Plan", "forTotal": "Total", "freeUser": "Free Users", "freeUsers": "Free Users", "inactiveUser": "Inactive Users", "inactiveUsers": "Inactive Users", "lastWeek": "Last week", "loadBalance": "<PERSON><PERSON>", "max": "Peak Value", "median": "Median", "messageDistribution": "User Message Distribution", "messages": "Number of Messages", "model": "Model Name", "modelCalls": "Call Count", "modelCallsToday": "Model Calls Today", "newSubscriptions": "New subscriptions", "newSubscriptionsToday": "New Subscriptions Today", "newUsers": "New Users", "newUsersToday": "New Users Today", "normalUser": "Regular User", "normalUsers": "Number of Regular Users", "overview": "Overview", "per80": "80th Percentile", "per90": "90th Percentile", "riskUser": "Risk User", "riskUsers": "Number of Risk Users", "rpm": "Requests per Minute", "spend": "Model Service Costs", "spendToday": "Model Service Costs Today", "subscriptionModeDistribution": "Payment Mode Distribution", "subscriptionPlansDistribution": "Plan Distribution", "subscriptionRecurringDistribution": "Subscription Cycle Distribution", "subscriptionStatusDistribution": "Subscription Status Distribution", "subscriptionUser": "Subscription Users", "subscriptionUsers": "Subscription Users", "thisWeek": "This week", "token": "Model Token Consumption", "topicDistribution": "User Topic Distribution", "topics": "Number of Topics", "totalAssistants": "Total assistants", "totalChannels": "Total Channels", "totalMessages": "Total messages", "totalModelCalls": "Total Model Calls", "totalModels": "Total Models", "totalSpends": "Total Model Service Costs", "totalSubscriptions": "Total subscriptions", "totalTopics": "Total Topics", "totalUsers": "Total Registered Users", "tpm": "Tokens per Minute", "userGroup": "User Group", "username": "Username", "users": "User Count", "weeklyActive": "Weekly Active Days", "weeklyActiveDistribution": "Weekly Active Days Distribution", "weight": "Weight"}, "time": {"compare": {"custom": "Custom", "disabled": "No Comparison", "everyDay": "Daily", "everyMonth": "Monthly", "everyWeek": "Weekly", "prev": "Previous Period", "prevMonth": "Last month", "prevWeek": "Same Day Last Week", "title": "Comparison"}, "createdAt": "Creation Time", "custom": "Custom Range", "last12Months": "Last 12 Months", "last24Hours": "Last 24 Hours", "last3Months": "Last 3 Months", "last4Weeks": "Last 4 Weeks", "last7Days": "Last 7 Days", "prevMonth": "Last Month", "thisMonth": "This Month", "thisQuarter": "This Quarter", "thisYear": "This Year", "today": "Today", "updatedAt": "Update Time", "yesterday": "Yesterday"}, "title": {"overview": "Overview", "profile": "User Profile", "today": "Today", "users": "User Management"}, "userPanel": {"anonymousNickName": "Anonymous User", "profile": "Profile"}, "welcome": "Welcome back <1>{{username}}</1>, here is the latest data overview", "yoy": {"daily": "Daily Year-over-Year", "monthly": "Monthly Year-over-Year"}}