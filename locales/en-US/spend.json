{"duration": {"TPS": "TPS(Token per Second): Tokens generated per second", "TTFT": "TTFT(Time To First Token): Time delay for the first token", "completion": "Time taken to complete", "latency": "Duration", "stage": {"end": "End", "fistToken": "First Token", "start": "Start"}}, "table": {"columns": {"TTFT": {"tooltip": "Time To First Token (TTFT), unit: seconds"}, "duration": "Duration", "emailOrUsernameOrUserId": "Email/Username/UserId", "model": "Model", "spend": "Credits", "startTime": "Creation Time", "totalTokens": "Total Tokens", "type": {"enums": {"acompletion": "Text Generation", "aembedding": "Vectorization", "aimage_generation": "Image Generation"}, "title": "Type"}, "user": "User"}, "desc": "Details of computing integration for text generation, vectorization, image generation, etc.", "more": "View Details", "title": "Compute Credits Usage Details", "totalToken": {"completion": "Output completion", "prompt": "Input prompts"}}}