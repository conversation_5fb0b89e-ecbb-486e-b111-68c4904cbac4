{"channel": {"dashboard": "Channel Dashboard", "management": "Channel Management", "model": "Model Management", "monitor": "Status Monitoring", "spend": "Usage Details", "title": "Channel"}, "console": {"featureFlag": "Feature Flag", "migration": "Data Migration", "status": "Application Status", "title": "<PERSON><PERSON><PERSON>", "webhook": "Webhook"}, "growth": "User Growth", "market": {"dashboard": "Market Overview", "organizations": "Organization Management", "pluginConfig": "Plugin Configuration", "pluginDetail": "Plug<PERSON>", "pluginVersion": "Plugin Version", "plugins": "Plugin Management", "review": "Content Review", "settings": "System Settings", "title": "Market", "users": "User Management"}, "operation": {"feedback": "User <PERSON>", "segmentation": "Segmented Operations", "title": "Operations"}, "overview": "Overview", "reporting": {"product": "Business Data", "revenue": "Revenue Analysis", "title": "Report"}, "revenue": "Revenue Statistics", "sem": "Marketing Center", "spend": "Points Usage Details", "subscription": {"budgets": "Budget Management", "dashboard": "Subscription Dashboard", "meters": "Meters", "orders": "Order Management", "plans": "Plan Management", "title": "Subscription"}, "user": {"dashboard": "User Dashboard", "management": "User Management", "profile": "User Details", "restriction": "Restriction List", "title": "User"}}