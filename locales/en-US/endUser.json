{"drawer": {"message": {"columns": {"content": "Content", "createdAt": "Created At", "role": "Role"}, "title": "User Messages"}}, "table": {"columns": {"bizData": "Active Metrics", "createdAt": "Registration Time", "creditUsage": "Credit Usage", "emailOrUsernameOrUserId": "Email/Username/UserId", "freeCredit": "Free Credit Usage", "id": "ID", "plan": "Subscription Plan", "subscriptionCredit": "Subscription Usage", "username": "Username"}, "credit": {"expired": "Expired", "expiredAt": "Expired at {{expiredAt}}", "used": "Used: ${{used}}"}, "header": {"type": {"active": "Active Users", "all": "All Users", "inactive": "Inactive Users", "risk": "Risk Users"}}, "title": "User Management", "user": {"isOnboard": "Onboarding Completed", "messages": "Messages", "risk": "At Risk"}}, "tabs": {"type": {"active": "Active Users", "all": "All Users", "inactive": "Inactive Users", "paid": "Paid Users", "risk": "At-Risk Users"}}}