{"dashboard": {"analysis": {"title": "Plugin Analysis"}, "installFailure": {"failureCount": "Failure Count", "failureRate": "Failure Rate", "mostCommonError": "Most Common Error", "pluginName": "Plugin Name", "showTotal": "Showing {{start}} to {{end}} of {{total}} entries", "title": "Installation Failure Analysis", "totalAttempts": "Total Attempts"}, "plugins": {"callCount": "Call Count", "installCount": "Install Count", "name": "Plugin Name", "top10MostCalled": "Top 10 Most Called Plugins", "top10Popular": "Top 10 Most Popular Plugins"}, "sections": {"installTrend": {"title": "Installation Trend"}, "latestPlugins": {"title": "Latest Plugins"}, "topPlugins": {"installs": "Installations", "title": "Top Plugins"}}, "stats": {"averageRating": "Average Rating", "averageScores": "Average Scores", "devices": "Total Devices", "devicesTrend": "Device Growth Trend", "installsTrend": "Plugin Installations", "lastMonth": "Last Month", "pendingReview": "Plugins Pending Review", "pluginCalls": "Total Plugin Calls", "pluginsTrend": "New Plugins Added", "prevPeriod": "Comparison Period", "totalInstalls": "Total Plugin Installs", "totalPlugins": "Total Plugins"}, "title": "Market Overview", "trends": "Trends"}, "dependencies": {"actions": {"deleteSuccess": "Dependency deleted successfully", "editSuccess": "<PERSON><PERSON>den<PERSON> edited successfully"}, "deleteConfirm": "Confirm Deletion", "deleteConfirmContent": "Are you sure you want to delete this system dependency?", "edit": {"title": "Edit System Dependency"}, "form": {"checkCommand": "Check Command", "description": "Description", "installInstructions": "Installation Instructions", "installInstructionsPlaceholder": "Please enter installation instructions for {{platform}}", "name": "Name", "type": "Type"}, "table": {"actions": {"delete": "Delete", "edit": "Edit", "title": "Actions"}, "columns": {"checkCommand": "Check Command", "description": "Description", "installInstructions": "Installation Method", "name": "Dependency Name", "type": "Type"}, "title": "Dependency List"}, "title": "System Dependency Management"}, "env": {"deleteConfirm": "Confirm Deletion", "deleteConfirmContent": "Are you sure you want to delete the environment variable {{key}} for {{identifier}}?", "deleteSuccess": "Environment variable deleted successfully", "edit": "Edit Environment Variable", "editSuccess": "Environment variable edited successfully", "hideValue": "Hide Content", "showValue": "Show Content", "table": {"actions": {"delete": "Delete", "edit": "Edit"}, "columns": {"actions": "Actions", "description": "Description", "identifier": "Plugin", "key": "Variable Name", "value": "Variable Value"}}, "title": "Environment Variable Management"}, "organizations": {"actions": {"statusUpdateSuccess": "Organization status updated successfully"}, "table": {"actions": {"activate": "Activate", "deactivate": "Deactivate", "title": "Actions", "view": "View"}, "columns": {"createdAt": "Creation Time", "description": "Description", "memberCount": "Member Count", "name": "Organization Name", "status": {"active": "Active", "inactive": "Inactive", "title": "Status"}}, "title": "Organization List"}, "title": "Organization Management"}, "pluginConfig": {"actions": {"updateSuccess": "Configuration updated successfully"}, "form": {"fields": {"apiKey": {"title": "API Key", "tooltip": "Key for accessing the plugin API"}, "baseUrl": {"title": "Base URL", "tooltip": "Base URL for the plugin API"}, "enabled": {"title": "Enabled", "tooltip": "Whether to enable this plugin"}, "timeout": {"title": "Timeout", "tooltip": "API request timeout (milliseconds)"}}, "title": "Plugin Configuration"}, "title": "Plugin Configuration"}, "pluginVersion": {"actions": {"publishSuccess": "Version published successfully", "updateSuccess": "Version updated successfully"}, "form": {"fields": {"changelog": {"title": "Changelog", "tooltip": "Description of version updates"}, "version": {"title": "Version Number", "tooltip": "Follows semantic versioning"}}, "title": "Version Information"}, "table": {"actions": {"publish": "Publish", "title": "Actions", "unpublish": "Unpublish"}, "columns": {"changelog": "Changelog", "createdAt": "Creation Time", "publishedAt": "Publication Time", "status": {"draft": "Draft", "published": "Published", "title": "Status"}, "version": "Version Number"}, "title": "Version List"}, "title": "Version Management"}, "plugins": {"actions": {"addVersion": "Add New Version", "delete": "Delete Plugin", "deleteSuccess": "Plugin deleted successfully", "edit": "<PERSON> Plugin", "editDetails": "<PERSON> Plugin <PERSON>", "isFeaturedUpdateSuccess": "Plugin featured status updated successfully", "makePrivate": "Make Private", "makePublic": "Make Public", "publish": "Publish", "statusUpdateSuccess": "Plugin status updated successfully", "unpublish": "Unpublish", "updating": "Updating plugin information..."}, "deleteConfirm": "Confirm Deletion", "deleteConfirmContent": "Are you sure you want to delete this plugin?", "deployment": {"add": "Add Deployment Option", "addSuccess": "Deployment option added successfully", "addTitle": "Add Deployment Option", "args": "Arguments", "argsPlaceholder": "List of arguments separated by spaces", "cancel": "Cancel", "command": "Command", "commandLine": "Command Line", "commandRequired": "Please enter a command", "confirmDelete": "Confirm Deletion", "connection": "Connection Method", "connectionType": "Connection Type", "connectionTypeRequired": "Please select a connection type", "delete": "Delete", "deleteConfirmMessage": "Are you sure you want to delete this deployment option? This action cannot be undone.", "deleteError": "Deletion failed, please try again later", "deleteSuccess": "Deployment option deleted", "description": "Installation and deployment method of the plugin", "descriptionPlaceholder": "Optional description", "edit": "Edit", "editTitle": "Edit Deployment Option", "empty": "No deployment options available, click the 'Add Deployment Option' button to add", "installation": "Installation Method", "installationMethod": "Installation Method", "installationMethodRequired": "Please select an installation method", "other": "Other Settings", "packageName": "Package Name", "packageNameRequired": "Please enter the package name", "recommended": "Recommended", "save": "Save", "saveError": "Save failed, please try again later", "systemDependencies": "System Dependencies", "title": "Deployment Options", "updateSuccess": "Deployment option updated"}, "detail": {"sections": {"basic": {"fields": {"category": "Category", "description": "Description", "identifier": "Identifier", "installCount": "Installation Count", "name": "Plugin Name", "rating": "Rating", "status": "Status", "tags": "Tags", "visibility": "Visibility"}, "title": "Basic Information"}, "versions": {"fields": {"latest": "Latest Version", "prompts": "Number of Prompts", "resources": "Number of Resources", "tools": "Number of Tools", "validated": "Validated"}, "title": "Version History"}}, "title": "Plug<PERSON>"}, "details": {"additionalInfo": "Additional Information", "author": "Author", "category": "Category", "created": "Creation Time", "description": "Description", "documentation": "Documentation", "externalLinks": "External Links", "homepage": "Homepage", "notSpecified": "Not Specified", "tags": "Tags", "type": "Type", "updated": "Update Time"}, "featured": "Featured", "manifest": {"basicInfo": "Basic Information", "contentSummary": "Content Summary", "fields": {"author": "Author", "description": "Description", "name": "Name", "version": "Version"}, "noPrompts": "No prompts in this manifest", "noResources": "No resources in this manifest", "noTools": "No tools in this manifest", "prompts": "Prompts", "resources": "Resources", "tabs": {"overview": "Overview", "prompts": "Prompts", "raw": "Raw JSON", "resources": "Resources", "tools": "Tools"}, "title": "<PERSON><PERSON><PERSON>", "tools": "Tools", "version": "Version {{version}}"}, "stats": {"created": "Creation Time", "installationStats": "Installation Statistics", "installationTrend": "Installation Trend Chart", "installs": "Installations", "noRatings": "No Ratings Yet", "noScores": "No Ratings Yet", "owner": "Owner", "pluginId": "Plugin ID", "rating": "Rating", "ratingDistribution": "Rating Distribution", "ratingDistributionChart": "Rating Distribution Chart", "ratings": "Ratings", "ratingsAndReviews": "Ratings and Reviews", "reviews": "Reviews", "score": "Score", "scores": "Ratings", "title": "Plugin Statistics", "totalInstalls": "Total Installations", "updated": "Update Time"}, "status": {"archived": "Archived", "deprecated": "Deprecated", "published": "Published", "unpublished": "Unpublished"}, "table": {"actions": {"delete": "Delete", "makeFeatured": "Make Featured", "makeUnFeatured": "Remove Featured", "publish": "Publish", "title": "Actions", "unpublish": "Unpublish", "view": "View"}, "columns": {"capabilities": {"title": "Capabilities"}, "createdAt": "Created At", "installCount": "Installation Count", "isFeatured": {"title": "Is Featured"}, "name": "Plugin Name", "rating": "Rating", "score": "Score", "stats": "Statistics", "status": {"draft": "Draft", "published": "Published", "rejected": "Rejected", "review": "Pending Review", "title": "Status"}, "updatedAt": "Updated At", "visibility": {"internal": "Team", "private": "Private", "public": "Public", "title": "Visibility", "unlisted": "Unlisted"}}, "title": "Plugin List"}, "tabs": {"deployment": "Installation & Deployment", "manifest": "Manifest", "overview": "Overview", "stats": "Statistics", "versions": "Versions"}, "title": "Plugin Management", "versions": {"addNew": "Add New Version", "description": "All Versions", "latest": "Latest", "prompts": "Prompts", "resources": "Resources", "title": "Version History", "tools": "Tools", "validated": "Validated", "view": "View", "viewManifest": "View Manifest"}, "visibility": {"private": "Private", "public": "Public", "unlisted": "Unlisted"}}, "review": {"actions": {"statusUpdateSuccess": "Review status updated successfully"}, "table": {"actions": {"approve": "Approve", "reject": "Reject", "title": "Actions", "view": "View"}, "columns": {"name": "Name", "status": {"approved": "Approved", "pending": "Pending Review", "rejected": "Rejected", "title": "Status"}, "submittedAt": "Submission Time", "submitter": "Submitter", "type": {"plugin": "Plugin", "resource": "Resource", "title": "Type"}}, "title": "Review List"}, "title": "Content Review"}, "settings": {"actions": {"updateSuccess": "Settings updated successfully"}, "form": {"fields": {"pluginSubmission": {"allowedTypes": "Allowed Plugin Types (one per line)", "enable": "Enable Plugin Submission", "maxSize": "Maximum Plugin <PERSON> (MB)", "requireReview": "Require Plugin Review", "title": "Plugin Submission"}, "resourceSubmission": {"allowedTypes": "Allowed Resource Types (one per line)", "enable": "Enable Resource Submission", "maxSize": "Maximum Resource Size (MB)", "requireReview": "Require Resource Review", "title": "Resource Submission"}}, "title": "System Configuration"}, "title": "System Settings"}, "users": {"actions": {"roleUpdateSuccess": "User role updated successfully", "statusUpdateSuccess": "User status updated successfully"}, "table": {"actions": {"activate": "Activate", "deactivate": "Deactivate", "title": "Actions", "view": "View"}, "columns": {"createdAt": "Creation Time", "email": "Email", "name": "Username", "role": {"admin": "Administrator", "member": "Member", "title": "Role"}, "status": {"active": "Active", "inactive": "Inactive", "title": "Status"}}, "title": "User List"}, "title": "User Management"}}