{"addModel": {"addModelTitle": "添加渠道模型关联", "editModelTitle": "编辑渠道模型关联", "selectChannel": "选择渠道", "selectChannelRequired": "请选择渠道", "loadChannelListFailedMessage": "加载渠道列表失败:", "loadModelListFailedMessage": "加载模型列表失败:", "updateModelSuccess": "关联模型更新成功", "updateModelFailed": "关联模型更新失败", "addModelSuccess": "关联模型添加成功", "addModelFailed": "关联模型添加失败", "selectModel": "选择模型", "selectModelRequired": "请选择模型", "selectModelFailedMessage": "加载模型列表失败:", "selectModelSuccessMessage": "模型列表加载成功", "selectModelFailed": "模型列表加载失败", "selectModelSuccess": "模型列表加载成功", "weight": "权重", "selectChannelPlaceholder": "请先选择渠道", "weightRequired": "请输入权重值", "weightTooltip": "用于负载均衡的权重值，数值越大权重越高", "weightPlaceholder": "请输入权重值（可选）"}, "table": {"getAssociationInfoFailed": "获取关联信息失败", "deleteAssociationSuccess": "关联模型删除成功", "deleteAssociationFailed": "关联模型删除失败", "channel": "渠道", "model": "模型", "weight": "权重", "inputPrice": "输入价格", "outputPrice": "输出价格", "contextWindowTokens": "最大上下文窗口", "abilityList": "能力列表", "loadBalance": "负载均衡", "price": "价格", "edit": "编辑", "delete": "删除", "channelList": "渠道列表", "addChannel": "添加渠道", "noAssociationChannelModel": "暂无关联的渠道模型"}, "detail": {"open": "解除禁用", "disable": "禁用", "confirm": "确定", "delete": "删除", "edit": "编辑", "deleteConfirm": "你确定要删除吗？", "deleteSuccess": "删除成功", "deleteFailed": "删除失败", "disableConfirm": "你确定要禁用吗？", "disableSuccess": "禁用成功", "disableFailed": "禁用失败", "openConfirm": "你确定要解除禁用吗？", "openSuccess": "解除禁用成功", "openFailed": "解除禁用失败", "modelId": "模型ID", "modelDescription": "模型描述", "inputPrice": "输入价格", "outputPrice": "输出价格", "createdAt": "创建时间", "fallbackModel": "Fallback 模型", "fallbackModelId": "Fallback 模型ID", "empty": "请选择一个模型"}}