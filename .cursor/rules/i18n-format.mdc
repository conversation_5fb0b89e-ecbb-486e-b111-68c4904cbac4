---
description: 国际化
globs: 
alwaysApply: false
---
# 国际化文件格式规范


## 目录结构
翻译文件应该遵循以下目录结构：
```
src/locales/
├── default/                # 默认语言（中文）
│   ├── market.ts          # 市场相关翻译
│   ├── auth.ts            # 认证相关翻译
│   └── user.ts            # 用户相关翻译
```

## 基本规则

1. 每个模块的根级键应该使用 `title` 作为标题
2. 避免使用既是 key 又是 string 的情况
3. 使用嵌套对象来组织相关的翻译
4. 使用有意义的键名，避免重复

## 示例

```typescript
export default {
  // 正确示例
  plugins: {
    title: '插件管理',  // 使用 title 作为标题
    status: {
      updateSuccess: '状态更新成功',
    },
    table: {
      title: '插件列表',  // 子模块也使用 title
      columns: {
        name: '插件名称',
        status: '状态',
      },
    },
  },

  // 错误示例
  plugins: {
    plugins: '插件管理',  // 错误：既是 key 又是 string
    status: '状态',      // 错误：应该放在 table.columns 下
  },
};
```

## 常见模式

1. 列表页面：
```typescript
{
  title: '页面标题',
  table: {
    title: '表格标题',
    columns: {
      // 列定义
    },
  },
  actions: {
    // 操作按钮
  },
}
```

2. 详情页面：
```typescript
{
  title: '页面标题',
  sections: {
    basic: {
      title: '基本信息',
      fields: {
        // 字段定义
      },
    },
  },
}
```

3. 表单页面：
```typescript
{
  title: '页面标题',
  form: {
    title: '表单标题',
    fields: {
      // 字段定义
    },
    actions: {
      // 操作按钮
    },
  },
}
```

## 注意事项

1. 保持键名的一致性，例如：
   - 使用 `title` 而不是 `name` 或 `label` 作为标题
   - 使用 `actions` 而不是 `buttons` 或 `operations`
   - 使用 `fields` 而不是 `items` 或 `properties`

2. 状态和类型使用嵌套对象：
```typescript
{
  status: {
    active: '活跃',
    inactive: '禁用',
  },
  type: {
    plugin: '插件',
    resource: '资源',
  },
}
```

3. 成功/失败消息使用 `Success`/`Error` 后缀：
```typescript
{
  actions: {
    updateSuccess: '更新成功',
    updateError: '更新失败',
  },
}
```
