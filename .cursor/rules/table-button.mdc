---
description: 
globs: 
alwaysApply: false
---
# Table Button Implementation Rules

## Overview
This document defines the implementation rules for table action buttons in our application.

## Button Types

### Primary Actions
- Should be displayed as icon buttons
- Limited to 1-2 most frequently used actions
- Must be visually distinct and easily accessible
- Example: View details, Edit

### Secondary Actions
- Should be placed in a dropdown menu (three dots)
- Used for less frequent actions
- Can include multiple options
- Example: Delete, Change status, Change visibility

## Implementation Guidelines

### Component Structure
- Create a separate `Actions.tsx` component for table actions
- Use `@lobehub/ui` components for consistent styling
- Implement using `ActionIcon` for primary actions
- Use `Dropdown` component for secondary actions

### Code Example
```tsx
<Flexbox gap={8} horizontal justify={'flex-end'}>
  <ActionIcon
    active
    icon={PrimaryIcon}
    size={'small'}
    title={t('action.title')}
  />
  <Dropdown
    menu={{
      items: [
        {
          icon: <Icon icon={SecondaryIcon} />,
          key: 'action',
          label: t('action.label'),
          onClick: () => handleAction(),
        },
      ],
    }}
    trigger={['click']}
  >
    <ActionIcon active icon={MoreHorizontalIcon} size={'small'} />
  </Dropdown>
</Flexbox>
```

### Styling Rules
- Use consistent icon sizes (small)
- Maintain proper spacing between buttons (gap: 8)
- Align buttons to the right
- Use appropriate colors for different action types (danger, warning, etc.)

### Accessibility
- Always include title/tooltip for icon buttons
- Use semantic icons that clearly represent the action
- Ensure proper contrast for visibility

### State Management
- Handle loading states appropriately
- Show success/error feedback using message component
- Implement proper error handling

## Best Practices
1. Keep primary actions minimal and clear
2. Group related actions in dropdown
3. Use consistent naming conventions
4. Implement proper TypeScript types
5. Add proper translations for all labels
6. Handle loading and error states
7. Implement proper confirmation for destructive actions
