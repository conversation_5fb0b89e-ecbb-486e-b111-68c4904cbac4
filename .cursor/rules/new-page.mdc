---
description: 新增页面或者修改页面的规则
globs: 
alwaysApply: false
---
# 新页面实现规范

## 目录结构
新页面应该遵循以下目录结构：
```
src/app/[variants]/(main)/{feature}/
├── page.tsx                    # 页面入口
└── features/
    └── {FeatureName}/         # 功能组件
        ├── index.tsx          # 组件入口
        └── components/        # 子组件
```

## 页面实现示例

### 1. 页面入口
```tsx
// src/app/[variants]/(main)/{feature}/page.tsx
'use client';

import { memo } from 'react';
import FeatureComponent from './features/FeatureComponent';

const Page = memo(() => {
  return <FeatureComponent />;
});

export default Page;
```

### 2. 功能组件
```tsx
// src/app/[variants]/(main)/{feature}/features/FeatureComponent/index.tsx
'use client';

import { ProColumns } from '@ant-design/pro-components';
import { Tag } from '@lobehub/ui';
import { But<PERSON>, Space } from 'antd';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';
import Table from '@/components/Table';
import { trpcQuery } from '@/libs/trpc/client';

const FeatureComponent = memo(() => {
  const { t } = useTranslation('feature');
  const { data, isLoading } = trpcQuery.feature.getData.useQuery();

  const columns: ProColumns<any>[] = [
    {
      dataIndex: 'name',
      key: 'name',
      title: t('table.name'),
    },
    {
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'active' ? 'success' : 'default'}>
          {t(`table.status.${status}`)}
        </Tag>
      ),
      title: t('table.status'),
    },
    {
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button type="link" href={`/${feature}/${record.id}`}>
            {t('table.view')}
          </Button>
          <Button type="link" href={`/${feature}/${record.id}/edit`}>
            {t('table.edit')}
          </Button>
        </Space>
      ),
      title: t('table.actions'),
    },
  ];

  return (
    <Flexbox gap={16} padding={24}>
      <h1 style={{ fontSize: 24, fontWeight: 'bold' }}>{t('title')}</h1>
      <Table
        columns={columns}
        dataSource={data}
        loading={isLoading}
        rowKey="id"
      />
    </Flexbox>
  );
});

export default FeatureComponent;
```

## 通用规范

### 数据获取
- 使用 trpcQuery 进行数据获取
- 实现加载状态处理
- 使用 React Query 的 useQuery 进行数据缓存和状态管理

### UI 组件使用
- 使用 @lobehub/ui 组件库
- 使用 @ant-design/pro-components 的 ProTable
- 使用自定义的 Table 组件
- 使用 react-layout-kit 的 Flexbox 进行布局

### 样式规范
- 使用 antd-style 的 createStyles 和 useTheme
- 使用内联样式或 CSS-in-JS
- 实现响应式设计

### 国际化
- 使用 i18n 进行文本翻译
- 翻译 key 应该遵循 '{feature}.{key}' 的格式

### 错误处理
- 实现适当的错误处理机制
- 显示友好的错误提示
- 提供重试机制

### 性能优化
- 使用 memo 优化组件重渲染
- 实现适当的数据缓存策略
- 优化数据获取逻辑
