---
description: 新页面 | table | list | 表格
globs: 
alwaysApply: false
---
# ProTable 最佳实践指南

## 数据请求与渲染

### 1. 请求函数定义
```typescript
// 前端请求定义
const request: ProTableProps<any, any>['request'] = async (params, sorts) => {
  return trpcClient.yourService.getList.query({
    params,
    sorts: sorts as SortQuery,
  });
};

// 后端路由定义 - 数据库数据源
export const yourRouter = router({
  getList: procedure
    .input(
      z.object({
        params: YourQuerySchema.optional(),
        sorts: SortQuerySchema.optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const result = await ctx.yourModel.getList(input.params, input.sorts);
      return { success: true, ...result };
    }),
});

// 后端路由定义 - SDK 数据源
export const sdkRouter = router({
  getList: procedure
    .input(
      z.object({
        params: YourQuerySchema.optional(),
        sorts: SortQuerySchema.optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      // 直接调用 SDK 方法获取数据
      const result = await ctx.sdkClient.getList({
        ...input.params,
        // 处理排序参数
        orderBy: input.sorts ? Object.entries(input.sorts).map(([key, value]) => ({
          [key]: value === 'ascend' ? 'asc' : 'desc',
        })) : undefined,
      });
      
      // 统一返回格式
      return {
        data: result.items,
        total: result.totalCount,
        success: true,
      };
    }),
});
```

### 2. 数据模型定义
```typescript
// 查询参数类型定义
export const YourQuerySchema = z
  .object({
    field1: z.string().optional(),
    field2: z.string().optional(),
    range: z.tuple([z.string(), z.string()]).optional(),
  })
  .merge(CommonQuerySchema);

// 数据项类型定义
export interface YourItem {
  id: string;
  field1: string;
  field2: string;
  // ... 其他字段
}
````


### 3. 表格配置最佳实践
- 使用 `memo` 优化性能
- 配置 `key` 属性确保唯一性
- 合理设置列宽
- 使用 `hideInSearch` 和 `hideInTable` 控制显示位置

```typescript
import Table from '@/components/Table';

<Table<YourType>
  columns={columns}
  headerTitle={t('table.title')}
  key={'id'}
  request={request}
/>
```

### 5. 列定义规范
- 使用 `ProColumns` 类型定义列
- 合理使用 `render` 函数处理复杂渲染
- 配置 `sorter` 支持排序
- 使用 `valueType` 和 `valueEnum` 处理数据类型
```typescript
const columns: ProColumns<YourType>[] = [
  {
    dataIndex: 'field',
    title: t('table.columns.field'),
    width: 180,
    sorter: true,
    valueType: 'dateTime',
  }
];
```

### 6. 性能优化建议
- 使用 `memo` 包装组件
- 合理使用 `hideInSearch` 和 `hideInTable`
- 避免不必要的重渲染
- 使用 `width` 固定列宽
- 合理使用数据库索引（数据库数据源）
- 优化查询条件构建
- 合理使用 SDK 缓存（SDK 数据源）
- 注意 SDK 请求频率限制

### 7. 国际化支持
- 使用 `useTranslation` 处理文本
- 统一使用翻译 key
```typescript
const { t } = useTranslation('namespace');
```

### 8. 类型安全
- 为表格数据定义接口
- 使用泛型确保类型安全
- 使用 Zod 进行参数验证
```typescript
interface YourType {
  id: string;
  // ... other fields
}
```

## 注意事项
1. 确保请求函数返回正确的数据结构
2. 合理处理加载和错误状态
3. 注意分页参数的同步
4. 避免在 render 函数中进行复杂计算
5. 合理使用缓存策略
8. 注意数据安全性验证