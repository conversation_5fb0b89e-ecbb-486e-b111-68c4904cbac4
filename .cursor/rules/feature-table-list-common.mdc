---
description: 
globs: 
alwaysApply: false
---
# FeatureTableList 通用管理类表格实现规范

本规范适用于所有"特性管理类"表格（如依赖管理、环境变量管理、系统设置等）的 CRUD 管理页面，统一交互、结构和开发体验，提升可维护性和扩展性。

---

## 1. 目录结构建议

每个管理类表格建议采用如下目录结构，便于职责分明、复用和维护：

- `index.tsx`：主表格组件，负责渲染数据、弹窗状态管理。
- `Actions.tsx`：操作栏，支持编辑、删除，使用 Modal 二次确认。
- `EditForm.tsx`：编辑弹窗表单，字段根据业务自定义。
- `type.ts`：类型定义，便于类型复用。

示例（以 PluginEnvList 为例）：

```
/features/PluginEnvList/
  ├── index.tsx
  ├── Actions.tsx
  ├── EditForm.tsx
  └── type.ts
```

## 2. 组件职责分明

- **index.tsx**
  - 负责表格渲染、数据请求、弹窗状态管理。
  - 所有操作和表单逻辑外置，保持组件简洁。
- **Actions.tsx**
  - 只负责操作栏（编辑、删除），回调刷新表格。
  - 删除操作统一二次确认，内容支持多语言和变量插值。
- **EditForm.tsx**
  - 只负责编辑弹窗，表单字段自定义。
  - 提交前 cleanObject，避免无效字段。

## 3. 类型与数据请求

- 每个业务实体（如 SystemDependency、PluginEnv）都应有独立 type.ts。
- 统一通过 trpcClient/Query/Mutation 获取和操作数据。
- 表格 request 支持排序、分页、搜索参数。
- 后端接口建议包含 list、update、delete，参数与类型与前端一致。

### 以 PluginEnv 为例的类型定义

```ts
export interface PluginEnv {
  id: number;
  identifier: string;
  key: string;
  value: string;
  description?: string;
}
```

### 典型后端接口

- `getPluginEnvs`：分页获取列表，支持排序、搜索。
- `updatePluginEnv`：根据 id 更新字段。
- `deletePluginEnv`：根据 id 删除。
- `createPluginEnv`：新增。

## 4. 表格交互与列渲染

- 支持自定义列渲染（如 value 掩码、identifier 跳转、description 二级文本等）。
- 操作栏固定右侧，宽度统一。
- 编辑、删除操作均通过 Actions 组件触发。
- value 字段如为敏感信息，默认掩码，点击可切换明文/掩码。
- identifier 字段如为主键，支持跳转到详情页。

## 5. 编辑弹窗

- 弹窗表单字段根据业务自定义，建议使用 antd Form + Modal。
- 提交时调用 updateMutation，成功后关闭弹窗并刷新表格。
- 编辑表单提交前会 cleanObject，避免无效字段。

## 6. 删除操作

- 统一使用 antd Modal.confirm，内容支持多语言和变量插值。
- 删除操作二次确认，内容如"确定要删除环境变量 {identifier} - {key} 吗？"
- 删除成功后刷新表格。

## 7. 多语言与可维护性

- 多语言 key 统一在业务模块下维护，命名风格如 feature.table.columns.xxx、feature.actions.xxx。
- 删除、编辑等操作内容支持变量插值，便于国际化。

## 8. 交互一致性与扩展性

- 所有管理类列表均应遵循本规范，保证交互、代码结构、样式一致，便于维护和扩展。
- 支持批量导入、分组折叠、权限控制等高级功能可在此基础上扩展。

---

## 9. 典型实现流程（以环境变量管理为例）

1. **表格渲染**：index.tsx 负责数据请求、分页、排序、搜索，渲染表格。
2. **操作栏**：Actions.tsx 提供编辑、删除按钮，删除时二次确认。
3. **编辑弹窗**：EditForm.tsx 负责表单，支持字段校验，提交时 cleanObject。
4. **数据请求**：通过 trpcClient 调用后端接口，增删改查。
5. **多语言**：所有提示、按钮、弹窗内容均用 i18n key，便于国际化。
6. **扩展性**：如需批量导入、分组、权限等，均可在此结构基础上扩展。

---

## 10. 代码风格与技术栈建议

- UI 组件：antd、@lobehub/ui
- 数据请求：trpc
- 状态管理：React hooks
- 国际化：i18n
- 类型安全：TypeScript

---

## 11. 参考代码片段

### 表格主组件（index.tsx）

```tsx
// ... 省略 imports
const PluginEnvList = () => {
  // 数据请求、状态管理
  // ...
  return (
    <ProTable
      columns={columns}
      request={fetchPluginEnvs}
      // ...
    />
    <EditForm visible={editVisible} onOk={handleEdit} />
  );
};
```

### 操作栏（Actions.tsx）

```tsx
const Actions = ({ record, onEdit, onDelete }) => (
  <>
    <Button onClick={() => onEdit(record)}>编辑</Button>
    <Button danger onClick={() => confirmDelete(record)}>删除</Button>
  </>
);
```

### 编辑弹窗（EditForm.tsx）

```tsx
const EditForm = ({ visible, onOk, initialValues }) => (
  <Modal visible={visible} onOk={handleSubmit}>
    <Form initialValues={initialValues}>
      {/* 字段自定义 */}
    </Form>
  </Modal>
);
```

---

## 12. 总结

本规范适用于所有基于表格的管理类页面，统一交互、结构和开发体验，提升可维护性和扩展性。后续如有新业务场景，均可在此基础上扩展实现。

如需详细代码模板或具体实现示例，可随时补充。
